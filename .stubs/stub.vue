<template>
    <div class="{{ kebabCase name }}">
        {{ pascalCase name }}
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => '{{ kebabCase name }}-' + componentId);

export interface {{ pascalCase name }}Props {
    foo: string;
}
const props = withDefaults(defineProps<{{ pascalCase name }}Props>(), {
    foo: 'bar'
})

const emit = defineEmits<{
    change: [id: number]
}>()
</script>

// Inline CSS
<style lang="scss">
.{{ kebabCase name }} {
    
}
</style>
// Imported CSS
<style lang="scss">
@import "./{{ pascalCase name }}";
</style>
