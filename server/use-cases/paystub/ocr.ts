import BlobService from '~/server/services/blob';
import DocIntelligenceService from '~/server/services/doc-intelligence';
import { AZURE_DI_MODELS } from '~/server/services/doc-intelligence/constants';
import { Frequency as FrequencyBase } from '~/types/base';
import { AnalysisDocument, DocIntelligenceAnalisysResult } from '~/types/doc-intelligence';
import { Paystub, PaystubAnalysisResult, PaystubLine, PaystubOcrAnalysisFields } from '~/types/paystub';
import { consola } from 'consola';

export function uploadPaystubToAzureBlob(fileName: string, data: Buffer | string): Promise<string> {
    const blobName = `paystubs/${fileName}`;
    const containerName = 'user-uploads';

    if (!fileName || !data) {
        throw createError({ statusCode: 400, message: 'File name and data are required.' });
    }

    if (typeof data !== 'string' && !Buffer.isBuffer(data)) {
        throw createError({ statusCode: 400, message: 'Data must be a string or a Buffer.' });
    }

    consola.info(`Uploading paystub to Azure Blob Storage: ${blobName}`);

    return BlobService.uploadDataToAzureBlob({ containerName, blobName, data });
}

export async function analyzePaystubWithOCR(fileUrl: string): Promise<PaystubAnalysisResult> {
    if (!fileUrl) {
        throw createError({ statusCode: 400, message: 'File URL is required for OCR analysis.' });
    }
    consola.info(`Analyzing paystub with OCR: ${fileUrl}`);

    const analysisResult = await DocIntelligenceService.analyzeDocumentUrl<PaystubOcrAnalysisFields>(
        AZURE_DI_MODELS.PAYSTUB_V2,
        fileUrl,
    );

    if (!analysisResult) {
        return {
            paystub: null,
            ocr: {
                overallConfidence: -Infinity,
                fields: null,
            },
        };
    }

    const generatedPaystub = adaptPaystubAnalysisToPaystub(analysisResult);
    const confidence = checkDocumentAnalysisConfidence(analysisResult);
    const ocr = getOcrFields(analysisResult);

    return {
        paystub: generatedPaystub,
        ocr: {
            overallConfidence: confidence,
            fields: ocr,
        },
    };
}

export function checkDocumentAnalysisConfidence(
    analysis: DocIntelligenceAnalisysResult<PaystubOcrAnalysisFields> | null,
) {
    return analysis?.analyzeResult?.documents?.[0]?.confidence || 0;
}

export function getOcrFields(
    analysis: DocIntelligenceAnalisysResult<PaystubOcrAnalysisFields> | null,
): PaystubOcrAnalysisFields | null {
    return analysis?.analyzeResult?.documents?.[0]?.fields || null;
}

export function adaptPaystubAnalysisToPaystub(
    analysis: DocIntelligenceAnalisysResult<PaystubOcrAnalysisFields>,
): Paystub {
    const isInvalidAnalysis =
        !analysis ||
        !analysis.analyzeResult ||
        !Array.isArray(analysis.analyzeResult.documents) ||
        analysis.analyzeResult.documents.length === 0;

    if (isInvalidAnalysis) {
        throw new Error('Invalid analysis result for adapting to Paystub.');
    }

    const document = analysis.analyzeResult.documents[0];

    const builder = new PaystubBuilderFromOCR(document);

    return builder
        .withName()
        .withDate()
        .withFrequency()
        .withIncomeItems()
        .withPreTaxItems()
        .withPostTaxItems()
        .withTaxItems()
        .build();
}

class PaystubBuilderFromOCR {
    private paystub: Paystub;

    constructor(private document: AnalysisDocument<PaystubOcrAnalysisFields>) {
        this.paystub = {
            name: 'New Paystub',
            payDate: new Date(),
            frequency: FrequencyBase.Monthly,
            incomeItems: [],
            preTaxItems: [],
            postTaxItems: [],
            taxesItems: [],
            customItems: [],
        };

        if (!document.fields) {
            throw new Error('Document fields are missing in the analysis result.');
        }
    }

    withName(): this {
        const nameField = this.document.fields.EmployerName;
        if (nameField && nameField.valueString) {
            this.paystub.name = nameField.valueString;
        }
        return this;
    }

    withDate(): this {
        const { PaystubDate, PayBeginDate, PayEndDate } = this.document.fields;

        const payDateField = PaystubDate?.valueDate || PayBeginDate?.valueDate || PayEndDate?.valueDate;
        this.paystub.payDate = payDateField ?? new Date();

        return this;
    }

    withFrequency(): this {
        const { Frequency } = this.document.fields;

        if (Frequency?.valueString) {
            const frequencyValue = Frequency.valueString.toLowerCase();

            switch (frequencyValue) {
                case 'weekly':
                    this.paystub.frequency = FrequencyBase.Weekly;
                    break;
                case 'biweekly':
                    this.paystub.frequency = FrequencyBase.Biweekly;
                    break;
                case 'monthly':
                    this.paystub.frequency = FrequencyBase.Monthly;
                    break;
                case 'semimonthly':
                    this.paystub.frequency = FrequencyBase.SemiMonthly;
                    break;
                case 'quarterly':
                    this.paystub.frequency = FrequencyBase.Quarterly;
                    break;
                case 'annually':
                case 'yearly':
                    this.paystub.frequency = FrequencyBase.Annually;
                    break;
                default:
                    this.paystub.frequency = FrequencyBase.Monthly;
            }
        }

        return this;
    }

    withIncomeItems(): this {
        const { Income } = this.document.fields;

        if (Array.isArray(Income?.valueArray)) {
            this.paystub.incomeItems = Income.valueArray.map(
                (item): PaystubLine => ({
                    description: item.valueObject?.Description?.valueString || 'Unknown Income',
                    rate: item.valueObject?.Rate?.valueNumber || null,
                    hours: item.valueObject?.Hours?.valueNumber || null,
                    current: item.valueObject?.Current?.valueNumber || null,
                    ytd: item.valueObject?.YTD?.valueNumber || null,
                }),
            );
        }

        return this;
    }

    withTaxItems(): this {
        const { Taxes } = this.document.fields;

        if (Array.isArray(Taxes?.valueArray)) {
            this.paystub.taxesItems = Taxes.valueArray.map(
                (item): PaystubLine => ({
                    description: item.valueObject?.Description?.valueString || 'Unknown Tax',
                    rate: null,
                    hours: null,
                    current: item.valueObject?.Current?.valueNumber || null,
                    ytd: item.valueObject?.YTD?.valueNumber || null,
                }),
            );
        }

        return this;
    }

    withPreTaxItems(): this {
        const { PreTaxDeductions } = this.document.fields;

        if (Array.isArray(PreTaxDeductions?.valueArray)) {
            this.paystub.preTaxItems = PreTaxDeductions.valueArray.map(
                (item): PaystubLine => ({
                    description: item.valueObject?.Description?.valueString || 'Unknown Pre-Tax Deduction',
                    rate: null,
                    hours: null,
                    current: item.valueObject?.Current?.valueNumber || null,
                    ytd: item.valueObject?.YTD?.valueNumber || null,
                }),
            );
        }

        return this;
    }

    withPostTaxItems(): this {
        const { PostTaxDeductions } = this.document.fields;

        if (Array.isArray(PostTaxDeductions?.valueArray)) {
            this.paystub.postTaxItems = PostTaxDeductions.valueArray.map(
                (item): PaystubLine => ({
                    description: item.valueObject?.Description?.valueString || 'Unknown Post-Tax Deduction',
                    rate: null,
                    hours: null,
                    current: item.valueObject?.Current?.valueNumber || null,
                    ytd: item.valueObject?.YTD?.valueNumber || null,
                }),
            );
        }

        return this;
    }

    build(): Paystub {
        return this.paystub;
    }
}
