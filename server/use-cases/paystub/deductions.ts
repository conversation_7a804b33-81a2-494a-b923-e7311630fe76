import { Types } from 'mongoose';
import { Deduction, NonTaxDeduction, TaxDeduction, Frequency } from '~/types/base';
import { ExtractedDeductions, PaystubLine } from '~/types/paystub';
import { Expense, ExpenseTaxes, ExpenseInsurance, ExpenseEmployeeBenefit, ExpenseOther } from '~/types/expenses';

type DeductionFromPaystubBuilder = (item: PaystubLine) => Deduction;

interface DeductionMatchStrategy {
    matcher: (description: string) => boolean;
    builder: DeductionFromPaystubBuilder;
}

/**
 * Extract deductions from paystub lines
 */
export function extractDeductions(
    preTaxItems: PaystubLine[] = [],
    taxesItems: PaystubLine[] = [],
    postTaxItems: PaystubLine[] = [],
): ExtractedDeductions {
    const preTaxDeductions: Deduction[] = preTaxItems.map(item => createDeduction(item, 'preTax'));
    const taxDeductions: TaxDeduction[] = taxesItems.map(item => createTaxDeduction(item));
    const postTaxDeductions: Deduction[] = postTaxItems.map(item => createDeduction(item, 'postTax'));

    return {
        preTaxDeductions,
        taxDeductions,
        postTaxDeductions,
    };
}

/**
 * Create a tax deduction object from paystub line
 */
function createTaxDeduction(item: PaystubLine): TaxDeduction {
    const description = item.description.toLowerCase();

    const taxStrategies: DeductionMatchStrategy[] = [
        {
            matcher: desc => /federal|fed withholding|fed tax/i.test(desc),
            builder: item => ({
                kind: 'FederalTax',
                amount: item.current || 0,
                tax: true,
            }),
        },
        {
            matcher: desc => /state|st withholding|st tax/i.test(desc),
            builder: item => ({
                kind: 'StateTax',
                amount: item.current || 0,
                tax: true,
            }),
        },
        {
            matcher: desc => /medicare|medi/i.test(desc),
            builder: item => ({
                kind: 'Medicare',
                amount: item.current || 0,
                tax: true,
            }),
        },
        {
            matcher: desc => /social security|ss tax|fica|oasdi/i.test(desc),
            builder: item => ({
                kind: 'SocialSecurity',
                amount: item.current || 0,
                tax: true,
            }),
        },
    ];

    const strategy = taxStrategies.find(s => s.matcher(description));

    if (strategy) {
        return strategy.builder(item) as TaxDeduction;
    }

    return {
        kind: 'Other',
        amount: item.current || 0,
        tax: true,
    };
}

/**
 * Create a deduction object from paystub line
 */
function createDeduction(item: PaystubLine, deductionType: 'preTax' | 'postTax'): Deduction {
    const description = item.description.toLowerCase();
    const isPreTax = deductionType === 'preTax';

    if (isRetirementContribution(description)) {
        return {
            kind: 'RetirementContribution',
            amount: item.current || 0,
            tax: false,
        } as NonTaxDeduction;
    }

    const deductionStrategies: DeductionMatchStrategy[] = [
        {
            matcher: desc => /health|medical|dental|vision|insurance/i.test(desc),
            builder: item => ({
                kind: 'HealthInsurance',
                amount: item.current || 0,
                tax: false,
            }),
        },
    ];

    const strategy = deductionStrategies.find(s => s.matcher(description));

    if (strategy) {
        return strategy.builder(item);
    }

    return {
        kind: 'Other',
        amount: item.current || 0,
        tax: false,
    } as NonTaxDeduction;
}

/**
 * Check if description indicates a retirement contribution
 */
function isRetirementContribution(description: string): boolean {
    const retirementKeywords = [
        '401k',
        '401(k)',
        'retirement',
        'hsa',
        'health savings',
        'ira',
        'pension',
        'retirement savings',
        'deferred comp',
    ];

    return retirementKeywords.some(keyword => description.toLowerCase().includes(keyword.toLowerCase()));
}

/**
 * Convert deductions to expenses for portfolio storage
 */
export function convertDeductionsToExpenses(
    deductions: ExtractedDeductions,
    payDate: Date,
    frequency: Frequency = Frequency.Biweekly,
): Expense[] {
    const expenses: Expense[] = [];

    // Convert tax deductions to tax expenses
    if (deductions.taxDeductions && deductions.taxDeductions.length) {
        deductions.taxDeductions.forEach(deduction => {
            const taxExpense = createExpenseFromTaxDeduction(deduction, payDate, frequency);
            expenses.push(taxExpense);
        });
    }

    // Convert pre-tax deductions to expenses
    if (deductions.preTaxDeductions && deductions.preTaxDeductions.length) {
        deductions.preTaxDeductions.forEach(deduction => {
            const expense = createExpenseFromDeduction(deduction, payDate, frequency, true);
            if (expense) expenses.push(expense);
        });
    }

    // Convert post-tax deductions to expenses
    if (deductions.postTaxDeductions && deductions.postTaxDeductions.length) {
        deductions.postTaxDeductions.forEach(deduction => {
            const expense = createExpenseFromDeduction(deduction, payDate, frequency, false);
            if (expense) expenses.push(expense);
        });
    }

    return expenses;
}

/**
 * Create an expense from a tax deduction
 */
function createExpenseFromTaxDeduction(deduction: TaxDeduction, payDate: Date, frequency: Frequency): ExpenseTaxes {
    let expenseType: string;

    switch (deduction.kind) {
        case 'FederalTax':
            expenseType = 'Federal Tax Withholding/Est. Pmts';
            break;
        case 'StateTax':
            expenseType = 'State/Local Income Tax Withholding';
            break;
        case 'Medicare':
            expenseType = 'Medicare';
            break;
        case 'SocialSecurity':
            expenseType = 'Social Security';
            break;
        default:
            expenseType = 'Other';
    }

    return {
        id: new Types.ObjectId().toString(),
        category: 'Expense',
        kind: 'ExpenseTaxes',
        description: `Withheld ${deduction.kind} - ${payDate.toLocaleDateString()}`,
        amount: deduction.amount,
        frequency,
        mode: 'Standard',
        expenseType: expenseType as any,
    };
}

/**
 * Create an expense from a non-tax deduction
 */
function createExpenseFromDeduction(
    deduction: Deduction & { kind?: NonTaxDeduction['kind'] | TaxDeduction['kind'] },
    payDate: Date,
    frequency: Frequency,
    isPreTax: boolean,
): Expense | null {
    // Skip retirement contributions as they are handled by asset extraction
    if (deduction.kind === 'RetirementContribution') {
        return null;
    }

    if (deduction.kind === 'HealthInsurance') {
        return {
            id: new Types.ObjectId().toString(),
            category: 'Expense',
            kind: 'ExpenseInsurance',
            description: `Health Insurance Premium - ${payDate.toLocaleDateString()}`,
            amount: deduction.amount,
            frequency,
            mode: 'Standard',
            expenseType: 'Healthcare',
        } as ExpenseInsurance;
    }

    if (isPreTax) {
        return {
            id: new Types.ObjectId().toString(),
            category: 'Expense',
            kind: 'ExpenseEmployeeBenefit',
            description: `Pre-tax Deduction - ${payDate.toLocaleDateString()}`,
            amount: deduction.amount,
            frequency,
            mode: 'Standard',
        } as ExpenseEmployeeBenefit;
    }

    return {
        id: new Types.ObjectId().toString(),
        category: 'Expense',
        kind: 'ExpenseOther',
        description: `Payroll Deduction - ${payDate.toLocaleDateString()}`,
        amount: deduction.amount,
        frequency,
        mode: 'Standard',
    } as ExpenseOther;
}
