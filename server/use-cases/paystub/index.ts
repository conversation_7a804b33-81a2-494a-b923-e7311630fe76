import type { ExtractedAssets, Paystub, PaystubLine, PaystubReport, ProcessedPaystubData } from '~/types/paystub';
import type { Asset } from '~/types/assets';
import { Frequency } from '~/types/base';
import { Document, Types } from 'mongoose';
import { assetContributionStrategies, defaultAssetBuilder } from './assetContribution';
import { convertDeductionsToExpenses, extractDeductions } from './deductions';
import { IncomeEmployee } from '~/types/income';
import { Portfolio, PortfolioUser } from '~/types/portfolio';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';
import { UserSession } from '#nuxt-oidc/types';
import { PaystubModel } from '~/server/models/paystub.schema';
import { frequencyToMonthlyFactor } from '~/server/constants';
import { createError } from 'h3';

/**
 * Calculate total amount from a collection of paystub lines
 */
export function calculateTotalFromItems(items: PaystubLine[] | undefined, paystubFrequency?: Frequency): number {
    if (!items || !items.length) return 0;

    const multiplier = frequencyToMonthlyFactor[paystubFrequency || Frequency.Monthly] ?? 0;

    const paystubTotal = items.reduce((total, item) => {
        let itemValue = item.current || 0;
        const itemMultiplier = item.frequencyPerMonth ?? multiplier;

        return total + itemValue * itemMultiplier;
    }, 0);

    return paystubTotal;
}

/**
 * Calculate Gross Pay from income items
 */
export function calculateGrossPay(paystub: Paystub): number {
    return calculateTotalFromItems(paystub.incomeItems, paystub.frequency);
}

/**
 * Calculate Adjusted Gross Income (Gross Pay - Pre-Tax Deductions)
 */
export function calculateAdjustedGrossIncome(paystub: Paystub): number {
    const grossPay = calculateGrossPay(paystub);
    const preTaxDeductions = calculateTotalFromItems(paystub.preTaxItems, paystub.frequency);

    const agi = grossPay - preTaxDeductions;

    return agi;
}

/**
 * Calculate Net Pay (Take-Home Pay) as Adjusted Gross Income - Total Taxes Withheld
 */
export function calculateNetPay(paystub: Paystub): number {
    const adjustedGrossIncome = calculateAdjustedGrossIncome(paystub);
    const taxesWithheld = calculateTotalFromItems(paystub.taxesItems, paystub.frequency);

    const netPay = adjustedGrossIncome - taxesWithheld;

    return netPay;
}

/**
 * Calculate Cash Flow Net Income as Net Pay - Total Post-Tax Deductions
 */
export function calculateCashFlowNetIncome(paystub: Paystub): number {
    const netPay = calculateNetPay(paystub);
    const postTaxDeductions = calculateTotalFromItems(paystub.postTaxItems, paystub.frequency);

    const cashFlowNetIncome = netPay - postTaxDeductions;

    return cashFlowNetIncome;
}

/**
 * Get report of all key paystub calculations
 */
export function generatePaystubReport(paystub: Paystub): PaystubReport {
    const grossIncome = calculateGrossPay(paystub);
    const preTaxDeductions = calculateTotalFromItems(paystub.preTaxItems, paystub.frequency);
    const adjustedGrossIncome = calculateAdjustedGrossIncome(paystub);
    const taxesWithheld = calculateTotalFromItems(paystub.taxesItems, paystub.frequency);
    const netPay = calculateNetPay(paystub);
    const postTaxDeductions = calculateTotalFromItems(paystub.postTaxItems, paystub.frequency);
    const cashFlowNetIncome = calculateCashFlowNetIncome(paystub);

    return {
        payDate: paystub.payDate,
        grossIncome,
        preTaxDeductions,
        adjustedGrossIncome,
        taxesWithheld,
        netPay,
        postTaxDeductions,
        cashFlowNetIncome,
        frequency: paystub.frequency,
    };
}

/**
 * Extract pre-tax and post-tax contributions as assets
 */
export function extractAssetContributions(paystub: Paystub): ExtractedAssets {
    const preTaxAssets: Asset[] = [];
    const postTaxAssets: Asset[] = [];

    const hasPreTaxItems = Array.isArray(paystub.preTaxItems) && paystub.preTaxItems.length;
    // Process pre-tax asset contributions (401k, HSA, etc.)
    if (hasPreTaxItems) {
        paystub.preTaxItems.forEach(item => {
            if (isRetirementContribution(item.description)) {
                const asset = createAssetFromContribution({
                    item,
                    payDate: paystub.payDate,
                    isPreTax: true,
                    frequency: paystub.frequency,
                });
                if (asset) preTaxAssets.push(asset);
            }
        });
    }

    // Process post-tax asset contributions (Roth 401k, Roth IRA, etc.)
    const hasPostTaxItems = Array.isArray(paystub.postTaxItems) && paystub.postTaxItems.length;

    if (hasPostTaxItems) {
        paystub.postTaxItems.forEach(item => {
            if (isPostTaxAssetContribution(item.description)) {
                const asset = createAssetFromContribution({
                    item,
                    payDate: paystub.payDate,
                    isPreTax: false,
                    frequency: paystub.frequency,
                });
                if (asset) postTaxAssets.push(asset);
            }
        });
    }

    return {
        preTaxAssets,
        postTaxAssets,
    };
}

/**
 * Check if description indicates a retirement contribution
 */
function isRetirementContribution(description: string): boolean {
    const retirementKeywords = [
        '401k',
        '401(k)',
        'retirement',
        'hsa',
        'health savings',
        'ira',
        'pension',
        'retirement savings',
        'deferred comp',
    ];

    return retirementKeywords.some(keyword => description.toLowerCase().includes(keyword.toLowerCase()));
}

/**
 * Check if description indicates a post-tax asset contribution
 */
function isPostTaxAssetContribution(description: string): boolean {
    const postTaxKeywords = ['roth', 'after-tax', 'after tax', 'roth 401', 'roth ira'];

    return postTaxKeywords.some(keyword => description.toLowerCase().includes(keyword.toLowerCase()));
}

type AssetFromContributionOptions = {
    item: PaystubLine;
    payDate: Date;
    isPreTax: boolean;
    frequency?: Frequency;
};

/**
 * Create an asset record from paystub contribution
 */
function createAssetFromContribution({
    item,
    payDate,
    isPreTax,
    frequency,
}: AssetFromContributionOptions): Asset | null {
    if (!item.current || item.current <= 0) return null;

    const strategy = assetContributionStrategies(item, isPreTax).find(s => s.matcher(item.description));
    const baseAsset = strategy ? strategy.builder() : defaultAssetBuilder(item, isPreTax);

    return {
        id: new Types.ObjectId().toString(),
        category: 'Asset',
        equityValue: item.current,
        mode: 'Standard',
        acquiredDate: payDate,
        associatedExpenses: [],
        associatedLiabilities: [],
        ...baseAsset,
    } as Asset;
}

/**
 * Process paystub to extract assets and deductions
 */
export function processPaystubData(paystub: Paystub): ProcessedPaystubData {
    const assets = extractAssetContributions(paystub);
    const deductions = extractDeductions(paystub.preTaxItems, paystub.taxesItems, paystub.postTaxItems);

    const report = generatePaystubReport(paystub);
    const deductionExpenses = convertDeductionsToExpenses(deductions, paystub.payDate, paystub.frequency);

    return {
        _id: paystub._id?.toString(),
        name: paystub.name,
        report,
        assets,
        deductions,
        expenses: deductionExpenses,
    };
}

/*
 * Find portfolio by user session
 */
export async function findPortfolioByUser(user: UserSession) {
    const userId = user.userData._id;

    let portfolio = await Portfolios.findByOwner(userId).exec();

    if (!portfolio) {
        throw createError({
            statusCode: 404,
            statusMessage: 'Portfolio not found',
        });
    }

    if (!Array.isArray(portfolio.income)) {
        portfolio.income = [];
    }

    if (!Array.isArray(portfolio.assets)) {
        portfolio.assets = [];
    }

    if (!Array.isArray(portfolio.expenses)) {
        portfolio.expenses = [];
    }

    const updatedBy = user.originalUser ?? user.userData._id;

    (portfolio as unknown as Portfolio & PortfolioUser).updatedAt = new Date();
    (portfolio as unknown as Portfolio & PortfolioUser).updatedBy = updatedBy;

    return portfolio;
}

/*
 * Save a snapshot of the portfolio
 */
export async function savePortfolioSnapshot(
    portfolio: Document<unknown, {}, Portfolio> & Portfolio & { _id: Types.ObjectId } & { __v: number },
    userId: string,
) {
    const { _id, ...snapshotData } = portfolio.toObject();

    const portfolioSnapshot = new PortfolioSnapshots(snapshotData);
    portfolioSnapshot.snapshotDate = new Date();
    portfolioSnapshot.updatedBy = userId;

    await portfolioSnapshot.save();
}

/**
 * Find an IncomeEmployee in the portfolio by ID
 */
export function findIncomeEmployee(
    portfolio: Document<unknown, {}, Portfolio> & Portfolio & { _id: Types.ObjectId } & { __v: number },
    incomeEmployeeId: string,
) {
    return portfolio.income?.find(income => income.id === incomeEmployeeId && income.kind === 'IncomeEmployee') as
        | IncomeEmployee
        | undefined;
}

/**
 * Update an IncomeEmployee with processed paystub data
 */
export function pushNewPaystubToIncomeEmployee(
    incomeEmployee: IncomeEmployee,
    employmentType: 'W2' | '1099',
    paystubData: ProcessedPaystubData,
) {
    const { assets, deductions, report } = paystubData;

    const associatedAssets = assets.postTaxAssets
        .map(asset => asset.id)
        .concat(assets.preTaxAssets.map(asset => asset.id));

    if (!Array.isArray(incomeEmployee.paystubs)) {
        incomeEmployee.paystubs = [];
    }

    incomeEmployee.paystubs.push(new Types.ObjectId(paystubData._id) as any);

    const fallbackIncomeDescription = `Paystub ${incomeEmployee.paystubs.length}`;

    incomeEmployee.employmentType = employmentType;
    incomeEmployee.amount = report.cashFlowNetIncome;
    incomeEmployee.frequency = Frequency.Monthly;
    incomeEmployee.description = paystubData.name || fallbackIncomeDescription;
    incomeEmployee.associatedAssets = associatedAssets as unknown as Asset[];
    incomeEmployee.deductions = [
        ...deductions.preTaxDeductions,
        ...deductions.taxDeductions,
        ...deductions.postTaxDeductions,
    ];

    return incomeEmployee;
}

/**
 * Update an existing paystub in the IncomeEmployee
 */
export function updateExistingPaystubInIncomeEmployee(
    incomeEmployee: IncomeEmployee,
    employmentType: 'W2' | '1099',
    paystubData: ProcessedPaystubData,
) {
    const { assets, deductions, report } = paystubData;

    const associatedAssets = assets.postTaxAssets
        .map(asset => asset.id)
        .concat(assets.preTaxAssets.map(asset => asset.id));

    if (!Array.isArray(incomeEmployee.paystubs)) {
        incomeEmployee.paystubs = [];
    }

    // Find and update the existing paystub
    const paystubIndex = incomeEmployee.paystubs.findIndex(paystub => paystub.toString() === paystubData._id);

    if (paystubIndex === -1) {
        // If not found, add it as new
        incomeEmployee.paystubs.push(new Types.ObjectId(paystubData._id) as any);
    }

    const fallbackIncomeDescription = `Paystub ${incomeEmployee.paystubs.length}`;

    incomeEmployee.employmentType = employmentType;
    incomeEmployee.amount = report.cashFlowNetIncome;
    incomeEmployee.frequency = Frequency.Monthly;
    incomeEmployee.description = paystubData.name || fallbackIncomeDescription;
    incomeEmployee.associatedAssets = associatedAssets as unknown as Asset[];
    incomeEmployee.deductions = [
        ...deductions.preTaxDeductions,
        ...deductions.taxDeductions,
        ...deductions.postTaxDeductions,
    ];

    return incomeEmployee;
}

/**
 * Get a paystub by its ID
 */
export async function getPaystubById(paystubId: string) {
    const existingPaystub = await PaystubModel.findById(paystubId).exec();
    if (!existingPaystub) {
        throw createError({
            statusCode: 404,
            statusMessage: 'Paystub not found',
        });
    }
    return existingPaystub;
}
