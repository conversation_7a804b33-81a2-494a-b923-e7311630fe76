import { Types } from 'mongoose';
import { Asset } from '~/types/assets';
import { PaystubLine } from '~/types/paystub';

type AssetFromPaystubBuilder = (item: PaystubLine, isPreTax: boolean) => Partial<Asset>;

interface AssetContributionStrategy {
    matcher: (description: string) => boolean;
    builder: () => Partial<Asset>;
}

export const assetContributionStrategies = (item: PaystubLine, isPreTax: boolean): AssetContributionStrategy[] => [
    {
        matcher: desc => desc.includes('401'),
        builder: () => ({
            kind: 'Asset401k',
            description: `${isPreTax ? 'Traditional' : 'Roth'} 401(k) - ${item.description}`,
            accountType: isPreTax ? 'Traditional' : 'Roth',
        }),
    },
    {
        matcher: desc => desc.includes('ira'),
        builder: () => ({
            kind: 'AssetIRA',
            description: `${isPreTax ? 'Traditional' : 'Roth'} IRA - ${item.description}`,
            accountType: isPreTax ? 'Traditional IRA' : 'Roth IRA',
        }),
    },
    {
        matcher: desc => desc.includes('hsa'),
        builder: () => ({
            kind: 'AssetBrokerageAccount',
            description: `HSA - ${item.description}`,
            accountType: 'Qualified Account',
        }),
    },
];

export const defaultAssetBuilder: AssetFromPaystubBuilder = (item, isPreTax) => ({
    kind: 'AssetBrokerageAccount',
    description: `${isPreTax ? 'Pre-tax' : 'Post-tax'} Contribution - ${item.description}`,
    accountType: isPreTax ? 'Qualified Account' : 'Non-Qualified Account',
});

export function createAssetFromContribution(item: PaystubLine, payDate: Date, isPreTax: boolean): Asset | null {
    if (!item.current || item.current <= 0) return null;

    const description = item.description.toLowerCase();

    const assetStrategies: {
        matcher: (desc: string) => boolean;
        builder: () => Partial<Asset>;
    }[] = [
        {
            matcher: desc => desc.includes('401'),
            builder: () => ({
                kind: 'Asset401k',
                description: `${isPreTax ? 'Traditional' : 'Roth'} 401(k) - ${item.description}`,
                accountType: isPreTax ? 'Traditional' : 'Roth',
            }),
        },
        {
            matcher: desc => desc.includes('ira'),
            builder: () => ({
                kind: 'AssetIRA',
                description: `${isPreTax ? 'Traditional' : 'Roth'} IRA - ${item.description}`,
                accountType: isPreTax ? 'Traditional IRA' : 'Roth IRA',
            }),
        },
        {
            matcher: desc => desc.includes('hsa'),
            builder: () => ({
                kind: 'AssetBrokerageAccount',
                description: `HSA - ${item.description}`,
                accountType: 'Qualified Account',
            }),
        },
    ];

    const defaultBuilder = () => ({
        kind: 'AssetBrokerageAccount',
        description: `${isPreTax ? 'Pre-tax' : 'Post-tax'} Contribution - ${item.description}`,
        accountType: isPreTax ? 'Qualified Account' : 'Non-Qualified Account',
    });

    const strategy = assetStrategies.find(s => s.matcher(description));
    const baseAsset = strategy ? strategy.builder() : defaultBuilder();

    return {
        id: new Types.ObjectId().toString(),
        category: 'Asset',
        equityValue: item.current,
        mode: 'Standard',
        acquiredDate: payDate,
        associatedExpenses: [],
        associatedLiabilities: [],
        ...baseAsset,
    } as Asset;
}
