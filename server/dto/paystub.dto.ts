import { z } from 'zod';

const paystubItemDefinition = z.object({
    _id: z.string().optional(),
    description: z.string(),
    frequencyPerMonth: z.number().nullable(),
    rate: z.number().nullable(),
    hours: z.number().nullable(),
    current: z.number().nullable(),
    ytd: z.number().nullable(),
    customLineOptions: z
        .object({
            title: z.string().optional(),
        })
        .optional(),
});

const paystubSchema = z
    .object({
        name: z.string(),
        payDate: z.coerce.date(),
        frequency: z.string(),
        incomeItems: z.array(paystubItemDefinition),
        preTaxItems: z.array(paystubItemDefinition),
        postTaxItems: z.array(paystubItemDefinition),
        taxesItems: z.array(paystubItemDefinition),
        customItems: z.array(paystubItemDefinition),
        createdAt: z.coerce.date().optional(),
        updatedAt: z.coerce.date().optional(),
        updatedBy: z.string().optional(),
    })
    .strict();

export const PaystubRequestBody = z.object({
    employmentType: z.enum(['W2', '1099']),
    incomeEmployeeId: z.string(),
    paystub: paystubSchema,
});

export const PutPaystubRequest = z.object({
    incomeEmployeeId: z.string(),
    paystub: paystubSchema,
});

export const PatchPaystubRequest = z.object({
    paystubId: z.string(),
    paystub: paystubSchema,
    incomeEmployeeId: z.string(),
});
