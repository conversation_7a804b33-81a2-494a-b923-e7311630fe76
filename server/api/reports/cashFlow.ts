﻿// noinspection JSUnusedGlobalSymbols

import _ from 'lodash';
import type { Expense } from '~/types/expenses';
import type { Income, IncomeEmployee } from '~/types/income';
import type { Portfolio } from '~/types/portfolio';
import type { CashFlowData, CashFlowItem } from '~/types/reports';
import { Frequency } from '~/types/base';
import { NormalizeToFrequency } from '~/utils';
import * as Categorizer from '~/server/utils/categorizer';
import { getUserId } from '../../utils/user';
import { Portfolios } from '~/server/models/portfolio.schema';
import * as PaystubUseCases from '~/server/use-cases/paystub';
import { PaystubModel } from '~/server/models/paystub.schema';
import { Paystub } from '~/types/paystub';

export default defineEventHandler(async (event): Promise<CashFlowData> => {
    console.log('Reports - CashFlow API: get');
    const userId = await getUserId(event);
    console.log(`User Key: ${userId}`);

    const portfolio = await Portfolios.findByOwner(userId).lean().populate('income.paystubs', {}, PaystubModel);
    if (!portfolio) {
        console.log('Could not find portfolio');
        return null;
    }

    const getIncomeCategorized = getCategorized(p => p.income, Categorizer.getIncomeSubcategory);
    const getExpensesCategorized = getCategorized(
        p =>
            p.expenses
                ?.filter(expense => expense.kind !== 'ExpenseSavingsBrokerage')
                .filter(expense => expense.amount || expense.value),
        Categorizer.getExpenseSubcategory,
    );
    const getSavingsCategorized = getCategorized(
        p =>
            p.expenses
                ?.filter(expense => expense.kind === 'ExpenseSavingsBrokerage')
                .filter(expense => expense.amount || expense.value),
        Categorizer.getSavingsSubCategory,
    );

    return {
        income: getIncomeCategorized(portfolio),
        expenses: getExpensesCategorized(portfolio),
        savings: getSavingsCategorized(portfolio),
    };
});

function getCategorized<T extends Expense | Income>(
    getItems: (p: Portfolio) => T[] | null | undefined,
    getCategory: (t: T, p: Portfolio) => string,
): (portfolio: Portfolio) => CashFlowItem[] {
    return (portfolio: Portfolio) => {
        const data = getItems(portfolio) ?? [];
        const categorizedData = data.map(i => {
            // Simple expenses and income are always Annual
            let amount;
            if (['Income', 'Expenses'].includes(i.category) && i.mode === 'Simple') {
                amount = i.value;
            } else if (i.kind === 'IncomeEmployee') {
                const { paystubs } = i as IncomeEmployee;
                amount =
                    (paystubs as Paystub[])?.reduce(
                        (acc, paystub) => acc + PaystubUseCases.calculateCashFlowNetIncome(paystub),
                        0,
                    ) ?? 0;
            } else {
                amount = i.amount ?? i.value;
            }

            let fromFrequency = i.frequency;
            if (!fromFrequency && (i.category === 'Income' || i.category === 'Expense'))
                fromFrequency = i.paymentFrequency ?? Frequency.Annually;
            if (!fromFrequency) fromFrequency = Frequency.Annually;

            if (i.kind === 'IncomeDividendInterest' && !i.amount && i.amountDividend && i.amountInterest) {
                // For IncomeDividendInterest, the total is split between dividend and interest
                amount = i.amountDividend + i.amountInterest;
            }

            return {
                category: getCategory(i, portfolio),
                annual: NormalizeToFrequency(amount ?? 0, fromFrequency, Frequency.Annually),
                monthly: NormalizeToFrequency(amount ?? 0, fromFrequency, Frequency.Monthly),
            };
        });

        return _(categorizedData)
            .groupBy(i => i.category)
            .map((values, key) => {
                return {
                    label: key,
                    annual: _.sumBy(values, i => i.annual),
                    monthly: _.sumBy(values, i => i.monthly),
                };
            })
            .value();
    };
}
