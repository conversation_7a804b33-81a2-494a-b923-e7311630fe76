﻿// noinspection JSUnusedGlobalSymbols

import _ from "lodash";
import type {Portfolio} from "~/types/portfolio";
import type {NetWorthData, NetWorthItem} from "~/types/reports";
import * as Categorizer from "~/server/utils/categorizer";
import {getUserId} from "../../utils/user";
import {Portfolios} from "~/server/models/portfolio.schema";

export default defineEventHandler(async (event) : Promise<NetWorthData> => {
    console.log("Reports - NetWorth API: get")
    const userId = await getUserId(event);
    console.log(`User Key: ${userId}`);

    const portfolio = await Portfolios.findByOwner(userId).lean();
    if (!portfolio) {
        console.log('Could not find portfolio');
        setResponseStatus(event, 400, "Portfolio not found");
        throw new Error("Portfolios not found");
    }
    const getAssetsCategorized = getCategorized(p => p.assets, Categorizer.getAssetSubcategory, i => i.equityValue);
    const getLiabilitiesCategorized = getCategorized(p => p.liabilities, Categorizer.getLiabilitySubCategory, i => i.balance);
    
    return {
        assets: getAssetsCategorized(portfolio),
        liabilities: getLiabilitiesCategorized(portfolio)
    }
});

function getCategorized<T>(
    getItems: (p:Portfolio) => T[] | null | undefined,
    getCategory: (t: T, p: Portfolio) => string,
    getAmount: (t: T) => number
) : (portfolio: Portfolio) => NetWorthItem[] {
    return (portfolio: Portfolio) => {
        const data = getItems(portfolio) ?? [];
        const categorizedData = data.map(i => ({
            category: getCategory(i, portfolio),
            value: getAmount(i)
        }));
        return _(categorizedData).groupBy(i => i.category).map((values, key) => {
            return {
                label: key,
                value: _.sumBy(values, i => i.value)
            }
        }).value()
    }
}
