﻿// noinspection JSUnusedGlobalSymbols

import { Frequency } from '~/types/base';
import type { Portfolio, PortfolioSnapshot } from '~/types/portfolio';
import type { DashboardReportCategory, DashboardReportData } from '~/types/dashboardReport';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';
import * as DateFns from 'date-fns';
import _ from 'lodash';
import { NormalizeToFrequency } from '~/utils';
import * as Categorizer from '../../utils/categorizer';
import { getTotal } from '../../utils/categorizer';
import { getUserId } from '../../utils/user';

export default defineEventHandler(async (event): Promise<DashboardReportData | null> => {
    console.log('Dashboard API: get');

    const userId = await getUserId(event);
    console.log(`User Key: ${userId}`);

    const portfolio = await Portfolios.findByOwner(userId).lean<Portfolio>();
    if (!portfolio) {
        console.log('Could not find portfolio');
        setResponseStatus(event, 400, 'Portfolio not found');
        throw new Error('Portfolio not found');
    }

    const portfolios = await PortfolioSnapshots.groupByDay(userId);

    if (!portfolios.length) {
        portfolios.push({
            ...portfolio,
            snapshotDate: new Date(),
            updatedBy: userId,
        });
    }

    const getNetWorthValue = (p: Portfolio) => getAssetsValue(p) - getLiabilitiesValue(p);
    const getAssetsValue = getTotal(
        p => p.assets,
        i => i.equityValue,
    );
    const getLiabilitiesValue = getTotal(
        p => p.liabilities,
        i => i.balance,
    );
    const getIncomeValue = (freq: Frequency) =>
        getTotal(
            p => p.income,
            i => NormalizeToFrequency(i.amount, i.frequency ?? i.paymentFrequency, freq),
        );
    const getExpensesValue = (freq: Frequency) =>
        getTotal(
            p => p.expenses,
            i => NormalizeToFrequency(i.amount, i.frequency ?? i.paymentFrequency, freq),
        );
    const getAssetsCategorized = getCategorized(
        p => p.assets,
        Categorizer.getAssetSubcategory,
        i => i.equityValue,
    );
    const getLiabilitiesCategorized = getCategorized(
        p => p.liabilities,
        Categorizer.getLiabilitySubCategory,
        i => i.balance,
    );
    const getIncomeCategorized = getCategorized(
        p => p.income,
        Categorizer.getIncomeSubcategory,
        i => i.amount,
    );
    const getExpensesCategorized = getCategorized(
        p => p.expenses,
        Categorizer.getExpenseSubcategory,
        i => i.amount,
    );

    const assetCategories = getAssetsCategorized(portfolio);
    const liabilityCategories = getLiabilitiesCategorized(portfolio);

    return {
        disclaimer: `As of ${DateFns.format(Date.now(), 'MMM d, yyyy')}`,
        netWorth: {
            value: getNetWorthValue(portfolio),
            timeSeries: getTimeSeries(portfolios, getNetWorthValue),
            categories: assetCategories.concat(liabilityCategories),
        },
        assets: {
            value: getAssetsValue(portfolio),
            timeSeries: getTimeSeries(portfolios, getAssetsValue),
            categories: assetCategories,
        },
        liabilities: {
            value: getLiabilitiesValue(portfolio),
            timeSeries: getTimeSeries(portfolios, getLiabilitiesValue),
            categories: liabilityCategories,
        },
        income: {
            value: {
                annually: getIncomeValue(Frequency.Annually)(portfolio),
                monthly: getIncomeValue(Frequency.Monthly)(portfolio),
            },
            timeSeries: getTimeSeries(portfolios, getIncomeValue(Frequency.Annually)),
            categories: getIncomeCategorized(portfolio),
        },
        expenses: {
            value: {
                annually: getExpensesValue(Frequency.Annually)(portfolio),
                monthly: getExpensesValue(Frequency.Monthly)(portfolio),
            },
            timeSeries: getTimeSeries(portfolios, getExpensesValue(Frequency.Annually)),
            categories: getExpensesCategorized(portfolio),
        },
    };
});

function getTimeSeries(portfolios: PortfolioSnapshot[], getValue: (portfolio: Portfolio) => number) {
    // : DashboardRowItem["charts"] {
    if (!portfolios.length) {
        return [];
    }

    return portfolios.map(portfolio => {
        return {
            date: portfolio.snapshotDate,
            value: getValue(portfolio),
        };
    });
}

function getCategorized<T extends { category: string; kind: string }>(
    getItems: (p: Portfolio) => T[] | null | undefined,
    getSubcategory: (t: T, p: Portfolio) => string,
    getAmount: (t: T) => number,
): (portfolio: Portfolio) => DashboardReportCategory[] {
    function getCategory(item: T, p: Portfolio) {
        return {
            category: item.category,
            subcategory: getSubcategory(item, p),
            kind: _.startCase(item.kind.replace(item.category, '')),
            value: getAmount(item),
        };
    }
    return (portfolio: Portfolio) => getItems(portfolio)?.map(i => getCategory(i, portfolio)) ?? [];
}
