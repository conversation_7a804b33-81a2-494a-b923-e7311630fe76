﻿// noinspection JSUnusedGlobalSymbols

import { Frequency } from '~/types/base';
import type { Portfolio, PortfolioSnapshot } from '~/types/portfolio';
import type { DashboardReportTimeSeries } from '~/types/dashboardReport';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';
import * as DateFns from 'date-fns';
import { NormalizeToFrequency } from '~/utils';
import { getTotal } from '../../utils/categorizer';
import { getUserId } from '../../utils/user';
import defu from 'defu';

export type TimeSeriesType = 'assets' | 'liabilities' | 'income' | 'expenses';
export type TimeSeriesRequest = {
    startDate?: string | Date;
    endDate?: string | Date;
    frequency?: Frequency;
    types?: TimeSeriesType[];
};
export type TimeSeriesResponse = {
    startDate: Date;
    endDate: Date;
    frequency: Frequency;
    disclaimer: string;
    data: Record<TimeSeriesType, DashboardReportTimeSeries[]>;
};

export default defineEventHandler(async (event): Promise<TimeSeriesResponse | null> => {
    const now = new Date();
    const request = defu(getQuery<TimeSeriesRequest>(event), {
        startDate: new Date(now.getFullYear(), now.getMonth() - 13, 1), // 13 months ago
        endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0), // End of the current month
        frequency: Frequency.Monthly,
        types: ['assets', 'liabilities', 'income', 'expenses'],
    }) as Required<TimeSeriesRequest>;

    console.log('TimeSeries API: get {request}', request);

    const userId = await getUserId(event);
    console.log(`User Key: ${userId}`);

    const portfolio = await Portfolios.findByOwner(userId).lean<Portfolio>();
    if (!portfolio) {
        console.log('Could not find portfolio');
        setResponseStatus(event, 400, 'Portfolio not found');
        throw new Error('Portfolio not found');
    }

    const portfolios = await PortfolioSnapshots.groupByDay(userId);

    if (!portfolios.length) {
        portfolios.push({
            ...portfolio,
            snapshotDate: new Date(),
            updatedBy: userId,
        });
    }

    const getAssetsValue = getTotal(
        p => p.assets,
        i => i.equityValue,
    );
    const getLiabilitiesValue = getTotal(
        p => p.liabilities,
        i => i.balance,
    );
    const getIncomeValue = (freq: Frequency) =>
        getTotal(
            p => p.income,
            i => {
                console.log('income', i);
                let incomeValue = i.amount ?? 0;
                if (i.forms) {
                    const incomeFormFields = ['nonemployeeCompensation', 'wages'];
                    const formIncomeTotals =
                        i.forms.reduce((acc: number, form) => {
                            const totalFormIncome =
                                incomeFormFields.reduce((acc: number, fieldName: string) => {
                                    if (form[fieldName] as number) {
                                        acc += form[fieldName];
                                    }
                                    return acc;
                                }, 0) ?? 0;
                            return totalFormIncome + acc;
                        }, 0) ?? 0;
                    console.log('formIncomeTotals', formIncomeTotals);
                    incomeValue += formIncomeTotals;
                    return NormalizeToFrequency(incomeValue, Frequency.Annually, freq);
                } else {
                    return NormalizeToFrequency(incomeValue, i.frequency ?? i.paymentFrequency, freq);
                }
            },
        );
    const getExpensesValue = (freq: Frequency) =>
        getTotal(
            p => p.expenses,
            i => NormalizeToFrequency(i.amount, i.frequency ?? i.paymentFrequency, freq),
        );

    // Filter portfolios based on date-range
    const filteredPortfolios = portfolios.filter(
        portfolio =>
            portfolio.snapshotDate &&
            portfolio.snapshotDate >= DateFns.toDate(request.startDate) &&
            portfolio.snapshotDate <= DateFns.toDate(request.endDate),
    );

    // Initialize the data object with empty arrays for each requested type
    const data: Record<TimeSeriesType, DashboardReportTimeSeries[]> = {} as Record<
        TimeSeriesType,
        DashboardReportTimeSeries[]
    >;

    const timePeriods = getTimePeriods(request);

    // Populate the data object based on requested types
    if (request.types.includes('assets')) {
        data.assets = getTimeSeries(timePeriods, filteredPortfolios, getAssetsValue);
    }

    if (request.types.includes('liabilities')) {
        data.liabilities = getTimeSeries(timePeriods, filteredPortfolios, getLiabilitiesValue);
    }

    if (request.types.includes('income')) {
        data.income = getTimeSeries(timePeriods, filteredPortfolios, getIncomeValue(request.frequency));
    }

    if (request.types.includes('expenses')) {
        data.expenses = getTimeSeries(timePeriods, filteredPortfolios, getExpensesValue(request.frequency));
    }

    return {
        startDate: DateFns.toDate(request.startDate),
        endDate: DateFns.toDate(request.endDate),
        frequency: request.frequency,
        disclaimer: `As of ${DateFns.format(Date.now(), 'MMM d, yyyy')}`,
        data,
    };
});

function getTimeSeries(
    timePeriods: DateFns.Interval[],
    portfolios: PortfolioSnapshot[],
    getValue: (portfolio: Portfolio) => number,
) {
    if (!portfolios.length) {
        return [];
    }

    const findPortfolio = (interval: DateFns.Interval) =>
        portfolios
            .filter(portfolio => DateFns.isWithinInterval(portfolio.snapshotDate, interval))
            .toSorted((a, b) => b.snapshotDate.getTime() - a.snapshotDate.getTime())
            .at(0);

    return timePeriods.map(p => {
        const portfolio = findPortfolio(p);
        return {
            date: DateFns.toDate(p.start),
            value: portfolio ? getValue(portfolio) : 0,
        };
    });
}

/**
 * Gets all time periods between a startDate and endDate based on the provided frequency.
 *
 * @returns An array of intervals representing the time periods.
 */
function getTimePeriods(request: Required<TimeSeriesRequest>): DateFns.Interval[] {
    //DateFns.toDate(request.startDate), DateFns.toDate(request.endDate), request.frequency
    const interval: DateFns.Interval = { start: request.startDate, end: request.endDate };
    switch (request.frequency) {
        case Frequency.Daily:
            return DateFns.eachDayOfInterval(interval).map(dt => ({
                start: dt,
                end: DateFns.endOfDay(dt),
            }));
        case Frequency.Weekly:
            return DateFns.eachWeekOfInterval(interval).map(dt => ({
                start: dt,
                end: DateFns.endOfWeek(dt),
            }));
        case Frequency.Biweekly:
            return DateFns.eachWeekOfInterval(interval, { step: 2 }).map(dt => ({
                start: dt,
                end: DateFns.endOfWeek(DateFns.addWeeks(dt, 1)),
            }));
        case Frequency.SemiMonthly: {
            return DateFns.eachMonthOfInterval(interval)
                .map(date => [date, DateFns.setDate(date, 15)])
                .flat()
                .filter(date => DateFns.isWithinInterval(date, interval))
                .sort((a, b) => a.getTime() - b.getTime())
                .map(dt => ({
                    start: dt,
                    end:
                        dt.getDay() === 1
                            ? DateFns.subMilliseconds(DateFns.setDate(dt, 15), 1)
                            : DateFns.endOfMonth(dt),
                }));
        }
        case Frequency.Monthly:
            return DateFns.eachMonthOfInterval(interval).map(dt => ({
                start: dt,
                end: DateFns.endOfMonth(dt),
            }));
        case Frequency.Bimonthly:
            return DateFns.eachMonthOfInterval(interval, { step: 2 }).map(dt => ({
                start: dt,
                end: DateFns.endOfMonth(DateFns.addMonths(dt, 1)),
            }));
        case Frequency.Quarterly:
            return DateFns.eachQuarterOfInterval(interval).map(dt => ({
                start: dt,
                end: DateFns.endOfQuarter(dt),
            }));
        case Frequency.Trimesterly:
            return DateFns.eachMonthOfInterval(interval, { step: 4 }).map(dt => ({
                start: dt,
                end: DateFns.endOfMonth(DateFns.addMonths(dt, 3)),
            }));
        case Frequency.SemiAnnually:
            return DateFns.eachMonthOfInterval(interval, { step: 6 }).map(dt => ({
                start: dt,
                end: DateFns.endOfMonth(DateFns.addMonths(dt, 5)),
            }));
        case Frequency.Annually:
        case Frequency.Yearly:
            return DateFns.eachYearOfInterval(interval).map(dt => ({
                start: dt,
                end: DateFns.endOfYear(dt),
            }));
        default:
            throw new Error(`Frequency "${request.frequency}" is not supported.`);
    }
}
