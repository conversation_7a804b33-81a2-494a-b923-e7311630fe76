﻿// noinspection JSUnusedGlobalSymbols

import ExcelJS from "exceljs";
import processAssets from "~/server/utils/import/assets";
import processLiabilities from "~/server/utils/import/liabilities";
import processInsurance from "~/server/utils/import/insurance";
import processIncome from "~/server/utils/import/income";
import processExpenses from "~/server/utils/import/expenses";
import type {Portfolio, PortfolioSnapshot} from "~/types/portfolio";
import {nanoid} from "nanoid";
import {Portfolios, PortfolioSnapshots} from "~/server/models/portfolio.schema";
import {UserModel} from "~/server/models/user.schema";

export default defineEventHandler(async (event) => {
    console.log("Import API: post");
    const body = await readBody<{ email: string, files: File[] }>(event)
    const savedPortfolios = [];
    const savedSnapshots = [];
    body.files.sort((a, b) => b.lastModified - a.lastModified);
    
    for (const file of body.files) {
        console.log(`${file.name} - ${new Date(file.lastModified).toUTCString()}`);
        const {binaryString} = parseDataUrl(file.content);

        const workbook =  new ExcelJS.Workbook();
        await workbook.xlsx.load(binaryString);

        let owner = await UserModel.findOne({ email: body.email });
        if (!owner) {
            owner = await UserModel.create({
                _id: nanoid(),
                authId: `temp|${body.email}`,
                email: body.email,
                joinDate: new Date(),
                role: 'Client'
            });
        }
        
        const portfolio : Portfolio = {
            assets: processAssets(workbook),
            expenses: processExpenses(workbook),
            income: processIncome(workbook),
            liabilities: processLiabilities(workbook),
            insurance: processInsurance(workbook),
            owners: [{
                owner: owner._id,
                userType: 'Owner',
                createdAt: new Date()
            }]
        }
        
        if (!savedPortfolios.length) {
            savedPortfolios.push(
                await Portfolios.findOneAndReplace(
                    {owners: {$elemMatch: {userType: 'Owner', owner: owner._id}}},
                    portfolio,
                    {upsert: true}
                )
            );
        }
        
        const portfolioSnapshot : PortfolioSnapshot = {
            ...portfolio,
            snapshotDate: new Date(file.lastModified),
            updatedBy: owner._id
        }
        savedSnapshots.push(await PortfolioSnapshots.create(portfolioSnapshot));
    }
    
    return {
        ok: true,
        portfolios: savedPortfolios,
        snapshots: savedSnapshots
    };
});

interface File {
    name: string
    content: string
    size: string
    type: string
    lastModified: number
}
