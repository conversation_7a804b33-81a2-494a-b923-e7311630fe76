﻿// noinspection JSUnusedGlobalSymbols

import Template from '~/server/utils/emailTemplates/InviteNewUser.vue';
import { renderEmail, getBaseUrl } from '~/server/utils/email';

export default defineEventHandler(async event => {
    const baseUrl = getBaseUrl(event);

    const query = getQuery(event);

    const email = await renderEmail(Template, {
        recipient: {
            firstName: '<PERSON>',
            lastName: '<PERSON>',
        },
        advisor: {
            firstName: '<PERSON>',
            lastName: '<PERSON><PERSON><PERSON>',
            displayName: '<PERSON>',
            email: '<EMAIL>',
            office: 'Geniant Dallas',
            phoneNumber: '************',
        },
        signUpLink: new URL('/UserInvite', baseUrl).href,
        companyName: 'Geniant',
        supportInfo: '<EMAIL>',
    });

    if (query.send) {
        console.log('Sending email');
        await sendEmail(
            event,
            {
                to: [
                    {
                        address: 'jere<PERSON>.<EMAIL>',
                        displayName: '<PERSON>',
                    },
                ],
            },
            'Test Subject',
            email,
        );
    }

    return email;
});
