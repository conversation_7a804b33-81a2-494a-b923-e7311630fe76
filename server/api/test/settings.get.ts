﻿export default defineEventHandler(async event => {
    const user = await getUser(event);
    if (!user.groups?.some(group => group === 'Administrator')) {
        throw createError({ statusCode: 403, statusMessage: 'Access Denied' });
    }
    const runtimeConfig = useRuntimeConfig(event);
    const appConfig = useAppConfig(event);
    return {
        headers: event.headers,
        appConfig: appConfig,
        runtimeConfig: runtimeConfig,
        env: process.env,
    };
});
