﻿// noinspection JSUnusedGlobalSymbols

import { getSignature } from '~/server/utils/email';

export default defineEventHandler(async event => {
    const query = getQuery(event);
    const sig = `${query.name ?? '<PERSON>'}`;
    const signature = getSignature(sig);
    return `<html lang="en"><body ><img style="border: 1px solid;" src="${signature.src}" width="${signature.width}px" alt="signature"/></body></html>`;
});
