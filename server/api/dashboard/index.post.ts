﻿// noinspection JSUnusedGlobalSymbols

import type { Dashboard } from '~/types/dashboard';
import { DashboardModel } from '~/server/models/dashboard.schema';

export default defineEventHandler(async (event): Promise<Dashboard> => {
    console.log('Dashboard API: post');

    console.log('event', event);

    const userId = await getUserId(event);

    const dashboardData = await readBody<Dashboard>(event);

    console.log('userId', userId);
    console.log('dashboardData', typeof dashboardData, dashboardData);

    dashboardData.updatedBy = userId;
    dashboardData.updatedAt = new Date();

    if (dashboardData._id) {
        await DashboardModel.replaceOne({ _id: dashboardData._id }, dashboardData).exec();
        return dashboardData;
    }
    return await DashboardModel.insertOne(dashboardData);
});
