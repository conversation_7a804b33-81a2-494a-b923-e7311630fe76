﻿import { resetPassword } from '../../utils/auth0';
import { createConsola } from 'consola';
const logger = createConsola().withTag('resetPassword');

export default defineEventHandler(async event => {
    logger.trace('Reset Password API: post');
    const user = await getUser(event);
    const providedUser = await readBody<{ userId: string }>(event);
    if (user.userData._id !== providedUser.userId) {
        logger.error('UserId mismatch. Expected: %s Provided: %s', user.userData._id, providedUser.userId);
        throw createError({
            statusCode: 401,
            message: 'Authentication Required',
        });
    }

    await resetPassword(user);
});
