﻿// noinspection JSUnusedGlobalSymbols

import { ofKind } from '~/types/base';
import type { PortfolioUser } from '~/types/portfolio';
import * as devalue from 'devalue';
import { UserModel } from '~/server/models/user.schema';
import { IntakeModel } from '~/server/models/intake.schema';
import { Portfolios } from '~/server/models/portfolio.schema';

export interface ProfileInvitation {
    ownerId: string;
    userType: 'Invited' | 'Advisor' | 'User' | 'Declined' | 'Revoked';
    createdAt: Date;
    updatedAt?: Date;
    updatedBy?: string;
    email: string;
    firstName: string;
    lastName: string;
    profileImage?: string;
}

export interface ProfileResponse {
    name: string;
    email: string;
    address?: string;
    spouse?: string;
    spouseEmail?: string;
    spousePhone?: string;
    phone?: string;
    dependents: {
        name?: string;
        relationship?: string;
        dob?: string;
    }[];
    invites: ProfileInvitation[];
    hasInvitations: boolean;
}

export default defineEventHandler(async event => {
    console.log('Profile API: get');

    const user = await getUser(event);
    const intakeData = await IntakeModel.findOne({ user: user.userData._id }).lean();
    const portfolio = await Portfolios.findByOwner(user.userData._id).lean();
    const homeAsset = portfolio?.assets?.find(ofKind('AssetHome'));
    const userName = [user.userData.firstName, intakeData?.name?.middleName, user.userData.lastName]
        .filter(Boolean)
        .join(' ');
    const phone = [intakeData?.phoneCountryCode, intakeData?.phone].filter(Boolean).join(' ');
    const spouseName = [intakeData?.spouse?.firstName, intakeData?.spouse?.middleName, intakeData?.spouse?.lastName]
        .filter(Boolean)
        .join(' ');
    const spousePhone = [intakeData?.spouse?.phoneCountryCode, intakeData?.spouse?.phone].filter(Boolean).join(' ');
    const dependents = intakeData?.dependents;
    const invites = portfolio?.owners.filter(o => o.userType !== 'Owner');
    const userInvites = invites ? await Promise.all(invites.map(mapInvite)) : [];
    const hasInvites = invites?.some(o => o.userType === 'Invited') ?? false;

    const result = {
        name: userName,
        email: `${user.userData.email}`,
        address: homeAsset?.address,
        spouse: spouseName,
        spouseEmail: intakeData?.spouse?.email,
        spousePhone: spousePhone,
        phone,
        dependents: dependents,
        invites: userInvites,
        hasInvitations: hasInvites,
    };
    console.log(result);

    return devalue.stringify(result);

    async function mapInvite(portfolioUser: PortfolioUser) {
        const user = await UserModel.findById(portfolioUser.owner);
        return {
            ownerId: portfolioUser.owner,
            userType: portfolioUser.userType,
            createdAt: portfolioUser.createdAt,
            updatedAt: portfolioUser.updatedAt,
            updatedBy: portfolioUser.updatedBy,
            email: user?.email ?? '',
            firstName: user?.firstName ?? '',
            lastName: user?.lastName ?? '',
            profileImage: user?.profileImage,
        };
    }
});
