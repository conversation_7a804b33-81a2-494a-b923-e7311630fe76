﻿import { disableUser, resetPassword } from '../../utils/auth0';
import { createConsola } from 'consola';
import { UserModel } from '~/server/models/user.schema';
const logger = createConsola().withTag('deleteAccount');

export default defineEventHandler(async event => {
    logger.info('Disable Account API: post');
    const user = await getUser(event);
    const providedUser = await readBody<{ userId: string; email?: string }>(event);
    if (user.userData._id !== providedUser.userId) {
        logger.error('UserId mismatch. Expected: `%s` Provided: `%s`', user.userData._id, providedUser.userId);
        throw createError({
            statusCode: 401,
            message: 'Authentication Required',
        });
    }
    logger.info('User Email: %s Provider Email: %s', user.userData.email, providedUser.email);
    if (!providedUser.email || user.userData.email.toLowerCase() !== providedUser.email.toLowerCase()) {
        logger.warn('User email mismatch. Expected: `%s` Provided: `%s`', user.userData.email, providedUser.email);
        throw createError({
            statusCode: 400,
            message: 'Invalid email address',
        });
    }

    const dbUser = await UserModel.findById(user.userData._id).exec();
    if (!dbUser) {
        logger.warn('User `%s` not found.', user.userData._id);
        throw createError({
            statusCode: 400,
            message: 'User not found',
        });
    }
    dbUser.isDisabled = true;
    await dbUser.save();

    await disableUser(user);
});
