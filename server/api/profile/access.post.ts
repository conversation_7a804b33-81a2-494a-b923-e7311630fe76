﻿// noinspection JSUnusedGlobalSymbols

import {PortfolioSnapshots, Portfolios} from "~/server/models/portfolio.schema";


export default defineEventHandler(async (event) => {
    console.log("Access API: post")

    const request = await readBody<{ownerId: string, action: 'accept'|'decline'|'revoke'}>(event);

    const userId = await getUserId(event);
    const portfolio= await Portfolios.findByOwner(userId);
    if (!portfolio) {
        console.log('Could not find portfolio');
        setResponseStatus(event, 400, "Portfolio not found");
        throw new Error("Portfolio not found");
    }

    const invite = portfolio?.owners.find(o => o.owner === request.ownerId && o.userType !== 'Owner');
    if (!invite) {
        console.log('Could not find invitation');
        setResponseStatus(event, 400, "Invitation not found");
        throw new Error("Invitation not found");
    }
    invite.userType = getUserType(request.action);
    invite.updatedAt = new Date();
    invite.updatedBy = userId;
    
    await portfolio.save();
    
    const portfolioSnapshot = new PortfolioSnapshots();
    portfolioSnapshot.overwrite({
        ...portfolio.toObject(),
        snapshotDate: new Date(),
        updatedBy: userId
    });
    await portfolioSnapshot.save();
});

function getUserType(action: 'accept'|'decline'|'revoke') {
    switch (action) {
        case 'accept':
            return 'Advisor';
        case 'decline':
            return 'Declined';
        case 'revoke':
            return 'Revoked';
    }
}
