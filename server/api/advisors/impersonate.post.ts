﻿// noinspection JSUnusedGlobalSymbols

import type {UserData} from "#nuxt-oidc/types";
import {impersonateUser, getUserId, stopImpersonation} from "../../utils/user";
import {UserModel} from "~/server/models/user.schema";
import {Portfolios} from "~/server/models/portfolio.schema";

export default defineEventHandler(async (event) => {
    console.log("Advisor Impersonate");
    await stopImpersonation(event); // stop impersonating if we currently are so we can start impersonating the new user
    const currentUserId = await getUserId(event);
    const request = await readBody<{userId: string}>(event);

    console.log(`Finding portfolio for user: ${request.userId} `)
    const portfolio = await Portfolios.findByOwner(request.userId).lean();
    
    // Check that the user has the access to impersonate this portfolio/user
    if (portfolio && portfolio.owners.some(portfolioOwner => portfolioOwner.owner === currentUserId && (portfolioOwner.userType === 'Advisor' || portfolioOwner.userType === 'Owner'))) {
        const user = await UserModel.findOne<UserData>({ _id: request.userId }).lean();
        if (user) {
            console.log(`Impersonating User: ${user.email}`);
            await impersonateUser(event, user)
        } else {
            console.log('Found portfolio but could not find user to impersonate')
        }
    }
    else if (portfolio) {
        console.log('User does not have Advisor permission to impersonate');
    } else {
        console.log('Could not find portfolio for user to impersonate');
    }
    const { userData } = await getUser(event);
    console.log(`Impersonating User Id: ${userData._id}`);
    return userData;
});

