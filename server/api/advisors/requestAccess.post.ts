﻿// noinspection JSUnusedGlobalSymbols

import type { UserData } from '#nuxt-oidc/types';
import { renderEmail, sendEmail, getBaseUrl } from '~/server/utils/email';
import Template from '~/server/utils/emailTemplates/AccessRequestAdvisor.vue';
import { UserModel } from '~/server/models/user.schema';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';

export default defineEventHandler(async event => {
    console.log('Advisors Request Access');
    const currentUserId = await getUserId(event);
    const currentUser = await UserModel.findOne<UserData>({ _id: currentUserId });
    const request = await readBody<{ userId: string }>(event);
    console.log('Finding portfolio for user: ', request.userId);
    const portfolioOwner = await UserModel.findOne<UserData>({ _id: request.userId }).lean();
    const portfolio = await Portfolios.findByOwner(request.userId);
    if (!portfolioOwner) {
        console.log('No portfolio owner found');
        setResponseStatus(event, 400, 'Portfolio owner not found');
        throw new Error('Portfolio owner not found');
    }
    console.log('Found Portfolio:', portfolio);
    if (!portfolio) {
        setResponseStatus(event, 400, 'Portfolio not found');
        throw new Error('Portfolio not found');
    }

    const owner = portfolio.owners.find(portfolioOwner => portfolioOwner.owner === currentUserId);
    if (owner) {
        owner.userType = 'Invited';
        owner.updatedAt = new Date();
        owner.updatedBy = currentUserId;
    } else {
        portfolio.owners.push({
            owner: currentUserId,
            userType: 'Invited',
            createdAt: new Date(),
        });
    }
    await portfolio.save();

    const portfolioSnapshot = new PortfolioSnapshots();
    portfolioSnapshot.overwrite({
        ...portfolio.toObject(),
        snapshotDate: new Date(),
        updatedBy: currentUserId,
    });
    await portfolioSnapshot.save();

    console.log('Sending email: ');
    const baseUrl = getBaseUrl(event);
    const accessLink = new URL('/client/settings/Access', baseUrl).href;
    const requestEmail = await renderEmail(Template, {
        recipient: {
            firstName: portfolioOwner.firstName,
            lastName: portfolioOwner.lastName,
        },
        advisor: {
            firstName: `${currentUser?.firstName}`,
            lastName: `${currentUser?.lastName}`,
            displayName: `${currentUser?.firstName} ${currentUser?.lastName}`,
            email: currentUser?.email ?? '',
            office: 'Geniant Dallas',
            phoneNumber: '************',
        },
        accessLink: accessLink,
        companyName: 'Geniant',
        supportInfo: '<EMAIL>',
    });

    await sendEmail(
        event,
        {
            to: [
                {
                    address: portfolioOwner.email,
                    displayName: `${portfolioOwner.firstName} ${portfolioOwner.lastName}`,
                },
            ],
        },
        'New Financial Advisor Request',
        requestEmail,
    );
});
