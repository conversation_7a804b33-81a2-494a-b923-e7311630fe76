﻿// noinspection JSUnusedGlobalSymbols

import type { UserData } from '#nuxt-oidc/types';
import { UserModel } from '~/server/models/user.schema';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';
import Template from '~/server/utils/emailTemplates/InviteNewUser.vue';
import { renderEmail, sendEmail, getBaseUrl } from '~/server/utils/email';
import { nanoid } from 'nanoid';

export default defineEventHandler(async event => {
    console.log('Advisors Invite User');
    const currentUserId = await getUserId(event);
    const currentUser = await UserModel.findOne({ _id: currentUserId }).exec();
    if (!currentUser) {
        console.log('Could not find current user');
        throw createError({
            statusCode: 400,
            message: 'Could not find current user',
        });
    }

    const request = await readBody<{ firstName: string; lastName: string; email: string; phone: string }>(event);

    // If the user exists, return an error code
    const existingUser = await UserModel.findOne<UserData>({ email: request.email }).lean();
    if (existingUser) {
        console.log('User already exists');
        throw createError({
            statusCode: 400,
            message: 'User already exists',
        });
    }

    // Create a new user with temporary id
    const newUser = new UserModel({
        _id: nanoid(),
        authId: `temp|${request.email}`,
        email: request.email,
        firstName: request.firstName,
        lastName: request.lastName,
        joinDate: new Date(),
    });
    await newUser.save();

    // Create a new portfolio and assign the user as owner
    // Add the advisor invite to the portfolio
    const newPortfolio = new Portfolios({
        owners: [
            {
                userType: 'Owner',
                owner: newUser.id,
                createdAt: new Date(),
            },
            {
                userType: 'Invited',
                owner: currentUserId,
                createdAt: new Date(),
            },
        ],
    });
    await newPortfolio.save();

    const portfolioSnapshot = new PortfolioSnapshots();
    portfolioSnapshot.overwrite({
        ...newPortfolio.toObject(),
        snapshotDate: new Date(),
        updatedBy: currentUserId,
    });
    await portfolioSnapshot.save();

    console.log('Sending email: ');
    const baseUrl = getBaseUrl(event);
    const signUpLink = new URL('/client/intake', baseUrl).href;
    const requestEmail = await renderEmail(Template, {
        recipient: {
            firstName: newUser.firstName,
            lastName: newUser.lastName,
        },
        advisor: {
            firstName: `${currentUser?.firstName}`,
            lastName: `${currentUser?.lastName}`,
            displayName: `${currentUser?.firstName} ${currentUser?.lastName}`,
            email: currentUser?.email ?? '',
            office: 'Geniant Dallas',
            phoneNumber: '************',
        },
        signUpLink: signUpLink,
        companyName: 'Geniant',
        supportInfo: '<EMAIL>',
    });

    await sendEmail(
        event,
        {
            to: [
                {
                    address: request.email,
                    displayName: `${request.firstName} ${request.lastName}`,
                },
            ],
        },
        'Join & Grant Advisor Access',
        requestEmail,
    );
});
