﻿// noinspection JSUnusedGlobalSymbols

import type {PortfolioUser, Portfolio} from "~/types/portfolio";
import {getUser} from "~/server/utils/user";
import * as devalue from "devalue";
import {getTotal} from "~/server/utils/categorizer";
import {Portfolios} from "~/server/models/portfolio.schema";
import {UserModel} from "~/server/models/user.schema";

type CommonUser = {
    _id: string,
    firstName: string,
    lastName: string,
    profileImage?: string,
    role: 'Client' | 'Advisor' | 'Administrator',
};
type JoinedUser = CommonUser & {
    status: 'Joined' | 'Self' | 'Impersonated',
    totalAssets: number,
    totalLiabilities: number,
    netWorth: number,
    accounts: number,
    onboarded: number,
    joinDate: Date
};
type PendingUser = CommonUser & {
    status: 'Pending' | 'Declined' | 'Revoked' | 'None'
    totalAssets?: null | undefined,
    totalLiabilities?: null | undefined,
    netWorth?: null | undefined,
    accounts?: null | undefined,
    onboarded?: null | undefined,
    joinDate?: null | undefined
}
export type UserManagementUser = JoinedUser | PendingUser;

export default defineEventHandler(async (event) => {
    console.log("Advisors Users API: get")

    const user = await getUser(event);
    console.log("Current user: ", user);

    const allPortfolios: Portfolio[] = await Portfolios.find({}).lean();
    const result = (await Promise.all(allPortfolios.map(toUserManagement))).filter(o => {return !!o;});
    console.log("Advisor Users Get Result: ", result);
    return devalue.stringify(result);

    async function toUserManagement(portfolio: Portfolio) : Promise<UserManagementUser | undefined> {
        //console.log(portfolio.owners);
        const ownerId = portfolio.owners.find(o => o.userType === 'Owner')?.owner;
        const owner = await UserModel.findById(ownerId);
        if (!owner) return undefined;
        const currentUserId = user.originalUser ?? user.userData._id;
        const thisUser = portfolio.owners.find(o => o.owner === currentUserId);
        const status = getStatus(thisUser);
        const role =
            (owner?.groups?.includes('Administrator')) ? 'Administrator' :    
                (owner?.groups?.includes('Advisor')) ? 'Advisor' : 
                    'Client';
        if (status === 'Pending' || status === 'Declined' || status === 'Revoked' || status === 'None') {
            return {
                _id: ownerId ?? '',
                firstName: owner?.firstName ?? '',
                lastName: owner?.lastName ?? '',
                profileImage: owner?.profileImage,
                role: role,
                status: status,
                joinDate: undefined,
                totalAssets: undefined,
                totalLiabilities: undefined,
                netWorth: undefined,
                accounts: undefined,
                onboarded: undefined,
            }
        }
        const totalAssets = getTotal(p => p.assets, i => i.equityValue)(portfolio);
        const totalLiabilities = getTotal(p => p.liabilities, i => i.balance)(portfolio);
        const netWorth = totalAssets - totalLiabilities;

        return {
            _id: ownerId ?? '',
            firstName: owner?.firstName ?? '',
            lastName: owner?.lastName ?? '',
            profileImage: owner?.profileImage,
            role: role,
            status: user.isImpersonated && user.userData._id === ownerId ? 'Impersonated' : status,
            joinDate: owner?.joinDate ?? new Date(),
            totalAssets: totalAssets,
            totalLiabilities: totalLiabilities,
            netWorth: netWorth,
            accounts: 1,
            onboarded: 100
        }
    }

    function getStatus(user: PortfolioUser | undefined) {
        switch (user?.userType) {
            case 'Invited':
                return 'Pending';
            case "Declined":
                return 'Declined';
            case "Revoked":
                return 'Revoked';
            case "Advisor":
                return 'Joined';
            case "User":
                return 'Joined';
            case "Owner":
                return 'Self';
            default:
                return 'None';
        }
    }
    
/*    
    const allUsers = await UserModel.find().lean();
    allUsers.map((user: UserData) => {
        return {
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role ?? 'Client',
            joinDate: user.joinDate
        }
    })
*/

});

