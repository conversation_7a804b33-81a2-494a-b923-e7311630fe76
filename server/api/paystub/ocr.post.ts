import { consola } from 'consola';
import { getReadBlobSasUrl } from '~/server/services/blob';

import { analyzePaystubWithOCR } from '~/server/use-cases/paystub/ocr';
import { PaystubAnalysisResult } from '~/types/paystub';

export default defineEventHandler(async (event): Promise<PaystubAnalysisResult | null> => {
    consola.info('OCR API: post');

    const body = await readBody<{ blobName: string }>(event);
    const blobName = body?.blobName;

    if (!blobName) {
        throw createError({ statusCode: 400, message: 'Missing blobName in request body.' });
    }

    try {
        const userId = await getUserId(event);
        const blobUrl = await getReadBlobSasUrl(blobName, 'user-uploads');
        const ocrResult = await analyzePaystubWithOCR(blobUrl);

        consola.info('PaystubOCRSuccess', {
            userId,
            fileName: blobName,
            route: '/api/paystub/ocr',
        });

        return ocrResult;
    } catch (error) {
        consola.error(error, {
            route: '/api/paystub/ocr',
            method: event.method,
        });
        throw error;
    }
});
