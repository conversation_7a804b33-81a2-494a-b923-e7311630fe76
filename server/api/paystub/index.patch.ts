import { z } from 'zod';
import { consola } from 'consola';

import type { Paystub } from '~/types/paystub';

import { PatchPaystubRequest } from '~/server/dto/paystub.dto';
import * as UseCases from '~/server/use-cases/paystub';

export default defineEventHandler(async (event): Promise<Paystub> => {
    consola.info('Paystub API: patch');

    const user = await getUser(event);
    consola.info(`Paystub API (patch): User ID: ${user.userData._id}`);

    const body = await readBody<z.infer<typeof PatchPaystubRequest>>(event);

    try {
        PatchPaystubRequest.parse(body);
    } catch (error) {
        consola.error('Paystub API (patch): Validation error:', error);
        throw createError({
            statusCode: 400,
            statusMessage: 'Invalid request body',
            data: error,
        });
    }

    const existingPaystub = await UseCases.getPaystubById(body.paystubId);
    if (!existingPaystub) {
        throw createError({
            statusCode: 404,
            statusMessage: 'Paystub not found',
        });
    }
    const portfolio = await UseCases.findPortfolioByUser(user);

    const incomeEmployee = UseCases.findIncomeEmployee(portfolio, body.incomeEmployeeId);
    if (!incomeEmployee) {
        throw createError({
            statusCode: 404,
            statusMessage: 'Income employee not found',
        });
    }

    for (const [key, value] of Object.entries(body.paystub)) {
        existingPaystub.set(key, value);
    }

    existingPaystub.updatedAt = new Date();
    existingPaystub.updatedBy = user.userData._id;

    const updatedPaystub = await existingPaystub.save();

    const paystubData = UseCases.processPaystubData(updatedPaystub);

    const updatedIncomeEmployee = UseCases.updateExistingPaystubInIncomeEmployee(
        incomeEmployee,
        incomeEmployee.employmentType,
        paystubData,
    );

    portfolio.income = portfolio.income!.map(income =>
        income.id === incomeEmployee.id ? updatedIncomeEmployee : income,
    );

    portfolio.markModified('income');

    const savedProtfolio = await portfolio.save();

    await UseCases.savePortfolioSnapshot(savedProtfolio, user.userData._id);

    return updatedPaystub;
});
