import { generateUploadUrl } from '~/server/services/blob';
import { consola } from 'consola';

export default defineEventHandler(async (event): Promise<{ uploadUrl: string; blobName: string }> => {
    consola.info('Generating upload URL for paystub');

    const userId = await getUserId(event);
    const blobName = `paystubs/${userId}-paystub-${Date.now()}.pdf`;
    const containerName = 'user-uploads';

    try {
        const uploadUrl = await generateUploadUrl(blobName, containerName);

        consola.info('Generated upload URL', {
            userId,
            blobName,
            uploadUrl,
            route: '/api/paystub/upload-url',
        });

        return { uploadUrl, blobName };
    } catch (error) {
        consola.error('Error generating upload URL', {
            userId,
            blobName,
            error: error instanceof Error ? error.message : String(error),
            route: '/api/paystub/upload-url',
        });
        throw createError({
            statusCode: 500,
            message: 'Failed to generate upload URL for paystub.',
        });
    }
});
