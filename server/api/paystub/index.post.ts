import type { EmploymentType, Paystub } from '~/types/paystub';
import type { UserSession } from '#nuxt-oidc/types';
import { z } from 'zod';
import { consola } from 'consola';

import { PaystubModel } from '~/server/models/paystub.schema';
import * as UseCases from '~/server/use-cases/paystub';
import { PaystubRequestBody } from '~/server/dto/paystub.dto';

export default defineEventHandler(async (event): Promise<Paystub> => {
    consola.info('Paystub API: post');

    const user = await getUser(event);
    consola.info(`Paystub API (post): User ID: ${user.userData._id}`);

    const body = await readBody<z.infer<typeof PaystubRequestBody>>(event);

    try {
        PaystubRequestBody.parse(body);
    } catch (error) {
        consola.error('Paystub API (post): Validation error:', error);
        throw createError({
            statusCode: 400,
            statusMessage: 'Invalid request body',
            data: error,
        });
    }

    const { paystub, employmentType, incomeEmployeeId } = body;

    paystub.createdAt = new Date();
    paystub.updatedAt = new Date();
    paystub.updatedBy = user.originalUser ?? user.userData._id;

    const savedPaystub = await new PaystubModel(paystub).save();

    await associateNewPaystubWithIncomeEmployee(incomeEmployeeId, savedPaystub, employmentType, user);

    return savedPaystub;
});

/**
 * Associates a paystub with the corresponding IncomeEmployee in the user's portfolio
 */
async function associateNewPaystubWithIncomeEmployee(
    incomeEmployeeId: string,
    paystub: Paystub,
    employmentType: EmploymentType,
    user: UserSession,
): Promise<void> {
    try {
        if (!paystub || !paystub._id) {
            consola.error('Invalid paystub data:', paystub);
            return;
        }

        const updatedBy = user.originalUser ?? user.userData._id;

        const targetPortfolio = await UseCases.findPortfolioByUser(user);
        const paystubData = UseCases.processPaystubData(paystub);

        const incomeEmployee = UseCases.findIncomeEmployee(targetPortfolio, incomeEmployeeId);
        if (!incomeEmployee) {
            consola.error(`Income employee with ID ${incomeEmployeeId} not found in portfolio`);
            return;
        }

        const updatedIncomeEmployee = UseCases.pushNewPaystubToIncomeEmployee(
            incomeEmployee,
            employmentType,
            paystubData,
        );

        targetPortfolio.income = targetPortfolio.income!.map(income =>
            income.id.toString() === incomeEmployeeId ? updatedIncomeEmployee : income,
        );

        targetPortfolio.markModified('income');

        if (Array.isArray(paystubData.expenses) && paystubData.expenses.length > 0) {
            targetPortfolio.expenses = [...(targetPortfolio.expenses || []), ...paystubData.expenses];
        }

        targetPortfolio.markModified('expenses');

        if (Array.isArray(paystubData.assets.postTaxAssets) && paystubData.assets.postTaxAssets.length > 0) {
            targetPortfolio.assets! = [...(targetPortfolio.assets || []), ...paystubData.assets.postTaxAssets];
        }

        if (Array.isArray(paystubData.assets.preTaxAssets) && paystubData.assets.preTaxAssets.length > 0) {
            targetPortfolio.assets! = [...(targetPortfolio.assets || []), ...paystubData.assets.preTaxAssets];
        }

        targetPortfolio.markModified('assets');

        await targetPortfolio.save();

        await UseCases.savePortfolioSnapshot(targetPortfolio, updatedBy);

        consola.info(`Associated paystub ${paystub._id} with portfolio and saved snapshot`);
    } catch (error) {
        consola.error('Error associating paystub with income:', error);
    }
}
