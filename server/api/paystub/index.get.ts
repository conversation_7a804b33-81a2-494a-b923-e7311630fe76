import type { Paystub } from '~/types/paystub';
import { PaystubModel } from '~/server/models/paystub.schema';
import { processPaystubData } from '~/server/use-cases/paystub';
import { consola } from 'consola';

/**
 * GET handler to retrieve a paystub by ID
 */
export default defineEventHandler(async (event): Promise<Paystub | null> => {
    consola.info('Paystub API: get');

    const user = await getUser(event);
    consola.info(`Paystub API (get): User ID: ${user.userData._id}`);

    const query = getQuery(event);
    const paystubId = query.id as string;

    if (!paystubId) {
        throw createError({
            statusCode: 400,
            statusMessage: 'Paystub ID is required',
        });
    }

    consola.info(`Fetching paystub with ID: ${paystubId}`);

    const paystub = await PaystubModel.findById(paystubId).exec();

    if (!paystub) {
        consola.info(`Paystub not found: ${paystubId}`);
        throw createError({
            statusCode: 404,
            statusMessage: 'Paystub not found',
        });
    }

    try {
        const report = processPaystubData(paystub);

        (paystub as any).report = report;
    } catch (error) {
        consola.error('Error generating paystub report:', error);
    }

    return paystub;
});
