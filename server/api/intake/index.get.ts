﻿// noinspection JSUnusedGlobalSymbols

import { IntakeDataWithUser, IntakeModel } from '~/server/models/intake.schema';
import { Portfolios } from '~/server/models/portfolio.schema';
import { getUserId, getUser } from '../../utils/user';

export default defineEventHandler(async (event): Promise<IntakeDataWithUser | null> => {
    console.log('Intake API: get');
    const userId = await getUserId(event);

    console.log(`IntakeKey (get): Intake Key: ${userId}`);
    const result: IntakeDataWithUser | null = await IntakeModel.findOne({ user: userId }).lean();
    if (!result) return null;

    const portfolio = await Portfolios.findByOwner(userId).lean();
    if (portfolio) {
        result.portfolio = portfolio;
    }

    return result;
});
