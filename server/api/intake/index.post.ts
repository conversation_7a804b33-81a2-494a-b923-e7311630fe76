﻿// noinspection JSUnusedGlobalSymbols

import type { IntakeData } from '~/types/intake';
import type { UserSession } from '#nuxt-oidc/types';

import { defaultPhoneCountryCode, IntakeModel } from '~/server/models/intake.schema';
import { Portfolios, PortfolioSnapshots } from '~/server/models/portfolio.schema';

export default defineEventHandler(async (event): Promise<IntakeData> => {
    console.log('Intake API: put');

    const user = await getUser(event);
    console.log(`IntakeKey (put): Intake Key: ${user.userData._id}`);

    const intakeData = await readBody<IntakeData>(event);
    await updatePortfolio(intakeData, user);

    if (intakeData.spouse?.phone && !intakeData.spouse.phoneCountryCode) {
        intakeData.spouse.phoneCountryCode = defaultPhoneCountryCode;
    }

    await IntakeModel.replaceOne(
        { user: user.userData._id },
        {
            ...intakeData,
            user: user.userData._id,
        },
        { upsert: true },
    ).exec();

    return intakeData;
});

async function updatePortfolio(intakeData: IntakeData, user: UserSession) {
    const portfolio = intakeData.portfolio;
    if (!portfolio) return;
    const userId = user.userData._id;
    const updatedBy = user.originalUser ?? user.userData._id;

    const existingPortfolio = (await Portfolios.findByOwner(userId).exec()) ?? new Portfolios();
    portfolio.owners = existingPortfolio.owners;
    if (!portfolio.owners || !portfolio.owners.length) {
        portfolio.owners = [
            {
                owner: userId,
                userType: 'Owner',
                createdAt: new Date(),
            },
        ];
    }
    existingPortfolio.overwrite(portfolio);

    await existingPortfolio.save();

    const snapshot = new PortfolioSnapshots(portfolio);
    snapshot.snapshotDate = new Date();
    snapshot.updatedBy = updatedBy;
    await snapshot.save();
}
