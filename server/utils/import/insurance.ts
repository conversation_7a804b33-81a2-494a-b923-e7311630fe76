﻿import * as ExcelJS from "exceljs";
import {nanoid} from "nanoid";
import * as Insurance from "~/types/insurance";
import {hasBorder, toData, toFrequency} from './utils';
import _ from "lodash";
import {Frequency} from "~/types/base";

export default function processInsurance(workbook: ExcelJS.Workbook) {
    let result: Insurance.Insurance[] = [];
    result = result
        .concat(processInsuranceLife(workbook))
        .concat(processInsuranceDisability(workbook))
        .concat(processInsuranceLTC(workbook));

    //console.log(JSON.stringify(result, null, 2));
    return result;
}

function processInsuranceLife(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.worksheets[7];
    const data = toData(worksheet, { headerRow: 6, rowStart: 7, predicate: hasBorder });
    return data.flatMap(toInsuranceLife);

    function toInsuranceLife(data: Record<string, string>): Insurance.InsuranceLife[] {
        if (!data['Death Benefit']) return [];
        return [{
            id: nanoid(),
            mode: 'Simple',
            category: 'Insurance',
            kind: 'InsuranceLife',
            description: data.Description,
            lifeInsuranceType: data.Type,
            companyName: data['Company Name'],
            deathBenefit: _.round(_.toNumber(data['Death Benefit']), 2),
            insured: data.Insured,
            owner: data.Owner,
            beneficiary: data.Beneficiary,
            premium: _.round(_.toNumber(data.Premium), 2),
            premiumFrequency: toFrequency(data['Prem Freq']),
            associatedAssets: []
        }];
    }
}

function processInsuranceDisability(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.worksheets[8];
    worksheet.columns = [
        { key: 'Description' },
        { key: 'Company' },
        { key: 'Insured' },
        { key: 'PremiumAmount' },
        { key: 'PremiumFrequency' },
        { key: 'WaitingPeriod' },
        { key: 'Period' },
        { key: 'BenefitAmount' },
        { key: 'BenefitFrequency' },
        { key: 'PreTax' }
    ];
    const data = toData(worksheet, { headerRow: 8, rowStart: 9, predicate: hasBorder });
    return data.flatMap(toInsuranceDisability);

    function toInsuranceDisability(data: Record<string, string>): Insurance.InsuranceDisability[] {
        if (!data.Individual) return [];
        return [{
            id: nanoid(),
            mode: 'Simple',
            category: 'Insurance',
            kind: 'InsuranceDisability',
            disabilityInsuranceType: data.Description.replace(/(\s*-.*)/gm, ''),
            description: data.Description,
            company: data.Company,
            beneficiary: data.Insured,
            insured: data.Insured,
            premium: _.round(_.toNumber(data.PremiumAmount), 2),
            premiumFrequency: toFrequency(data.PremiumFrequency),
            waitingPeriodInDays: _.toNumber(data.WaitingPeriod),
            benefit: _.round(_.toNumber(data.BenefitAmount), 2),
            benefitFrequency: toFrequency(data.BenefitFrequency)
        }];
    }
}
function processInsuranceLTC(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.worksheets[8];
    worksheet.columns = [
        { key: 'Description' },
        { key: 'Insured' },
        { key: 'PremiumAmount' },
        { key: 'PremiumFrequency' },
        { key: 'WaitingPeriod' },
        { key: 'Period' },
        { key: 'BenefitAmount' },
        { key: 'BenefitFrequency' },
        { key: 'PctIncrease' },
    ];
    const data = toData(worksheet, { headerRow: 23, rowStart: 24, predicate: hasBorder });

    return data.flatMap(toInsuranceLTC);

    function toInsuranceLTC(data: Record<string, string>): Insurance.InsuranceLTC[] {
        if (!data['(Co. Name)']) return [];
        return [{
            id: nanoid(),
            mode: 'Simple',
            category: 'Insurance',
            kind: 'InsuranceLTC',
            description: data.Description,
            company: '',
            insured: data.Insured,
            beneficiary: data.Insured,
            premium: _.round(_.toNumber(data.PremiumAmount), 2),
            premiumFrequency: toFrequency(data.PremiumFrequency),
            waitingPeriodInDays: _.toNumber(data.WaitingPeriod),
            benefit: _.round(_.toNumber(data.BenefitAmount), 2),
            benefitFrequency: toFrequency(data.BenefitFrequency)
        }];
    }
}