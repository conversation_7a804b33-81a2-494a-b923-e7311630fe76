﻿import {Frequency} from "~/types/base";
import ExcelJS from "exceljs";

export function toData(worksheet: ExcelJS.Worksheet | undefined, options: { headerRow?: number,  rowStart: number,  rowEnd?: number, predicate?: (row: ExcelJS.Row) => boolean }) {
    return [...toDataIter(worksheet, options)];
}

export function* toDataIter(worksheet: ExcelJS.Worksheet | undefined, options: { headerRow?: number,  rowStart: number,  rowEnd?: number, predicate?: (row: ExcelJS.Row) => boolean }) {
    if (!worksheet) return;
    const headerMap = getHeaderMap(worksheet.findRow(options.headerRow ?? -1));
    const length = (options.rowEnd ?? worksheet.rowCount) - options.rowStart + 1;
    const dataRows = worksheet.findRows(options.rowStart, length);
    if (!dataRows) return;
    const useKeys = worksheet.columns.some(c => !!c.key);
    for (const row of dataRows) {
        if (options.predicate && !options.predicate(row)) return;
        let result: Record<string, string> = {};
        if (useKeys) {
            worksheet.eachColumnKey((col, colNumber) => {
                if (!col.key) return;
                const cell = row.getCell(col.key);
                if (cell.effectiveType != ExcelJS.ValueType.Null) {
                    result[col.key] = cell.text;
                }
            });
        }
        else {
            row.eachCell((cell, colNumber) => {
                if (cell.value === null) return;
                const map = `${headerMap?.get(colNumber) ?? colNumber}`;
                result[map] = cell.text;
            });
        }
        yield result;
    }
}

function getHeaderMap(headerRow?: ExcelJS.Row) {
    if (!headerRow) return;
    const headerMap = new Map<number,  string>();
    headerRow.eachCell((cell, colNum) => {
        headerMap.set(colNum, cell.text);
    });
    return headerMap;
}


export function firstRow(worksheet: ExcelJS.Worksheet, predicate: (row: ExcelJS.Row) => boolean) {
    if (!worksheet) return;
    for (let rowNumber = 0; rowNumber < worksheet.rowCount; rowNumber++) {
        const row = worksheet.findRow(rowNumber);
        if (row && predicate(row)) return row;
    }
}

export const undefinedHandler = {
    canProcess() { return true; },
    process() { return undefined; }
}
export function hasBorder(row: ExcelJS.Row) {
    return row && !!row.getCell(1).style.border?.bottom;
}

export type importHandler<TResult> = {
    canProcess(data: Record<string, string>) : boolean,
    process(data: Record<string, string>) : TResult,
}

export function processData<TResult>(data: Record<string,  string>[], handlers: importHandler<TResult>[]) : TResult[] {
    function toResult(data: Record<string, string>) {
        const process = handlers.find(({canProcess}) => canProcess(data))?.process;
        return process ? [process(data)] : [];
    }
    return data.flatMap(toResult);
}

export function toFrequency(val: ExcelJS.CellValue) : Frequency {
    switch (`${val}`) {
        case '1':
            return Frequency.Annually;
        case '2':
            return Frequency.SemiAnnually;
        case '4':
            return Frequency.Quarterly;
        case '6':
            return Frequency.Bimonthly;
        case '3':
            return Frequency.Trimesterly;
        case '12':
            return Frequency.Monthly;
        case '24':
            return Frequency.SemiMonthly;
        case '26':
            return Frequency.Biweekly;
        case '52':
            return Frequency.Weekly;
        case '365':
            return Frequency.Daily;
        default:
            return Frequency.Other;
    }
}