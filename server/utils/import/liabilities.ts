﻿import * as ExcelJS from "exceljs";
import {Frequency, PersonalBusinessType, VehicleType} from "~/types/base";
import {nanoid} from "nanoid";
import * as Liabilities from "~/types/liabilities";
import {hasBorder, processData, toData, toFrequency} from './utils';
import {Asset} from "~/types/assets";
import type {Expense} from "~/types/expenses";
import _ from "lodash";

type BaseLiability = Liabilities.BaseLiability & {
    debtor: string,
    balance: number,
    interestRate: number,
    minimumPayment: number,
    originalBalance: number,
    originationDate: Date,
    termLength: number,
    paymentFrequency: Frequency,
    associatedExpenses: Expense[],
    associatedAssets: Asset[]
}
function getBaseLiability(data: Record<string, string>) : BaseLiability {
    return {
        id: nanoid(),
        mode: 'Simple',
        category: 'Liability',
        description: data.Description,
        debtor: data.Debtor ?? data['Debt*'],
        balance: _.round(_.toNumber(data['Current Balance']), 2),
        interestRate: _.toNumber(data['Int Rate']),
        minimumPayment: _.round(_.toNumber(data['Payment (Principal + Interest)']), 2),
        originalBalance: _.round(_.toNumber(data['Orig Bal']), 2),
        originationDate: new Date(data['Orig Date']),
        termLength: _.toNumber(data['Orig Term (Mos)']),
        paymentFrequency: toFrequency(data['Pmt Freq']),
        loanType: PersonalBusinessType.Personal,
        associatedExpenses: [],
        associatedAssets: []
    }
}

const liabilityHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Category == 'First Mortgage' || data.Category == 'Second Home'; },
        process(data: Record<string, string>) : Liabilities.LiabilityMortgage {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityMortgage',
                mortgageType: 'Mortgage'
            }                
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Second Mortgage'; },
        process(data: Record<string, string>) : Liabilities.LiabilityMortgage {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityMortgage',
                mortgageType: '2nd Mortgage'
            }
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Home Equity'; },
        process(data: Record<string, string>) : Liabilities.LiabilityMortgage {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityMortgage',
                mortgageType: 'Home Equity'
            }
        }
    },
    
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Auto Loan'; },
        process(data: Record<string, string>) : Liabilities.LiabilityAutoLoan {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityAutoLoan',
                vehicleType: VehicleType.Auto,
                loanType: PersonalBusinessType.Personal
            }
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Student Loan'; },
        process(data: Record<string, string>) : Liabilities.LiabilityStudentLoan {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityStudentLoan',
                studentLoanType: 'Public',
                loanType: PersonalBusinessType.Personal,
                associatedAssets: undefined
            }
        }
    },
    {
        canProcess(data: Record<string, string>) { return ['Charge Acct', 'Credit Card'].some(d => data.Category.includes(d)); },
        process(data: Record<string, string>) : Liabilities.LiabilityCreditCard {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityCreditCard'
            }
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Business Loan'; },
        process(data: Record<string, string>) : Liabilities.LiabilityBusinessLoan {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityBusinessLoan',
                loanType: PersonalBusinessType.Business
            }
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category == 'Rental Real Estate'; },
        process(data: Record<string, string>) : Liabilities.LiabilityOther {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityOther'
            }
        }
    },
    {
        canProcess() { return true; },
        process(data: Record<string, string>) : Liabilities.LiabilityOther {
            return {
                ...getBaseLiability(data),
                kind: 'LiabilityOther'
            }
        }
    }
]



export default function processLiabilities(workbook: ExcelJS.Workbook) {
    let data = toData(workbook.worksheets[6], { headerRow: 6, rowStart: 7, predicate: hasBorder });
    data = data.filter(data => !!data.Description);
    return processData<Liabilities.Liability>(data, liabilityHandlers);
}