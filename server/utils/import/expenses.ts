﻿import * as ExcelJS from "exceljs";
import {nanoid} from "nanoid";
import * as Expenses from "~/types/expenses";
import {firstRow, toData, toFrequency} from './utils';
import {Frequency} from "~/types/base";
import _ from "lodash";

type BaseExpense = Expenses.BaseExpense & {
    amount: number,
    frequency: Frequency
}
function getBaseExpense(data: Record<string, string>) : BaseExpense {
    return {
        id: nanoid(),
        mode: 'Simple',
        category: 'Expense',
        description: data.Description,
        amount: _.round(_.toNumber(data.Amount), 2),
        frequency: toFrequency(data['Frequency'])
    }
}

export default function processExpenses(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.worksheets[9];
    worksheet.columns = [{ key: 'Description' }, { key: 'Amount' }, {}, { key: 'Frequency' }, {}, { key: 'AnnualAmount' }, {}, { key: 'Notes' }];

    let result: Expenses.Expense[] = [];
    result = result
        .concat(processDiscretionaryExpenses(worksheet))
        .concat(processCharitableExpenses(worksheet))
        .concat(processFoodClothingTransportationExpenses(worksheet))
        .concat(processHousingExpenses(worksheet))
        .concat(processRentalExpenses(worksheet))
        .concat(processBusinessExpenses(worksheet))
        .concat(processOtherCommittedExpenses(worksheet))
        .concat(processEmployeeBenefitExpenses(worksheet))
        .concat(processTaxExpenses(worksheet))
        .concat(processSavingsExpenses(worksheet));

    //console.log(JSON.stringify(result, null, 2));
    return result;
}

function processDiscretionaryExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Discretionary Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseDiscretionary[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseDiscretionary'
        }];
    }
}
function processCharitableExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Charitable Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseCharitable[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseCharitable'
        }];
    }
}
function processFoodClothingTransportationExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Food, Clothing, Transportation');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseFoodClothingTransportation[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseFoodClothingTransportation'
        }];
    }
}
function processHousingExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Housing Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseHousing[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseHousing',
            expenseType: getExpenseType(housingExpenseTypes, data.Description, 'Other'), 
        }];
    }
}

function processRentalExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Rental Real Estate Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseRentalRealEstate[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseRentalRealEstate',
            expenseType: getExpenseType(rentalExpenseTypes, data.Description, 'Other')
        }];
    }
}

function processBusinessExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Business Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseBusiness[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseBusiness',
            expenseType: getExpenseType(businessExpenseTypes, data.Description, 'Other')
        }];
    }
}
function processOtherCommittedExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Other Committed Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseOtherCommitted[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseOtherCommitted'
        }];
    }
}
function processEmployeeBenefitExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Employee Benefit Expenses');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseEmployeeBenefit[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseEmployeeBenefit'
        }];
    }
}
function processTaxExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Taxes:');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseTaxes[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseTaxes',
            expenseType: getExpenseType(taxExpenseTypes, data.Description, 'Other')
        }];
    }
}
function processSavingsExpenses(worksheet: ExcelJS.Worksheet) {
    const expenseRow = firstRow(worksheet,  row => row.getCell(1).text === 'Savings:');
    if (!expenseRow) return [];
    let data = toData(worksheet, { rowStart: expenseRow.number+1, predicate: row => row.hasValues });
    return data.flatMap(toExpense);

    function toExpense(data: Record<string, string>) : Expenses.ExpenseSavingsBrokerage[] {
        if (!data.Frequency) return [];
        return [{
            ...getBaseExpense(data),
            kind: 'ExpenseSavingsBrokerage',
            expenseType: getExpenseType(savingsExpenseTypes, data.Description, 'Non-Qualified Savings'),
            associatedAssets: []
        }];
    }
}

function getExpenseType<TResult>(expenseTypes: ({match: RegExp, expenseType: TResult})[], description: string, def: TResult) : TResult {
    try {
        const matchedExpenseType = expenseTypes.find(c => c.match.test(`${description}`));
        if (!matchedExpenseType) {
            console.warn(`Could not find expense type for ${description}`);
            return def;
        }
        return matchedExpenseType.expenseType;
    }
    catch (e) {
        console.error(e);
        console.error(`Could not find expense type for ${description}`);
        throw e;
    }
}

type ExpenseTypeMatcher<T extends Expenses.Expense & { expenseType: any }> = {
    match: RegExp,
    expenseType: T["expenseType"]
}

const housingExpenseTypes : ExpenseTypeMatcher<Expenses.ExpenseHousing>[] = [
    {
        match: /^Rent/gmi,
        expenseType: 'Rent'
    },
    {
        match: /^Home Ins Premium/gmi,
        expenseType: 'Home Insurance Premiums'
    },
    {
        match: /^Real Estate Taxes/gmi,
        expenseType: 'Real Estate Taxes'
    },
    {
        match: /^Renters Insurance Premiums/gmi,
        expenseType: 'Renters Insurance Premiums'
    },
    {
        match: /^Electric/gmi,
        expenseType: 'Electric'
    },
    {
        match: /^Gas/gmi,
        expenseType: 'Gas'
    },
    {
        match: /^Water/gmi,
        expenseType: 'Water'
    },
    {
        match: /^Maid/gmi,
        expenseType: 'Maid'
    },
    {
        match: /^Lawn/gmi,
        expenseType: 'Lawn'
    },
    {
        match: /^Home Owner Association Dues/gmi,
        expenseType: 'HOA Dues'
    },
    {
        match: /^Security System/gmi,
        expenseType: 'Security System'
    },
    {
        match: /^Pool Maintenance/gmi,
        expenseType: 'Pool Maintenance'
    },
    {
        match: /^Home Improvements/gmi,
        expenseType: 'Home Improvements'
    },
    {
        match: /^Other/gmi,
        expenseType: 'Other'
    }
]
const rentalExpenseTypes : ExpenseTypeMatcher<Expenses.ExpenseRentalRealEstate>[] = [
    {
        match: /^Rental Real Estate Taxes/gmi,
        expenseType: 'Taxes'
    },
    {
        match: /^Rental Real Estate Ins/gmi,
        expenseType: 'Insurance'
    },
    {
        match: /^Other/gmi,
        expenseType: 'Other'
    }
]
const businessExpenseTypes : ExpenseTypeMatcher<Expenses.ExpenseBusiness>[] = [
    {
        match: /^Business Overhead/gmi,
        expenseType: 'Business Overhead'
    },
    {
        match: /^Business Taxes/gmi,
        expenseType: 'Business Taxes'
    },
    {
        match: /^Employee Benefit Expenses/gmi,
        expenseType: 'Employee Benefit Expenses'
    },
    {
        match: /^Other/gmi,
        expenseType: 'Other'
    }
    /*
    "Business Overhead" | "Business Taxes" | "Employee Benefit Expenses" | "Other";
     */
]
const taxExpenseTypes : ExpenseTypeMatcher<Expenses.ExpenseTaxes>[] = [
    {
        match: /^Federal Tax Withholding/gmi,
        expenseType: 'Federal Tax Withholding/Est. Pmts'
    },
    {
        match: /^Commission Tax Withholding/gmi,
        expenseType: 'Commission Tax Withholding'
    },
    {
        match: /^Bonus Withholding/gmi,
        expenseType: 'Bonus Withholding'
    },
    {
        match: /^State\/ Local Inc. Tax Withholding/gmi,
        expenseType: 'State/Local Income Tax Withholding'
    },
    {
        match: /^State\/ Local Inc. Commission/gmi,
        expenseType: 'State/Local Income Commission Tax'
    },
    {
        match: /^State\/ Local Inc. Bonus/gmi,
        expenseType: 'State/Local Income Bonus Tax'
    },
    {
        match: /^Social Security(?!.*Commission|.*Bo)/gmi,
        expenseType: 'Social Security'
    },
    {
        match: /^Social Security - Commission/gmi,
        expenseType: 'Social Security - Commission'
    },
    {
        match: /^Social Security - Bonus/gmi,
        expenseType: 'Social Security - Bonus'
    },
    {
        match: /^Medicare(?!.*Commission|.*Bo)/gmi,
        expenseType: 'Medicare'
    },
    {
        match: /^Medicare - Commission/gmi,
        expenseType: 'Medicare - Commission'
    },
    {
        match: /^Medicare - Bo/gmi,
        expenseType: 'Medicare - Bonus'
    },
    {
        match: /^Estimated Tax/gmi,
        expenseType: 'Estimated Tax Payments'
    },
    {
        match: /^SS\/Pension Tax/gmi,
        expenseType: 'SS/Pension Tax Withholding'
    }
]
const savingsExpenseTypes :  ExpenseTypeMatcher<Expenses.ExpenseSavingsBrokerage>[] = [
    {
        match: /Ret.Plan Savings.{2,3}401/gmi,
        expenseType: '401(k) / 403(b)'
    },
    {
        match: /Ret.Plan Savings - Roth 401/gmi,
        expenseType: 'Roth 401(k) / 403(b)'
    },
    {
        match: /Ret.Plan Savings - After Tax 401/gmi,
        expenseType: 'After Tax 401(k) / 403(b)'
    },
    {
        match: /IRA Savings.*\(Traditional\)/gmi,
        expenseType: 'IRA Savings (Traditional)'
    },
    {
        match: /Roth IRA Savings|IRA Savings.*\(Roth\)/gmi,
        expenseType: 'Roth IRA Savings (After Tax)'
    },
    {
        match: /Other Retirement Savings/gmi,
        expenseType: 'Other Retirement Savings'
    },
    {
        match: /Education . - CESA/gmi,
        expenseType: 'Education - CESA'
    },
    {
        match: /Education . - UTMA/gmi,
        expenseType: 'UTMA'
    },
    {
        match: /Cash Savings/gmi,
        expenseType: 'Cash Savings'
    },
    {
        match: /Non-Qualified/gmi,
        expenseType: 'Non-Qualified Savings'
    },
]
