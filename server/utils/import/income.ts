﻿import * as ExcelJS from "exceljs";
import {nanoid} from "nanoid";
import * as Income from "~/types/income";
import {toFrequency, undefinedHandler} from './utils';
import {Frequency} from "~/types/base";
import _ from "lodash";

type BaseIncome = Income.BaseIncome & {
    amount: number,
    frequency: Frequency,
}

const incomeHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Salary/Wage'); },
        process(baseIncome: BaseIncome) : Income.IncomeEmployee {
            return {
                ...baseIncome,
                kind: 'IncomeEmployee',
                employmentType: 'W2',
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Commission'); },
        process(baseIncome: BaseIncome) : Income.IncomeEmployee {
            return {
                ...baseIncome,
                kind: 'IncomeEmployee',
                employmentType: 'W2',
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Bonus'); },
        process(baseIncome: BaseIncome) : Income.IncomeEmployee {
            return {
                ...baseIncome,
                kind: 'IncomeEmployee',
                employmentType: 'W2',
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Pension'); },
        process(baseIncome: BaseIncome) : Income.IncomeRetirement {
            return {
                ...baseIncome,
                kind: 'IncomeRetirement',
                retirementType: 'Pension',
                taxes: 0
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Social Security'); },
        process(baseIncome: BaseIncome) : Income.IncomeRetirement {
            return {
                ...baseIncome,
                kind: 'IncomeRetirement',
                retirementType: 'Social Security',
                taxes: 0
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Retirement Income'); },
        process(baseIncome: BaseIncome) : Income.IncomeRetirement {
            return {
                ...baseIncome,
                kind: 'IncomeRetirement',
                retirementType: 'Retirement',
                taxes: 0
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Other Income'); },
        process(baseIncome: BaseIncome): Income.IncomeOther {
            return {
                ...baseIncome,
                kind: 'IncomeOther',
                taxes: 0
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Rental Income'); },
        process(baseIncome: BaseIncome) : Income.IncomeRental {
            return {
                ...baseIncome,
                kind: 'IncomeRental',
                taxes: 0
            };
        }
    }, {
        canProcess(data: Record<string, string>) { return data.Description.startsWith('Business Income'); },
        process(baseIncome: BaseIncome) : Income.IncomeBusiness {
            return {
                ...baseIncome,
                kind: 'IncomeBusiness'
            };
        }
    }
];

export default function processIncome(workbook: ExcelJS.Workbook) {
    const worksheet = workbook.worksheets[9];
    worksheet.columns = [{ key: 'Description' }, { key: 'Amount' }, {}, { key: 'Frequency' }, {}, { key: 'AnnualAmount' }, {}, { key: 'Notes' }];
    
    const data = toData(worksheet, {
        headerRow: 9,
        rowStart: 11,
        predicate: (row) => {
            if (!row) {
                return true;
            }
            return row.getCell(1).type !== 0;
        }
    });
    return data.flatMap(toIncome);

    function toIncome(data: Record<string, string>) {
        if (!data.Description || !_.toNumber(data.Amount)) return [];
        
        const baseData : BaseIncome = {
            id: nanoid(),
            mode: 'Simple',
            category: 'Income',
            description: data.Description,
            notes: data.Notes,
            amount: _.round(_.toNumber(data.Amount), 2),
            frequency: toFrequency(data.Frequency),
        }

        const { process } = incomeHandlers.find(({canProcess}) => canProcess(data)) ?? undefinedHandler;
        const result = process(baseData);
        return result ? [result] : [];
    }
}