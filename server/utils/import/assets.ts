﻿import * as Assets from "~/types/assets";
import * as ExcelJS from "exceljs";
import {VehicleType} from "~/types/base";
import {nanoid} from "nanoid";
import {hasBorder, processData, toData} from './utils';
import type {Expense} from "~/types/expenses";
import type {Liability} from "~/types/liabilities";
import _ from "lodash";

type BaseAsset = Assets.BaseAsset & {
    owners: string,
    equityValue: number,
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}
function getBaseAsset(data: Record<string, string>) : BaseAsset {
    return {
        id: nanoid(),
        mode: 'Simple',
        category: 'Asset',
        description: data.Description,
        owners: data['Owner(s)*'],
        equityValue: _.round(_.toNumber(data['Current Value']), 2),
        associatedExpenses: [],
        associatedLiabilities: []
    }
}

const personalAssetHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Category.endsWith("Residence"); },
        process(data: Record<string, string>) : Assets.AssetHome {
            return {
                ...getBaseAsset(data),
                kind: 'AssetHome',
                address: data.Description
            };
        }
    },
    {
        canProcess(data: Record<string, string>) { return ['Auto', 'Boat', 'Motorcycle', 'RV'].includes(data.Category); },
        process(data: Record<string, string>) : Assets.AssetVehicle {
            const vehicleType = (Object.values(VehicleType)).includes(data.Category as VehicleType) ? data.Category as VehicleType : VehicleType.Other;
            return {
                ...getBaseAsset(data),
                kind: 'AssetVehicle',
                vehicleType: vehicleType
            };
        }
    },
    {
        canProcess(data: Record<string, string>) { return ['Personal Property', 'Home Furnishings'].includes(data.Category); },
        process(data: Record<string, string>) : Assets.AssetPersonalProperty {
            return {
                ...getBaseAsset(data),
                kind: 'AssetPersonalProperty'
            };
        }
    }
];
const cashAssetHandlers = [
    {
        canProcess(data: Record<string, string>) { return !!data['Current Value']; },
        process(data: Record<string, string>): Assets.AssetBankAccount {
            return {
                ...getBaseAsset(data),
                kind: 'AssetBankAccount',
                purpose: data.Purpose
            };
        }
    }
]
const businessRentalAssetHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Category === 'Rental Property'; },
        process(data: Record<string, string>) : Assets.AssetRealEstate {
            return {
                ...getBaseAsset(data),
                kind: 'AssetRealEstate',
                address: data.Description
            };
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category === 'Business'; },
        process(data: Record<string, string>) : Assets.AssetBusiness {
            return {
                ...getBaseAsset(data),
                kind: 'AssetBusiness',
                purpose: data.Purpose
            };
        }
    }
];
const nonQualifiedAssetHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Category === 'NQ'; },
        process(data: Record<string, string>) : Assets.AssetBrokerageAccount {
            return {
                ...getBaseAsset(data),
                kind: 'AssetBrokerageAccount',
                accountType: 'Non-Qualified Account',
            };
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category === 'VUL Cash Value'; },
        process(data: Record<string, string>) : Assets.AssetLifeInsurance {
            return {
                ...getBaseAsset(data),
                kind: 'AssetLifeInsurance',
                accountType: 'Variable Universal Life'
            };
        }
    }
];
const qualifiedAssetHandlers = [
    {
        canProcess(data: Record<string, string>) { return data.Description.includes('IRA'); },
        process(data: Record<string, string>) : Assets.AssetIRA {
            const accountType = 
                data.Category.includes('Roth') || data.Description.includes('Roth') ? 'Roth IRA' :
                data.Category.includes('SEP') || data.Description.includes('SEP') ? 'SEP IRA' :
                data.Category.includes('SRA') || data.Description.includes('SRA') ? 'SIMPLE IRA' :
                data.Category.includes('Simple') || data.Description.includes('Simple') ? 'SIMPLE IRA' :
                    'Traditional IRA';
            return {
                ...getBaseAsset(data),
                kind: 'AssetIRA',
                accountType: accountType
            };
        }
    },
    {
        canProcess(data: Record<string, string>) { return data.Category === '401k'; },
        process(data: Record<string, string>) : Assets.Asset401k {
            const accountType =
                data.Category.includes('Roth') || data.Description.includes('Roth') ? 'Roth' :
                data.Category.includes('Solo') || data.Description.includes('Solo') ? 'Solo' :
                    'Traditional';
            return {
                ...getBaseAsset(data),
                kind: 'Asset401k',
                accountType: accountType
            };
        }
    }
];

export default function processAssets(workbook: ExcelJS.Workbook): Assets.Asset[] {
    let result: Assets.Asset[] = [];
    result = result
        .concat(processData<Assets.Asset>(toData(workbook.worksheets[2], { headerRow: 6, rowStart: 7, predicate: hasBorder }).filter(data => !!data['Current Value']), personalAssetHandlers))
        .concat(processData<Assets.Asset>(toData(workbook.worksheets[3], { headerRow: 6, rowStart: 7, predicate: hasBorder }).filter(data => !!data['Current Value']), cashAssetHandlers))
        .concat(processData<Assets.Asset>(toData(workbook.worksheets[3], { headerRow: 25, rowStart: 26, predicate: hasBorder }).filter(data => !!data['Current Value']), businessRentalAssetHandlers))
        .concat(processData<Assets.Asset>(toData(workbook.worksheets[4], { headerRow: 6, rowStart: 7, predicate: hasBorder }).filter(data => !!data['Current Value']), nonQualifiedAssetHandlers))
        .concat(processData<Assets.Asset>(toData(workbook.worksheets[5], { headerRow: 6, rowStart: 7, predicate: hasBorder }).filter(data => !!data['Current Value']), qualifiedAssetHandlers));

    //console.log(JSON.stringify(result, null, 2));
    return result;
}