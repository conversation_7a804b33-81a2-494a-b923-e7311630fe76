﻿import {Asset} from "~/types/assets";
import {Liability} from "~/types/liabilities";
import {Portfolio} from "~/types/portfolio";
import {PersonalBusinessType} from "~/types/base";
import {Income} from "~/types/income";
import {Expense} from "~/types/expenses";

export function getTotal<T>(getVals: (p:Portfolio) => T[] | null | undefined, getAmount: (p:T) => number | undefined | null) {
    return function (p:Portfolio) {
        return getVals(p)?.reduce((acc, val) => acc + (getAmount(val) ?? 0), 0) ?? 0;
    }
}

export function getAssetSubcategory(asset: Asset) {
    switch (true) {
        case isPersonalAsset(asset):
            return "Personal";
        case isBusinessAsset(asset):
            return "Business";
        case isRentalAsset(asset):
            return "Rental";
        case isCashAsset(asset):
            return "Cash";
        case isNonQualifiedAsset(asset):
            return "Non Qualified";
        case isQualifiedAsset(asset):
            return "Qualified";
        default:
            return "Other";
    }

    function isPersonalAsset(asset: Asset) {
        return asset.kind === 'AssetHome' || asset.kind === 'AssetVehicle' || asset.kind === 'AssetPersonalProperty' ||
            (asset.kind === 'AssetRealEstate' && !asset.associatedLiabilities?.some(l => l.loanType !== PersonalBusinessType.Personal)) ||
            (asset.kind === 'AssetEquipment');
    }

    function isBusinessAsset(asset: Asset) {
        return asset.kind === 'AssetBusiness';
    }

    function isRentalAsset(asset: Asset) {
        return asset.kind === 'AssetRealEstate' && asset.associatedLiabilities?.some(l => l.loanType === PersonalBusinessType.Rental);
    }

    function isCashAsset(asset: Asset) {
        return asset.kind === 'AssetCDMoneyMarket' ||
            (asset.kind === 'AssetBankAccount') ||
            (asset.kind === 'AssetBrokerageAccount' && asset.accountType === 'Cash Account');
    }

    function isNonQualifiedAsset(asset: Asset) {
        return (asset.kind === 'AssetBrokerageAccount' && asset.accountType === 'Non-Qualified Account') ||
            (asset.kind === 'AssetManagedAccount' && asset.accountType === 'Non-Qualified Account') ||
            asset.kind === 'Asset529CESA' || asset.kind === 'AssetUTMA' || asset.kind === 'AssetLifeInsurance' ||
            asset.kind === 'AssetStock' || asset.kind === 'AssetDeferredCompensation';
    }

    function isQualifiedAsset(asset: Asset) {
        return (asset.kind === 'AssetBrokerageAccount' && asset.accountType === 'Qualified Account') ||
            (asset.kind === 'AssetManagedAccount' && asset.accountType === 'Qualified Account') ||
            asset.kind === 'AssetIRA' || asset.kind === 'Asset401k' || asset.kind === 'AssetPension';
    }
}
export function getLiabilitySubCategory(liability: Liability, portfolio: Portfolio) {
    switch (true) {
        case isMortgage(liability):
            return "Mortgage";
        case isSecondHome(liability):
            return "Second Home";
        case isCarLoan(liability):
            return "Car Loans";
        case isStudentLoan(liability):
            return "Student Loans";
        case isConsumer(liability):
            return "Consumer";
        case isRentalLoan(liability):
            return "Rental Loans";
        case isPersonalLoan(liability):
            return "Personal Loans";
        case isBusinessLoan(liability):
            return "Business Loans";
        default:
            return "Other";
    }

    function isMortgage(liability: Liability) {
        if (liability.loanType !== PersonalBusinessType.Personal) return false;
        if (liability.kind !== "LiabilityMortgage") return false;
        const homes = portfolio.assets?.filter(asset => asset.kind === "AssetHome");
        const firstHome = homes?.length ? homes[0].id : null;
        if (firstHome === null) return true;
        if (!liability.associatedAssets) return true;
        if (liability.associatedAssets.length === 0) return true;
        return liability.associatedAssets.some(asset => asset.id === firstHome);
    }

    function isSecondHome(liability: Liability) {
        if (liability.loanType !== PersonalBusinessType.Personal) return false;
        if (liability.kind !== "LiabilityMortgage") return false;
        const homes = portfolio.assets?.filter(asset => asset.kind === "AssetHome");
        const firstHome = homes?.length ? homes[0].id : null;
        if (firstHome === null) return false;
        if (!liability.associatedAssets) return false;
        if (liability.associatedAssets.length === 0) return false;
        return liability.associatedAssets.some(asset => asset.id !== firstHome);
    }

    function isCarLoan(liability: Liability) {
        if (liability.loanType !== PersonalBusinessType.Personal) return false;
        return liability.kind === 'LiabilityAutoLoan';
    }

    function isStudentLoan(liability: Liability) {
        return liability.kind === 'LiabilityStudentLoan';
    }

    function isConsumer(liability: Liability) {
        if (liability.loanType !== PersonalBusinessType.Personal) return false;
        return liability.kind === 'LiabilityCreditCard';
    }

    function isRentalLoan(liability: Liability) {
        return liability.loanType === PersonalBusinessType.Rental;
    }

    function isPersonalLoan(liability: Liability) {
        if (liability.loanType !== PersonalBusinessType.Personal) return false;

        if (liability.kind === "LiabilityMortgage") {
            return liability.mortgageType === '2nd Mortgage' || liability.mortgageType === 'Home Equity' || liability.mortgageType === 'Other';
        }
        return false;
    }

    function isBusinessLoan(liability: Liability) {
        return liability.loanType === PersonalBusinessType.Business;
    }
}
export function getIncomeSubcategory(income: Income) {
    switch (true) {
        case isEarned(income): return "Earned";
        case isPension(income): return "Pension";
        case isSocialSecurity(income): return "Social Security";
        case isRetirementPlan(income): return "Retirement Plans";
        case isRental(income): return "Rental";
        case isBusiness(income): return "Business";
        default: return "Other";
    }

    function isEarned(income: Income) {
        return income.kind === 'IncomeEmployee';
    }
    function isPension(income: Income) {
        return income.kind === 'IncomeRetirement' && income.retirementType === 'Pension';
    }
    function isSocialSecurity(income: Income) {
        return income.kind === 'IncomeRetirement' && income.retirementType === 'Social Security';
    }
    function isRetirementPlan(income: Income) {
        return income.kind === 'IncomeRetirement' && (income.retirementType === 'Retirement' || income.retirementType ==='RMD');
    }
    function isRental(income: Income) {
        return income.kind === 'IncomeRental'
    }
    function isBusiness(income: Income) {
        return income.kind === 'IncomeBusiness';
    }
}
export function getExpenseSubcategory(expense: Expense) {
    switch (true) {
        case isCommitted(expense): return "Committed";
        case isDiscretionary(expense): return "Discretionary";
        case isCharitable(expense): return "Charitable";
        case isHousing(expense): return "Housing";
        case isEmployeeBenefit(expense): return "Employee Benefits";
        case isAutoLoan(expense): return "Auto Loans";
        case isStudentLoan(expense): return "Student Loans";
        case isConsumerDebt(expense): return "Consumer Debt";
        case isRentalRealEstate(expense): return "Rental Real Estate";
        case isBusinessLoans(expense): return "Business Loans";
        case isBusinessExpenses(expense): return "Business Expenses";
        case isTaxes(expense): return "Taxes";
        default: return "Other";
    }

    function isCommitted(expense: Expense) {
        return expense.kind === 'ExpenseOtherCommitted';
    }
    function isDiscretionary(expense: Expense) {
        return expense.kind === 'ExpenseDiscretionary';
    }
    function isCharitable(expense: Expense) {
        return expense.kind === 'ExpenseDonation';
    }
    function isHousing(expense: Expense) {
        return expense.kind === 'ExpenseHousing';
    }
    function isEmployeeBenefit(expense: Expense) {
        return expense.kind === 'ExpenseEmployeeBenefit';
    }
    function isAutoLoan(expense: Expense) {
        return expense.kind === 'ExpenseLoanPayment' && expense.associatedLiabilities?.some(liability => liability.kind === 'LiabilityAutoLoan');
    }
    function isStudentLoan(expense: Expense) {
        return expense.kind === 'ExpenseLoanPayment' && expense.associatedLiabilities?.some(liability => liability.kind === 'LiabilityStudentLoan');
    }
    function isConsumerDebt(expense: Expense) {
        return expense.kind === 'ExpenseLoanPayment' && expense.associatedLiabilities?.some(liability => liability.kind === 'LiabilityCreditCard');
    }
    function isRentalRealEstate(expense: Expense) {
        return expense.kind === 'ExpenseRentalRealEstate';
    }
    function isBusinessLoans(expense: Expense) {
        return expense.kind === 'ExpenseLoanPayment' && expense.associatedLiabilities?.some(liability => liability.kind === 'LiabilityBusinessLoan');
    }
    function isBusinessExpenses(expense: Expense) {
        return expense.kind === 'ExpenseBusiness';
    }
    function isTaxes(expense: Expense) {
        return expense.kind === 'ExpenseTaxes';
    }
}
export function getSavingsSubCategory(expense: Expense) {
    return expense.description;
}
