﻿import { ApiResponse, AuthenticationClient, ManagementClient } from 'auth0';
import { UserSession } from '#nuxt-oidc/types';
import { createConsola } from 'consola';
import { colors } from 'consola/utils';

const logger = createConsola().withDefaults({ tag: 'utils.auth0' });

export async function resetPassword(user: UserSession): Promise<void> {
    const authenticationClient = new AuthenticationClient(getConnectionOptions());

    logger.info('Calling auth0 changePassword for user: `%s`', user.userData.email);
    const response = await authenticationClient.database.changePassword({
        email: user.userData.email,
        connection: 'Username-Password-Authentication',
    });
    validateResponse(response);
    logger.info('ChangePassword success: ', response);
}

export async function disableUser(user: UserSession): Promise<void> {
    const connectionOptions = getConnectionOptions();
    logger.info(connectionOptions);
    const managementClient = new ManagementClient(connectionOptions);

    logger.info('Calling auth0 update user for user: `%s`', user.userData.authId);
    const response = await managementClient.users.update(
        { id: user.userData.authId },
        { user_metadata: { disabled: true } },
    );
    validateResponse(response);
    logger.info('UpdateUser success: ', response);
}

function validateResponse(response: ApiResponse<unknown>) {
    if (response.status < 200 || response.status > 299) {
        logger.error('Received error response: ', response);
        throw createError({
            statusCode: 500,
            message: 'An error occurred',
        });
    }
}

function getConnectionOptions() {
    const runtimeConfig = useRuntimeConfig();
    const config = runtimeConfig.oidc.providers.auth0;
    if (!config || !config.baseUrl || !config.clientId || !config.clientSecret) {
        throw new Error('Auth0 is not configured. Please configure Auth0 properly.');
    }
    const domain = new URL(config.baseUrl).hostname;

    return {
        domain: domain,
        clientId: config.clientId,
        clientSecret: config.clientSecret,
    };
}
