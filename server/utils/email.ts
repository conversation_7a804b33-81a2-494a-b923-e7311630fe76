﻿import {
    EmailAttachment,
    EmailClient,
    EmailMessage,
    EmailRecipients,
    EmailSendResponse,
    KnownEmailSendStatus,
} from '@azure/communication-email';
import { AllowedComponentProps, Component, createSSRApp, VNodeProps } from 'vue';
import { renderToString } from '@vue/server-renderer';
import mime from 'mime';
import mjml2html from 'mjml';
import { PollerLike, PollOperationState } from '@azure/core-lro';
import { Resvg } from '@resvg/resvg-js';
import { EventHandlerRequest, getRequestURL, H3Event } from 'h3';
import * as path from 'node:path';

type ExtractComponentProps<TComponent> = TComponent extends new () => {
    $props: infer P;
}
    ? Omit<P, keyof VNodeProps | keyof AllowedComponentProps>
    : never;

export async function sendEmail(
    event: H3Event<EventHandlerRequest>,
    recipients: EmailRecipients,
    subject: string,
    body: string,
) {
    const runtimeConfig = useRuntimeConfig();
    if (!runtimeConfig.sendEmail) {
        console.info('Email configured to NOT send. Change runtimeConfig.sendEmail to true in order to send emails.');
        return;
    }

    const extracted = extractImages(body);
    const attachments = (await Promise.all(extracted.attachments)).filter<EmailAttachment>(i => i !== null);

    const connectionString = runtimeConfig.emailConnectionString;
    const emailSender = runtimeConfig.emailSender;
    if (!connectionString || !emailSender) {
        console.info('Saving to email storage.');
        const dataStorage = useStorage('email');
        const filename = generateRandomId(10);
        await dataStorage.setItemRaw(`${filename}.html`, extracted.html.replaceAll('cid:', `./${filename}.`));

        await Promise.all(
            attachments.map(async item => {
                await dataStorage.setItemRaw(
                    `${filename}.${item.contentId}`,
                    Buffer.from(item.contentInBase64, 'base64'),
                );
            }),
        );
        return;
    }

    console.info('Sending email');
    console.info('Email connection string: ', connectionString);
    console.info('Email sender: ', emailSender);
    const emailClient = new EmailClient(connectionString);
    const message: EmailMessage = {
        senderAddress: emailSender,
        recipients: recipients,
        content: {
            subject: subject,
            html: extracted.html,
        },
        attachments: attachments,
    };

    const poller = await emailClient.beginSend(message);
    event.waitUntil(waitForPoller(poller));
}

export function getBaseUrl(event: H3Event<EventHandlerRequest>) {
    const runtimeConfig = useRuntimeConfig();
    if (runtimeConfig.emailBaseUrl) return runtimeConfig.emailBaseUrl;
    return getRequestURL(event, { xForwardedHost: true, xForwardedProto: true });
}

async function waitForPoller(poller: PollerLike<PollOperationState<EmailSendResponse>, EmailSendResponse>) {
    const POLLER_WAIT_TIME = 10;

    if (!poller.getOperationState().isStarted) {
        throw 'Poller was not started.';
    }

    let timeElapsed = 0;
    while (!poller.isDone()) {
        await poller.poll();
        console.info('Email send polling in progress');

        await new Promise(resolve => setTimeout(resolve, POLLER_WAIT_TIME * 1000));
        timeElapsed += 10;

        if (timeElapsed > 18 * POLLER_WAIT_TIME) {
            throw 'Polling timed out.';
        }
    }
    const result = poller.getResult();
    if (!result) {
        throw 'Poller returned no result';
    }
    if (result.status === KnownEmailSendStatus.Succeeded) {
        console.info(`Successfully sent the email (operation id: ${result.id})`);
    } else {
        throw result.error;
    }
}

export async function renderEmail<T extends Component>(
    component: T,
    props?: ExtractComponentProps<T> & { advisor: { displayName: string } },
) {
    const app = createSSRApp(component, props || {});
    let markup = await renderToString(app);
    markup = markup.replace('<!--[-->', '').replace('<!--]-->', '');
    const { html } = mjml2html(markup);
    return html;
}

export function extractImages(html: string) {
    const attachments: Promise<EmailAttachment | null>[] = [];
    const regex = /<img\s+[^>]*src="([^"]*)"/g;

    const result = html.replace(regex, replaceSrc);

    return {
        html: result,
        attachments: attachments,
    };

    function replaceSrc(str: string, src: string) {
        const contentId = generateRandomId(16);
        attachments.push(getAttachment(src, contentId));
        return str.replace(src, `cid:${contentId}`);
    }

    async function getAttachment(src: string, contentId: string) {
        const reInline = /data:(?<mime>.*);base64,(?<b64>.*)/.exec(src);
        let base64: string;
        let contentType: string;
        if (reInline && reInline.groups) {
            base64 = reInline.groups.b64;
            contentType = reInline.groups.mime;
        } else {
            const asset = await useStorage().getItemRaw<Buffer | Uint8Array>(`assets:server:${src}`);
            if (!asset) return null;

            const buffer = Buffer.isBuffer(asset) ? asset : Buffer.from(asset);
            base64 = buffer.toString('base64');
            contentType = mime.getType(src) ?? 'image/png';
        }

        const extension = mime.getExtension(contentType);
        return {
            name: `${contentId}.${extension}`,
            contentId: contentId,
            contentType: contentType,
            contentInBase64: base64,
        };
    }
}

export function getSignature(displayName: string) {
    const content = `<svg viewBox='0 0 600 600' xmlns='http://www.w3.org/2000/svg'><text x='80' y='80' font-size='65' fill='#57719C'>${displayName}</text></svg>`;
    const signatureFont = path.join(process.cwd(), 'public', 'fonts', 'QwitcherGrypen-Regular.ttf');
    const reSvg = new Resvg(content, {
        background: 'rgba(0, 0, 0, 0)',
        font: {
            fontFiles: [signatureFont], // Load custom fonts.
            loadSystemFonts: false, // It will be faster to disable loading system fonts.
            defaultFontFamily: 'Qwitcher Grypen', // You can omit this.
        },
        textRendering: 1,
    });
    const svgBounds = reSvg.getBBox();
    if (svgBounds) {
        reSvg.cropByBBox(svgBounds);
    }
    const pngData = reSvg.render();
    return {
        src: 'data:image/png;base64,' + pngData.asPng().toString('base64'),
        width: pngData.width,
        height: pngData.height,
    };
}

function generateRandomId(length: number = 10) {
    // noinspection SpellCheckingInspection
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let randomId = '';
    for (let i = 0; i < length; i++) {
        randomId += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return randomId;
}
