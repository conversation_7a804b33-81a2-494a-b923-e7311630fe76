﻿import { getUserSession } from '#nuxt-oidc/server/utils/session';
import { ProviderKeys, UserData, UserGroup, UserSession } from '#nuxt-oidc/types';
import type { AuthSessionConfig, ProviderSessionConfig } from '#nuxt-oidc/types';
import * as providerPresets from '#nuxt-oidc/providers';
import { EventHandlerRequest, H3Event, SessionConfig } from 'h3';
import { UserDataWithGroups, UserModel } from '../models/user.schema';
import { IntakeModel } from '~/server/models/intake.schema';
import defu from 'defu';
import { nanoid } from 'nanoid';

const groupMap = useRuntimeConfig().groupMap as Record<string, UserGroup>;

export async function getUserId(event: H3Event<EventHandlerRequest>) {
    const session = await getUser(event);
    return session.userData._id;
}

export async function getUser(event: H3Event<EventHandlerRequest>) {
    const session = await getFPUserSession(event);

    if (!session.userData._id || !session.userData.email) {
        throw createError({
            statusCode: 401,
            message: 'Authentication Required',
        });
    }

    return session;
}

export async function impersonateUser(event: H3Event, impersonatedUser: UserData) {
    const session = await getFPUserSession(event);

    session.isImpersonated = true;
    session.originalUser = session.userData._id;
    session.userData = impersonatedUser;

    return await setUserSession(event, {
        canRefresh: session.canRefresh,
        expireAt: session.expireAt,
        provider: session.provider,
        isImpersonated: session.isImpersonated,
        originalUser: session.originalUser,
        userData: session.userData,
    });
}

export async function stopImpersonation(event: H3Event) {
    const session = await getFPUserSession(event);
    if (!session.isImpersonated) return;

    session.isImpersonated = false;
    session.originalUser = undefined;

    await setUserSession(event, session);
}

export async function getFPUserSession(event: H3Event, session?: UserSession) {
    session = await getUserSession(event);
    //const needsUpdate = !session.groups || !session.userData;
    session.groups = getUserGroups(session);
    session.userData = await getUserData(event, session);

    // if (needsUpdate) {
    //     const setSession = await setUserSession(event, session);
    //     console.log('set session', setSession);
    // }
    return session;
}

function getUserGroups(session: UserSession) {
    if (session.userData?.isDisabled || session.userInfo?.user_metadata?.disabled) {
        console.log('Disabled account');
        return ['Deleted'];
    }

    const appMetadata = session.userInfo?.app_metadata as Record<string, any>;
    if (appMetadata && appMetadata.roles) {
        return unique<UserGroup>(['Client', ...appMetadata.roles]);
    }

    if (session.groups) return session.groups;

    // Map group claim ids to group names
    let mappedClaims = Array.prototype.map(group => groupMap[group], session.claims?.['groups'] ?? []).filter(Boolean);
    return unique<UserGroup>(['Client', ...mappedClaims]);
}

async function getUserData(event: H3Event, session: UserSession): Promise<UserDataWithGroups> {
    if (session.isImpersonated && session.userData) return session.userData;

    const authId = `${session.userInfo?.sub ?? session.claims?.sub}`;
    const userEmail = `${session.userInfo?.email ?? session.claims?.email}`;

    if (!authId || !userEmail) {
        throw createError({
            statusCode: 401,
            message: 'Authentication Required',
        });
    }

    const userData =
        (await UserModel.findByAuthId(authId).exec()) ??
        (await UserModel.findByAuthId(`temp|${userEmail}`).exec()) ??
        new UserModel({ _id: nanoid(), email: userEmail });

    const intakeData = await IntakeModel.findOne({ user: userData._id }).exec();

    userData.set({
        authId: authId,
        email: userEmail,
        phone: intakeData?.phone,
        joinDate: userData?.joinDate ?? new Date(),
        firstName: `${intakeData?.name?.firstName ?? session.userInfo?.given_name ?? session.claims?.given_name}`,
        lastName: `${intakeData?.name?.lastName ?? session.userInfo?.family_name ?? session.claims?.family_name}`,
        profileImage: (session.userInfo?.picture as string) ?? undefined,
        groups: session.groups,
        intakeCompleted: intakeData?.intakeCompleted ?? false,
    });

    await userData.save();

    return userData.toObject();
}

function unique<T>(array: ArrayLike<T> | null | undefined): T[] {
    return !array ? [] : Array.from<T>(new Set<T>(Array.from(array)));
}

// The built-in setUserSession in nuxt-oidc-auth uses defu() to combine the sessions.
// This isn't what we want here. Especially since it ends up duplicating any arrays stored in the session.
// But also because we explicitly want to _unset_ data in the session at times.
async function setUserSession(event: H3Event, data: UserSession) {
    const session = await _useSession(event);
    await session.update(data);
    return session.data;
}

const sessionName = 'nuxt-oidc-auth';
let sessionConfig: Pick<SessionConfig, 'name' | 'password'> & AuthSessionConfig;
const providerSessionConfigs: Record<ProviderKeys, ProviderSessionConfig> = {} as any;
function _useSession(event: H3Event) {
    if (!sessionConfig || !Object.keys(providerSessionConfigs).length) {
        // Merge sessionConfig
        sessionConfig = defu(
            { password: process.env.NUXT_OIDC_SESSION_SECRET!, name: sessionName },
            useRuntimeConfig(event).oidc.session,
        );
        // Merge providerSessionConfigs
        Object.keys(useRuntimeConfig(event).oidc.providers)
            .map(key => key as ProviderKeys)
            .forEach(
                key =>
                    (providerSessionConfigs[key] = defu(
                        useRuntimeConfig(event).oidc.providers[key]?.sessionConfiguration,
                        providerPresets[key].sessionConfiguration,
                        {
                            automaticRefresh: useRuntimeConfig(event).oidc.session.automaticRefresh,
                            expirationCheck: useRuntimeConfig(event).oidc.session.expirationCheck,
                            expirationThreshold: useRuntimeConfig(event).oidc.session.expirationThreshold,
                        },
                    ) as ProviderSessionConfig),
            );
    }
    return useSession<UserSession>(event, sessionConfig);
}
