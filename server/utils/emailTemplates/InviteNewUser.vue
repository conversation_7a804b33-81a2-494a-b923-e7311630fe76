﻿<template>
    <EmailTemplate title="Account Invitation & Financial Access Request">
        <template #header>Join & <PERSON> Advisor Access</template>
        <template #body>
            <mj-text mj-class="body">
                <p class="bold">Dear {{ recipient.firstName }},</p>
                <p>
                    <strong>{{ advisor.firstName }} {{ advisor.lastName }}</strong>, one of our trusted financial advisors,
                    would like to help you manage and plan your finances. To get started, please follow
                    the link below to create a new account. Once your account is set up and you've granted
                    permission, {{ advisor.firstName }} can securely access your profile and
                    provide tailored financial advice.
                </p>
                <p>Here are some quick details about your advisor:</p>
                <ul class="list">
                    <li class="bold">{{ advisor.firstName }} {{ advisor.lastName }}</li>
                    <li>{{ advisor.office }}</li>
                    <li>{{ advisor.phoneNumber }}</li>
                    <li>{{ advisor.email }}</li>
                </ul>
            </mj-text>

            <mj-button :href="signUpLink">
                CREATE ACCOUNT
            </mj-button>

            <mj-text>
                <p>
                    If you have any questions or need assistance, our support team is here to help at
                    {{ supportInfo }}.
                </p>
                <p>
                    Thank you for choosing us to be a part of your financial journey. We look forward to
                    working with you!
                </p>
                <div>Best regards,</div>
            </mj-text>
            <mj-image :alt="advisor.displayName" :src="signature.src" align="left" :width="`${signature.width}px`" />
            <mj-text>
                <div>{{ advisor.displayName }}</div>
                <div>{{ companyName }}</div>
            </mj-text>
        </template>
    </EmailTemplate>
</template>

<script setup lang="ts">
import EmailTemplate from '~/components/email/EmailTemplate.vue'
import type {UserData} from "#nuxt-oidc/types";
import {computed} from "vue";
import {getSignature} from "~/server/utils/email";

const signature = computed(() => getSignature(props.advisor.displayName));

export interface InviteNewUserProps {
    recipient: {
        firstName: string,
        lastName: string
    },
    advisor: {
        firstName: string,
        lastName: string,
        displayName: string,
        office: string,
        phoneNumber: string,
        email: string,
        signatureUrl?: string
    },
    signUpLink: string,
    supportInfo: string,
    companyName: string
}

const props = defineProps<InviteNewUserProps>()
</script>