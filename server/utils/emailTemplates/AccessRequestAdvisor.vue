﻿<!--suppress HtmlUnknownTag, UnknownAttribute -->
<template>
    <EmailTemplate title="New Financial Advisor Request">
        <template #header>New Financial Advisor Request</template>
        <template #body>
            <mj-text mj-class="body">
                <p class="bold">Dear {{ recipient.firstName }},</p>
                <p>
                    <strong>{{ advisor.displayName }}</strong>, one of our trusted financial advisors that you're connected
                    with,
                    has requested to link with your financial profile. By granting access, {{ advisor.firstName }} will be able
                    to provide you with more tailored advice, deeper insights, and personalized strategies to
                    help you
                    achieve your financial goals.
                </p>
                <p>To approve the connection, just confirm the advisor's information and click below:</p>
                <ul class="list">
                    <li class="bold">{{ advisor.displayName }}</li>
                    <li>{{ advisor.office }}</li>
                    <li>{{ advisor.phoneNumber }}</li>
                    <li>{{ advisor.email }}</li>
                </ul>
            </mj-text>
            <mj-button :href="accessLink">
                ACCEPT ADVISOR
            </mj-button>
            <mj-text>
                <p>Have questions? We're here to help at {{ supportInfo }}.</p>
                <p>We appreciate the opportunity to serve you, and we look forward to your continued success.
                </p>
                <div>Best regards,</div>
            </mj-text>
            <mj-image :alt="advisor.displayName" :src="signature.src" align="left" :width="`${signature.width}px`" />
            <mj-text>
                <div>{{ advisor.displayName }}</div>
                <div>{{ companyName }}</div>
            </mj-text>
        </template>
    </EmailTemplate>
 </template>
<script setup lang="ts">
import EmailTemplate from "~/components/email/EmailTemplate.vue"
import { getSignature } from "../email"
import { computed } from "vue";

const signature = computed(() => getSignature(props.advisor.displayName));

export interface RequestAccessAdvisorProps {
    recipient: {
        firstName: string,
        lastName: string
    },
    advisor: {
        firstName: string,
        lastName: string,
        displayName: string,
        office: string,
        phoneNumber: string,
        email: string,
        signatureUrl?: string
    },
    accessLink: string,
    supportInfo: string,
    companyName: string
}

const props = defineProps<RequestAccessAdvisorProps>();

</script>
