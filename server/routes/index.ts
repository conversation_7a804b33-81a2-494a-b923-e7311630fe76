﻿// noinspection JSUnusedGlobalSymbols

import { getUserId } from '~/server/utils/user';
import { IntakeModel } from '~/server/models/intake.schema';
import { Portfolios } from '~/server/models/portfolio.schema';
import { UserModel } from '~/server/models/user.schema';

export default defineEventHandler(async event => {
    try {
        const postLogin = getCookie(event, 'post_login');
        if (postLogin) {
            console.log('postLogin', postLogin);
            deleteCookie(event, 'post_login');
            // ignore post-login redirects to /client/intake or /client/dashboard so we can handle those properly below
            if (!postLogin.match(/\/client\/intake|\/client\/dashboard/i)) {
                return sendRedirect(event, postLogin);
            }
        }

        const userId = await getUserId(event);

        // Check if user is an advisor first
        const user = await UserModel.findOne({ _id: userId }).lean();
        if (user?.groups?.includes('Advisor')) {
            console.log('User is advisor. Redirecting to /advisor/UserManagement');
            return sendRedirect(event, '/advisor/UserManagement');
        }

        const result = await IntakeModel.findOne({ user: userId });
        if (!result?.intakeCompleted) {
            console.log('Intake not complete. Redirecting to /client/intake');
            return sendRedirect(event, '/client/intake');
        }

        const portfolio = await Portfolios.findByOwner(userId);
        const hasInvites = portfolio?.owners.some(o => o.userType === 'Invited') ?? false;
        if (hasInvites) {
            console.log('Has Invites. Redirecting to /client/settings/access');
            return sendRedirect(event, '/client/settings/access');
        }

        console.log('Redirecting to /client/dashboard');
        return sendRedirect(event, '/client/dashboard');
    } catch (error: any) {
        if (error.statusCode !== 401) {
            console.error(error);
            console.log('Error occurred. Redirecting to /client/intake');
        }
        return sendRedirect(event, '/client/intake');
    }
});
