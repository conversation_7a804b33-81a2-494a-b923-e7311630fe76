import { Frequency } from '~/types/base';

export const frequencyToMonthlyFactor: Record<Frequency, number> = {
    [Frequency.Daily]: 30,
    [Frequency.Weekly]: 4,
    [Frequency.Biweekly]: 2,
    [Frequency.SemiMonthly]: 2,
    [Frequency.Monthly]: 1,
    [Frequency.Bimonthly]: 0.5,
    [Frequency.Trimesterly]: 1 / 3,
    [Frequency.Quarterly]: 1 / 3,
    [Frequency.SemiAnnually]: 1 / 6,
    [Frequency.Annually]: 1 / 12,
    [Frequency.Yearly]: 1 / 12,
    [Frequency.Other]: 1,
};
