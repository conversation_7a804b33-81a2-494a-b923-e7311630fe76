﻿// noinspection JSUnusedGlobalSymbols

import type { Dashboard } from '~/types/dashboard';
import mongoose from 'mongoose';

export const DashboardModel = mongoose.model<Dashboard>(
    'Dashboard',
    new mongoose.Schema<Dashboard>(
        {
            name: String,
            description: String,
            widgets: [],
            createdAt: { type: Date, default: Date.now },
            updatedAt: Date,
            updatedBy: { type: String, ref: 'User' },
        },
        {
            strict: false,
        },
    ),
);
