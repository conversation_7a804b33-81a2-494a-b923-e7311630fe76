﻿import { UserData, UserGroup } from '#nuxt-oidc/types';
import mongoose, { type HydratedDocument, type Model, type QueryWithHelpers } from 'mongoose';

export type UserDataWithGroups = UserData & { groups?: UserGroup[] };

interface UserDataModel extends Model<UserDataWithGroups> {
    findByAuthId(
        authId: string,
    ): QueryWithHelpers<
        HydratedDocument<UserDataWithGroups> | null,
        HydratedDocument<UserDataWithGroups>,
        {},
        HydratedDocument<UserDataWithGroups>,
        'findOne'
    >;
}

const UserSchema = new mongoose.Schema<UserDataWithGroups, UserDataModel>(
    {
        _id: { type: String, required: true },
        authId: { type: String, required: true, index: true, unique: true },
        email: { type: String, required: true },
        firstName: { type: String },
        lastName: { type: String },
        joinDate: { type: Date },
        groups: [String],
        profileImage: { type: String },
        isDisabled: { type: Boolean, default: false },
    },
    {
        statics: {
            findByAuthId(this: Model<UserDataWithGroups>, authId: string) {
                return this.findOne({ authId: authId });
            },
        },
    },
);

export const UserModel = mongoose.model<UserDataWithGroups, UserDataModel>('User', UserSchema);
