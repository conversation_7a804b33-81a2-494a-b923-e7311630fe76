﻿// noinspection JSUnusedGlobalSymbols
import {Portfolio, PortfolioSnapshot} from "~/types/portfolio";
import mongoose, {Aggregate, Model, QueryWithHelpers, HydratedDocument} from "mongoose";

const PortfolioUserSchema = new mongoose.Schema({
    userType: {
        type: String,
        enum: ["Owner", "Advisor", "User", "Invited", "Declined", "Revoked"],
        required: true,
    },
    owner: { type: String, required: true, index: true },
    createdAt: { type: Date, required: true, default: Date.now },
    updatedAt: { type: Date },
    updatedBy: { type: String },
});

interface PortfolioQueryHelpers {
    byOwner(ownerId: string): QueryWithHelpers<HydratedDocument<Portfolio>[], HydratedDocument<Portfolio>, PortfolioQueryHelpers>;
}
interface PortfolioModel extends Model<Portfolio, PortfolioQueryHelpers> {
    findByOwner(ownerId: string): QueryWithHelpers<HydratedDocument<Portfolio> | null, HydratedDocument<Portfolio>, {}, HydratedDocument<Portfolio>, 'findOne'>;
}

const PortfolioSchema = new mongoose.Schema<Portfolio, PortfolioModel, {}, PortfolioQueryHelpers>({
    income: [],
    assets: [],
    expenses: [],
    liabilities: [],
    owners: { type: [PortfolioUserSchema], required: true }
}, {
    query: {
        byOwner(this: QueryWithHelpers<any, HydratedDocument<Portfolio>, PortfolioQueryHelpers>, ownerId: string) {
            return this.find({ owners: { $elemMatch: { owner: ownerId, userType: "Owner"} } });
        }
    },
    statics: {
        findByOwner(this: Model<Portfolio>, ownerId: string): QueryWithHelpers<HydratedDocument<Portfolio> | null, HydratedDocument<Portfolio>, {}, HydratedDocument<Portfolio>, 'findOne'> {
            return this.findOne({ owners: { $elemMatch: { owner: ownerId, userType: "Owner"} } });
        }
    }
});

export const Portfolios = mongoose.model<Portfolio, PortfolioModel, PortfolioQueryHelpers>('Portfolio', PortfolioSchema);

interface PortfolioSnapshotModel extends Model<PortfolioSnapshot> {
    groupByDay(userId: string): Aggregate<Array<PortfolioSnapshot>>;
}

const PortfolioSnapshotSchema = new mongoose.Schema<PortfolioSnapshot, PortfolioSnapshotModel>({
        income: [],
        assets: [],
        expenses: [],
        liabilities: [],
        owners: { type: [PortfolioUserSchema], required: true }, // Extend the Portfolio schema
        snapshotDate: {
            type: Date,
            index: true
        },
        updatedBy: {
            type: String,
            ref: 'User'
        }
    },
    {
        statics: {
            groupByDay(this: Model<PortfolioSnapshot>, userId: string) {
                return this.aggregate()
                    .match({owners: {$elemMatch: {userType: "Owner", owner: userId}}})
                    .sort({snapshotDate: 1})
                    .group({
                        _id: {$dateToString: {format: "%Y-%m-%d", date: "$snapshotDate"}},
                        doc: {$last: "$$ROOT"}
                    })
                    .replaceRoot("$doc")
                    .sort({snapshotDate: -1});
            }
        }
    });

export const PortfolioSnapshots = mongoose.model<PortfolioSnapshot, PortfolioSnapshotModel>('PortfolioSnapshot', PortfolioSnapshotSchema);

