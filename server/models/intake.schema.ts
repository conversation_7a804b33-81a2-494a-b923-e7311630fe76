﻿// noinspection JSUnusedGlobalSymbols

import { IntakeData } from '~/types/intake';
import mongoose from 'mongoose';

export type IntakeDataWithUser = IntakeData & { user: string };

export const defaultPhoneCountryCode = '+1';

export const IntakeModel = mongoose.model<IntakeDataWithUser>(
    'Intake',
    new mongoose.Schema<IntakeDataWithUser>(
        {
            user: {
                type: String,
                required: true,
                ref: 'User',
                index: true,
            },
            phoneCountryCode: {
                type: String,
                required: false,
                default: defaultPhoneCountryCode,
            },
        },
        {
            strict: false,
        },
    ),
);
