﻿// noinspection JSUnusedGlobalSymbols

import type { Paystub, PaystubLine } from '~/types/paystub';
import mongoose from 'mongoose';
import { Frequency } from '~/types/base';

const PaystubLineSchema = new mongoose.Schema<PaystubLine>({
    description: { type: String, required: true },
    frequencyPerMonth: { type: Number, default: null },
    rate: { type: Number, default: null },
    hours: { type: Number, default: null },
    current: { type: Number, default: null },
    ytd: { type: Number, default: null },
    customLineOptions: {
        title: { type: String, default: '' },
    },
});

export const PaystubModel = mongoose.model<Paystub>(
    'Paystub',
    new mongoose.Schema<Paystub>(
        {
            name: String,
            payDate: { type: Date, default: Date.now },
            frequency: {
                type: String,
                enum: Object.values(Frequency),
                default: Frequency.Monthly,
            },
            incomeItems: { type: [PaystubLineSchema], default: [] },
            preTaxItems: { type: [PaystubLineSchema], default: [] },
            postTaxItems: { type: [PaystubLineSchema], default: [] },
            taxesItems: { type: [PaystubLineSchema], default: [] },
            customItems: { type: [PaystubLineSchema], default: [] },
            createdAt: { type: Date, default: Date.now },
            updatedAt: Date,
            updatedBy: { type: String, ref: 'User' },
        },
        {
            strict: false,
        },
    ),
);

export type PaystubDocument = mongoose.Document & Paystub;
