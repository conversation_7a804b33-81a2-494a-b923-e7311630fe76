﻿// noinspection JSUnusedGlobalSymbols

import mongo from 'unstorage/drivers/mongodb'
import {createConsola} from 'consola';
import {colors} from "consola/utils";
import {MongoClient} from "mongodb";

const logger = createConsola().withDefaults({tag: 'authStorage'})

export default defineNitroPlugin(async () => {
    const mongooseUri = useRuntimeConfig().mongodb?.authUri;
    if (mongooseUri) {
        try {
            const storage = useStorage();
            await storage.unmount('oidc');
            // Not thrilled with this just to parse out the db name,
            // but parseOptions isn't exposed by the mongodb driver
            const mongoClient = new MongoClient(mongooseUri);
            const database = mongoClient.options.dbName ?? 'fin-pro-auth';
            logger.log('Connecting to', mongooseUri);
            logger.log('Database', database);
            // Mount driver
            storage.mount('oidc', mongo({
                connectionString: mongooseUri,
                databaseName: database,
                collectionName: 'oidc',
            }));

            logger.success('MongoDB storage for OIDC mounted');
        } catch (error) {
            logger.error(colors.red(`Error mounting MongoDB storage for OIDC: ${error}`));
        }
    }
});