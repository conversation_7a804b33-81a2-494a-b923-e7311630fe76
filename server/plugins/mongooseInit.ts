﻿// noinspection JSUnusedGlobalSymbols

import {createConsola} from 'consola';
import {colors} from "consola/utils";
import {NitroAppPlugin} from "nitropack/types";
import mongoose from "mongoose";

const logger = createConsola().withDefaults({tag: 'mongoose'})

export default defineNitroPlugin(async (app) => {
    const mongooseUri = useRuntimeConfig().mongodb?.dataUri;
    if (mongooseUri) {
        try {
            await mongoose.connect(mongooseUri);
            logger.success('Connected to MongoDB');
        } catch (err) {
            logger.error(colors.red(`Error connecting to MongoDB: ${err}`))
        }
    }
});