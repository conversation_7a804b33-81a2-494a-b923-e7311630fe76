import * as AzureDI from '@azure-rest/ai-document-intelligence';
import { AzureKeyCredential } from '@azure/core-auth';
import { AZURE_DI_ENDPOINT, AZURE_DI_KEY } from './constants';
import { DocIntelligenceAnalisysResult } from '~/types/doc-intelligence';
import { consola } from 'consola';

const credentials = new AzureKeyCredential(AZURE_DI_KEY);

export const DocumentIntelligenceClient = AzureDI.default(AZURE_DI_ENDPOINT, credentials);

export async function analyzeDocumentUrl<T>(
    modelId: string,
    urlSource: string,
): Promise<DocIntelligenceAnalisysResult<T> | null> {
    const initialResponse = await DocumentIntelligenceClient.path('/documentModels/{modelId}:analyze', modelId).post({
        contentType: 'application/json',
        body: {
            urlSource: urlSource,
        },
    });

    if (AzureDI.isUnexpected(initialResponse)) {
        throw initialResponse.body.error;
    }

    const result = await AzureDI.getLongRunningPoller(DocumentIntelligenceClient, initialResponse).pollUntilDone();

    const analysisBody = result.body as DocIntelligenceAnalisysResult<T>;

    if (analysisBody?.status !== 'succeeded') {
        consola.error('Document analysis failed:', result);
        return null;
    }

    return analysisBody;
}

export default {
    analyzeDocumentUrl,
};
