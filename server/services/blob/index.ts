import {
    BlobSASPermissions,
    BlobServiceClient,
    BlockBlobClient,
    RestError,
    StorageSharedKeyCredential,
} from '@azure/storage-blob';
import { Readable } from 'stream';
import { consola } from 'consola';
import {
    BLOB_ACCOUNT_NAME,
    BLOB_ACCOUNT_KEY,
    AZURE_BLOB_CONNECTION_STRING,
    AzureBlobSasPermissions,
} from './constants';

interface UploadDataOptions {
    containerName: string;
    blobName: string;
    data: Buffer | string;
    generateSas?: boolean;
}

/**
 * Uploads data to Azure Blob Storage and returns the URL with a SAS token.
 * @param  { containerName, blobName, data } - The options for uploading data.
 * @param {string} containerName - The name of the container to upload to.
 * @param {string} blobName - The name of the blob to create.
 * @param {Buffer | string} data - The data to upload.
 * @returns {Promise<string>} - The URL of the uploaded blob with a SAS token.
 */
export async function uploadDataToAzureBlob({
    containerName,
    blobName,
    data,
    generateSas = true,
}: UploadDataOptions): Promise<string> {
    if (!BLOB_ACCOUNT_NAME || !BLOB_ACCOUNT_KEY) {
        throw new Error('Azure Storage account name or key is not set.');
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(AZURE_BLOB_CONNECTION_STRING);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    try {
        await containerClient.createIfNotExists();
    } catch (error) {
        if (!(error instanceof RestError)) {
            consola.error(`Unexpected error while createIfNotExists: ${error}`);
        } else if (error.statusCode !== 409) {
            consola.warn(`Container ${containerName} already exists or could not be created: ${error.message}`);
        }
    }

    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    if (typeof data === 'string') {
        await blockBlobClient.upload(data, Buffer.byteLength(data));
    } else if (Buffer.isBuffer(data)) {
        const stream = Readable.from(data);
        const bufferSize = 4 * 1024 * 1024; // 4 MB
        await blockBlobClient.uploadStream(stream, bufferSize, 20, {
            onProgress: progress => {
                consola.info(`Uploaded ${progress.loadedBytes} bytes`);
            },
        });
    } else {
        throw new Error('Unsupported data type for blob upload.');
    }

    if (!generateSas) {
        return blockBlobClient.url;
    }

    return await generateSasUrl(blockBlobClient);
}

async function generateSasUrl(blockBlobClient: BlockBlobClient): Promise<string> {
    const expiresOn = new Date();
    expiresOn.setHours(expiresOn.getHours() + 1);

    const sasToken = await blockBlobClient.generateSasUrl({
        expiresOn,
        permissions: BlobSASPermissions.parse(AzureBlobSasPermissions.Read),
    });

    return sasToken;
}

export async function getReadBlobSasUrl(blobName: string, containerName: string): Promise<string> {
    if (!BLOB_ACCOUNT_NAME || !BLOB_ACCOUNT_KEY) {
        throw new Error('Azure Storage account name or key is not set.');
    }

    const credentials = new StorageSharedKeyCredential(BLOB_ACCOUNT_NAME, BLOB_ACCOUNT_KEY);
    consola.info(`Using Azure Storage account: ${BLOB_ACCOUNT_NAME}`);
    const blobServiceClient = new BlobServiceClient(`https://${BLOB_ACCOUNT_NAME}.blob.core.windows.net`, credentials);
    const containerClient = blobServiceClient.getContainerClient(containerName);

    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    const sasUrl = await blockBlobClient.generateSasUrl({
        permissions: BlobSASPermissions.parse('r'), // read
        startsOn: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        expiresOn: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes in future
    });

    consola.info(`Generated SAS URL for blob ${blobName}: ${sasUrl}`);

    return sasUrl;
}

export async function generateUploadUrl(blobName: string, container: string): Promise<string> {
    if (!BLOB_ACCOUNT_NAME || !BLOB_ACCOUNT_KEY) {
        throw new Error('Azure Storage account name or key is not set.');
    }

    const credentials = new StorageSharedKeyCredential(BLOB_ACCOUNT_NAME, BLOB_ACCOUNT_KEY);
    consola.info(`Using Azure Storage account: ${BLOB_ACCOUNT_NAME}`);
    const blobServiceClient = new BlobServiceClient(`https://${BLOB_ACCOUNT_NAME}.blob.core.windows.net`, credentials);

    const containerClient = blobServiceClient.getContainerClient(container);

    try {
        await containerClient.createIfNotExists();
    } catch (error) {
        if (!(error instanceof RestError)) {
            consola.error(`Unexpected error while createIfNotExists: ${error}`);
        } else if (error.statusCode !== 409) {
            consola.warn(`Container ${container} already exists or could not be created: ${error.message}`);
        }
    }

    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    const sasUrl = await blockBlobClient.generateSasUrl({
        permissions: BlobSASPermissions.parse('cw'), // create and write
        startsOn: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
        expiresOn: new Date(Date.now() + 15 * 60 * 1000), // 15 minutes in future
    });

    consola.info(`Generated SAS URL for blob ${blobName}: ${sasUrl}`);

    return sasUrl;
}

export default {
    generateUploadUrl,
    uploadDataToAzureBlob,
};
