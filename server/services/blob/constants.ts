export const BLOB_ACCOUNT_NAME = process.env.NUXT_AZURE_STORAGE_ACCOUNT_NAME!;
export const BLOB_ACCOUNT_KEY = process.env.NUXT_AZURE_STORAGE_ACCOUNT_KEY!;

export const AZURE_BLOB_CONNECTION_STRING = `DefaultEndpointsProtocol=https;AccountName=${BLOB_ACCOUNT_NAME};AccountKey=${BLOB_ACCOUNT_KEY};EndpointSuffix=core.windows.net`;

export enum AzureBlobSasPermissions {
    Read = 'r',
    Write = 'w',
    Delete = 'd',
    List = 'l',
    Add = 'a',
    Create = 'c',
    Update = 'u',
}
