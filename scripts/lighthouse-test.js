import lighthouse from 'lighthouse';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import puppeteer from 'puppeteer-core';
import { executablePath } from 'puppeteer';
import 'dotenv/config';

// Required to resolve __dirname in ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create reports directory if it doesn't exist
const reportsDir = path.join(__dirname, '../reports');
if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir);
}

// Verify required environment variables
const requiredEnvVars = [
    'AUTH0_TEST_USERNAME',
    'AUTH0_TEST_PASSWORD',
    'AUTH0_ADMIN_TEST_USERNAME',
    'AUTH0_ADMIN_TEST_PASSWORD',
];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(varName => {
        console.error(`   - ${varName}`);
    });
    console.error('\nPlease create a .env file with these variables. See .env.example for reference.');
    process.exit(1);
}

// Read environment-based flags
const isAdvisor = process.env.IS_ADVISOR === 'true';

// Login credentials from environment variables
const credentials = isAdvisor
    ? {
          username: process.env.AUTH0_ADMIN_TEST_USERNAME,
          password: process.env.AUTH0_ADMIN_TEST_PASSWORD,
      }
    : {
          username: process.env.AUTH0_TEST_USERNAME,
          password: process.env.AUTH0_TEST_PASSWORD,
      };

// Login via app -> Auth0 -> back to app, then return browser and cookies
async function loginWithAuth0() {
    const loginTriggerUrl = isAdvisor
        ? 'http://localhost:3000/advisor/dashboard'
        : 'http://localhost:3000/client/dashboard';

    const browser = await puppeteer.launch({
        headless: true,
        executablePath: executablePath(),
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--remote-debugging-port=9222'],
    });

    const page = await browser.newPage();
    page.setDefaultNavigationTimeout(30000);

    console.log('Navigating to protected app route (to trigger Auth0)...');
    await page.goto(loginTriggerUrl, { waitUntil: 'networkidle0' });

    console.log('Waiting for Auth0 login form...');
    // Updated selectors for Auth0's Universal Login
    await page.waitForSelector('#username, [name="username"]', { timeout: 20000 });

    // Try both potential selectors for username and password
    const usernameSelector = (await page.$('#username')) ? '#username' : '[name="username"]';
    const passwordSelector = (await page.$('#password')) ? '#password' : '[name="password"]';

    await page.type(usernameSelector, credentials.username);
    await page.type(passwordSelector, credentials.password);

    // Updated submit button selector to be more flexible
    const submitButton = await page.waitForSelector(
        'button[type="submit"], button[name="submit"], input[type="submit"]',
    );
    await submitButton.click();

    console.log('Waiting for redirect back to app...');
    await page.waitForNavigation({ waitUntil: 'networkidle0' });

    const finalUrl = page.url();
    console.log('Redirected to:', finalUrl);

    if (!finalUrl.startsWith('http://localhost')) {
        throw new Error('Login did not redirect to the app. Check login flow.');
    }

    const cookies = await page.cookies();
    console.log(`✅ Captured ${cookies.length} cookies for session`);

    return { browser, cookies };
}

// Run Lighthouse using the same Puppeteer session
async function runLighthouse(url, cookies, browser) {
    const page = (await browser.pages())[0];
    await page.setCookie(...cookies);

    const port = new URL(browser.wsEndpoint()).port;

    const options = {
        logLevel: 'info',
        output: 'html',
        port,
        settings: {
            onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
            formFactor: 'desktop',
            screenEmulation: {
                mobile: false,
                disable: false,
                width: 1350,
                height: 940,
                deviceScaleRatio: 1,
            },
        },
    };

    console.log('🚀 Running Lighthouse...');
    const result = await lighthouse(url, options);

    console.log('✅ Lighthouse Scores:');
    console.log('Performance:', result.lhr.categories.performance.score * 100);
    console.log('Accessibility:', result.lhr.categories.accessibility.score * 100);
    console.log('Best Practices:', result.lhr.categories['best-practices'].score * 100);
    console.log('SEO:', result.lhr.categories.seo.score * 100);

    return result;
}

// Main runner
async function main() {
    const clientRoutes = [
        'http://localhost:3000/client/intake/personal',
        'http://localhost:3000/client/intake/situation',
        'http://localhost:3000/client/intake/home',
        'http://localhost:3000/client/intake/assets',
        'http://localhost:3000/client/intake/liabilities',
        'http://localhost:3000/client/intake/insurance',
        'http://localhost:3000/client/intake/income',
        'http://localhost:3000/client/intake/expenses',
        'http://localhost:3000/client/intake/review',
        'http://localhost:3000/client/dashboard',
        'http://localhost:3000/client/intake/complete',
        'http://localhost:3000/client/settings/Profile',
        'http://localhost:3000/client/settings/Security',
        'http://localhost:3000/client/settings/Access',
    ];

    const advisorRoutes = ['http://localhost:3000/advisor/UserManagement'];

    const routes = isAdvisor ? advisorRoutes : clientRoutes;
    const rolePrefix = isAdvisor ? 'advisor' : 'client';

    try {
        console.log(`🔐 Logging into Auth0 as ${rolePrefix}...`);
        const { browser, cookies } = await loginWithAuth0();

        for (const url of routes) {
            console.log(`📊 Running Lighthouse audit on ${url}...`);
            const routeName = new URL(url).pathname.replace(/\//g, '-').slice(1);
            const reportPath = path.join(reportsDir, `lighthouse-report-${rolePrefix}-${routeName}.html`);

            const result = await runLighthouse(url, cookies, browser);

            fs.writeFileSync(reportPath, result.report);
            console.log(`✅ Report for ${url} saved to ${reportPath}`);
        }

        await browser.close();
        console.log('🎉 All audits complete! Reports saved in:', reportsDir);
    } catch (err) {
        console.error('❌ Lighthouse test failed:', err);
        process.exit(1);
    }
}

main();
