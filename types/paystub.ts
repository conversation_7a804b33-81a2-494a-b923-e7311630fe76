import type { Asset } from './assets';
import type { Deduction, Frequency, TaxDeduction } from './base';
import type { FieldValueOCR } from './doc-intelligence';
import type { Expense } from './expenses';

export type EmploymentType = 'W2' | '1099';

export interface Paystub {
    _id?: string;
    name: string;
    payDate: Date;
    frequency: Frequency;
    incomeItems: Array<PaystubLine>;
    preTaxItems: Array<PaystubLine>;
    postTaxItems: Array<PaystubLine>;
    taxesItems: Array<PaystubLine>;
    customItems: Array<PaystubLine>;
    createdAt?: Date;
    updatedAt?: Date;
    updatedBy?: string;
}

export interface PaystubLine {
    _id?: string;
    description: string;
    frequencyPerMonth?: number;
    rate: number | null;
    hours: number | null;
    current: number | null;
    ytd: number | null;
    customLineOptions?: {
        title: string;
    };
}

export enum PaystubFrequency {
    Daily = 1,
    Weekly = 7,
    Biweekly = 14,
    SemiMonthly = 15,
    Monthly = 30,
    Bimonthly = 60,
    Trimesterly = 91,
    Quarterly = 91,
    SemiAnnually = 182,
    Annually = 365,
    Yearly = 365,
    Other = 0,
}

export interface ExtractedDeductions {
    preTaxDeductions: Deduction[];
    taxDeductions: TaxDeduction[];
    postTaxDeductions: Deduction[];
}

export interface ProcessedPaystubData {
    _id?: string;
    name: string;
    report: PaystubReport;
    assets: ExtractedAssets;
    deductions: ExtractedDeductions;
    expenses: Expense[];
}

export interface PaystubReport {
    payDate: Date;
    grossIncome: number;
    preTaxDeductions: number;
    adjustedGrossIncome: number;
    taxesWithheld: number;
    netPay: number;
    postTaxDeductions: number;
    cashFlowNetIncome: number;
    frequency: Frequency;
}

export interface ExtractedAssets {
    preTaxAssets: Asset[];
    postTaxAssets: Asset[];
}

export interface PatchPaystubRequest {
    incomeEmployeeId: string;
    paystubId: string;
    paystub: Partial<Paystub>;
}

export interface PaystubOcrAnalysisFields {
    EmployeeName: FieldValueOCR;
    EmployerName: FieldValueOCR;
    PaystubDate: FieldValueOCR;
    PayBeginDate: FieldValueOCR;
    PayEndDate: FieldValueOCR;
    PayRate: FieldValueOCR;
    GrossPayCurrent: FieldValueOCR;
    GrossPayYTD: FieldValueOCR;
    TotalDeductionsCurrent: FieldValueOCR;
    TotalDeductionsYTD: FieldValueOCR;
    NetPayCurrent: FieldValueOCR;
    NetPayYTD: FieldValueOCR;
    Taxes: PostTaxDeductionsOCR;
    Income: IncomeOCR;
    PreTaxDeductions: PreTaxDeductionsOCR;
    TotalPreTaxCurrent: FieldValueOCR;
    TotalPreTaxYTD: FieldValueOCR;
    PostTaxDeductions: PostTaxDeductionsOCR;
    TotalPostTaxCurrent: FieldValueOCR;
    TotalPostTaxYTD: FieldValueOCR;
    EmployeeJobTitle: FieldValueOCR;
    Frequency: FieldValueOCR;
    TotalTaxesCurrent: FieldValueOCR;
    TotalTaxesYTD: FieldValueOCR;
}

export interface IncomeOCR {
    type: 'array';
    valueArray: Array<{
        type: string;
        valueObject: {
            Description: FieldValueOCR;
            Hours: FieldValueOCR;
            Current: FieldValueOCR;
            YTD: FieldValueOCR;
            Rate: FieldValueOCR;
        };
        confidence: number;
    }>;
    confidence: number;
}

export interface PostTaxDeductionsOCR {
    type: 'array';
    valueArray: Array<{
        type: string;
        valueObject: {
            Description: FieldValueOCR;
            Current: FieldValueOCR;
            YTD: FieldValueOCR;
        };
        confidence: number;
    }>;
    confidence: number;
}

export interface PreTaxDeductionsOCR {
    type: 'array';
    valueArray: Array<{
        type: string;
        valueObject: {
            Description: FieldValueOCR;
            Current: FieldValueOCR;
            YTD: FieldValueOCR;
        };
        confidence: number;
    }>;
    confidence: number;
}

export interface PaystubAnalysisResult {
    paystub: Paystub | null;
    ocr: {
        overallConfidence: number;
        fields: PaystubOcrAnalysisFields | null;
    };
}

// Paystub Type guards
export function isPaystub(obj: unknown): obj is Paystub {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const paystub = obj as Paystub;

    // Check required properties
    if (
        typeof paystub.name !== 'string' ||
        typeof paystub.payDate !== 'string' ||
        typeof paystub.frequency !== 'string' ||
        !Array.isArray(paystub.incomeItems) ||
        !Array.isArray(paystub.preTaxItems) ||
        !Array.isArray(paystub.postTaxItems) ||
        !Array.isArray(paystub.taxesItems) ||
        !Array.isArray(paystub.customItems)
    ) {
        console.log(
            'isPaystub required',
            typeof paystub.name !== 'string',
            typeof paystub.payDate !== 'string',
            typeof paystub.frequency !== 'string',
            !Array.isArray(paystub.incomeItems),
            !Array.isArray(paystub.preTaxItems),
            !Array.isArray(paystub.postTaxItems),
            !Array.isArray(paystub.taxesItems),
            !Array.isArray(paystub.customItems),
        );
        return false;
    }

    // Check optional properties
    if (
        (paystub._id !== undefined && typeof paystub._id !== 'string') ||
        (paystub.createdAt !== undefined && !(paystub.createdAt instanceof Date)) ||
        (paystub.updatedAt !== undefined && !(paystub.updatedAt instanceof Date)) ||
        (paystub.updatedBy !== undefined && typeof paystub.updatedBy !== 'string')
    ) {
        console.log(
            'isPaystub optional',
            paystub._id !== undefined && typeof paystub._id !== 'string',
            paystub.createdAt !== undefined && !(paystub.createdAt instanceof Date),
            paystub.updatedAt !== undefined && !(paystub.updatedAt instanceof Date),
            paystub.updatedBy !== undefined && typeof paystub.updatedBy !== 'string',
        );
        return false;
    }

    // Check that all item arrays contain valid PaystubLine objects
    const allItemArrays = [
        paystub.incomeItems,
        paystub.preTaxItems,
        paystub.postTaxItems,
        paystub.taxesItems,
        paystub.customItems,
    ];

    return allItemArrays.every(itemArray => itemArray.every(item => isPaystubLine(item)));
}

// Helper function to type guard PaystubLine objects
export function isPaystubLine(obj: unknown): obj is PaystubLine {
    if (!obj || typeof obj !== 'object') {
        return false;
    }

    const line = obj as PaystubLine;

    // Check required and nullable properties
    if (
        typeof line.description !== 'string' ||
        (line.frequencyPerMonth !== undefined &&
            line.frequencyPerMonth !== null &&
            typeof line.frequencyPerMonth !== 'number') ||
        (line.rate !== null && typeof line.rate !== 'number') ||
        (line.hours !== null && typeof line.hours !== 'number') ||
        (line.current !== null && typeof line.current !== 'number') ||
        (line.ytd !== null && typeof line.ytd !== 'number')
    ) {
        console.log(
            'isPaystubLine required and nullable',
            typeof line.description !== 'string',
            line.frequencyPerMonth !== undefined &&
                line.frequencyPerMonth !== null &&
                typeof line.frequencyPerMonth !== 'number',
            line.rate !== null && typeof line.rate !== 'number',
            line.hours !== null && typeof line.hours !== 'number',
            line.current !== null && typeof line.current !== 'number',
            line.ytd !== null && typeof line.ytd !== 'number',
            obj,
        );
        return false;
    }

    // Check optional properties
    if (
        (line._id !== undefined && typeof line._id !== 'string') ||
        (line.customLineOptions !== undefined &&
            (typeof line.customLineOptions !== 'object' ||
                (line.customLineOptions.title !== undefined && typeof line.customLineOptions.title !== 'string')))
    ) {
        console.log(
            'isPaystubLine optional',
            line._id !== undefined && typeof line._id !== 'string',
            line.customLineOptions !== undefined &&
                (typeof line.customLineOptions !== 'object' ||
                    (line.customLineOptions.title !== undefined && typeof line.customLineOptions.title !== 'string')),
            obj,
        );
        return false;
    }

    return true;
}
