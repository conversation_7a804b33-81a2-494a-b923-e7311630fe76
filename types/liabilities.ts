﻿// noinspection JSUnusedGlobalSymbols

import type {BaseLineItem, PersonalBusinessType, VehicleType} from "./base";
import type {Expense} from "./expenses";
import type {Asset} from "./assets";
import {Frequency} from "./base";

export type Liability = LiabilityMortgage |
    LiabilityAutoLoan |
    LiabilityStudentLoan |
    LiabilityCreditCard |
    LiabilityBusinessLoan |
    LiabilityEquipmentLoan |
    LiabilityUnsecureLoan |
    LiabilitySecureLoan |
    LiabilityMarginLoan |
    LiabilityPledgeLoan |
    LiabilityRetirement401k |
    LiabilityLifeInsurance |
    LiabilityOther;    

export type BaseLiability = BaseLineItem & {
    category: "Liability";
    debtor?: string;
    balance: number;
    interestRate: number;
    minimumPayment: number;
    originationDate?: Date;
    termLength?: number;
    paymentDate?: Date;
    paymentFrequency: Frequency;
    originalBalance?: number;
    loanType: PersonalBusinessType;
    associatedExpenses?: Expense[];
    associatedAssets?: Asset[];
}

export type LiabilityMortgage = BaseLiability & {
    kind: 'LiabilityMortgage';
    mortgageType: 'Mortgage' | 'Home Equity' | '2nd Mortgage' | 'Other';
}

export type LiabilityAutoLoan = BaseLiability & {
    kind: 'LiabilityAutoLoan';
    vehicleType: VehicleType;
}
export type LiabilityStudentLoan = BaseLiability & {
    kind: 'LiabilityStudentLoan';
    studentLoanType: 'Public' | 'Private';
    loanType: PersonalBusinessType.Personal;
    associatedAssets: undefined | null;
}
export type LiabilityCreditCard = BaseLiability & {
    kind: 'LiabilityCreditCard';
}
export type LiabilityBusinessLoan = BaseLiability & {
    kind: 'LiabilityBusinessLoan';
    loanType: PersonalBusinessType.Business;
}
export type LiabilityEquipmentLoan = BaseLiability & {
    kind: 'LiabilityEquipmentLoan';
}
export type LiabilityUnsecureLoan = BaseLiability & {
    kind: 'LiabilityUnsecureLoan';
    associatedAssets: undefined | null;
}
export type LiabilitySecureLoan = BaseLiability & {
    kind: 'LiabilitySecureLoan';
}
export type LiabilityMarginLoan = BaseLiability & {
    kind: 'LiabilityMarginLoan';
}
export type LiabilityPledgeLoan = BaseLiability & {
    kind: 'LiabilityPledgeLoan';
}
export type LiabilityRetirement401k = BaseLiability & {
    kind: 'LiabilityRetirement401k';
    originalBalance: undefined | null;
}
export type LiabilityLifeInsurance = BaseLiability & {
    kind: 'LiabilityLifeInsurance';
    originalBalance: undefined | null;
}
export type LiabilityOther = BaseLiability & {
    kind: 'LiabilityOther';
}