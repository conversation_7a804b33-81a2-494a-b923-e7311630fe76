export interface DocIntelligenceAnalisysResult<T> {
    status: string;
    createdDateTime: Date;
    lastUpdatedDateTime: Date;
    analyzeResult: AnalyzeResult<T>;
}

export interface AnalyzeResult<T> {
    apiVersion: Date;
    modelId: string;
    stringIndexType: string;
    content: string;
    pages: Page[];
    tables: Table[];
    paragraphs: Paragraph[];
    styles: any[];
    documents: AnalysisDocument<T>[];
    contentFormat: string;
    sections: Section[];
}

export interface AnalysisDocument<T> {
    docType: string;
    boundingRegions: BoundingRegion[];
    fields: T;
    confidence: number;
    spans: Span[];
}

interface BoundingRegion {
    pageNumber: number;
    polygon: number[];
}

export interface FieldValueOCR {
    type: Type;
    valueString?: string;
    content?: string;
    boundingRegions?: BoundingRegion[];
    confidence: number;
    spans?: Span[];
    valueNumber?: number;
    valueDate?: Date;
}

interface Span {
    offset: number;
    length: number;
}

enum Type {
    Date = 'date',
    Number = 'number',
    String = 'string',
}

interface Page {
    pageNumber: number;
    angle: number;
    width: number;
    height: number;
    unit: string;
    words: Word[];
    lines: Line[];
    spans: Span[];
}

interface Line {
    content: string;
    polygon: number[];
    spans: Span[];
}

interface Word {
    content: string;
    polygon: number[];
    confidence: number;
    span: Span;
}

interface Paragraph {
    spans: Span[];
    boundingRegions: BoundingRegion[];
    content: string;
    role?: string;
}

interface Section {
    spans: Span[];
    elements: string[];
}

interface Table {
    rowCount: number;
    columnCount: number;
    cells: Cell[];
    boundingRegions: BoundingRegion[];
    spans: Span[];
}

interface Cell {
    rowIndex: number;
    columnIndex: number;
    content: string;
    boundingRegions: BoundingRegion[];
    spans: Span[];
    elements: string[];
    kind?: string;
}
