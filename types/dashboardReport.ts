﻿export type DashboardReportTimeSeries = {
    date: Date;
    value: number;
};
export type DashboardReportCategory = {
    category: string;
    subcategory: string;
    kind: string;
    value: number;
};
export type DashboardReportData = {
    disclaimer: string;
    netWorth: {
        value: number;
        timeSeries: DashboardReportTimeSeries[];
        categories: DashboardReportCategory[];
    };
    assets: {
        value: number;
        timeSeries: DashboardReportTimeSeries[];
        categories: DashboardReportCategory[];
    };
    liabilities: {
        value: number;
        timeSeries: DashboardReportTimeSeries[];
        categories: DashboardReportCategory[];
    };
    income: {
        value: { annually: number; monthly: number };
        timeSeries: DashboardReportTimeSeries[];
        categories: DashboardReportCategory[];
    };
    expenses: {
        value: { annually: number; monthly: number };
        timeSeries: DashboardReportTimeSeries[];
        categories: DashboardReportCategory[];
    };
};
