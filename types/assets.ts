﻿// noinspection JSUnusedGlobalSymbols

import type {BaseLineItem, PersonalBusinessType, VehicleType} from './base';
import type {Liability} from './liabilities';
import type {Expense} from "./expenses";
import {BankAccountType} from "./base";


export type Asset = AssetHome |
    AssetVehicle |
    AssetPersonalProperty |
    AssetInvestmentAccount |
    AssetRealEstate |
    AssetEquipment |
    AssetBankAccount |
    AssetCDMoneyMarket |
    AssetBrokerageAccount |
    AssetManagedAccount |
    AssetIRA |
    Asset401k |
    AssetStock |
    AssetPension |
    Asset529CESA |
    AssetUTMA |
    AssetDeferredCompensation |
    AssetLifeInsurance |
    AssetBusiness |
    AssetOther;

export type BaseAsset = BaseLineItem & {
    category: 'Asset';
    owners?: string;
    equityValue: number;
}

export type AssetHome = BaseAsset & {
    kind: 'AssetHome';
    address: string;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetVehicle = BaseAsset & {
    kind: 'AssetVehicle'
    vehicleType: VehicleType;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetPersonalProperty = BaseAsset & {
    kind: 'AssetPersonalProperty'
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetInvestmentAccount = BaseAsset & {
    kind: 'AssetInvestmentAccount'
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetRealEstate = BaseAsset & {
    kind: 'AssetRealEstate';
    // Removed per Chris Garcia's feedback
    // realEstateType: PersonalBusinessType;
    address: string;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetEquipment = BaseAsset & {
    kind: 'AssetEquipment';
    // Removed per Chris Garcia's feedback
    // equipmentType: PersonalBusinessType;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetBankAccount = BaseAsset & {
    kind: 'AssetBankAccount';
    // Removed per Chris Garcia's feedback
    // personalBusinessType: PersonalBusinessType;
    // accountType: BankAccountType;
    purpose?: string;
    interestRate?: number;
    maturityDate?: Date;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetCDMoneyMarket = BaseAsset & {
    kind: 'AssetCDMoneyMarket';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
    taxes: number;
}

export type AssetBrokerageAccount = BaseAsset & {
    kind: 'AssetBrokerageAccount';
    accountType: 'Cash Account' | 'Qualified Account' | 'Non-Qualified Account';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetManagedAccount = BaseAsset & {
    kind: 'AssetManagedAccount';
    accountType: 'Qualified Account' | 'Non-Qualified Account';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetIRA = BaseAsset & {
    kind: 'AssetIRA';
    accountType: 'Traditional IRA' | 'Roth IRA' | 'SEP IRA' | 'SIMPLE IRA';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type Asset401k = BaseAsset & {
    kind: 'Asset401k';
    accountType: 'Traditional' | 'Roth' | 'Solo';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetPension = BaseAsset & {
    kind: 'AssetPension';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type Asset529CESA = BaseAsset & {
    kind: 'Asset529CESA';
    accountType: '529 Plan' | 'CESA';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

export type AssetUTMA = BaseAsset & {
    kind: 'AssetUTMA';
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

// Currently not implemented in the intake form
export type AssetStock = BaseAsset & {
    kind: 'AssetStock';
    acquiredDate?: Date;
    stockType: 'ESPP' | 'RSU' | 'Stock Options';
}

// Currently not implemented in the intake form
export type AssetDeferredCompensation = BaseAsset & {
    kind: 'AssetDeferredCompensation';
    acquiredDate?: Date;
}

// Currently not implemented in the intake form
export type AssetLifeInsurance = BaseAsset & {
    kind: 'AssetLifeInsurance';
    accountType: 'Term' | 'Life' | 'Variable Universal Life';
    surrenderValue?: number;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

// Currently not implemented in the intake form
export type AssetBusiness = BaseAsset & {
    kind: 'AssetBusiness';
    purpose?: string;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}

// Currently not implemented in the intake form
export type AssetOther = BaseAsset & {
    kind: 'AssetOther';
    purpose?: string;
    acquiredDate?: Date;
    associatedExpenses: Expense[];
    associatedLiabilities: Liability[];
}
