﻿// noinspection JSUnusedGlobalSymbols
import type { BaseLineItem, Deduction, Frequency } from './base';
import type { Asset } from './assets';
import type { Liability } from './liabilities';
import type { Expense } from './expenses';
import type { Paystub } from './paystub';
import type { Types } from 'mongoose';

export type Income =
    | IncomeEmployee
    | IncomeBusiness
    | IncomeRental
    | IncomeAlimony
    | IncomeDividendInterest
    | IncomeRoyalty
    | IncomeRetirement
    | IncomeChildSupport
    | IncomeOther;

export type IncomeLineItemFormKind = '1099' | 'W2' | 'Other';

export type IncomeLineItemForm = {
    id: string;
    kind: IncomeLineItemFormKind;
    [key: string]: any;
};

export type BaseIncome = BaseLineItem & {
    category: 'Income';
    amount: number;
    frequency: Frequency;
    value?: number; // added to account for problems with mapping
    paymentFrequency?: Frequency; // added to account for problems with mapping
    notes?: string;
    forms?: IncomeLineItemForm[];
};
export type IncomeEmployee = BaseIncome & {
    kind: 'IncomeEmployee';
    // Trimmed down per <PERSON>'s feedback
    employmentType: 'W2' | '1099';
    paystubs?: Paystub[] | Types.ObjectId[]; // Paystubs can be an array of Paystub objects or ObjectIds
    deductions?: Deduction[];
    associatedAssets?: Asset[];
};

export type IncomeBusiness = BaseIncome & {
    kind: 'IncomeBusiness';
    deductions?: Deduction[];
    associatedAssets?: Asset[];
};
export type IncomeRental = BaseIncome & {
    kind: 'IncomeRental';
    taxes: number;
    associatedExpenses?: Expense[];
    associatedLiabilities?: Liability[];
};
export type IncomeAlimony = BaseIncome & {
    kind: 'IncomeAlimony';
    taxes: number;
    associatedExpenses?: Expense[];
};
export type IncomeDividendInterest = BaseIncome & {
    kind: 'IncomeDividendInterest';
    amountDividend: number;
    amountInterest: number;
    taxes: number;
};
export type IncomeRoyalty = BaseIncome & {
    kind: 'IncomeRoyalty';
    taxes: number;
};
export type IncomeRetirement = BaseIncome & {
    kind: 'IncomeRetirement';
    retirementType: 'RMD' | 'Pension' | 'Social Security' | 'Retirement';
    taxes: number;
    associatedAssets?: Asset[];
};
export type IncomeChildSupport = BaseIncome & {
    kind: 'IncomeChildSupport';
    expenses: number;
};
export type IncomeOther = BaseIncome & {
    kind: 'IncomeOther';
    taxes: number;
};
