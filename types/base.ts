﻿// noinspection JSUnusedGlobalSymbols


import type {Asset} from "~/types/assets";
import type {Liability} from "~/types/liabilities";
import type {Income} from "~/types/income";
import type {Expense} from "~/types/expenses";
import type {Insurance} from "~/types/insurance";

export type LineItem = Asset | Liability | Income | Expense | Insurance;
export type LineItemKey = KeysOfUnion<LineItem>;
export type UnionLineItem = AllUnionType<LineItem>;
type KeysOfUnion<T> = T extends T ? keyof T : never;
type AllUnionType<T> = {
    [K in KeysOfUnion<T>]?: T[K];
}

export function ofKind<K extends LineItem['kind']>(kind: K) {
    return (item: LineItem): item is Extract<LineItem, { kind: K }> => item.kind === kind;
}

export type BaseLineItem = {
    id: string;
    description: string;
    category: string;
    mode: 'Standard' | 'Simple'
}

export interface Deduction {
    amount: number;
}

export interface TaxDeduction extends Deduction {
    kind: 'FederalTax' | 'StateTax' | 'Medicare' | 'SocialSecurity' | 'Other'
    tax: true;
}

export interface NonTaxDeduction extends Deduction {
    kind: 'HealthInsurance' | 'RetirementContribution' | 'Other';
    tax: false;
}

export enum Frequency {
    Daily = 'Daily',
    Weekly = 'Weekly',
    Biweekly = 'Biweekly',
    SemiMonthly = 'Semimonthly',
    Monthly = 'Monthly',
    Bimonthly = 'Bimonthly',
    Trimesterly = 'Trimesterly',
    Quarterly = 'Quarterly',
    SemiAnnually = 'Semiannually',
    Annually = 'Annually',
    Yearly = 'Yearly',
    Other = 'Other'
}
export const FrequencyOptions = Object.keys(Frequency).map(k => ({ label: k, value: k}));

export enum VehicleType {
    Auto = 'Auto',
    Car = 'Car',
    Boat = 'Boat',
    Motorcycle = 'Motorcycle',
    RV = "RV",
    Other = 'Other'
}

export enum PersonalBusinessType {
    Personal = 'Personal',
    Business = 'Business',
    Rental = 'Rental',
    Other = 'Other'
}

export enum BankAccountType {
    Checking = 'Checking', 
    Savings = 'Savings', 
    MoneyMarkets = 'Money Markets', 
    CDs = "CD's",
    OneAccount = 'One Account',
    Other = 'Other'
}
