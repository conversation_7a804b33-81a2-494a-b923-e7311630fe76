﻿// noinspection JSUnusedGlobalSymbols

import type {Frequency, BaseLineItem} from "./base";
import type {Asset} from "./assets";
import type {Liability} from "./liabilities";

export type Expense = ExpenseDiscretionary |
    ExpenseCharitable |
    ExpenseFoodClothingTransportation |
    ExpenseRentalRealEstate |
    ExpenseOtherCommitted |
    ExpenseEmployeeBenefit |
    ExpenseSavingsBrokerage |
    ExpenseHousing |
    ExpenseTaxes |
    ExpenseInsurance |
    ExpenseChildcareTuition |
    ExpenseSubscription |
    ExpenseLoanPayment |
    ExpenseHealthcare |
    ExpenseAuto |
    ExpenseDining |
    ExpenseDonation |
    ExpenseBusiness |
    ExpenseClothingCosmetic |
    ExpenseLeisureRecreation |
    ExpenseOther;

export type BaseExpense = BaseLineItem & {
    category: "Expense";
    amount: number;
    frequency: Frequency;
    value?: number; // added to account for problems with mapping
    paymentFrequency?: Frequency; // added to account for problems with mapping
    notes?: string;

    entangledWith?: string; // used to track entangled items (e.g. the loan this payment expense is for) 
}
export type ExpenseDiscretionary = BaseExpense & {
    kind: 'ExpenseDiscretionary';
}
export type ExpenseCharitable = BaseExpense & {
    kind: 'ExpenseCharitable';
}
export type ExpenseFoodClothingTransportation = BaseExpense & {
    kind: 'ExpenseFoodClothingTransportation';
}
export type ExpenseRentalRealEstate = BaseExpense & {
    kind: 'ExpenseRentalRealEstate';
    expenseType: 'Taxes' | 'Insurance' | 'Other';
}
export type ExpenseOtherCommitted = BaseExpense & {
    kind: 'ExpenseOtherCommitted';
}
export type ExpenseEmployeeBenefit = BaseExpense & {
    kind: 'ExpenseEmployeeBenefit';
}
export type ExpenseSavingsBrokerage = BaseExpense & {
    kind: 'ExpenseSavingsBrokerage';
    expenseType: "401(k) / 403(b)" | "Roth 401(k) / 403(b)" | "Ret Plan Savings" | "IRA Savings (Traditional)" | "After Tax 401(k) / 403(b)" | "Roth IRA Savings (After Tax)" | "Other Retirement Savings" | "Education - CESA" | "UTMA" | "Cash Savings" | "Non-Qualified Savings" | "Bank Charges";
    associatedAssets: Asset[];
}
export type ExpenseGroceries = BaseExpense & {
    kind: 'ExpenseGroceries'
}
export type ExpenseHousing = BaseExpense & {
    kind: 'ExpenseHousing';
    expenseType: 'Rent' | 'Home Insurance Premiums' | 'Real Estate Taxes' | 'Renters Insurance Premiums' | 'Maintenance' | 'Mortgage' | 'Electric' | 'Gas' | 'Water' | 'Maid' | 'Lawn' | 'HOA Dues' | 'Security System' | 'Pool Maintenance' | 'Home Improvements' | 'Other';
}
export type ExpenseTaxes = BaseExpense & {
    kind: 'ExpenseTaxes';
    expenseType: 'Vehicle Tax' |
        'Real Estate Taxes' |
        'Rental Real Estate Taxes' |
        'Business Taxes' |
        'Federal Tax Withholding/Est. Pmts' |
        'Commission Tax Withholding' |
        'Bonus Withholding' |
        'State/Local Income Tax Withholding' |
        'State/Local Income Commission Tax' |
        'State/Local Income Bonus Tax' |
        'Social Security' |
        'Social Security - Commission' |
        'Social Security - Bonus' |
        'Medicare' |
        'Medicare - Commission' |
        'Medicare - Bonus' |
        'Estimated Tax Payments' |
        'SS/Pension Tax Withholding' |
        'Other';
}
export type ExpenseInsurance = BaseExpense & {
    kind: 'ExpenseInsurance';
    expenseType: 'Home' | 'Renters' | 'Auto' | 'Healthcare' | 'Life' | 'Other';
}
export type ExpenseChildcareTuition = BaseExpense & {
    kind: 'ExpenseChildcareTuition';
    expenseType: "Alimony" | "Child Support" | "Dependent Care" | "Education" | "Other";
}
export type ExpenseSubscription = BaseExpense & {
    kind: 'ExpenseSubscription';
}
export type ExpenseLoanPayment = BaseExpense & {
    kind: 'ExpenseLoanPayment';
    associatedLiabilities: Liability[];
}
export type ExpenseHealthcare = BaseExpense & {
    kind: 'ExpenseHealthcare';
    expenseType: "Doctor Visits" | "Vitamins" | "Prescription Drugs";
}
export type ExpenseAuto = BaseExpense & {
    kind: 'ExpenseAuto';
    expenseType: 'Gas' | 'Maintenance' | 'Registration' | 'Oil Changes' | 'Tolltag' | 'Taxes' | 'Inspections' | 'Other';
}
export type ExpenseDining = BaseExpense & {
    kind: 'ExpenseDining';
}
export type ExpenseDonation = BaseExpense & {
    kind: 'ExpenseDonation';
}
export type ExpenseBusiness = BaseExpense & {
    kind: 'ExpenseBusiness';
    expenseType: "Business Overhead" | "Business Taxes" | "Employee Benefit Expenses" | "Other";
}
export type ExpenseClothingCosmetic = BaseExpense & {
    kind: 'ExpenseClothingCosmetic';
    expenseType: 'Clothing' | 'Dry Cleaning' | 'Jewelry' | 'Hair Care' | 'Nails' | 'Make-Up' | 'Other';
}
export type ExpenseLeisureRecreation = BaseExpense & {
    kind: 'ExpenseLeisureRecreation';
    expenseType: "Streaming Subscriptions" | "Newspaper" | "Gym" | "Entertainment" | "Memberships" | "Travel" | "Other";
}
export type ExpenseOther = BaseExpense & {
    kind: 'ExpenseOther';
}
