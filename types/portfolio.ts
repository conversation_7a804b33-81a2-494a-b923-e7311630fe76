﻿// noinspection JSUnusedGlobalSymbols
//import {Frequency, PersonalBusinessType, VehicleType} from "~/types/base";
//import {Schema} from "mongoose";

import type {Income} from "./income";
import type {Liability} from "./liabilities";
import type {Asset} from "./assets";
import type {Expense} from "./expenses";
import type {Insurance} from "~/types/insurance";

export interface Portfolio {
    owners: PortfolioUser[];
    assets?: Asset[];
    expenses?: Expense[];
    income?: Income[];
    insurance?: Insurance[];
    liabilities?: Liability[];
}

export interface PortfolioSnapshot extends Portfolio {
    snapshotDate: Date;
    updatedBy: string;
}

export interface PortfolioUser {
    userType: 'Owner' | 'Advisor' | 'User' | 'Invited' | 'Declined' | 'Revoked';
    owner: string;
    createdAt: Date;
    updatedAt?: Date;
    updatedBy?: string;
}
