﻿import type { Portfolio } from '~/types/portfolio';

export interface IntakeData {
    name: {
        firstName?: string;
        middleName?: string;
        lastName?: string;
    };
    email?: string;
    phone?: string;
    phoneCountryCode?: string;
    birthdate?: string;
    maritalStatus?: string;
    spouse: {
        firstName?: string;
        middleName?: string;
        lastName?: string;
        birthdate?: string;
        phone?: string;
        phoneCountryCode?: string;
        email?: string;
    };
    dependents: {
        name?: string;
        relationship?: string;
        dob?: string;
    }[];

    situations: string[];

    portfolio?: Portfolio;
    intakeCompleted?: boolean;

    home: {
        type?: 'own' | 'rent' | string;
        own?: {
            estimatedValue?: number;
            owed?: number;
            monthlyPayment?: number;
        };
        rent?: {
            monthlyPayment?: number;
        };
    };
}
