﻿// noinspection JSUnusedGlobalSymbols

import type {BaseLineItem, Frequency} from './base';
import type {Asset} from './assets';

export type Insurance = InsuranceLife | InsuranceDisability | InsuranceLTC;

export type BaseInsurance = BaseLineItem & {
    category: 'Insurance';
    kind: string;
}
export type InsuranceLife = BaseInsurance & {
    kind: 'InsuranceLife';
    lifeInsuranceType: string;
    companyName: string;
    deathBenefit: number;
    insured: string;
    owner: string;
    beneficiary: string;
    premium?: number;
    premiumFrequency?: Frequency;
    associatedAssets: Asset[];
}
export type InsuranceDisability = BaseInsurance & {
    kind: 'InsuranceDisability';
    disabilityInsuranceType: string;
    company: string;
    insured: string;
    beneficiary: string;
    waitingPeriodInDays: number;
    benefit: number;
    benefitFrequency: Frequency;
    anniversary?: Date;
    policyNumber?: string;
    premium?: number;
    premiumFrequency?: Frequency;
}
export type InsuranceLTC = BaseInsurance & {
    kind: 'InsuranceLTC';
    company: string;
    insured: string;
    beneficiary: string;
    waitingPeriodInDays: number;
    benefit: number;
    benefitFrequency: Frequency;
    anniversary?: Date;
    policyNumber?: string;
    premium?: number;
    premiumFrequency?: Frequency;
}
