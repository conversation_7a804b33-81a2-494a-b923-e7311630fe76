﻿// noinspection JSUnusedGlobalSymbols

import {isInRole} from "~/utils";

export default defineNuxtRouteMiddleware(async (to) => {
    const { login, loggedIn, user } = useOidcAuth();
    if (!loggedIn.value) {
        const postLogin = useCookie('post_login');
        postLogin.value = to.fullPath;
        
        await login();
        throw createError({ statusCode: 401, statusMessage: 'Unauthorized' });    
    }
    if (!isInRole(user, ['Client', 'Advisor', 'Administrator'])) {
        throw createError({statusCode: 403, statusMessage: 'Access Denied'})
    }
});
