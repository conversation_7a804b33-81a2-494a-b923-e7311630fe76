name: Azure Static Web Apps CI/CD
env:
  PR_NUMBER: ${{ github.event.number }}
  
on:
  push:
    branches:
      - main
      - dev
      - uat

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name == 'main' && 'production' || github.ref_name == 'dev' && 'dev' || 'uat' }}
    name: Build and Deploy Job
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          production_branch: "main"
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_ASHY_RIVER_072A6BD1E }}
          repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
          action: "upload"
          ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
          # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
          app_build_command: ${{ env.BUILD_COMMAND }}
          app_location: "/" # App source code path
          api_location: ".output/server" # Api source code path - optional
          output_location: ".output/public" # Built app content directory - optional
          ###### End of Repository/Build Configurations ######
        
  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          app_location: "/"
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_NICE_GLACIER_005716B10 }}
          action: "close"
