# Paystub OCR Endpoint Documentation

## Endpoint Details

- **URL**: `/api/paystub/ocr`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Authentication**: Required (uses user session)

## Request Format

The endpoint accepts a multipart form data request with the paystub image file.

## File Requirements

- Supported formats: PDF JPEG/JPG, PNG, BMP, TIFF, HEIF
- Maximum file size for analyzing documents: is 500 MB for paid (S0) tier and 4 MB for free (F0) tier.
- Image dimensions must be between 50 pixels x 50 pixels and 10,000 pixels x 10,000 pixel
- If your PDFs are password-locked, you must remove the lock before submission
- Image quality: Good readability for best OCR results

### Required Fields

- `file`: The paystub image file (PDF, JPEG, PNG)

## Example Usage

### Using Nuxt's useFetch

```javascript
async function uploadPaystub(paystubFile) {
    const formData = new FormData();
    formData.append('file', paystubFile);

    const { data, error } = await useFetch('/api/paystub/ocr', {
        method: 'POST',
        body: formData,
    });

    if (error.value) {
        console.error('Error uploading paystub:', error.value);
        throw error.value;
    }

    return data.value;
}
```

## Response Format

The endpoint returns a `PaystubAnalysisResult` object containing the extracted data:

```typescript
interface PaystubAnalysisResult {
    paystub: Paystub | null;
    ocr: {
        overallConfidence: number;
        fields: PaystubOcrAnalysisFields | null;
    };
}
```

### `PaystubAnalysisResult` properties

| Property                  | Description                                            |
| ------------------------- | ------------------------------------------------------ |
| `paystub`                 | Paystub parsed information                             |
| `ocr`.`overallConfidence` | Document analysis overall confidence provided by Azure |
| `ocr`.`fields`            | Raw fields information returned from Azure DI analysis |
