{"name": "nuxt-app", "private": true, "type": "module", "engines": {"node": ">= 20.0.0"}, "scripts": {"build": "nuxt build", "build:test": "nuxt build --envName test", "dev": "nuxt dev", "start": "npx @azure/static-web-apps-cli start .output/public --api-location .output/server", "startNode": "node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "storybook": "storybook dev -p 6006 --no-open", "test": "jest ./tests/automated-tests/automated.test.js", "test:unit": "vitest run", "test:ui": "vitest --ui", "test-storybook": "test-storybook", "build-storybook": "storybook build", "new": "plop component", "open": "plop open", "mongo": "docker compose up", "mongo-dev": "concurrently -c \"auto\" -n mongo,dev", "lighthouse": "node scripts/lighthouse-test.js", "prettier": "prettier --write", "prepare": "husky install"}, "dependencies": {"@azure-rest/ai-document-intelligence": "^1.0.0", "@azure/communication-email": "^1.0.1-beta.1", "@azure/core-auth": "^1.9.0", "@azure/storage-blob": "^12.27.0", "@floating-ui/vue": "^1.0.6", "@headlessui-float/vue": "^0.13.3", "@headlessui/vue": "^1.7.19", "@nuxt/content": "^2.13.4", "@nuxt/image": "^1.7.0", "@pinia/nuxt": "^0.5.1", "@popperjs/core": "^2.11.8", "@resvg/resvg-js": "^2.6.2", "@storybook/addon-a11y": "^8.0.4", "@storybook/jest": "^0.2.3", "@storybook/test-runner": "^0.17.0", "@storybook/testing-library": "^0.2.2", "@vitejs/plugin-vue": "^5.2.1", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^10.9.0", "auth0": "^4.21.0", "busboy": "^1.6.0", "chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-chart-treemap": "^2.3.1", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^3.6.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "filepond": "^4.32.8", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "formidable": "^3.5.4", "html-to-text": "^9.0.5", "human-format": "^1.2.0", "inquirer-select-directory": "^1.2.0", "lodash": "^4.17.21", "mime": "^4.0.6", "mjml": "^4.15.3", "modern-normalize": "^2.0.0", "mongoose": "^8.8.2", "nouislider": "^15.8.1", "numeral": "^2.0.6", "nuxt": "^3.12.4", "nuxt-applicationinsights": "^1.0.1", "nuxt-oidc-auth": "^1.0.0-beta.5", "pinia": "^2.1.7", "pluralize": "^8.0.0", "vue": "^3.4.21", "vue-currency-input": "^3.1.0", "vue-filepond": "^7.0.4", "vue-router": "^4.3.0", "vuedraggable": "^4.0.1", "zod": "^3.25.67"}, "devDependencies": {"@chromatic-com/storybook": "^1.2.25", "@nuxtjs/storybook": "^8.0.0", "@nuxtjs/tailwindcss": "^6.12.1", "@storybook/addon-designs": "^8.0.0", "@storybook/addon-essentials": "^8.0.4", "@storybook/addon-interactions": "^8.0.4", "@storybook/addon-links": "^8.0.4", "@storybook/blocks": "^8.0.4", "@storybook/test": "^8.0.4", "@storybook/vue3": "^8.0.4", "@storybook/vue3-vite": "^8.0.4", "@testing-library/vue": "^8.1.0", "@types/busboy": "^1.5.4", "@types/formidable": "^3.4.5", "@types/html-to-text": "^9.0.4", "@types/lodash": "^4.17.16", "@types/mjml": "^4.7.4", "@types/numeral": "^2.0.5", "@types/opentype.js": "^1.3.8", "@types/pluralize": "^0.0.33", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@vitest/ui": "^3.1.1", "@vue/compiler-sfc": "^3.5.13", "@vue/test-utils": "^2.4.0-alpha.2", "autoprefixer": "^10.4.19", "chromatic": "^13.1.2", "chrome-launcher": "^1.1.2", "eslint": "^8.57.0", "eslint-plugin-nuxt": "^4.0.0", "eslint-plugin-vue": "^9.24.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lighthouse": "^12.5.1", "nuxt-file-storage": "^0.2.7", "plop": "^4.0.1", "postcss": "^8.4.38", "prettier": "^3.5.3", "puppeteer": "^24.5.0", "sass": "^1.72.0", "selenium-webdriver": "^4.19.0", "storybook": "^8.0.4", "tailwindcss": "^3.4.13", "vite-plugin-sass-glob-import": "^3.0.2", "vite-svg-loader": "^5.1.0", "vitest": "^3.1.1"}, "browserslist": [">0.3%", "not ie 11", "not dead", "not op_mini all", "last 3 version", "Chrome >= 35", "Firefox >= 38", "Edge >= 10", "Explorer >= 10", "ie >= 10", "iOS >= 8", "Safari >= 8", "Android 2.3", "Android >= 4", "Opera >= 12"], "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.24.0"}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{js,css,md}": "prettier --write"}}