import { vi } from 'vitest';
import { create<PERSON><PERSON>, setActive<PERSON><PERSON> } from 'pinia';
import { defineStore } from 'pinia';

// Initialize Pinia
const pinia = createPinia();
setActivePinia(pinia);

// Mock Nuxt's useRequestFetch
vi.mock('#app', () => ({
    useRequestFetch: () => vi.fn(),
}));

// Mock NuxtLink component
vi.mock('#components', () => ({
    NuxtLink: {
        name: 'NuxtLink',
        props: ['to'],
        template: '<a :href="to"><slot /></a>',
    },
}));

// Mock other Nuxt-specific components
vi.mock('#components', () => ({
    default: {
        name: 'NuxtPage',
        template: '<div><slot /></div>',
    },
}));

// Mock checkbox-input component
vi.mock('~/components/forms/CheckboxInput.vue', () => ({
    default: {
        name: 'CheckboxInput',
        props: ['modelValue', 'size', 'label', 'id', 'name'],
        template:
            '<div class="checkbox-input" :class="`checkbox-input-s-${size}`"><input type="checkbox" :id="id" :name="name" v-model="modelValue" /><label v-if="label" :for="id">{{ label }}</label></div>',
    },
}));

// Mock SvgIcon component
vi.mock('~/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        props: ['shape', 'width', 'height', 'color', 'viewBox'],
        template:
            '<svg :id="`svg-icon-${Math.floor(Math.random() * 1000000)}`" :width="width || 24" :height="height || 24" :viewBox="viewBox || \'0 0 24 24\'" :style="{ color }" fill="none" class="svg-icon"><path d="M12 2L2 7v10l10 5 10-5V7L12 2z"/></svg>',
    },
}));

// Mock Button component
vi.mock('~/components/elements/Button.vue', () => ({
    default: {
        name: 'Button',
        props: ['svg-icon', 'variant', 'uppercase', 'to', 'loading', 'disabled'],
        template:
            '<component :is="to ? \'a\' : \'button\'" :href="to" :class="[\'button\', variant ? `button-c-${variant}` : \'\', { \'button-disabled\': disabled || loading }]" :disabled="disabled || loading"><span v-if="svgIcon" class="button-icon button-icon-right button-icon-c-primary"></span><slot /></component>',
    },
}));

// Mock defineStore
vi.mock('pinia', async () => {
    const actual = await vi.importActual('pinia');
    return {
        ...actual,
        defineStore: vi.fn((id, setup) => {
            return () => setup();
        }),
    };
});

vi.mock('~/server/utils/insights', () => ({
    useLogger: () => ({
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        event: vi.fn(),
    }),
}));
