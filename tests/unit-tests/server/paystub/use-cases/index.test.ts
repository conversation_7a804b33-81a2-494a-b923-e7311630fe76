import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as PaystubUseCases from '~/server/use-cases/paystub';
import { Frequency } from '~/types/base';
import type { ProviderKeysWithDev } from '#nuxt-oidc/types';
import * as H3 from 'h3';
import { Types } from 'mongoose';

// Minimal PaystubLine mock with all required fields
const defaultPaystubLine = (overrides = {}) => ({
    current: 100,
    frequencyPerMonth: 1,
    description: 'Test',
    rate: 0,
    hours: 0,
    ytd: 0,
    ...overrides,
});

const samplePaystub = {
    incomeItems: [
        defaultPaystubLine({ current: 1000, description: 'Salary' }),
        defaultPaystubLine({ current: 500, description: 'Bonus' }),
    ],
    preTaxItems: [defaultPaystubLine({ current: 100, description: '401k' })],
    taxesItems: [defaultPaystubLine({ current: 200, description: 'Federal Tax' })],
    postTaxItems: [defaultPaystubLine({ current: 50, description: 'Roth IRA' })],
    customItems: [],
    payDate: new Date('2023-01-01'),
    frequency: Frequency.Monthly,
    name: 'Test Paystub',
};

// Mock PortfolioSnapshots globally for all tests that need it
vi.mock('~/server/models/portfolio.schema', async importOriginal => {
    const actual = await importOriginal();
    return {
        // @ts-expect-error vi.mock does not support type inference
        ...actual,
        PortfolioSnapshots: vi.fn().mockImplementation(() => ({ save: vi.fn().mockResolvedValue(undefined) })),
    };
});

describe('Paystub Use Cases', () => {
    it('calculateTotalFromItems returns correct total', () => {
        const items = [
            defaultPaystubLine({ current: 100, frequencyPerMonth: 2 }),
            defaultPaystubLine({ current: 50, frequencyPerMonth: 1 }),
        ];
        expect(PaystubUseCases.calculateTotalFromItems(items, Frequency.Monthly)).toBe(250);
    });

    it('calculateGrossPay returns correct value', () => {
        expect(PaystubUseCases.calculateGrossPay(samplePaystub)).toBe(1500);
    });

    it('calculateAdjustedGrossIncome returns correct value', () => {
        expect(PaystubUseCases.calculateAdjustedGrossIncome(samplePaystub)).toBe(1400);
    });

    it('calculateNetPay returns correct value', () => {
        expect(PaystubUseCases.calculateNetPay(samplePaystub)).toBe(1200);
    });

    it('calculateCashFlowNetIncome returns correct value', () => {
        expect(PaystubUseCases.calculateCashFlowNetIncome(samplePaystub)).toBe(1150);
    });

    it('generatePaystubReport returns expected report', () => {
        const report = PaystubUseCases.generatePaystubReport(samplePaystub);
        expect(report.grossIncome).toBe(1500);
        expect(report.preTaxDeductions).toBe(100);
        expect(report.adjustedGrossIncome).toBe(1400);
        expect(report.taxesWithheld).toBe(200);
        expect(report.netPay).toBe(1200);
        expect(report.postTaxDeductions).toBe(50);
        expect(report.cashFlowNetIncome).toBe(1150);
        expect(report.frequency).toBe(Frequency.Monthly);
    });

    it('extractAssetContributions returns correct assets', () => {
        const assets = PaystubUseCases.extractAssetContributions(samplePaystub);
        expect(assets.preTaxAssets.length).toBe(1);
        expect(assets.postTaxAssets.length).toBe(1);
        expect(assets.preTaxAssets[0].equityValue).toBe(100);
        expect(assets.postTaxAssets[0].equityValue).toBe(50);
    });

    it('processPaystubData returns expected structure', () => {
        const data = PaystubUseCases.processPaystubData(samplePaystub);
        expect(data.report.grossIncome).toBe(1500);
        expect(data.assets.preTaxAssets.length).toBe(1);
        expect(data.assets.postTaxAssets.length).toBe(1);
        expect(Array.isArray(data.expenses)).toBe(true);
    });

    // DB-related functions (findPortfolioByUser, savePortfolioSnapshot, getPaystubById) should be tested with mocks
    it('findPortfolioByUser throws if not found (mocked)', async () => {
        const { Portfolios } = await import('~/server/models/portfolio.schema');
        vi.spyOn(Portfolios, 'findByOwner').mockImplementationOnce(
            () => ({ exec: vi.fn().mockResolvedValueOnce(null) }) as any,
        );
        const user = {
            userData: {
                _id: 'user1',
                authId: 'auth1',
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                roles: [],
                joinDate: new Date(),
                isDisabled: false,
            },
            provider: 'oidc' as ProviderKeysWithDev,
            canRefresh: false,
            expireAt: 0,
        };
        await expect(PaystubUseCases.findPortfolioByUser(user)).rejects.toThrow('Portfolio not found');
    });

    it('savePortfolioSnapshot saves a snapshot (mocked)', async () => {
        const { PortfolioSnapshots } = await import('~/server/models/portfolio.schema');
        const saveMock = vi.fn().mockResolvedValue(undefined);
        (PortfolioSnapshots as any).mockImplementation(() => ({ save: saveMock }));

        const mockPortfolio = {
            _id: { toString: () => 'portfolio1' },
            toObject: vi.fn().mockReturnValue({
                _id: 'portfolio1',
                income: [],
                assets: [],
                expenses: [],
                owners: [],
            }),
            income: [],
            assets: [],
            expenses: [],
            owners: [],
            overwrite: vi.fn(),
            markModified: vi.fn(),
        } as any;

        await PaystubUseCases.savePortfolioSnapshot(mockPortfolio, 'user1');

        expect(PortfolioSnapshots).toHaveBeenCalled();
        expect(PortfolioSnapshots).toHaveBeenCalledWith(
            expect.not.objectContaining({
                _id: expect.anything(),
            }),
        );
        expect(mockPortfolio.toObject).toHaveBeenCalled();
        expect(saveMock).toHaveBeenCalled();
    });

    it('findIncomeEmployee returns correct employee', () => {
        const mockPortfolio = {
            _id: { toString: () => 'portfolio1' },
            income: [
                { id: 'emp1', kind: 'IncomeEmployee' },
                { id: 'emp2', kind: 'Other' },
            ],
        } as any;
        const result = PaystubUseCases.findIncomeEmployee(mockPortfolio, 'emp1');
        expect(result).toEqual({ id: 'emp1', kind: 'IncomeEmployee' });
        expect(PaystubUseCases.findIncomeEmployee(mockPortfolio, 'notfound')).toBeUndefined();
    });

    it('updateIncomeEmployeeWithPaystubData updates fields as expected', () => {
        const incomeEmployee = {
            id: 'emp1',
            description: '',
            category: '',
            mode: '',
            paystubs: [],
            associatedAssets: [],
            deductions: [],
        } as any;

        const paystubData = {
            _id: new Types.ObjectId().toString(),
            name: 'Stub',
            report: {
                payDate: new Date(),
                grossIncome: 1000,
                preTaxDeductions: 100,
                adjustedGrossIncome: 900,
                taxesWithheld: 100,
                netPay: 800,
                postTaxDeductions: 50,
                cashFlowNetIncome: 123,
                frequency: Frequency.Monthly,
            },
            assets: {
                preTaxAssets: [
                    {
                        id: 'a1',
                        description: '',
                        category: '',
                        mode: 'Standard',
                        equityValue: 0,
                        acquiredDate: new Date(),
                        associatedExpenses: [],
                        associatedLiabilities: [],
                    },
                ],
                postTaxAssets: [
                    {
                        id: 'a2',
                        description: '',
                        category: '',
                        mode: 'Standard',
                        equityValue: 0,
                        acquiredDate: new Date(),
                        associatedExpenses: [],
                        associatedLiabilities: [],
                    },
                ],
            },
            deductions: { preTaxDeductions: [1], taxDeductions: [2], postTaxDeductions: [3] },
            expenses: [],
        };
        const updated = PaystubUseCases.pushNewPaystubToIncomeEmployee(incomeEmployee, 'W2', paystubData as any);
        expect(updated.employmentType).toBe('W2');
        expect(updated.amount).toBe(123);
        expect(updated.frequency).toBe(Frequency.Monthly);
        expect(updated.description).toBe('Stub');
        expect(updated.associatedAssets).toEqual(['a2', 'a1']);
        expect(updated.deductions).toEqual([1, 2, 3]);
        expect((updated.paystubs || []).length).toBe(1);
    });

    it('getPaystubById throws if not found (mocked)', async () => {
        const { PaystubModel } = await import('~/server/models/paystub.schema');
        vi.spyOn(PaystubModel, 'findById').mockImplementationOnce(
            () => ({ exec: vi.fn().mockResolvedValueOnce(null as any) }) as any,
        );
        await expect(PaystubUseCases.getPaystubById('notfound')).rejects.toThrow('Paystub not found');
    });
});
