import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock dependencies
const mockUploadDataToAzureBlob = vi.fn();
const mockAnalyzeDocumentUrl = vi.fn();

vi.mock('~/server/services/blob', () => ({
    default: { uploadDataToAzureBlob: mockUploadDataToAzureBlob },
}));
vi.mock('~/server/services/doc-intelligence', () => ({
    default: { analyzeDocumentUrl: mockAnalyzeDocumentUrl },
}));

vi.mock('~/server/utils/multipart-form', () => ({
    parseMultipartFormData: vi.fn().mockResolvedValue({
        files: [{ filename: 'file.pdf', bufferFile: Buffer.from('abc') }],
        fields: [],
    }),
}));

vi.mock('~/server/services/doc-intelligence/constants', () => ({ AZURE_DI_MODELS: { PAYSTUB_V2: 'model' } }));

describe('OCR Use cases', () => {
    beforeEach(() => {
        vi.resetModules();
        vi.unstubAllGlobals();
        mockUploadDataToAzureBlob.mockReset();
        mockAnalyzeDocumentUrl.mockReset();
        vi.stubGlobal('createError', ({ message }: any) => new Error(message));
    });
    afterEach(() => {
        vi.unstubAllGlobals();
    });

    describe('uploadPaystubToAzureBlob', () => {
        it('uploads and returns URL', async () => {
            mockUploadDataToAzureBlob.mockResolvedValue('https://blob/url');
            const { uploadPaystubToAzureBlob } = await import('~/server/use-cases/paystub/ocr');
            const url = await uploadPaystubToAzureBlob('file.pdf', Buffer.from('abc'));
            expect(mockUploadDataToAzureBlob).toHaveBeenCalledWith({
                containerName: 'user-uploads',
                blobName: 'paystubs/file.pdf',
                data: Buffer.from('abc'),
            });
            expect(url).toBe('https://blob/url');
        });
        it('throws if fileName or data missing', async () => {
            const { uploadPaystubToAzureBlob } = await import('~/server/use-cases/paystub/ocr');
            expect(() => uploadPaystubToAzureBlob('', Buffer.from('abc'))).toThrow();
            expect(() => uploadPaystubToAzureBlob('file.pdf', undefined as any)).toThrow();
        });
        it('throws if data is not string or Buffer', async () => {
            const { uploadPaystubToAzureBlob } = await import('~/server/use-cases/paystub/ocr');
            expect(() => uploadPaystubToAzureBlob('file.pdf', 123 as any)).toThrow();
        });
    });

    describe('analyzePaystubWithOCR', () => {
        it('returns null paystub and ocr if analysisResult is null', async () => {
            mockAnalyzeDocumentUrl.mockResolvedValue(null);
            const { analyzePaystubWithOCR } = await import('~/server/use-cases/paystub/ocr');
            const result = await analyzePaystubWithOCR('https://blob/file.pdf');
            expect(result.paystub).toBeNull();
            expect(result.ocr.fields).toBeNull();
            expect(result.ocr.overallConfidence).toBe(-Infinity);
        });
        it('throws if fileUrl is missing', async () => {
            const { analyzePaystubWithOCR } = await import('~/server/use-cases/paystub/ocr');
            await expect(analyzePaystubWithOCR('')).rejects.toThrow();
        });
    });

    describe('checkDocumentAnalysisConfidence', () => {
        it('returns confidence if present', async () => {
            const { checkDocumentAnalysisConfidence } = await import('~/server/use-cases/paystub/ocr');
            const analysis = { analyzeResult: { documents: [{ confidence: 0.8 }] } } as any;
            expect(checkDocumentAnalysisConfidence(analysis)).toBe(0.8);
        });
        it('returns 0 if not present', async () => {
            const { checkDocumentAnalysisConfidence } = await import('~/server/use-cases/paystub/ocr');
            expect(checkDocumentAnalysisConfidence(null)).toBe(0);
            expect(checkDocumentAnalysisConfidence({ analyzeResult: {} } as any)).toBe(0);
        });
    });

    describe('getOcrFields', () => {
        it('returns fields if present', async () => {
            const { getOcrFields } = await import('~/server/use-cases/paystub/ocr');
            const analysis = { analyzeResult: { documents: [{ fields: { foo: 'bar' } }] } } as any;
            expect(getOcrFields(analysis)).toEqual({ foo: 'bar' });
        });
        it('returns null if not present', async () => {
            const { getOcrFields } = await import('~/server/use-cases/paystub/ocr');
            expect(getOcrFields(null)).toBeNull();
            expect(getOcrFields({ analyzeResult: {} } as any)).toBeNull();
        });
    });

    describe('adaptPaystubAnalysisToPaystub', () => {
        it('throws if analysis is invalid', async () => {
            const { adaptPaystubAnalysisToPaystub } = await import('~/server/use-cases/paystub/ocr');
            expect(() => adaptPaystubAnalysisToPaystub(null as any)).toThrow();
            expect(() => adaptPaystubAnalysisToPaystub({} as any)).toThrow();
            expect(() => adaptPaystubAnalysisToPaystub({ analyzeResult: { documents: [] } } as any)).toThrow();
        });
        it('throws if document.fields is missing', async () => {
            const { adaptPaystubAnalysisToPaystub } = await import('~/server/use-cases/paystub/ocr');
            expect(() => adaptPaystubAnalysisToPaystub({ analyzeResult: { documents: [{}] } } as any)).toThrow();
        });
        it('returns Paystub with expected fields', async () => {
            const { adaptPaystubAnalysisToPaystub } = await import('~/server/use-cases/paystub/ocr');
            const analysis = {
                analyzeResult: {
                    documents: [
                        {
                            fields: {
                                EmployerName: { valueString: 'Test Employer' },
                                PaystubDate: { valueDate: new Date('2023-01-01') },
                                Frequency: { valueString: 'biweekly' },
                                Income: {
                                    valueArray: [
                                        {
                                            valueObject: {
                                                Description: { valueString: 'Salary' },
                                                Rate: { valueNumber: 100 },
                                                Hours: { valueNumber: 80 },
                                                Current: { valueNumber: 2000 },
                                                YTD: { valueNumber: 4000 },
                                            },
                                        },
                                    ],
                                },
                                Taxes: {
                                    valueArray: [
                                        {
                                            valueObject: {
                                                Description: { valueString: 'Federal' },
                                                Current: { valueNumber: 300 },
                                                YTD: { valueNumber: 600 },
                                            },
                                        },
                                    ],
                                },
                                PreTaxDeductions: {
                                    valueArray: [
                                        {
                                            valueObject: {
                                                Description: { valueString: '401k' },
                                                Current: { valueNumber: 100 },
                                                YTD: { valueNumber: 200 },
                                            },
                                        },
                                    ],
                                },
                                PostTaxDeductions: {
                                    valueArray: [
                                        {
                                            valueObject: {
                                                Description: { valueString: 'Health' },
                                                Current: { valueNumber: 50 },
                                                YTD: { valueNumber: 100 },
                                            },
                                        },
                                    ],
                                },
                            },
                        },
                    ],
                },
            } as any;
            const paystub = adaptPaystubAnalysisToPaystub(analysis);
            expect(paystub.name).toBe('Test Employer');
            expect(paystub.payDate).toEqual(new Date('2023-01-01'));
            expect(paystub.frequency).toBe('Biweekly');
            expect(paystub.incomeItems[0].description).toBe('Salary');
            expect(paystub.incomeItems[0].rate).toBe(100);
            expect(paystub.incomeItems[0].hours).toBe(80);
            expect(paystub.incomeItems[0].current).toBe(2000);
            expect(paystub.incomeItems[0].ytd).toBe(4000);
            expect(paystub.taxesItems[0].description).toBe('Federal');
            expect(paystub.taxesItems[0].current).toBe(300);
            expect(paystub.taxesItems[0].ytd).toBe(600);
            expect(paystub.preTaxItems[0].description).toBe('401k');
            expect(paystub.preTaxItems[0].current).toBe(100);
            expect(paystub.preTaxItems[0].ytd).toBe(200);
            expect(paystub.postTaxItems[0].description).toBe('Health');
            expect(paystub.postTaxItems[0].current).toBe(50);
            expect(paystub.postTaxItems[0].ytd).toBe(100);
        });
    });
});
