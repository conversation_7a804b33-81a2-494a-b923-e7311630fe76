import { describe, it, expect, vi } from 'vitest';
import { PaystubRequestBody, PutPaystubRequest, PatchPaystubRequest } from '~/server/dto/paystub.dto';

const validPaystub = {
    name: 'Test Paystub',
    payDate: new Date(),
    frequency: 'Monthly',
    incomeItems: [],
    preTaxItems: [],
    postTaxItems: [],
    taxesItems: [],
    customItems: [],
};

describe('Paystub DTO Zod Schemas', () => {
    beforeAll(() => {
        vi.spyOn(console, 'log').mockImplementation(() => {});
        vi.spyOn(console, 'error').mockImplementation(() => {});
    });

    it('validates a correct PaystubRequestBody', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: validPaystub,
        };

        expect(() => PaystubRequestBody.parse(data)).not.toThrow();
    });

    it('fails if employmentType is invalid', () => {
        const data = {
            employmentType: 'INVALID',
            incomeEmployeeId: 'emp1',
            paystub: validPaystub,
        };

        expect(() => PaystubRequestBody.parse(data)).toThrow();
    });

    it('fails if paystub is missing required fields', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: {},
        };

        expect(() => PaystubRequestBody.parse(data)).toThrow();
    });

    it('validates a correct PutPaystubRequest', () => {
        const data = {
            incomeEmployeeId: 'emp1',
            paystub: validPaystub,
        };

        expect(() => PutPaystubRequest.parse(data)).not.toThrow();
    });

    it('fails if incomeEmployeeId is missing in PutPaystubRequest', () => {
        const data = {
            paystub: validPaystub,
        };

        expect(() => PutPaystubRequest.parse(data)).toThrow();
    });

    it('validates a correct PatchPaystubRequest', () => {
        const validPatchPaystub = {
            name: 'Patch Paystub',
            payDate: new Date(),
            frequency: '',
            incomeItems: [],
            preTaxItems: [],
            postTaxItems: [],
            taxesItems: [],
            customItems: [],
        };

        const data = {
            paystubId: 'id1',
            paystub: validPatchPaystub,
            incomeEmployeeId: 'emp1',
        };

        expect(() => PatchPaystubRequest.parse(data)).not.toThrow();
    });

    it('fails if paystubId is missing in PatchPaystubRequest', () => {
        const data = {
            paystub: { name: 'New Name' },
            incomeEmployeeId: 'emp1',
        };

        expect(() => PatchPaystubRequest.parse(data)).toThrow();
    });

    it('fails if incomeEmployeeId is missing in PatchPaystubRequest', () => {
        const data = {
            paystubId: 'id1',
            paystub: { name: 'New Name' },
        };
        expect(() => PatchPaystubRequest.parse(data)).toThrow();
    });

    it('fails if __v is present in PaystubRequestBody', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: { ...validPaystub, __v: 1 },
        };

        expect(() => PaystubRequestBody.parse(data)).toThrow();
    });

    it('fails if __v is present in PutPaystubRequest', () => {
        const data = {
            incomeEmployeeId: 'emp1',
            paystub: { ...validPaystub, __v: 1 },
        };

        expect(() => PutPaystubRequest.parse(data)).toThrow();
    });

    it('fails if __v is present in PatchPaystubRequest', () => {
        const data = {
            paystubId: 'id1',
            paystub: { name: 'New Name', __v: 1 },
            incomeEmployeeId: 'emp1',
        };

        expect(() => PatchPaystubRequest.parse(data)).toThrow();
    });

    it('fails if _id is present in PatchPaystubRequest', () => {
        const data = {
            paystubId: 'id1',
            paystub: { _id: 'should-fail-w-id', name: 'New Name' },
            incomeEmployeeId: 'emp1',
        };

        expect(() => PatchPaystubRequest.parse(data)).toThrow();
    });

    it('fails if paystubId is missing in PatchPaystubRequest', () => {
        const data = {
            paystub: { name: 'New Name' },
            incomeEmployeeId: 'emp1',
        };

        expect(() => PatchPaystubRequest.parse(data)).toThrow();
    });

    it('successfully parses when sent a ISO 8601 date string', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: {
                ...validPaystub,
                payDate: new Date().toISOString(),
            },
        };

        expect(() => PaystubRequestBody.parse(data)).not.toThrow();
    });

    it('fails if payDate is not a valid date', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: {
                ...validPaystub,
                payDate: 'invalid-date',
            },
        };

        expect(() => PaystubRequestBody.parse(data)).toThrow();
    });

    it('does not fail if frequencyPerMonth is null', () => {
        const data = {
            employmentType: 'W2',
            incomeEmployeeId: 'emp1',
            paystub: {
                ...validPaystub,
                incomeItems: [
                    {
                        description: 'Test Income Item',
                        frequencyPerMonth: null,
                        rate: 1000,
                        hours: null,
                        current: 1000,
                        ytd: 5000,
                    },
                ],
            },
        };

        expect(() => PaystubRequestBody.parse(data)).not.toThrow();
    });
});
