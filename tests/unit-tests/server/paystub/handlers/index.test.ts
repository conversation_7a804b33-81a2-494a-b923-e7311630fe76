import { vi } from 'vitest';

import { describe, it, expect, beforeEach } from 'vitest';
import { consola } from 'consola';
import * as PaystubSchema from '~/server/models/paystub.schema';
import * as UseCases from '~/server/use-cases/paystub';

vi.mock('~/server/models/paystub.schema', () => {
    const PaystubModel = vi.fn();
    (PaystubModel as any).findById = vi.fn();
    return { PaystubModel };
});

vi.mock('~/server/use-cases/paystub', () => ({
    getPaystubById: vi.fn(),
    findPortfolioByUser: vi.fn(),
    findIncomeEmployee: vi.fn(),
    processPaystubData: vi.fn(),
    updateExistingPaystubInIncomeEmployee: vi.fn(),
    savePortfolioSnapshot: vi.fn(),
    pushNewPaystubToIncomeEmployee: vi.fn(),
    generatePaystubReport: vi.fn(),
}));

vi.mock('~/server/use-cases/paystub/ocr', () => ({
    uploadPaystubToAzureBlob: vi.fn().mockResolvedValue('https://blob/paystub.pdf'),
    analyzePaystubWithOCR: vi.fn().mockResolvedValue({ paystub: {}, ocr: {} }),
}));

vi.mock('~/server/utils/multipart-form', () => ({
    parseMultipartFormData: vi.fn().mockResolvedValue({
        files: [{ filename: 'file.pdf', bufferFile: Buffer.from('abc') }],
        fields: [],
    }),
}));

const mockGetUser = vi.fn();
const mockGetQuery = vi.fn();
const mockReadBody = vi.fn();

vi.stubGlobal('getUser', mockGetUser);
vi.stubGlobal('getQuery', mockGetQuery);
vi.stubGlobal('readBody', mockReadBody);
vi.stubGlobal('getUserId', vi.fn().mockResolvedValue('user1'));
vi.stubGlobal('createError', ({ statusCode, statusMessage }: { statusCode: number; statusMessage: string }) => {
    const err = new Error(statusMessage);
    (err as any).statusCode = statusCode;
    return err;
});
vi.stubGlobal('defineEventHandler', (fn: any) => fn);

vi.mock('fs/promises', () => {
    const readFile = vi.fn().mockResolvedValue(Buffer.from('filedata'));
    return {
        __esModule: true,
        default: { readFile },
        readFile,
    };
});

describe('Paystub API Handlers', () => {
    beforeAll(() => {
        vi.spyOn(consola, 'log').mockImplementation(() => {});
        vi.spyOn(consola, 'error').mockImplementation(() => {});

        // Set envs before any other imports
        vi.stubEnv('NUXT_AZURE_STORAGE_ACCOUNT_NAME', 'mockaccount');
        vi.stubEnv('NUXT_AZURE_STORAGE_ACCOUNT_KEY', 'mockkey');
        vi.stubEnv('NUXT_AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT', 'https://azure-endpoint');
        vi.stubEnv('NUXT_AZURE_DOCUMENT_INTELLIGENCE_KEY', 'azure-key');
    });

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('GET /paystub', () => {
        it('returns paystub with report', async () => {
            const { default: handler } = await import('~/server/api/paystub/index.get');
            const paystub = { _id: '1' };

            mockGetUser.mockResolvedValue({ userData: { _id: 'u1' } });
            mockGetQuery.mockReturnValue({ id: '1' });
            (PaystubSchema.PaystubModel.findById as any).mockReturnValue({ exec: () => Promise.resolve(paystub) });
            (UseCases.processPaystubData as any).mockReturnValue({ report: 'report' });

            const event = {} as any;
            const result = await handler(event);

            expect(result).toBe(paystub);
            expect((result as any).report).toBeDefined();
        });

        it('throws 400 if no id', async () => {
            const { default: handler } = await import('~/server/api/paystub/index.get');

            mockGetUser.mockResolvedValue({ userData: { _id: 'u1' } });
            mockGetQuery.mockReturnValue({});

            const event = {} as any;
            await expect(handler(event)).rejects.toThrow('Paystub ID is required');
        });

        it('throws 404 if not found', async () => {
            const { default: handler } = await import('~/server/api/paystub/index.get');

            mockGetUser.mockResolvedValue({ userData: { _id: 'u1' } });
            mockGetQuery.mockReturnValue({ id: 'notfound' });
            (PaystubSchema.PaystubModel.findById as any).mockReturnValue({ exec: () => Promise.resolve(null) });

            const event = {} as any;
            await expect(handler(event)).rejects.toThrow('Paystub not found');
        });
    });

    describe('POST /paystub', () => {
        it('creates and associates paystub', async () => {
            const { default: handler } = await import('~/server/api/paystub/index.post');
            const user = { userData: { _id: 'u1' } };

            const mockPaystubRequestBody = {
                employmentType: 'W2' as const,
                incomeEmployeeId: '*********',
                paystub: {
                    name: 'Example Company',
                    payDate: new Date('2023-10-15'),
                    frequency: 'biweekly',
                    incomeItems: [
                        {
                            description: 'Regular Pay',
                            frequencyPerMonth: null,
                            rate: 25,
                            hours: 80,
                            current: 2000,
                            ytd: 42000,
                        },
                    ],
                    preTaxItems: [
                        {
                            description: '401(k)',
                            frequencyPerMonth: null,
                            current: 200,
                            ytd: 4200,
                            rate: null,
                            hours: null,
                        },
                    ],
                    postTaxItems: [
                        {
                            description: 'Health Insurance',
                            frequencyPerMonth: null,
                            current: 150,
                            ytd: 3150,
                            rate: null,
                            hours: null,
                        },
                    ],
                    taxesItems: [
                        {
                            description: 'Federal Income Tax',
                            frequencyPerMonth: null,
                            current: 300,
                            ytd: 6300,
                            rate: null,
                            hours: null,
                        },
                    ],
                    customItems: [],
                },
            };

            mockGetUser.mockResolvedValue(user);
            mockReadBody.mockResolvedValue(mockPaystubRequestBody);
            (PaystubSchema.PaystubModel as any).mockImplementation((_data: any) => ({
                save: () => Promise.resolve({ _id: 'valid-id', ...mockPaystubRequestBody.paystub }),
            }));

            const event = {} as any;
            const result = await handler(event as any);

            expect(result).toStrictEqual({ _id: 'valid-id', ...mockPaystubRequestBody.paystub });
        });
    });

    describe('PATCH /paystub', () => {
        it('updates paystub and portfolio', async () => {
            const { default: handler } = await import('~/server/api/paystub/index.patch');
            const user = { userData: { _id: 'u1' } };
            const mockPatchPaystubRequest = {
                paystubId: '12345abcde',
                incomeEmployeeId: '67890fghij',
                paystub: {
                    name: 'Patch Paystub',
                    payDate: new Date('2023-01-31'),
                    frequency: 'bi-weekly',
                    incomeItems: [
                        {
                            description: 'Base Salary',
                            frequencyPerMonth: null,
                            rate: 50.0,
                            hours: 80,
                            current: 4000.0,
                            ytd: 4000.0,
                        },
                    ],
                    preTaxItems: [
                        {
                            description: '401(k)',
                            frequencyPerMonth: null,
                            current: 400.0,
                            rate: null,
                            hours: null,
                            ytd: 400.0,
                        },
                    ],
                    postTaxItems: [
                        {
                            description: 'Health Insurance',
                            frequencyPerMonth: null,
                            current: 150.0,
                            rate: null,
                            hours: null,
                            ytd: 150.0,
                        },
                    ],
                    taxesItems: [
                        {
                            description: 'Federal Income Tax',
                            frequencyPerMonth: null,
                            current: 800.0,
                            rate: null,
                            hours: null,
                            ytd: 800.0,
                        },
                    ],
                    customItems: [],
                },
            };

            const paystub = {
                set: vi.fn(),
                save: vi
                    .fn()
                    .mockResolvedValue({ _id: mockPatchPaystubRequest.paystubId, ...mockPatchPaystubRequest.paystub }),
            };
            const portfolio = {
                income: [{ id: mockPatchPaystubRequest.incomeEmployeeId }],
                markModified: vi.fn(),
                save: vi.fn(),
            };
            const incomeEmployee = { id: mockPatchPaystubRequest.incomeEmployeeId, employmentType: 'W2' };

            mockGetUser.mockResolvedValue(user);
            mockReadBody.mockResolvedValue(mockPatchPaystubRequest);

            (UseCases.getPaystubById as any).mockResolvedValue(paystub);
            (UseCases.findPortfolioByUser as any).mockResolvedValue(portfolio);
            (UseCases.findIncomeEmployee as any).mockReturnValue(incomeEmployee);
            (UseCases.processPaystubData as any).mockReturnValue({});
            (UseCases.updateExistingPaystubInIncomeEmployee as any).mockReturnValue({
                id: mockPatchPaystubRequest.incomeEmployeeId,
            });
            (UseCases.savePortfolioSnapshot as any).mockResolvedValue(undefined);

            const event = {} as any;
            const result = await handler(event);

            expect(result._id).toBe(mockPatchPaystubRequest.paystubId);
            expect(portfolio.save).toHaveBeenCalled();
            expect(UseCases.savePortfolioSnapshot).toHaveBeenCalled();
        });
    });

    describe('POST /paystub/ocr', () => {
        it('returns OCR result for valid blob', async () => {
            const { default: handler } = await import('~/server/api/paystub/ocr.post');
            const mockResult = { paystub: {}, ocr: {} };

            // Simulate request body with blobName
            mockReadBody.mockResolvedValue({ blobName: 'paystubs/test.pdf' });

            const event = {} as any;
            const result = await handler(event);

            expect(result).toEqual(mockResult);
        });

        it('throws 400 if blobName is missing', async () => {
            const { default: handler } = await import('~/server/api/paystub/ocr.post');
            mockReadBody.mockResolvedValue({});
            const event = {} as any;
            await expect(handler(event)).rejects.toThrow();
        });
    });
});
