import { describe, it, expect, vi, beforeEach } from 'vitest';

const mockPath = vi.fn();
const mockPost = vi.fn();
const mockPollUntilDone = vi.fn();
const mockGetLongRunningPoller = vi.fn();
const mockIsUnexpected = vi.fn();

vi.mock('@azure-rest/ai-document-intelligence', () => ({
    default: vi.fn(() => ({ path: mockPath })),
    getLongRunningPoller: mockGetLongRunningPoller,
    isUnexpected: mockIsUnexpected,
}));
vi.mock('@azure/core-auth', () => ({ AzureKeyCredential: vi.fn() }));
vi.mock('~/server/services/doc-intelligence/constants', () => ({
    AZURE_DI_ENDPOINT: 'endpoint',
    AZURE_DI_KEY: 'key',
}));

describe('analyzeDocumentUrl', () => {
    beforeEach(() => {
        vi.resetModules();
        mockPath.mockReset();
        mockPost.mockReset();
        mockPollUntilDone.mockReset();
        mockGetLongRunningPoller.mockReset();
        mockIsUnexpected.mockReset();

        vi.stubGlobal('console', {
            error: vi.fn(),
            log: vi.fn(),
            warn: vi.fn(),
            info: vi.fn(),
        });
    });

    it('returns analysis result on success', async () => {
        const initialResponse = { body: {}, status: 200 };
        const pollResult = { body: { status: 'succeeded', result: { foo: 'bar' } } };
        mockPost.mockResolvedValue(initialResponse);
        mockPath.mockReturnValue({ post: mockPost });
        mockIsUnexpected.mockReturnValue(false);
        mockPollUntilDone.mockResolvedValue(pollResult);
        mockGetLongRunningPoller.mockReturnValue({ pollUntilDone: mockPollUntilDone });

        const { analyzeDocumentUrl } = await import('~/server/services/doc-intelligence/index');
        const result = await analyzeDocumentUrl('model', 'url');
        expect(mockPath).toHaveBeenCalledWith('/documentModels/{modelId}:analyze', 'model');
        expect(mockPost).toHaveBeenCalledWith({
            contentType: 'application/json',
            body: { urlSource: 'url' },
        });
        expect(result).toEqual({ status: 'succeeded', result: { foo: 'bar' } });
    });

    it('throws if initial response is unexpected', async () => {
        const initialResponse = { body: { error: new Error('fail') }, status: 400 };
        mockPost.mockResolvedValue(initialResponse);
        mockPath.mockReturnValue({ post: mockPost });
        mockIsUnexpected.mockReturnValue(true);

        const { analyzeDocumentUrl } = await import('~/server/services/doc-intelligence/index');
        await expect(analyzeDocumentUrl('model', 'url')).rejects.toThrow('fail');
    });

    it('returns null if analysis status is not succeeded', async () => {
        const initialResponse = { body: {}, status: 200 };
        const pollResult = { body: { status: 'failed' } };
        mockPost.mockResolvedValue(initialResponse);
        mockPath.mockReturnValue({ post: mockPost });
        mockIsUnexpected.mockReturnValue(false);
        mockPollUntilDone.mockResolvedValue(pollResult);
        mockGetLongRunningPoller.mockReturnValue({ pollUntilDone: mockPollUntilDone });

        const { analyzeDocumentUrl } = await import('~/server/services/doc-intelligence/index');
        const result = await analyzeDocumentUrl('model', 'url');
        expect(result).toBeNull();
    });
});
