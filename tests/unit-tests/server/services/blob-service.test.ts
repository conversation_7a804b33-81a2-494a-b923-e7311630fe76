import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock Azure SDK
const mockUpload = vi.fn();
const mockUploadStream = vi.fn();
const mockCreateIfNotExists = vi.fn();
const mockGetBlockBlobClient = vi.fn();
const mockGetContainerClient = vi.fn();
const mockGenerateSasUrl = vi.fn();

vi.mock('@azure/storage-blob', () => ({
    BlobServiceClient: {
        fromConnectionString: vi.fn(() => ({
            getContainerClient: mockGetContainerClient,
        })),
    },
    BlobSASPermissions: { parse: vi.fn() },
    BlockBlobClient: vi.fn(),
}));

// Patch constants
vi.mock('~/server/services/blob/constants', () => ({
    BLOB_ACCOUNT_NAME: 'testaccount',
    BLOB_ACCOUNT_KEY: 'testkey',
    AZURE_BLOB_CONNECTION_STRING: 'UseDevelopmentStorage=true;',
    AzureBlobSasPermissions: { Read: 'r' },
}));

describe('uploadDataToAzureBlob', () => {
    beforeEach(() => {
        vi.resetModules();
        mockUpload.mockReset();
        mockCreateIfNotExists.mockReset();
        mockGetBlockBlobClient.mockReset();
        mockGetContainerClient.mockReset();
        mockGenerateSasUrl.mockReset();
    });

    it('uploads data and returns SAS URL', async () => {
        const blockBlobClient = { generateSasUrl: mockGenerateSasUrl, uploadStream: mockUploadStream };
        mockGetBlockBlobClient.mockReturnValue(blockBlobClient);
        mockGetContainerClient.mockReturnValue({
            createIfNotExists: mockCreateIfNotExists,
            getBlockBlobClient: mockGetBlockBlobClient,
        });
        mockUploadStream.mockResolvedValue(undefined);
        mockCreateIfNotExists.mockResolvedValue(undefined);
        mockGenerateSasUrl.mockResolvedValue('https://blob/sas-url');

        const { uploadDataToAzureBlob } = await import('~/server/services/blob/index');
        const url = await uploadDataToAzureBlob({
            containerName: 'test',
            blobName: 'file.txt',
            data: Buffer.from('abc'),
        });
        expect(mockGetContainerClient).toHaveBeenCalledWith('test');
        expect(mockCreateIfNotExists).toHaveBeenCalled();
        expect(mockGetBlockBlobClient).toHaveBeenCalledWith('file.txt');
        expect(mockUploadStream).toHaveBeenCalledWith(expect.anything(), 4 * 1024 * 1024, 20, {
            onProgress: expect.any(Function),
        });
        expect(mockGenerateSasUrl).toHaveBeenCalled();
        expect(url).toBe('https://blob/sas-url');
    });

    it('uploads data and returns URL without SAS if generateSas=false', async () => {
        const blockBlobClient = { url: 'https://blob/no-sas', uploadStream: mockUploadStream };
        mockGetBlockBlobClient.mockReturnValue(blockBlobClient);
        mockGetContainerClient.mockReturnValue({
            createIfNotExists: mockCreateIfNotExists,
            getBlockBlobClient: mockGetBlockBlobClient,
        });
        mockUploadStream.mockResolvedValue(undefined);
        mockCreateIfNotExists.mockResolvedValue(undefined);

        const { uploadDataToAzureBlob } = await import('~/server/services/blob/index');
        const url = await uploadDataToAzureBlob({
            containerName: 'test',
            blobName: 'file.txt',
            data: Buffer.from('abc'),
            generateSas: false,
        });
        expect(url).toBe('https://blob/no-sas');
        expect(mockGenerateSasUrl).not.toHaveBeenCalled();
    });

    it('throws if credentials are missing', async () => {
        vi.doMock('~/server/services/blob/constants', () => ({
            BLOB_ACCOUNT_NAME: '',
            BLOB_ACCOUNT_KEY: '',
            AZURE_BLOB_CONNECTION_STRING: '',
            AzureBlobSasPermissions: { Read: 'r' },
        }));
        const { uploadDataToAzureBlob } = await import('~/server/services/blob/index');
        await expect(
            uploadDataToAzureBlob({
                containerName: 'test',
                blobName: 'file.txt',
                data: Buffer.from('abc'),
            }),
        ).rejects.toThrow('Azure Storage account name or key is not set.');
    });
});
