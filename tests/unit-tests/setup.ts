import { vi } from 'vitest';

// Mock Vue
vi.mock('vue', () => {
    return {
        ref: vi.fn(value => ({ value })),
        computed: vi.fn(fn => ({ value: fn() })),
        watch: vi.fn((source, callback) => {
            // Return a function to stop watching
            return () => {};
        }),
        nextTick: vi.fn(() => Promise.resolve()),
        getCurrentInstance: vi.fn(() => ({})),
        defineOptions: vi.fn(),
        defineProps: vi.fn(() => ({})),
        defineEmits: vi.fn(() => vi.fn()),
        withDefaults: vi.fn((props, defaults) => ({ ...props, ...defaults })),
        defineComponent: vi.fn(options => options),
        mergeModels: vi.fn(value => value),
    };
});
