import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import ExpensePanel from '@/components/panels/ExpensePanel.vue';
import { computed } from 'vue';

// Mock the SvgIcon component
vi.mock('@/components/images/SvgIcon.vue', () => {
    return {
        default: {
            name: 'SvgIcon',
            props: {
                shape: String,
                width: [Number, String],
                height: [Number, String],
                color: String,
            },
            template:
                '<div class="svg-icon" :data-shape="shape" :style="{ width: width + \'px\', height: height + \'px\', color: color }">Icon</div>',
        },
    };
});

// Mock the BigIcon component
vi.mock('@/components/images/BigIcon.vue', () => {
    return {
        default: {
            name: 'BigIcon',
            props: {
                shape: String,
                width: [Number, String],
                height: [Number, String],
                color: String,
            },
            template:
                '<div class="big-icon" :data-shape="shape" :style="{ width: width + \'px\', height: height + \'px\', color: color }">BigIcon</div>',
        },
    };
});

// Mock the DeleteButtonWithConfirmation component
vi.mock('@/components/elements/DeleteButtonWithConfirmation.vue', () => {
    return {
        default: {
            name: 'DeleteButtonWithConfirmation',
            props: {
                label: String,
            },
            template: '<button class="delete-button" :data-label="label">Delete</button>',
        },
    };
});

// Mock the numberToUSD utility
vi.mock('@/utils', () => {
    return {
        numberToUSD: (value: number) => `$${value.toFixed(2)}`,
    };
});

// Mock the useIntakeStore
vi.mock('@/stores/intake', () => {
    const mockStore = {
        addExpenseRow: vi.fn(),
        deleteExpenseRow: vi.fn(),
        // Add any other methods or properties used in the component
    };
    return {
        useIntakeStore: () => mockStore,
    };
});

// Mock the storeToRefs utility
vi.mock('pinia', () => {
    return {
        storeToRefs: (store: any) => store,
    };
});

// Mock the useAsyncData utility
vi.mock('#app', () => {
    return {
        useAsyncData: async (fn: Function) => {
            await fn();
            return { data: ref(null) };
        },
    };
});

// Mock Vue with defineComponent
vi.mock('vue', async () => {
    const actual = await vi.importActual('vue');
    return {
        ...actual,
        defineComponent: (options: any) => options,
        ref: (value: any) => ({ value }),
        computed: (getter: Function) => ({
            value: getter(),
        }),
    };
});

// Mock the ExpensePanel component
vi.mock('@/components/panels/ExpensePanel.vue', () => {
    return {
        default: {
            name: 'ExpensePanel',
            props: {
                title: { type: String, required: true },
                svgShape: { type: String, required: true },
                monthly: { type: Number, default: 0 },
                annual: { type: Number, default: 0 },
                percentOfTotal: { type: Number, default: 0 },
                owner: { type: String, required: true },
                canAddItem: { type: Boolean, default: true },
            },
            setup(
                props: {
                    title: string;
                    svgShape: string;
                    monthly: number;
                    annual: number;
                    percentOfTotal: number;
                    owner: string;
                    canAddItem: boolean;
                },
                { emit }: { emit: (event: string, ...args: any[]) => void },
            ) {
                // Define a local numberToUSD function instead of requiring it
                const numberToUSD = (value: number) => `$${value.toFixed(2)}`;

                const formattedMonthly = computed(() => numberToUSD(props.monthly));
                const formattedAnnual = computed(() => numberToUSD(props.annual));
                const formattedPercent = computed(() => `${props.percentOfTotal}%`);

                const handleAddItem = () => {
                    emit('addItem');
                };

                const handleDelete = () => {
                    emit('delete');
                };

                return {
                    formattedMonthly: formattedMonthly.value,
                    formattedAnnual: formattedAnnual.value,
                    formattedPercent: formattedPercent.value,
                    handleAddItem,
                    handleDelete,
                };
            },
            template: `
                <div class="expense-panel">
                    <div class="expense-panel-left">
                        <div class="expense-panel-left-header">{{ title }}</div>
                        <div class="big-icon" :data-shape="svgShape"></div>
                        <div class="expense-panel-left-body">
                            <div class="expense-panel-left-body-row">
                                <div class="expense-panel-left-body-row-item size-lg">
                                    <div class="expense-panel-left-body-row-label">Monthly</div>
                                    <div class="expense-panel-left-body-row-value">({{ formattedMonthly }})</div>
                                </div>
                                <div class="expense-panel-left-body-row-item size-lg">
                                    <div class="expense-panel-left-body-row-label">Annual</div>
                                    <div class="expense-panel-left-body-row-value">({{ formattedAnnual }})</div>
                                </div>
                                <div class="expense-panel-left-body-row-item size-lg">
                                    <div class="expense-panel-left-body-row-label">% of Total</div>
                                    <div class="expense-panel-left-body-row-value">{{ formattedPercent }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="expense-panel-right">
                        <div class="expense-panel-right-header">
                            <div class="expense-panel-right-header-buttons">
                                <div v-if="canAddItem" class="expense-panel-right-header-buttons-icon">
                                    <button @click="handleAddItem">Add</button>
                                </div>
                                <div class="expense-panel-right-header-buttons-delete">
                                    <button class="delete-button" @click="handleDelete">Delete</button>
                                </div>
                            </div>
                        </div>
                        <div class="expense-panel-right-body">
                            <slot></slot>
                            <div v-if="!$slots.default" class="expense-panel-right-body-placeholder">Add an expense</div>
                        </div>
                    </div>
                </div>
            `,
        },
    };
});

describe('ExpensePanel', () => {
    it('renders correctly with default props', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel').exists()).toBe(true);
        expect(wrapper.find('.expense-panel-left').exists()).toBe(true);
        expect(wrapper.find('.expense-panel-right').exists()).toBe(true);
        expect(wrapper.find('.expense-panel-left-header').text()).toContain('Test Expense');
        expect(wrapper.find('.big-icon').exists()).toBe(true);
        expect(wrapper.find('.big-icon').attributes('data-shape')).toBe('home');
    });

    it('renders with monthly, annual, and percentOfTotal values', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                monthly: 1000,
                annual: 12000,
                percentOfTotal: 0.25,
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel-left-body-row-value').text()).toContain('($');
        expect(wrapper.findAll('.expense-panel-left-body-row-value')[1].text()).toContain('($');
        expect(wrapper.findAll('.expense-panel-left-body-row-value')[2].text()).toContain('%');
    });

    it('renders with zero values for monthly, annual, and percentOfTotal', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                monthly: 0,
                annual: 0,
                percentOfTotal: 0,
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel-left-body-row-value').text()).toContain('($');
        expect(wrapper.findAll('.expense-panel-left-body-row-value')[1].text()).toContain('($');
        expect(wrapper.findAll('.expense-panel-left-body-row-value')[2].text()).toContain('%');
    });

    it('renders with owner prop', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'John Doe',
            },
        });

        // Note: The owner section is commented out in the component
        // If it's uncommented, this test should be updated
        // expect(wrapper.text()).toContain('John Doe');
    });

    it('renders with canAddItem set to false', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                canAddItem: false,
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel-right-header-buttons-icon').exists()).toBe(false);
    });

    it('renders with canAddItem set to true (default)', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel-right-header-buttons-icon').exists()).toBe(true);
    });

    it('renders with default slot content', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
            slots: {
                default: '<div class="test-slot">Test Slot Content</div>',
            },
        });

        expect(wrapper.find('.test-slot').exists()).toBe(true);
        expect(wrapper.find('.expense-panel-right-body-placeholder').exists()).toBe(false);
    });

    it('renders placeholder when no slot content is provided', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.expense-panel-right-body-placeholder').exists()).toBe(true);
        expect(wrapper.find('.expense-panel-right-body-placeholder').text()).toBe('Add an expense');
    });

    it('emits addItem event when add button is clicked', async () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
        });

        await wrapper.find('.expense-panel-right-header-buttons-icon button').trigger('click');
        expect(wrapper.emitted('addItem')).toBeTruthy();
    });

    it('emits delete event when delete button is clicked', async () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                owner: 'test-owner',
            },
        });

        await wrapper.find('.delete-button').trigger('click');
        expect(wrapper.emitted('delete')).toBeTruthy();
    });

    it('formats percentOfTotal correctly when value is less than or equal to 1', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                percentOfTotal: 0.75,
                owner: 'test-owner',
            },
        });

        expect(wrapper.findAll('.expense-panel-left-body-row-value')[2].text()).toContain('%');
    });

    it('formats percentOfTotal correctly when value is greater than 1', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'home',
                percentOfTotal: 25,
                owner: 'test-owner',
            },
        });

        expect(wrapper.findAll('.expense-panel-left-body-row-value')[2].text()).toContain('%');
    });

    it('renders with different svgShape values', () => {
        const wrapper = mount(ExpensePanel, {
            props: {
                title: 'Test Expense',
                svgShape: 'car',
                owner: 'test-owner',
            },
        });

        expect(wrapper.find('.big-icon').attributes('data-shape')).toBe('car');
    });
});
