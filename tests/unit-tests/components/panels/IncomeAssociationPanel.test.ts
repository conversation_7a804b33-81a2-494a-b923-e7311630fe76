import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import IncomeAssociationPanel from '../../../../components/panels/IncomeAssociationPanel.vue';

describe('IncomeAssociationPanel', () => {
    const defaultProps = {
        title: 'Test Title',
        federalExpenses: '12k',
        federalExpensesLabel: 'Federal - Taxes',
        medicareExpenses: '14k',
        medicareExpensesLabel: 'Medicare - Premiums',
        secondExpense: true,
    };

    it('renders with default props', () => {
        const wrapper = mount(IncomeAssociationPanel, {
            props: defaultProps,
        });

        expect(wrapper.text()).toContain('Test Title');
        expect(wrapper.text()).toContain('Federal - Taxes');
        expect(wrapper.text()).toContain('$12k');
        expect(wrapper.text()).toContain('Medicare - Premiums');
        expect(wrapper.text()).toContain('$14k');
    });

    it('renders without second expense when secondExpense is false', () => {
        const wrapper = mount(IncomeAssociationPanel, {
            props: {
                ...defaultProps,
                secondExpense: false,
            },
        });

        expect(wrapper.text()).toContain('Federal - Taxes');
        expect(wrapper.text()).toContain('$12k');
        expect(wrapper.text()).not.toContain('Medicare - Premiums');
        expect(wrapper.text()).not.toContain('$14k');
    });

    it('emits change event when description is updated', async () => {
        const wrapper = mount(IncomeAssociationPanel, {
            props: defaultProps,
        });

        const descriptionInput = wrapper.find('input[placeholder="Enter description"]');
        await descriptionInput.setValue('New Description');
        await descriptionInput.trigger('change');

        expect(wrapper.emitted('change')).toBeTruthy();
        expect(wrapper.emitted('change')?.[0]).toEqual(['description', 'New Description']);
    });

    it('emits change event when gross income is updated', async () => {
        const wrapper = mount(IncomeAssociationPanel, {
            props: defaultProps,
        });

        const grossIncomeInput = wrapper.find('input[placeholder="Enter gross income"]');
        await grossIncomeInput.setValue('50000');
        await grossIncomeInput.trigger('change');

        expect(wrapper.emitted('change')).toBeTruthy();
        expect(wrapper.emitted('change')?.[0]).toEqual(['grossIncome', '50000']);
    });

    it('has a button with dot-menu icon', () => {
        const wrapper = mount(IncomeAssociationPanel, {
            props: defaultProps,
        });

        const button = wrapper.findComponent({ name: 'Button' });
        expect(button.exists()).toBe(true);
        expect(button.props('iconOnly')).toBe(true);
        expect(button.props('variant')).toBe('muted');
        expect(button.props('svgIcon')).toBe('dot-menu');
    });

    it('logs to console when button is clicked', () => {
        const consoleSpy = vi.spyOn(console, 'log');
        const wrapper = mount(IncomeAssociationPanel, {
            props: defaultProps,
        });

        const button = wrapper.findComponent({ name: 'Button' });
        button.trigger('click');

        expect(consoleSpy).toHaveBeenCalledWith('handleclick');
        consoleSpy.mockRestore();
    });
});
