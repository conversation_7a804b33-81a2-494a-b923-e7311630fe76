import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import LineItem from '../../../../components/panels/LineItem.vue';
import TextInput from '../../../../components/forms/TextInput.vue';
import SvgIcon from '../../../../components/images/SvgIcon.vue';

// Mock child components
vi.mock('../../../../components/forms/TextInput.vue', () => ({
    default: {
        name: 'TextInput',
        template: '<div class="text-input-mock"><input data-testid="mock-input" /></div>',
        props: ['modelValue', 'placeholder', 'disabled'],
    },
}));

vi.mock('../../../../components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        template: '<span data-testid="mock-svg-icon" />',
        props: ['shape', 'width', 'height'],
    },
}));

vi.mock('../../../../components/elements/Button.vue', () => ({
    default: {
        name: 'Button',
        template: '<button data-testid="mock-button"><slot /></button>',
        props: ['variant', 'iconOnly', 'svgIcon'],
    },
}));

describe('LineItem', () => {
    const defaultItems = [
        {
            label: 'Description',
            description: 'Description of property/item',
            editDescription: 'Description of property/item',
            isEditing: false,
            required: true,
        },
        {
            label: 'Address',
            description: 'The address of property',
            editDescription: 'The address of property',
            isEditing: false,
            required: true,
        },
        {
            label: 'Estimated value',
            description: 'The estimated or appraised value of the item/property',
            editDescription: 'The estimated or appraised value of the item/property',
            isEditing: false,
            required: true,
        },
        {
            label: 'Owner',
            description: 'The individual or entity that owns the property/item',
            editDescription: 'The individual or entity that owns the property/item',
            isEditing: false,
            required: false,
        },
        {
            label: 'Income',
            description: 'All income generated by this property/item',
            editDescription: 'All income generated by this property/item',
            isEditing: false,
            required: false,
        },
        {
            label: 'Liabilities',
            description: 'All debt associated with this property/item',
            editDescription: 'All debt associated with this property/item',
            isEditing: false,
            required: false,
        },
        {
            label: 'Expenses',
            description: 'All expenses and taxes associated with this property/item',
            editDescription: 'All expenses and taxes associated with this property/item',
            isEditing: false,
            required: false,
        },
    ];

    it('renders with default state', () => {
        const wrapper = mount(LineItem, {
            global: {
                stubs: {
                    TextInput: true,
                    SvgIcon: true,
                    Button: true,
                },
            },
        });

        // Check header content
        expect(wrapper.find('.line-item-header-name').text()).toBe('Tony');
        expect(wrapper.find('.line-item-header-item').text()).toBe('GT3RS');

        // Check navigation tabs
        expect(wrapper.find('.line-item-body-nav-link.active').text()).toBe('Overview');
        expect(wrapper.findAll('.line-item-body-nav-link')).toHaveLength(3);

        // Check overview content is visible by default
        expect(wrapper.find('.line-item-body-section').isVisible()).toBe(true);
        expect(wrapper.find('.line-item-body-kpi')).toBeTruthy();
        expect(wrapper.find('.line-item-body-chart')).toBeTruthy();
    });

    it('switches between tabs correctly', async () => {
        const wrapper = mount(LineItem, {
            global: {
                stubs: {
                    TextInput: true,
                    SvgIcon: true,
                    Button: true,
                },
            },
        });

        // Click on Details tab
        await wrapper.findAll('.line-item-body-nav-link')[1].trigger('click');
        expect(wrapper.find('.line-item-body-nav-link.active').text()).toBe('Details');
        expect(wrapper.find('.line-item-body-section').isVisible()).toBe(false);

        // Click on Transactions tab
        await wrapper.findAll('.line-item-body-nav-link')[2].trigger('click');
        expect(wrapper.find('.line-item-body-nav-link.active').text()).toBe('Transactions');
        expect(wrapper.find('.line-item-body-section').isVisible()).toBe(false);
    });

    it('toggles edit mode for items', async () => {
        const consoleSpy = vi.spyOn(console, 'log');
        const wrapper = mount(LineItem, {
            global: {
                stubs: {
                    TextInput: true,
                    SvgIcon: true,
                    Button: true,
                },
            },
        });

        // Click on first item to enter edit mode
        await wrapper.findAll('.line-item-body-details')[0].trigger('click');
        expect(consoleSpy).toHaveBeenCalledWith('Toggling edit mode for index:', 0);
        expect(wrapper.find('.line-item-body-details-edit').exists()).toBe(true);
        expect(wrapper.findComponent(TextInput).exists()).toBe(true);

        // Check edit buttons are visible
        expect(wrapper.find('.line-item-body-details-buttons').exists()).toBe(false);
        expect(wrapper.find('.line-item-body-details-buttons-actions').exists()).toBe(false);
        consoleSpy.mockRestore();
    });

    it('shows required field indicators', () => {
        const wrapper = mount(LineItem, {
            global: {
                stubs: {
                    TextInput: true,
                    SvgIcon: true,
                    Button: true,
                },
            },
        });

        // Switch to details tab
        wrapper.findAll('.line-item-body-nav-link')[1].trigger('click');

        // Check that required fields have asterisk icon
        const requiredFields = wrapper
            .findAll('.line-item-body-details')
            .filter(w => w.findComponent(SvgIcon).props('shape') === 'asterisk');
        expect(requiredFields).toHaveLength(3); // Description, Address, and Estimated value are required
    });

    it('shows delete button in details tab', () => {
        const wrapper = mount(LineItem, {
            global: {
                stubs: {
                    TextInput: true,
                    SvgIcon: true,
                    Button: true,
                },
            },
        });

        // Initially delete button should not be visible
        expect(wrapper.find('.line-item-footer').exists()).toBe(true);

        // Switch to details tab
        wrapper.findAll('.line-item-body-nav-link')[1].trigger('click');

        // Delete button should be visible
        expect(wrapper.find('.line-item-footer').exists()).toBe(true);
        expect(wrapper.find('.line-item-footer').text()).toContain('Delete asset');
    });
});
