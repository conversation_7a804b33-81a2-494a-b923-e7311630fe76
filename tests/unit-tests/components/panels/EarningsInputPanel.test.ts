import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import EarningsInputPanel from '@/components/panels/EarningsInputPanel.vue';
import { computed } from 'vue';

// Mock the SvgIcon component
vi.mock('@/components/images/SvgIcon.vue', () => {
    return {
        default: {
            name: 'SvgIcon',
            props: {
                shape: String,
                width: [Number, String],
                height: [Number, String],
                color: String,
            },
            template:
                '<div class="svg-icon" :data-shape="shape" :style="{ width: width + \'px\', height: height + \'px\', color: color }">Icon</div>',
        },
    };
});

// Mock the Button component
vi.mock('@/components/elements/Button.vue', () => {
    return {
        default: {
            name: 'Button',
            props: {
                type: String,
                title: String,
                severity: String,
            },
            template: '<button :class="[\'button\', severity]">{{ title }}</button>',
        },
    };
});

// Mock the TextInput component
vi.mock('@/components/forms/TextInput.vue', () => {
    return {
        default: {
            name: 'TextInput',
            props: {
                modelValue: String,
                label: String,
                placeholder: String,
                readonly: Boolean,
            },
            template:
                '<div class="text-input"><label>{{ label }}</label><input :value="modelValue" :placeholder="placeholder" :readonly="readonly" /></div>',
        },
    };
});

// Mock the CheckBoxInput component
vi.mock('@/components/forms/CheckBoxInput.vue', () => {
    return {
        default: {
            name: 'CheckBoxInput',
            props: {
                modelValue: Boolean,
                label: String,
            },
            template:
                '<div class="checkbox-input"><input type="checkbox" :checked="modelValue" /><label>{{ label }}</label></div>',
        },
    };
});

// Mock the useIntakeStore
vi.mock('@/stores/intake', () => {
    const mockStore = {
        addEarningsRow: vi.fn(),
        deleteEarningsRow: vi.fn(),
        // Add any other methods or properties used in the component
    };
    return {
        useIntakeStore: () => mockStore,
    };
});

// Mock the storeToRefs utility
vi.mock('pinia', () => {
    return {
        storeToRefs: (store: any) => store,
    };
});

// Mock the useAsyncData utility
vi.mock('#app', () => {
    return {
        useAsyncData: async (fn: Function) => {
            await fn();
            return { data: ref(null) };
        },
    };
});

// Mock Vue with defineComponent
vi.mock('vue', async () => {
    const actual = await vi.importActual('vue');
    return {
        ...actual,
        defineComponent: (options: any) => options,
        ref: (value: any) => ({ value }),
        computed: (getter: Function) => ({
            value: getter(),
        }),
    };
});

// Mock the EarningsInputPanel component
vi.mock('@/components/panels/EarningsInputPanel.vue', () => {
    return {
        default: {
            name: 'EarningsInputPanel',
            props: {
                type: String,
                title: String,
                mode: String,
                box1Label: String,
                box2Label: String,
                box3Label: String,
                severity: String,
            },
            template: `
                <div class="earnings-input-panel">
                    <div class="earnings-input-panel-header">
                        <div class="earnings-input-panel-header-title">{{ title }}</div>
                        <div class="earnings-input-panel-header-icon">
                            <svg-icon shape="earnings" width="24" height="24" color="primary" />
                        </div>
                    </div>
                    <div class="earnings-input-panel-body">
                        <div class="earnings-input-panel-body-row">
                            <div class="earnings-input-panel-body-row-item">
                                <text-input :label="box1Label" placeholder="Enter value" />
                            </div>
                            <div class="earnings-input-panel-body-row-item">
                                <text-input :label="box2Label" placeholder="Enter value" />
                            </div>
                            <div class="earnings-input-panel-body-row-item">
                                <text-input :label="box3Label" placeholder="Enter value" />
                            </div>
                        </div>
                        <div v-if="mode === 'w2'" class="earnings-input-panel-body-row">
                            <div class="earnings-input-panel-body-row-item">
                                <checkbox-input label="Box 1" />
                            </div>
                            <div class="earnings-input-panel-body-row-item">
                                <checkbox-input label="Box 2" />
                            </div>
                            <div class="earnings-input-panel-body-row-item">
                                <checkbox-input label="Box 3" />
                            </div>
                        </div>
                    </div>
                    <div class="earnings-input-panel-footer">
                        <button class="button cancel">cancel</button>
                        <button class="button add">add</button>
                    </div>
                </div>
            `,
        },
    };
});

describe('EarningsInputPanel', () => {
    it('renders correctly with default props', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add W2 to',
                title: 'W2 Income',
                mode: 'w2',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                    CheckBoxInput: {
                        template:
                            '<div class="checkbox-input"><input type="checkbox" /><label>{{ label }}</label></div>',
                        props: ['label'],
                    },
                },
            },
        });

        expect(wrapper.find('.earnings-input-panel').exists()).toBe(true);
    });

    it('renders correctly in 1099 mode', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add 1099 to',
                title: 'Contractor Income',
                mode: '1099',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                },
            },
        });

        expect(wrapper.find('.earnings-input-panel').exists()).toBe(true);
        expect(wrapper.text()).toContain('Contractor Income');
    });

    it('renders correctly in default mode with custom labels', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add to',
                title: 'Custom Income',
                mode: 'default',
                box1Label: 'Custom Label 1',
                box2Label: 'Custom Label 2',
                box3Label: 'Custom Label 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                },
            },
        });

        expect(wrapper.find('.earnings-input-panel').exists()).toBe(true);
        expect(wrapper.text()).toContain('Custom Label 1');
        expect(wrapper.text()).toContain('Custom Label 2');
        expect(wrapper.text()).toContain('Custom Label 3');
    });

    it('renders SVG icons correctly', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add W2 to',
                title: 'W2 Income',
                mode: 'w2',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                    CheckBoxInput: {
                        template:
                            '<div class="checkbox-input"><input type="checkbox" /><label>{{ label }}</label></div>',
                        props: ['label'],
                    },
                },
            },
        });

        const icons = wrapper.findAll('.svg-icon');
        expect(icons.length).toBeGreaterThan(0);
    });

    it('renders footer buttons correctly', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add W2 to',
                title: 'W2 Income',
                mode: 'w2',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                    CheckBoxInput: {
                        template:
                            '<div class="checkbox-input"><input type="checkbox" /><label>{{ label }}</label></div>',
                        props: ['label'],
                    },
                },
            },
        });

        const buttons = wrapper.findAll('button');
        expect(buttons.length).toBe(2);
        expect(buttons[0].text()).toBe('cancel');
        expect(buttons[1].text()).toBe('add');
    });

    it('renders readonly input in default mode', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add to',
                title: 'Custom Income',
                mode: 'default',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input readonly /></div>',
                        props: ['label'],
                    },
                },
            },
        });

        const readonlyInput = wrapper.find('input[readonly]');
        expect(readonlyInput.exists()).toBe(true);
    });

    it('renders checkbox inputs in W2 mode', () => {
        const wrapper = mount(EarningsInputPanel, {
            props: {
                type: 'Add W2 to',
                title: 'W2 Income',
                mode: 'w2',
                box1Label: 'Box 1',
                box2Label: 'Box 2',
                box3Label: 'Box 3',
            },
            global: {
                components: {
                    SvgIcon: {
                        template: '<div class="svg-icon" :data-shape="shape">Icon</div>',
                        props: ['shape'],
                    },
                    TextInput: {
                        template: '<div class="text-input"><label>{{ label }}</label><input /></div>',
                        props: ['label'],
                    },
                    CheckBoxInput: {
                        template:
                            '<div class="checkbox-input"><input type="checkbox" /><label>{{ label }}</label></div>',
                        props: ['label'],
                    },
                    'checkbox-input': {
                        template:
                            '<div class="checkbox-input"><input type="checkbox" /><label>{{ label }}</label></div>',
                        props: ['label'],
                    },
                },
            },
        });

        const checkboxes = wrapper.findAll('input[type="checkbox"]');
        expect(checkboxes.length).toBe(3);
    });
});
