import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import MaybeList from '@/components/partials/MaybeList.vue';

interface TestItem {
    id: number;
    name: string;
}

interface DifferentTestItem {
    id: number;
    type: string;
}

describe('MaybeList', () => {
    it('renders empty slot when items is null', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: null as unknown as TestItem | TestItem[],
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(true);
        expect(wrapper.find('.item').exists()).toBe(true);
    });

    it('renders empty slot when items is an empty array', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: [],
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(true);
        expect(wrapper.find('.item').exists()).toBe(false);
    });

    it('renders single item when items is not an array', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: { id: 1, name: 'Test Item' },
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(false);
        expect(wrapper.find('.item').text()).toBe('Test Item');
    });

    it('renders multiple items when items is an array', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: [
                    { id: 1, name: 'Item 1' },
                    { id: 2, name: 'Item 2' },
                ],
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(false);
        const items = wrapper.findAll('.item');
        expect(items).toHaveLength(2);
        expect(items[0].text()).toBe('Item 1');
        expect(items[1].text()).toBe('Item 2');
    });

    it('handles undefined items', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: undefined as unknown as TestItem | TestItem[],
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(true);
        expect(wrapper.find('.item').exists()).toBe(true);
    });

    it('passes item data to slot props', () => {
        const wrapper = mount(MaybeList<TestItem>, {
            props: {
                items: { id: 1, name: 'Test Item' },
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.name }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(false);
        expect(wrapper.find('.item').text()).toBe('Test Item');
    });

    it('handles different types of items', () => {
        const wrapper = mount(MaybeList<DifferentTestItem>, {
            props: {
                items: [
                    { id: 1, type: 'type1' },
                    { id: 2, type: 'type2' },
                    { id: 3, type: 'type3' },
                ],
            },
            slots: {
                empty: '<div class="empty-state">No items</div>',
                item: `
          <template #default="item">
            <div class="item">{{ item.type }}</div>
          </template>
        `,
            },
        });

        expect(wrapper.find('.empty-state').exists()).toBe(false);
        const items = wrapper.findAll('.item');
        expect(items).toHaveLength(3);
        expect(items[0].text()).toBe('type1');
        expect(items[1].text()).toBe('type2');
        expect(items[2].text()).toBe('type3');
    });
});
