import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import IntakeTitleBar from '@/components/partials/IntakeTitleBar.vue';
import type { IntakeTitleBarProps } from '@/components/partials/IntakeTitleBar.vue';
import Button from '@/components/elements/Button.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';

// Mock the SvgIcon component
vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        props: ['shape', 'color', 'width', 'height', 'viewBox'],
        template: '<svg class="svg-icon" :id="elementId"></svg>',
    },
}));

describe('IntakeTitleBar', () => {
    it('renders with default props', () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        expect(wrapper.find('h3').text()).toBe('Test Title');
        expect(wrapper.find('.intake-layout-title-actions').exists()).toBe(true);

        const buttons = wrapper.findAllComponents(Button);
        expect(buttons).toHaveLength(2);

        // Check prev button
        expect(buttons[0].props('iconOnly')).toBe(true);
        expect(buttons[0].props('svgIcon')).toBe('keyboard-arrow-left');
        expect(buttons[0].props('variant')).toBe('muted');

        // Check next button
        expect(buttons[1].props('iconOnly')).toBe(false);
        expect(buttons[1].props('svgIcon')).toEqual({ position: 'right', shape: 'keyboard-arrow-right' });
        expect(buttons[1].text()).toBe('Continue');
    });

    it('renders with custom next text', () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
                nextText: 'Next Step',
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const nextButton = wrapper.findAllComponents(Button)[1];
        expect(nextButton.text()).toBe('Next Step');
    });

    it('disables next button when nextDisabled is true', () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
                nextDisabled: true,
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const nextButton = wrapper.findAllComponents(Button)[1];
        expect(nextButton.props('disabled')).toBe(true);
    });

    it('shows/hides next arrow based on nextArrow prop', async () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
                nextArrow: false,
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const nextButton = wrapper.findAllComponents(Button)[1];
        expect(nextButton.props('svgIcon')).toBeNull();

        await wrapper.setProps({ nextArrow: true });
        expect(nextButton.props('svgIcon')).toEqual({ position: 'right', shape: 'keyboard-arrow-right' });
    });

    it('emits prevClicked event when prev button is clicked', async () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const prevButton = wrapper.findAllComponents(Button)[0];
        await prevButton.trigger('click');
        expect(wrapper.emitted('prevClicked')).toBeTruthy();
    });

    it('emits nextClicked event when next button is clicked', async () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const nextButton = wrapper.findAllComponents(Button)[1];
        await nextButton.trigger('click');
        expect(wrapper.emitted('nextClicked')).toBeTruthy();
    });

    it('does not emit nextClicked when next button is disabled', async () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: 'Test Title',
                nextDisabled: true,
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        const nextButton = wrapper.findAllComponents(Button)[1];
        await nextButton.trigger('click');
        expect(wrapper.emitted('nextClicked')).toBeFalsy();
    });

    it('renders title with HTML content', () => {
        const wrapper = mount(IntakeTitleBar, {
            props: {
                title: '<span>Test</span> Title',
            } as IntakeTitleBarProps,
            global: {
                components: {
                    ElementsButton: Button,
                    SvgIcon,
                },
            },
        });

        expect(wrapper.find('h3').html()).toContain('<span>Test</span> Title');
    });
});
