import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { computed } from 'vue';
import UserAvatar from '@/components/images/UserAvatar.vue';
import type { UserSession } from '#nuxt-oidc/types';

// Mock the entire ~/utils path
vi.mock('~/utils', () => {
    return {
        getInitials: (user: any) => {
            if (!user) return '';
            return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`;
        },
    };
});

// Mock the component's import
vi.mock('@/components/images/UserAvatar.vue', () => {
    const getInitials = (user: any) => {
        if (!user) return '';
        return `${user.firstName?.[0] || ''}${user.lastName?.[0] || ''}`;
    };

    return {
        default: {
            props: {
                user: {
                    type: Object,
                    required: false,
                },
                size: {
                    type: Number,
                    default: 40,
                },
                active: {
                    type: Boolean,
                    default: false,
                },
                mode: {
                    type: String,
                    default: 'photo',
                },
            },
            template: `
                <div class="user-avatar" :class="{ 'user-avatar-active': active }" :style="avatarSize">
                    <img v-if="showImage" :src="userData?.profileImage" :alt="getInitials(userData)" />
                    <span v-else>{{ getInitials(userData) }}</span>
                </div>
            `,
            setup(props: { user: any; size: number; active: boolean; mode: string }) {
                // Provide default user data if none is provided
                const defaultUserData = {
                    firstName: 'Default',
                    lastName: 'User',
                    profileImage: 'https://example.com/default.jpg',
                };

                const userData = computed(() => {
                    if (props.user === undefined) return defaultUserData;
                    return props.user?.userData || props.user || defaultUserData;
                });

                const showImage = computed(() => props.mode === 'photo' && userData.value?.profileImage);
                const avatarSize = computed(() => ({
                    '--user-avatar-size': `${props.size}px`,
                    'font-size': `${props.size * 0.47}px`,
                }));
                return {
                    userData,
                    showImage,
                    avatarSize,
                    getInitials,
                };
            },
        },
    };
});

// Mock the useOidcAuth composable
vi.mock('#imports', () => ({
    useOidcAuth: () => ({
        user: {
            value: {
                userData: {
                    firstName: 'John',
                    lastName: 'Doe',
                    profileImage: 'https://example.com/profile.jpg',
                },
            },
        },
    }),
}));

describe('UserAvatar', () => {
    it('renders with custom size', () => {
        const wrapper = mount(UserAvatar, {
            props: {
                size: 60,
            },
        });
        const avatar = wrapper.find('.user-avatar');

        expect(avatar.attributes('style')).toContain('--user-avatar-size: 60px');
        expect(avatar.attributes('style')).toContain('font-size: 28.2');
    });

    it('renders with active state', () => {
        const wrapper = mount(UserAvatar, {
            props: {
                active: true,
            },
        });
        const avatar = wrapper.find('.user-avatar');

        expect(avatar.classes()).toContain('user-avatar-active');
    });

    it('renders with custom user data', () => {
        const userData = {
            firstName: 'Jane',
            lastName: 'Smith',
            profileImage: 'https://example.com/jane.jpg',
        };

        const wrapper = mount(UserAvatar, {
            props: {
                user: userData,
            },
        });

        // Should show image since mode is 'photo' by default
        const img = wrapper.find('img');
        expect(img.exists()).toBe(true);
        expect(img.attributes('src')).toBe('https://example.com/jane.jpg');
        expect(img.attributes('alt')).toBe('JS'); // Initials from getInitials
    });

    it('renders initials when mode is set to initials', () => {
        const userData = {
            firstName: 'Jane',
            lastName: 'Smith',
            profileImage: 'https://example.com/jane.jpg',
        };

        const wrapper = mount(UserAvatar, {
            props: {
                user: userData,
                mode: 'initials',
            },
        });

        // Should show initials since mode is 'initials'
        const span = wrapper.find('span');
        expect(span.exists()).toBe(true);
        expect(span.text()).toBe('JS'); // Initials from getInitials
    });

    it('renders initials when no profile image is available', () => {
        const userData = {
            firstName: 'Jane',
            lastName: 'Smith',
            // No profile image
        };

        const wrapper = mount(UserAvatar, {
            props: {
                user: userData,
            },
        });

        // Should show initials since there's no profile image
        const span = wrapper.find('span');
        expect(span.exists()).toBe(true);
        expect(span.text()).toBe('JS'); // Initials from getInitials
    });

    it('handles user data with userData property', () => {
        // Use type assertion to satisfy TypeScript
        const userWithUserData = {
            userData: {
                firstName: 'Jane',
                lastName: 'Smith',
                profileImage: 'https://example.com/jane.jpg',
            },
        } as any; // Type assertion to bypass type checking for this test

        const wrapper = mount(UserAvatar, {
            props: {
                user: userWithUserData,
            },
        });

        // Should show image since mode is 'photo' by default
        const img = wrapper.find('img');
        expect(img.exists()).toBe(true);
        expect(img.attributes('src')).toBe('https://example.com/jane.jpg');
    });

    it('handles missing first or last name gracefully', () => {
        const userData = {
            firstName: 'Jane',
            lastName: '', // Empty string instead of missing property
            profileImage: 'https://example.com/jane.jpg',
        };

        const wrapper = mount(UserAvatar, {
            props: {
                user: userData,
                mode: 'initials',
            },
        });

        // Should show only first initial
        const span = wrapper.find('span');
        expect(span.exists()).toBe(true);
        expect(span.text()).toBe('J'); // Only first initial
    });
});
