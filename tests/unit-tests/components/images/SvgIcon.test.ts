import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import SvgIcon from '@/components/images/SvgIcon.vue';
import { svgIconShapes } from '@/components/images/SvgIconShapes';

describe('SvgIcon', () => {
    it('renders with default props', () => {
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        expect(svg.attributes('width')).toBe('24');
        expect(svg.attributes('height')).toBe('24');
        expect(svg.attributes('viewBox')).toBe('0 0 24 24');
        expect(svg.attributes('fill')).toBe('none');
        expect(svg.attributes('class')).toBe('svg-icon');
    });

    it('renders with custom color', () => {
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
                color: 'red',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('style')).toBe('color: red;');
    });

    it('renders with custom width and height', () => {
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
                width: 32,
                height: 32,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('width')).toBe('32');
        expect(svg.attributes('height')).toBe('32');
    });

    it('renders with string width and height', () => {
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
                width: '32px',
                height: '32px',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('width')).toBe('32');
        expect(svg.attributes('height')).toBe('32');
    });

    it('renders with custom viewBox', () => {
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
                viewBox: '0 0 32 32',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('viewBox')).toBe('0 0 32 32');
    });

    it('renders different shapes', () => {
        // Test a few different shapes
        const shapesToTest = ['info', 'warning', 'error', 'success'];

        shapesToTest.forEach(shape => {
            const wrapper = mount(SvgIcon, {
                props: { shape },
            });

            const svg = wrapper.find('svg');
            expect(svg.exists()).toBe(true);

            // Get the SVG content and shape data
            const svgContent = svg.html();
            const shapeData = svgIconShapes.get(shape);

            // Instead of direct comparison, check for key elements that should be present
            // in both the SVG content and the shape data
            if (shapeData) {
                // Extract path data from shape data (looking for d="..." attributes)
                const pathDataMatches = shapeData.match(/d="([^"]+)"/g);
                if (pathDataMatches) {
                    // Check if at least one path data is present in the rendered SVG
                    const hasPathData = pathDataMatches.some(pathData => svgContent.includes(pathData.split('"')[1]));
                    expect(hasPathData).toBe(true);
                }
            }
        });
    });

    it('generates unique IDs for multiple instances', () => {
        const wrapper1 = mount(SvgIcon, {
            props: { shape: 'info' },
        });
        const wrapper2 = mount(SvgIcon, {
            props: { shape: 'info' },
        });

        const svg1 = wrapper1.find('svg');
        const svg2 = wrapper2.find('svg');

        const id1 = svg1.attributes('id');
        const id2 = svg2.attributes('id');

        expect(id1).not.toBe(id2);
        expect(id1).toMatch(/^svg-icon-\d+$/);
        expect(id2).toMatch(/^svg-icon-\d+$/);
    });

    it('handles null props gracefully', () => {
        // Instead of passing null values, we'll test with undefined values
        // which should trigger the default values in the component
        const wrapper = mount(SvgIcon, {
            props: {
                shape: 'info',
                color: null,
                width: undefined,
                height: undefined,
                viewBox: undefined,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        // Check that default values are used
        expect(svg.attributes('width')).toBe('24'); // Default width
        expect(svg.attributes('height')).toBe('24'); // Default height
        expect(svg.attributes('viewBox')).toBe('0 0 24 24'); // Default viewBox
        expect(svg.attributes('style')).toBeUndefined();
    });

    it('replaces SVG IDs in content with unique IDs', () => {
        const wrapper = mount(SvgIcon, {
            props: { shape: 'info' },
        });
        const svg = wrapper.find('svg');
        const id = svg.attributes('id');
        if (!id) {
            throw new Error('SVG ID should be defined');
        }
        const numericId = id.split('-')[2]; // Extract the numeric part of the ID

        // Check if the SVG content has been updated with the unique ID
        expect(svg.html()).toContain(`SVG_${numericId}_`);
    });
});
