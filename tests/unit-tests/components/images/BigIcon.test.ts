import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import BigIcon from '@/components/images/BigIcon.vue';
import type { TBigIconShapeKey } from '@/components/images/BigIconShapes';
import { bigIconShapes } from '@/components/images/BigIconShapes';

describe('BigIcon', () => {
    it('renders with default props', () => {
        const wrapper = mount(BigIcon, {
            props: {
                shape: 'document-things' as TBigIconShapeKey,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        expect(svg.attributes('width')).toBe('52');
        expect(svg.attributes('height')).toBe('52');
        expect(svg.attributes('viewBox')).toBe('0 0 52 52');
        expect(svg.attributes('fill')).toBe('none');
    });

    it('renders with custom width and height', () => {
        const wrapper = mount(BigIcon, {
            props: {
                shape: 'document-things' as TBigIconShapeKey,
                width: 100,
                height: 100,
            },
        });

        const svg = wrapper.find('svg');
        expect(svg.attributes('width')).toBe('100');
        expect(svg.attributes('height')).toBe('100');
    });

    it('renders with custom color', () => {
        const wrapper = mount(BigIcon, {
            props: {
                shape: 'document-things' as TBigIconShapeKey,
                color: 'red',
            },
        });

        const svg = wrapper.find('svg');
        expect(svg.attributes('style')).toBe('color: red;');
    });

    it('renders different shapes', () => {
        // Test a few different shapes
        const shapesToTest: TBigIconShapeKey[] = ['document-things', 'credit-card', 'hours'];

        shapesToTest.forEach(shape => {
            const wrapper = mount(BigIcon, {
                props: { shape },
            });

            const svg = wrapper.find('svg');
            expect(svg.exists()).toBe(true);

            // Verify the SVG content contains the shape's path data
            const svgContent = svg.html();
            const shapeData = bigIconShapes.get(shape);
            // Check if any path in the shape data is present in the SVG content
            const paths = shapeData?.split('"').filter(p => p.startsWith('M'));
            expect(paths?.some(path => svgContent.includes(path))).toBe(true);
        });
    });

    it('generates unique IDs for multiple instances', () => {
        const wrapper1 = mount(BigIcon, {
            props: { shape: 'document-things' as TBigIconShapeKey },
        });
        const wrapper2 = mount(BigIcon, {
            props: { shape: 'document-things' as TBigIconShapeKey },
        });

        const svg1 = wrapper1.find('svg');
        const svg2 = wrapper2.find('svg');

        const id1 = svg1.attributes('id');
        const id2 = svg2.attributes('id');

        expect(id1).not.toBe(id2);
        expect(id1).toMatch(/^svg-icon-\d+$/);
        expect(id2).toMatch(/^svg-icon-\d+$/);
    });

    it('handles invalid shape gracefully', () => {
        const wrapper = mount(BigIcon, {
            props: {
                shape: 'invalid-shape' as TBigIconShapeKey,
            },
        });

        const svg = wrapper.find('svg');
        expect(svg.exists()).toBe(true);
        expect(svg.html()).not.toContain('undefined');
    });
});
