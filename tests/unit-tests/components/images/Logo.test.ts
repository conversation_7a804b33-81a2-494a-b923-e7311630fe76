import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import Logo from '@/components/images/Logo.vue';

describe('Logo', () => {
    it('renders with default props (full variant)', () => {
        const wrapper = mount(Logo);
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        expect(svg.attributes('viewBox')).toBe('0 0 126 30');
        expect(svg.attributes('width')).toBe('126');
        expect(svg.attributes('height')).toBe('30');
        expect(svg.attributes('fill')).toBe('none');
        expect(svg.attributes('class')).toBe('logo');
    });

    it('renders brandmark variant', () => {
        const wrapper = mount(Logo, {
            props: {
                variant: 'brandmark',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        expect(svg.attributes('viewBox')).toBe('0 0 56 50');
        expect(svg.attributes('width')).toBe('56');
        expect(svg.attributes('height')).toBe('30');
        expect(svg.attributes('fill')).toBe('none');
        expect(svg.attributes('class')).toBe('logo');
    });

    it('renders with custom color', () => {
        const wrapper = mount(Logo, {
            props: {
                color: 'red',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('style')).toBe('color: red;');
    });

    it('renders with custom width and height', () => {
        const wrapper = mount(Logo, {
            props: {
                width: 200,
                height: 50,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('width')).toBe('200');
        expect(svg.attributes('height')).toBe('50');
    });

    it('renders brandmark with custom width and height', () => {
        const wrapper = mount(Logo, {
            props: {
                variant: 'brandmark',
                width: 100,
                height: 100,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('width')).toBe('100');
        expect(svg.attributes('height')).toBe('100');
    });

    it('renders with string width and height', () => {
        const wrapper = mount(Logo, {
            props: {
                width: '200px',
                height: '50px',
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.attributes('width')).toBe('200px');
        expect(svg.attributes('height')).toBe('50px');
    });

    it('contains correct SVG paths for full variant', () => {
        const wrapper = mount(Logo);
        const svg = wrapper.find('svg');
        const paths = svg.findAll('path');

        expect(paths.length).toBeGreaterThan(0);
        // Check for some key path elements that should be present
        expect(svg.html()).toContain('fill-rule="evenodd"');
        expect(svg.html()).toContain('clip-rule="evenodd"');
    });

    it('contains correct SVG paths for brandmark variant', () => {
        const wrapper = mount(Logo, {
            props: {
                variant: 'brandmark',
            },
        });
        const svg = wrapper.find('svg');
        const paths = svg.findAll('path');

        expect(paths.length).toBeGreaterThan(0);
        // Check for some key path elements that should be present
        expect(svg.html()).toContain('fill-rule="evenodd"');
        expect(svg.html()).toContain('clip-rule="evenodd"');
    });

    it('handles null props gracefully', () => {
        const wrapper = mount(Logo, {
            props: {
                variant: null,
                color: null,
                width: null,
                height: null,
            },
        });
        const svg = wrapper.find('svg');

        expect(svg.exists()).toBe(true);
        expect(svg.attributes('width')).toBe('126'); // Default for full variant
        expect(svg.attributes('height')).toBe('30');
        expect(svg.attributes('style')).toBeUndefined();
    });
});
