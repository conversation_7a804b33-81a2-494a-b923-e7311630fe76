import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import LineChart from '@/components/dashboard/LineChart.vue';
import Chart from 'chart.js/auto';
import type { ChartData, Point, ChartOptions, LegendOptions } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { colors, getAxisFormatter } from '@/components/dashboard/chartjs';
import type { ComponentPublicInstance } from 'vue';
import type { AxisFormat } from '@/components/dashboard/chartjs';

// Mock Chart.js
vi.mock('chart.js/auto', () => ({
    default: {
        register: vi.fn(),
        defaults: {
            plugins: {},
        },
        constructor: vi.fn(),
    },
}));

interface LineChartProps {
    data: ChartData<'line', (number | Point | null)[], unknown>;
    legend?: boolean | LegendOptions<'line'>;
    showPoints?: boolean;
    showLabels?: boolean;
    showGrid?: boolean;
    min?: number;
    max?: number;
    yLabelFormat?: AxisFormat | null;
    xLabelFormat?: AxisFormat | null;
    xAxis?: boolean | string;
    yAxis?: boolean | string;
    curved?: boolean;
}

interface ChartConfiguration {
    type: 'line';
    data: ChartData<'line', (number | Point | null)[], unknown>;
    options: ChartOptions<'line'> & {
        datasets?: {
            line?: {
                tension?: number;
            };
        };
        elements?: {
            point?: {
                pointStyle?: boolean | string;
            };
        };
        scales?: {
            x?: {
                ticks?: {
                    display?: boolean;
                };
            };
            y?: {
                suggestedMin?: number;
                suggestedMax?: number;
                beginAtZero?: boolean;
                ticks?: {
                    display?: boolean;
                };
            };
        };
    };
}

interface LineChartInstance extends ComponentPublicInstance {
    elementId: string;
    mappedDatasets: any[];
    chartProps: ChartConfiguration;
    yAxisCallback: { callback: (value: number) => string };
    xAxisCallback: { callback: (value: string) => string };
    $props: LineChartProps;
}

type LineChartWrapper = VueWrapper<LineChartInstance>;

describe('LineChart', () => {
    let wrapper: LineChartWrapper;
    const mockData: ChartData<'line', (number | Point | null)[], unknown> = {
        labels: ['Jan', 'Feb', 'Mar'],
        datasets: [
            {
                label: 'Dataset 1',
                data: [10, 20, 30],
            },
        ],
    };

    beforeEach(() => {
        wrapper = mount(LineChart, {
            props: {
                data: mockData,
                legend: true,
            },
        }) as unknown as LineChartWrapper;
    });

    it('renders the chart element', () => {
        const elementId = wrapper.vm.elementId;
        expect(wrapper.find(`#${elementId}`).exists()).toBe(true);
    });

    it('initializes with correct props', () => {
        expect(wrapper.vm.$props.data).toEqual(mockData);
        expect(wrapper.vm.$props.legend).toBe(true);
    });

    it('updates chart when data changes', async () => {
        const newData = {
            labels: ['Apr', 'May', 'Jun'],
            datasets: [
                {
                    label: 'Dataset 1',
                    data: [40, 50, 60],
                },
            ],
        };
        await wrapper.setProps({ data: newData });
        expect(wrapper.vm.$props.data).toEqual(newData);
    });

    it('handles legend prop changes', async () => {
        await wrapper.setProps({ legend: false });
        expect(wrapper.vm.$props.legend).toBe(false);
    });

    it('handles show points prop', async () => {
        await wrapper.setProps({ showPoints: true });
        expect(wrapper.vm.$props.showPoints).toBe(true);
    });

    it('handles show labels prop', async () => {
        await wrapper.setProps({ showLabels: true });
        expect(wrapper.vm.chartProps.options.plugins?.datalabels?.display).toBe('auto');
    });

    it('renders the line chart container', () => {
        expect(wrapper.find('.line-chart').exists()).toBe(true);
    });

    it('renders a canvas element with correct id', () => {
        const elementId = wrapper.vm.elementId;
        expect(wrapper.find(`#${elementId}`).exists()).toBe(true);
    });

    it('generates unique element IDs for multiple instances', () => {
        const wrapper2 = mount(LineChart, {
            props: {
                data: mockData,
            },
        }) as unknown as LineChartWrapper;
        expect(wrapper.vm.elementId).not.toBe(wrapper2.vm.elementId);
    });

    it('maps datasets with correct colors', () => {
        const mappedDatasets = wrapper.vm.mappedDatasets;
        expect(mappedDatasets[0]?.borderColor).toBe(colors[0]);
    });

    it('computes correct chart configuration', () => {
        const chartConfig = wrapper.vm.chartProps;
        expect(chartConfig.type).toBe('line');
        expect(chartConfig.data.labels).toEqual(mockData.labels);
        expect(chartConfig.data.datasets[0].data).toEqual(mockData.datasets[0].data);
        expect(chartConfig.data.datasets[0].label).toBe(mockData.datasets[0].label);
    });

    it('handles axis formatting correctly', async () => {
        await wrapper.setProps({
            yLabelFormat: 'currency',
            xLabelFormat: 'percent',
        });

        const yAxisCallback = wrapper.vm.yAxisCallback;
        const xAxisCallback = wrapper.vm.xAxisCallback;

        expect(yAxisCallback.callback).toBe(getAxisFormatter('currency'));
        expect(xAxisCallback.callback).toBe(getAxisFormatter('percent'));
    });

    it('handles curved lines configuration', async () => {
        // Default (false)
        expect(wrapper.vm.chartProps.options.datasets?.line?.tension).toBe(0);

        // Set to true
        await wrapper.setProps({ curved: true });
        expect(wrapper.vm.chartProps.options.datasets?.line?.tension).toBeGreaterThan(0);
    });

    it('handles point display configuration', async () => {
        // Default (false)
        expect(wrapper.vm.chartProps.options.elements?.point?.pointStyle).toBe(false);

        // Set to true
        await wrapper.setProps({ showPoints: true });
        expect(wrapper.vm.chartProps.options.elements?.point?.pointStyle).toBe('circle');
    });

    it('handles axis display configuration', async () => {
        // Test hiding both axes
        await wrapper.setProps({ xAxis: false, yAxis: false });
        expect(wrapper.vm.chartProps.options.scales?.x?.ticks?.display).toBe(false);
        expect(wrapper.vm.chartProps.options.scales?.y?.ticks?.display).toBe(false);

        // Test showing both axes
        await wrapper.setProps({ xAxis: true, yAxis: true });
        expect(wrapper.vm.chartProps.options.scales?.x?.ticks?.display).toBe(true);
        expect(wrapper.vm.chartProps.options.scales?.y?.ticks?.display).toBe(true);
    });

    it('handles min/max configuration', async () => {
        await wrapper.setProps({ min: 0, max: 100 });
        expect(wrapper.vm.chartProps.options.scales?.y?.suggestedMin).toBe(0);
        expect(wrapper.vm.chartProps.options.scales?.y?.suggestedMax).toBe(100);
    });
});
