import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import TreeChart from '@/components/dashboard/TreeChart.vue';
import Chart from 'chart.js/auto';
import { TreemapController, TreemapElement } from 'chartjs-chart-treemap';
import type { ComponentPublicInstance } from 'vue';
import type { ChartConfiguration } from 'chart.js';

// Mock Chart.js
vi.mock('chart.js/auto', () => {
    const mockRegister = vi.fn();
    const mockChart = vi.fn().mockImplementation(() => ({
        destroy: vi.fn(),
        update: vi.fn(),
    }));

    // Create a constructor function that can be used with 'new'
    const ChartConstructor = function (ctx: any, config: any) {
        return mockChart(ctx, config);
    };

    // Add static methods to the constructor
    ChartConstructor.register = mockRegister;
    ChartConstructor.defaults = {
        plugins: {},
    };

    // Make sure register is available on both the constructor and the default export
    const defaultExport = ChartConstructor;
    defaultExport.register = mockRegister;
    defaultExport.defaults = {
        plugins: {},
    };

    return {
        default: defaultExport,
        Chart: ChartConstructor,
        register: mockRegister,
        defaults: {
            plugins: {},
        },
    };
});

type TreeChartInstance = ComponentPublicInstance & {
    elementId: string;
    chartProps: ChartConfiguration<'treemap'>;
};

describe('TreeChart', () => {
    let wrapper: VueWrapper<TreeChartInstance>;
    const mockData = {
        datasets: [
            {
                data: [], // Required by ChartDataset
                key: 'value', // The property to use for values
                groups: ['category'], // The grouping hierarchy
                tree: [
                    {
                        category: 'Category 1',
                        value: 100,
                    },
                    {
                        category: 'Category 2',
                        value: 200,
                    },
                ],
            },
        ],
    };

    beforeEach(() => {
        wrapper = mount(TreeChart, {
            props: {
                data: mockData,
                max: 300,
            },
        }) as unknown as VueWrapper<TreeChartInstance>;
    });

    it('renders the tree chart container', () => {
        expect(wrapper.find('.tree-chart').exists()).toBe(true);
    });

    it('renders a canvas element with correct id', () => {
        const elementId = (wrapper.vm as TreeChartInstance).elementId;
        expect(wrapper.find(`#${elementId}`).exists()).toBe(true);
    });

    it('generates a unique element id for each instance', () => {
        const wrapper2 = mount(TreeChart, {
            props: {
                data: mockData,
                max: 300,
            },
        }) as unknown as VueWrapper<TreeChartInstance>;
        expect((wrapper.vm as TreeChartInstance).elementId).not.toBe((wrapper2.vm as TreeChartInstance).elementId);
    });

    it('registers TreemapController and TreemapElement on mount', () => {
        expect(Chart.register).toHaveBeenCalledWith(TreemapController, TreemapElement);
    });

    it('computes correct chart configuration', () => {
        const chartConfig = (wrapper.vm as TreeChartInstance).chartProps;
        expect(chartConfig.type).toBe('treemap');
        expect(chartConfig.data).toEqual(mockData);
        expect(chartConfig.options?.responsive).toBe(true);
        expect(chartConfig.options?.maintainAspectRatio).toBe(false);
        expect(chartConfig.options?.plugins?.legend?.display).toBe(false);
    });

    it('applies correct styling to the chart container', () => {
        const container = wrapper.find('.tree-chart');
        expect(container.classes()).toContain('tree-chart');
    });
});
