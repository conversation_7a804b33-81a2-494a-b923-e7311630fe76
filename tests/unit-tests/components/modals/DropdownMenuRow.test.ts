import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import DropdownMenuRow from '@/components/modals/DropdownMenuRow.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';

// Mock the SvgIcon component
vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        props: ['shape', 'width', 'height'],
        template: '<div class="mock-svg-icon"></div>',
    },
}));

describe('DropdownMenuRow', () => {
    it('renders with label prop', () => {
        const label = 'Test Label';
        const wrapper = mount(DropdownMenuRow, {
            props: { label },
        });

        // Check if label is rendered
        const labelElement = wrapper.find('.dropdown-menu-row-label');
        expect(labelElement.exists()).toBe(true);
        expect(labelElement.text()).toBe(label);
    });

    it('renders with icon shape prop', () => {
        const iconShape = 'edit';
        const wrapper = mount(DropdownMenuRow, {
            props: { iconShape },
        });

        // Check if icon container exists
        const iconContainer = wrapper.find('.dropdown-menu-row-icon');
        expect(iconContainer.exists()).toBe(true);

        // Check if SvgIcon is rendered with correct props
        const svgIcon = wrapper.findComponent(SvgIcon);
        expect(svgIcon.exists()).toBe(true);
        expect(svgIcon.props('shape')).toBe(iconShape);
        expect(svgIcon.props('width')).toBe(22);
        expect(svgIcon.props('height')).toBe(22);
    });

    it('renders slot content when provided', () => {
        const wrapper = mount(DropdownMenuRow, {
            slots: {
                default: '<span class="test-slot">Slot Content</span>',
            },
        });

        // Check if slot content is rendered
        const slotContent = wrapper.find('.test-slot');
        expect(slotContent.exists()).toBe(true);
        expect(slotContent.text()).toBe('Slot Content');
    });

    it('applies correct styling classes', () => {
        const wrapper = mount(DropdownMenuRow);

        // Check if the button has the correct class
        const button = wrapper.find('.dropdown-menu-row');
        expect(button.classes()).toContain('dropdown-menu-row');

        // Check if the button is a button element
        expect(button.element.tagName).toBe('BUTTON');
    });

    it('emits click event when clicked', async () => {
        const wrapper = mount(DropdownMenuRow);

        // Click the button
        await wrapper.find('.dropdown-menu-row').trigger('click');

        // Check if click event was emitted
        expect(wrapper.emitted('click')).toBeTruthy();
    });
});
