import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import DropdownMenu from '@/components/modals/DropdownMenu.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';
import PopoverModal from '@/components/modals/PopoverModal.vue';
import PopoverModalContent from '@/components/modals/PopoverModalContent.vue';

// Mock the child components
vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        props: ['shape', 'width', 'height'],
        template: '<div class="mock-svg-icon"></div>',
    },
}));

vi.mock('@/components/modals/PopoverModal.vue', () => ({
    default: {
        name: 'PopoverModal',
        props: ['isOpen'],
        template: '<div class="mock-popover-modal"><slot v-if="isOpen"></slot></div>',
    },
}));

vi.mock('@/components/modals/PopoverModalContent.vue', () => ({
    default: {
        name: 'PopoverModalContent',
        template: '<div class="mock-popover-modal-content"><slot></slot></div>',
    },
}));

describe('DropdownMenu', () => {
    it('renders with default props', () => {
        const wrapper = mount(DropdownMenu);

        // Check if the button exists
        const button = wrapper.find('.dropdown-menu-button');
        expect(button.exists()).toBe(true);

        // Check if SvgIcon is rendered with default props
        const svgIcon = wrapper.findComponent(SvgIcon);
        expect(svgIcon.exists()).toBe(true);
        expect(svgIcon.props('shape')).toBe('dot-menu');
        expect(svgIcon.props('width')).toBe(32);
        expect(svgIcon.props('height')).toBe(32);

        // Check if PopoverModal is rendered
        const popoverModal = wrapper.findComponent(PopoverModal);
        expect(popoverModal.exists()).toBe(true);
        expect(popoverModal.props('isOpen')).toBe(false);
    });

    it('renders with custom props', () => {
        const wrapper = mount(DropdownMenu, {
            props: {
                iconShape: 'edit',
                iconSize: 24,
            },
        });

        // Check if SvgIcon is rendered with custom props
        const svgIcon = wrapper.findComponent(SvgIcon);
        expect(svgIcon.props('shape')).toBe('edit');
        expect(svgIcon.props('width')).toBe(24);
        expect(svgIcon.props('height')).toBe(24);
    });

    it('toggles dropdown when button is clicked', async () => {
        const wrapper = mount(DropdownMenu);
        const button = wrapper.find('.dropdown-menu-button');

        // Initial state
        expect(wrapper.findComponent(PopoverModal).props('isOpen')).toBe(false);

        // Click button
        await button.trigger('click');
        expect(wrapper.findComponent(PopoverModal).props('isOpen')).toBe(true);

        // Click button again
        await button.trigger('click');
        expect(wrapper.findComponent(PopoverModal).props('isOpen')).toBe(false);
    });

    it('renders slot content when dropdown is open', async () => {
        const wrapper = mount(DropdownMenu, {
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        // Initially closed
        expect(wrapper.find('.test-content').exists()).toBe(false);

        // Open dropdown
        await wrapper.find('.dropdown-menu-button').trigger('click');

        // Content should be visible
        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').text()).toBe('Test Content');
    });

    it('closes dropdown when PopoverModal emits close event', async () => {
        const wrapper = mount(DropdownMenu);

        // Open dropdown
        await wrapper.find('.dropdown-menu-button').trigger('click');
        expect(wrapper.findComponent(PopoverModal).props('isOpen')).toBe(true);

        // Emit close event
        await wrapper.findComponent(PopoverModal).vm.$emit('close');

        // Dropdown should be closed
        expect(wrapper.findComponent(PopoverModal).props('isOpen')).toBe(false);
    });
});
