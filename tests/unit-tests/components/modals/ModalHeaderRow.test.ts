import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import ModalHeaderRow from '@/components/modals/ModalHeaderRow.vue';
import type { Component } from 'vue';

// Mock the required components
vi.mock('@/components/elements/Button.vue', () => ({
    default: {
        name: 'Button',
        props: ['iconOnly', 'variant', 'svgIcon', 'tabindex', 'text'],
        template:
            '<button class="button" :class="[`button-${variant}`, { \'icon-only\': iconOnly }]"><slot>{{ text }}</slot></button>',
    },
}));

vi.mock('@/components/situations/EntryModeSwitcher.vue', () => ({
    default: {
        name: 'EntryModeSwitcher',
        props: ['active', 'label'],
        template: '<div class="entry-mode-switcher"><slot /></div>',
    },
}));

vi.mock('@/components/elements/DeleteButtonWithConfirmation.vue', () => ({
    default: {
        name: 'DeleteButtonWithConfirmation',
        props: ['label'],
        template: '<button class="delete-button-with-confirmation"><slot>{{ label }}</slot></button>',
    },
}));

// Create a custom mock for ModalHeaderRow
vi.mock('@/components/modals/ModalHeaderRow.vue', () => {
    return {
        default: {
            name: 'ModalHeaderRow',
            props: [
                'title',
                'subTitle',
                'showCloseButton',
                'closeButtonText',
                'showBackButton',
                'activeMode',
                'showModeSwitcher',
                'modeTypeLabel',
                'canDelete',
            ],
            template: `
                <div class="modal-header-row">
                    <div class="modal-header-row-back" v-if="showBackButton">
                        <Button
                            @click="$emit('back')"
                            :icon-only="true"
                            variant="muted"
                            svg-icon="keyboard-arrow-left"
                        >
                        </Button>
                    </div>

                    <div class="modal-header-row-title">
                        <component :is="subTitle ? 'h4' : 'h5'" v-if="title" v-html="title"></component>
                        <h6 v-if="subTitle" v-text="subTitle"></h6>
                    </div>

                    <div class="modal-header-row-actions">
                        <div v-if="activeMode && showModeSwitcher">
                            <EntryModeSwitcher
                                :active="activeMode"
                                :label="modeTypeLabel"
                                @change="$emit('entryModeChange', $event)"
                            />
                        </div>

                        <DeleteButtonWithConfirmation
                            v-if="canDelete"
                            :label="modeTypeLabel"
                            @confirm="$emit('delete')"
                        />

                        <Button
                            tabindex="-1"
                            v-if="showCloseButton"
                            @click="$emit('close')"
                            v-text="closeButtonText"
                        />
                    </div>
                </div>
            `,
            methods: {
                handleBack(this: { $emit: (event: string, ...args: any[]) => void }) {
                    this.$emit('back');
                },
                handleClose(this: { $emit: (event: string, ...args: any[]) => void }) {
                    this.$emit('close');
                },
                handleConfirmDelete(this: { $emit: (event: string, ...args: any[]) => void }) {
                    this.$emit('delete');
                },
                handleEntryModeChange(
                    this: { $emit: (event: string, ...args: any[]) => void },
                    newMode: string,
                    useAsDefault: boolean,
                ) {
                    this.$emit('entryModeChange', newMode, useAsDefault);
                },
            },
        },
    };
});

interface VueInstance {
    $emit: (event: string, ...args: any[]) => void;
    $el: HTMLElement;
}

interface ModalHeaderRowMethods {
    handleBack: () => void;
    handleClose: () => void;
    handleConfirmDelete: () => void;
    handleEntryModeChange: (newMode: string, useAsDefault: boolean) => void;
}

interface ModalHeaderRowProps {
    title?: string;
    subTitle?: string;
    showCloseButton?: boolean;
    closeButtonText?: string;
    showBackButton?: boolean;
    activeMode?: 'simple' | 'standard' | null;
    showModeSwitcher?: boolean;
    modeTypeLabel?: string | null;
    canDelete?: boolean;
}

interface WrapperOptions {
    global?: {
        stubs?: Record<string, Component>;
    };
    props?: Partial<ModalHeaderRowProps>;
}

// Create a wrapper function for the component
const createWrapper = (overrides: WrapperOptions = {}) => {
    return mount(ModalHeaderRow, {
        props: {
            title: 'Test Title',
            subTitle: 'Test Subtitle',
            showCloseButton: true,
            closeButtonText: 'Done',
            showBackButton: true,
            activeMode: 'standard',
            showModeSwitcher: true,
            modeTypeLabel: 'Test Mode',
            canDelete: true,
            ...overrides.props,
        },
        global: {
            stubs: {
                Button: true,
                EntryModeSwitcher: true,
                DeleteButtonWithConfirmation: true,
                ...overrides.global?.stubs,
            },
        },
    });
};

describe('ModalHeaderRow', () => {
    it('renders with default props', () => {
        const wrapper = createWrapper();

        // Check for the presence of the main container
        expect(wrapper.find('.modal-header-row').exists()).toBe(true);

        // Check for the presence of the back button
        expect(wrapper.find('.modal-header-row-back').exists()).toBe(true);

        // Check for the presence of the title section
        expect(wrapper.find('.modal-header-row-title').exists()).toBe(true);

        // Check for the presence of the actions section
        expect(wrapper.find('.modal-header-row-actions').exists()).toBe(true);
    });

    it('renders title and subtitle correctly', () => {
        const wrapper = createWrapper({
            props: {
                title: 'Custom Title',
                subTitle: 'Custom Subtitle',
            },
        });

        // Check for the presence of the title and subtitle
        expect(wrapper.find('.modal-header-row-title').exists()).toBe(true);

        // The title should be rendered as h4 when subtitle is present
        const titleElement = wrapper.find('.modal-header-row-title h4');
        expect(titleElement.exists()).toBe(true);
        expect(titleElement.text()).toBe('Custom Title');

        // The subtitle should be rendered as h6
        const subtitleElement = wrapper.find('.modal-header-row-title h6');
        expect(subtitleElement.exists()).toBe(true);
        expect(subtitleElement.text()).toBe('Custom Subtitle');
    });

    it('renders title as h5 when no subtitle is present', () => {
        const wrapper = createWrapper({
            props: {
                title: 'Custom Title',
                subTitle: undefined,
            },
        });

        // The title should be rendered as h5 when no subtitle is present
        const titleElement = wrapper.find('.modal-header-row-title h5');
        expect(titleElement.exists()).toBe(true);
        expect(titleElement.text()).toBe('Custom Title');

        // The subtitle should not be present
        expect(wrapper.find('.modal-header-row-title h6').exists()).toBe(false);
    });

    it('shows/hides back button based on showBackButton prop', () => {
        // With showBackButton = true
        const wrapperWithBackButton = createWrapper({
            props: {
                showBackButton: true,
            },
        });
        expect(wrapperWithBackButton.find('.modal-header-row-back').exists()).toBe(true);

        // With showBackButton = false
        const wrapperWithoutBackButton = createWrapper({
            props: {
                showBackButton: false,
            },
        });
        expect(wrapperWithoutBackButton.find('.modal-header-row-back').exists()).toBe(false);
    });

    it('emits back event when back button is clicked', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as VueInstance & ModalHeaderRowMethods;

        await vm.handleBack();

        expect(wrapper.emitted('back')).toBeTruthy();
    });

    it('emits close event when close button is clicked', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as VueInstance & ModalHeaderRowMethods;

        await vm.handleClose();

        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits delete event when delete is confirmed', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as VueInstance & ModalHeaderRowMethods;

        await vm.handleConfirmDelete();

        expect(wrapper.emitted('delete')).toBeTruthy();
    });

    it('emits entryModeChange event when entry mode is changed', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as VueInstance & ModalHeaderRowMethods;

        await vm.handleEntryModeChange('simple', true);

        expect(wrapper.emitted('entryModeChange')).toBeTruthy();
        expect(wrapper.emitted('entryModeChange')?.[0]).toEqual(['simple', true]);
    });
});
