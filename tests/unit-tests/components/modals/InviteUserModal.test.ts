import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import InviteUserModal from '@/components/modals/InviteUserModal.vue';
import type { Component } from 'vue';

interface WrapperOptions {
    global?: {
        stubs?: Record<string, Component>;
    };
}

const globalStubs = {
    Button: {
        template: '<button class="mock-button" @click="$emit(\'click\', $event)"><slot></slot></button>',
        props: ['variant'],
    },
    SvgIcon: {
        template: '<div class="mock-svg-icon" :data-shape="shape"></div>',
        props: ['shape', 'width', 'height'],
    },
    TextInput: {
        template:
            '<input type="text" :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" />',
        props: ['modelValue', 'label', 'size'],
        emits: ['update:modelValue'],
    },
};

const createWrapper = (overrides: WrapperOptions = {}) =>
    mount(InviteUserModal, {
        global: {
            stubs: {
                ...globalStubs,
                ...overrides.global?.stubs,
            },
            ...overrides.global,
        },
    });

describe('InviteUserModal', () => {
    it('renders the modal with correct structure', () => {
        const wrapper = createWrapper();

        expect(wrapper.find('.invite-user-modal').exists()).toBe(true);
        expect(wrapper.find('.invite-user-modal-header').exists()).toBe(true);
        expect(wrapper.find('.invite-user-modal-header span').text()).toBe('Invite user');
        expect(wrapper.find('.invite-user-modal-form').exists()).toBe(true);
        expect(wrapper.find('.invite-user-modal-form-primary').exists()).toBe(true);
        expect(wrapper.find('.invite-user-modal-actions').exists()).toBe(true);
    });

    it('renders all form inputs for primary user', () => {
        const wrapper = createWrapper();

        const inputs = wrapper.findAllComponents(globalStubs.TextInput);
        expect(inputs).toHaveLength(4);

        const labels = inputs.map(input => input.props('label'));
        expect(labels).toEqual(['First name', 'Last name', 'Email', 'Phone (optional)']);
    });

    it('renders action buttons with correct text and variants', () => {
        const wrapper = createWrapper();

        const buttons = wrapper.findAllComponents(globalStubs.Button);
        expect(buttons).toHaveLength(2);

        const [cancelButton, sendButton] = buttons;
        expect(cancelButton.text()).toBe('Cancel');
        expect(cancelButton.props('variant')).toBe('muted');
        expect(sendButton.text()).toBe('Send Invite');
        expect(sendButton.props('variant')).toBe('default');
    });

    it('emits close event when cancel button is clicked', async () => {
        const wrapper = createWrapper();

        const cancelButton = wrapper.findAllComponents(globalStubs.Button)[0];
        await cancelButton.trigger('click');

        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits send event with form data when send button is clicked', async () => {
        const wrapper = createWrapper();

        // Fill in the form
        const inputs = wrapper.findAllComponents(globalStubs.TextInput);
        await inputs[0].vm.$emit('update:modelValue', 'John');
        await inputs[1].vm.$emit('update:modelValue', 'Doe');
        await inputs[2].vm.$emit('update:modelValue', '<EMAIL>');
        await inputs[3].vm.$emit('update:modelValue', '1234567890');

        // Click send button
        const sendButton = wrapper.findAllComponents(globalStubs.Button)[1];
        await sendButton.trigger('click');

        const emitted = wrapper.emitted('send');
        expect(emitted).toBeTruthy();
        expect(emitted![0][0]).toEqual({
            primary: {
                firstName: 'John',
                lastName: 'Doe',
                email: '<EMAIL>',
                phone: '1234567890',
            },
            secondary: {
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
            },
        });
    });

    it('handles empty form submission', async () => {
        const wrapper = createWrapper();

        const sendButton = wrapper.findAllComponents(globalStubs.Button)[1];
        await sendButton.trigger('click');

        const emitted = wrapper.emitted('send');
        expect(emitted).toBeTruthy();
        expect(emitted![0][0]).toEqual({
            primary: {
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
            },
            secondary: {
                firstName: '',
                lastName: '',
                email: '',
                phone: '',
            },
        });
    });

    it('emits close event when close icon is clicked', async () => {
        const wrapper = createWrapper();

        const closeButton = wrapper.find('.popover-modal-close');
        await closeButton.trigger('click');

        expect(wrapper.emitted('close')).toBeTruthy();
    });
});
