import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import SubModal from '@/components/modals/SubModal.vue';

// Mock child components
vi.mock('@/components/modals/Modal.vue', () => ({
    default: {
        name: 'Modal',
        template: '<div class="modal"><slot /></div>',
        props: ['isOpen'],
        emits: ['close'],
    },
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        template: '<div class="svg-icon"><slot /></div>',
        props: ['shape', 'width', 'height'],
    },
}));

vi.mock('@/components/elements/Button.vue', () => ({
    default: {
        name: 'Button',
        template: '<button class="button" :class="variant"><slot /></button>',
        props: ['variant'],
    },
}));

describe('SubModal', () => {
    it('renders with default props', () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
            },
        });

        expect(wrapper.find('.sub-modal').exists()).toBe(true);
        expect(wrapper.find('.sub-modal-header-title-container-title').text()).toBe('Test Modal');
        expect(wrapper.find('.sub-modal-footer').exists()).toBe(true);
        expect(wrapper.find('.sub-modal-footer .button:last-child').text()).toBe('Confirm');
    });

    it('renders without footer when showFooter is false', () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
                showFooter: false,
            },
        });

        expect(wrapper.find('.sub-modal-footer').exists()).toBe(false);
    });

    it('renders with custom confirm text', () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
                confirmText: 'Save',
            },
        });

        expect(wrapper.find('.sub-modal-footer .button:last-child').text()).toBe('Save');
    });

    it('emits close event when close button is clicked', async () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
            },
        });

        await wrapper.find('.sub-modal-header-buttons .svg-icon').trigger('click');
        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits close event when cancel button is clicked', async () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
            },
        });

        await wrapper.find('.sub-modal-footer .button:first-child').trigger('click');
        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits confirm event when confirm button is clicked', async () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
            },
        });

        await wrapper.find('.sub-modal-footer .button:last-child').trigger('click');
        expect(wrapper.emitted('confirm')).toBeTruthy();
    });

    it('renders slot content', () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: 'Test Modal',
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').text()).toBe('Test Content');
    });

    it('renders title with HTML content', () => {
        const wrapper = mount(SubModal, {
            props: {
                isOpen: true,
                title: '<span class="test-title">Test <strong>Modal</strong></span>',
            },
        });

        const titleElement = wrapper.find('.sub-modal-header-title-container-title');
        expect(titleElement.html()).toContain('<span class="test-title">Test <strong>Modal</strong></span>');
    });
});
