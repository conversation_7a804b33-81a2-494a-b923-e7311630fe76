import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import PopoverModalContent from '@/components/modals/PopoverModalContent.vue';

describe('PopoverModalContent', () => {
    it('renders default slot content', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').text()).toBe('Test Content');
    });

    it('renders header slot when provided', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                header: '<div class="test-header">Header Content</div>',
                default: '<div>Default Content</div>',
            },
        });

        expect(wrapper.find('.popover-modal-content-header').exists()).toBe(true);
        expect(wrapper.find('.test-header').text()).toBe('Header Content');
    });

    it('does not render header slot when not provided', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                default: '<div>Default Content</div>',
            },
        });

        expect(wrapper.find('.popover-modal-content-header').exists()).toBe(false);
    });

    it('renders footer slot when provided', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                footer: '<div class="test-footer">Footer Content</div>',
                default: '<div>Default Content</div>',
            },
        });

        expect(wrapper.find('.popover-modal-content-footer').exists()).toBe(true);
        expect(wrapper.find('.test-footer').text()).toBe('Footer Content');
    });

    it('does not render footer slot when not provided', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                default: '<div>Default Content</div>',
            },
        });

        expect(wrapper.find('.popover-modal-content-footer').exists()).toBe(false);
    });

    it('renders all slots when provided', () => {
        const wrapper = mount(PopoverModalContent, {
            slots: {
                header: '<div class="test-header">Header Content</div>',
                default: '<div class="test-content">Default Content</div>',
                footer: '<div class="test-footer">Footer Content</div>',
            },
        });

        expect(wrapper.find('.popover-modal-content-header').exists()).toBe(true);
        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-content-footer').exists()).toBe(true);
    });
});
