import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import type { Component } from 'vue';
import * as vueHooks from '../../mocks/vue';
import { h } from 'vue';

// Mock Vue's lifecycle hooks - this must be hoisted to the top
vi.mock('vue', async (importOriginal: () => Promise<any>) => {
    const actual = await importOriginal();
    return {
        ...actual,
        onMounted: vueHooks.onMounted,
        onUnmounted: vueHooks.onUnmounted,
        onBeforeMount: vueHooks.onBeforeMount,
        onBeforeUnmount: vueHooks.onBeforeUnmount,
        onUpdated: vueHooks.onUpdated,
        onBeforeUpdate: vueHooks.onBeforeUpdate,
        onErrorCaptured: vueHooks.onErrorCaptured,
        onActivated: vueHooks.onActivated,
        onDeactivated: vueHooks.onDeactivated,
        ref: vueHooks.ref,
        computed: vueHooks.computed,
        watch: vueHooks.watch,
        watchEffect: vueHooks.watchEffect,
        nextTick: vueHooks.nextTick,
        getCurrentInstance: vueHooks.getCurrentInstance,
        useTemplateRef: vi.fn(() => ({ value: null })),
    };
});

// Mock @floating-ui/vue
vi.mock('@floating-ui/vue', () => ({
    useFloating: vi.fn(() => ({
        floatingStyles: vueHooks.ref({}),
    })),
    autoUpdate: vi.fn(() => {}),
    autoPlacement: vi.fn(() => {}),
    flip: vi.fn(() => {}),
    offset: vi.fn(() => {}),
    shift: vi.fn(() => {}),
    size: vi.fn(() => {}),
}));

// Mock @vueuse/core
vi.mock('@vueuse/core', () => ({
    onClickOutside: vi.fn((el, callback) => {
        // Simulate click outside by calling the callback
        setTimeout(() => callback(), 0);
        return () => {};
    }),
    onKeyStroke: vi.fn((key, callback) => {
        // Simulate key press by calling the callback
        setTimeout(() => callback(), 0);
        return () => {};
    }),
}));

// Import the component after mocking Vue
import PopoverModal from '@/components/modals/PopoverModal.vue';

// Helper types
interface VueInstance {
    $el: HTMLElement;
}
interface WrapperOptions {
    props?: {
        isOpen?: boolean;
        triggerElement?: any;
        withOverlay?: boolean;
        dynamicPlacement?: boolean;
    };
    slots?: {
        default?: string;
    };
}

// Create a wrapper function
const createWrapper = (options: WrapperOptions = {}) => {
    return mount(PopoverModal, {
        props: {
            isOpen: true,
            withOverlay: true,
            dynamicPlacement: true,
            ...options.props,
        },
        slots: {
            default: options.slots?.default || '<div>Test content</div>',
        },
    });
};

describe('PopoverModal', () => {
    beforeEach(() => {
        vi.clearAllMocks();
        // Mock document.getElementById with proper typing
        document.getElementById = vi.fn((id: string) => {
            const element = document.createElement('div');
            element.id = id;
            return element;
        });
    });

    it('renders with default props', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.popover-modal').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-dialog').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-overlay').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-outer-wrapper').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-inner-wrapper').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-panel').exists()).toBe(true);
        expect(wrapper.find('.popover-modal-dynamic').exists()).toBe(true);
    });

    it('renders without overlay when withOverlay is false', async () => {
        const wrapper = createWrapper({
            props: {
                withOverlay: false,
            },
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.popover-modal-overlay').exists()).toBe(false);
    });

    it('renders with static placement when dynamicPlacement is false', async () => {
        const wrapper = createWrapper({
            props: {
                dynamicPlacement: false,
            },
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.popover-modal-static').exists()).toBe(true);
    });

    it('does not render content when isOpen is false', async () => {
        const wrapper = createWrapper({
            props: {
                isOpen: false,
            },
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.popover-modal-overlay').exists()).toBe(false);
        expect(wrapper.find('.popover-modal-outer-wrapper').exists()).toBe(false);
    });

    it('emits close event when Escape key is pressed', async () => {
        // Set up fake timers
        vi.useFakeTimers();

        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();

        // Trigger the Escape key event
        await vi.runAllTimers();

        expect(wrapper.emitted('close')).toBeTruthy();

        // Restore real timers
        vi.useRealTimers();
    });

    it('emits close event when clicking outside', async () => {
        // Set up fake timers
        vi.useFakeTimers();

        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();

        // Trigger the click outside event
        await vi.runAllTimers();

        expect(wrapper.emitted('close')).toBeTruthy();

        // Restore real timers
        vi.useRealTimers();
    });

    it('sets triggerRef to the element when triggerElement is a string', async () => {
        const wrapper = createWrapper({
            props: {
                triggerElement: 'test-trigger',
            },
        });
        await wrapper.vm.$nextTick();

        expect(document.getElementById).toHaveBeenCalledWith('test-trigger');
    });

    it('sets triggerRef to the element when triggerElement is a ref', async () => {
        const triggerRef = vueHooks.ref({ id: 'test-trigger-ref' });
        const wrapper = createWrapper({
            props: {
                triggerElement: triggerRef,
            },
        });
        await wrapper.vm.$nextTick();

        // The component should use the ref directly
        expect(document.getElementById).not.toHaveBeenCalled();
    });

    it('renders slot content', async () => {
        const wrapper = createWrapper({
            slots: {
                default: '<div class="test-slot">Custom content</div>',
            },
        });
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.test-slot').exists()).toBe(true);
        expect(wrapper.find('.test-slot').text()).toBe('Custom content');
    });
});
