import { describe, it, expect, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import FiltersModal from '@/components/modals/FiltersModal.vue';
import type { FiltersModalFilterGroup } from '@/components/modals/FiltersModal.vue';
import type { RangeSliderProps } from '@/components/forms/RangeSlider.vue';
import type { Component } from 'vue';

interface WrapperOptions {
    props?: {
        isOpen?: boolean;
        filterGroups?: FiltersModalFilterGroup[];
        onClose?: () => void;
        onConfirm?: (filters: FiltersModalFilterGroup[]) => void;
    };
    global?: {
        stubs?: Record<string, Component>;
    };
}

interface CheckboxInputProps {
    label: string;
    name: string;
    modelValue: boolean;
}

interface CheckboxInputData {
    localValue: boolean;
}

const globalStubs = {
    Modal: {
        template: '<div class="mock-modal"><slot></slot></div>',
        props: ['isOpen'],
    },
    Button: {
        template: '<button class="mock-button" @click="$emit(\'click\', $event)"><slot></slot></button>',
        props: ['variant', 'svgIcon', 'uppercase'],
    },
    SvgIcon: {
        template: '<div class="mock-svg-icon" :data-shape="shape"></div>',
        props: ['shape', 'width', 'height'],
    },
    RangeSlider: {
        template:
            '<div class="mock-range-slider" :data-mode="mode" :data-min="min" :data-max="max" :data-bottom="bottom" :data-top="top"></div>',
        props: ['mode', 'min', 'max', 'bottom', 'top'],
        emits: ['change'],
    },
    CheckboxInput: {
        template:
            '<div class="mock-checkbox"><input type="checkbox" :checked="modelValue" @change="$emit(\'update:modelValue\', $event.target.checked)" /></div>',
        props: {
            label: String,
            name: String,
            modelValue: Boolean,
        },
        emits: ['update:modelValue'],
        data(): CheckboxInputData {
            return {
                localValue: false,
            };
        },
        watch: {
            modelValue: {
                handler(newVal: boolean) {
                    (this as any).localValue = newVal;
                },
                immediate: true,
            },
        },
    },
};

const mockRangeData: RangeSliderProps = {
    min: 0,
    max: 1000,
    bottom: 0,
    top: 1000,
};

const mockFilterGroups: FiltersModalFilterGroup[] = [
    {
        name: 'price',
        title: 'Price Range',
        description: 'Filter by price range',
        type: 'range',
        mode: 'currency',
        data: mockRangeData,
    },
    {
        name: 'status',
        title: 'Status',
        description: 'Filter by status',
        type: 'checkboxes',
        data: [
            { label: 'Active', name: 'active', value: 'active', checked: true },
            { label: 'Inactive', name: 'inactive', value: 'inactive', checked: false },
        ],
    },
];

const createWrapper = (overrides: WrapperOptions = {}) =>
    mount(FiltersModal, {
        props: {
            isOpen: false,
            filterGroups: mockFilterGroups,
            ...overrides.props,
        },
        global: {
            stubs: {
                ...globalStubs,
                ...overrides.global?.stubs,
            },
            ...overrides.global,
        },
    });

describe('FiltersModal', () => {
    it('renders with default props', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        expect(wrapper.find('.filters-modal-header').exists()).toBe(true);
        expect(wrapper.find('.filters-modal-header-title').text()).toBe('Filters');
        expect(wrapper.findComponent(globalStubs.Modal).props('isOpen')).toBe(true);
        expect(wrapper.find('.filters-modal-header-close').exists()).toBe(true);
        expect(wrapper.findComponent(globalStubs.SvgIcon).props('shape')).toBe('close');
    });

    it('renders filter groups correctly', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        const filterGroups = wrapper.findAll('.filters-modal-filter-group');
        expect(filterGroups).toHaveLength(2);

        const [rangeGroup, checkboxGroup] = filterGroups;

        expect(rangeGroup.find('h5').text()).toBe('Price Range');
        expect(rangeGroup.find('.filters-modal-filter-group-description').text()).toBe('Filter by price range');
        expect(rangeGroup.findComponent(globalStubs.RangeSlider).exists()).toBe(true);

        expect(checkboxGroup.find('h5').text()).toBe('Status');
        expect(checkboxGroup.find('.filters-modal-filter-group-description').text()).toBe('Filter by status');
        expect(checkboxGroup.findComponent(globalStubs.CheckboxInput).exists()).toBe(true);
    });

    it('handles range slider changes', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        const rangeSlider = wrapper.findComponent(globalStubs.RangeSlider);
        expect(rangeSlider.exists()).toBe(true);

        await rangeSlider.vm.$emit('change', { bottom: 100, top: 500 });
        await wrapper.vm.$nextTick();

        const confirmButton = wrapper.find('.filters-modal-confirm');
        await confirmButton.trigger('click');
        await wrapper.vm.$nextTick();

        const emitted = wrapper.emitted('confirm');
        expect(emitted).toBeTruthy();

        const updatedGroups = emitted![0][0] as FiltersModalFilterGroup[];
        const updatedRange = updatedGroups.find(g => g.name === 'price');

        expect(updatedRange?.type).toBe('range');
        if (updatedRange?.type === 'range') {
            expect(updatedRange.data.bottom).toBe(100);
            expect(updatedRange.data.top).toBe(500);
        }
    });

    it('handles checkbox changes', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        const checkbox = wrapper.findComponent(globalStubs.CheckboxInput);
        expect(checkbox.exists()).toBe(true);

        // Emit the update event directly instead of using setProps
        await checkbox.vm.$emit('update:modelValue', false);
        await wrapper.vm.$nextTick();

        const confirmButton = wrapper.find('.filters-modal-confirm');
        await confirmButton.trigger('click');
        await wrapper.vm.$nextTick();

        const emitted = wrapper.emitted('confirm');
        expect(emitted).toBeTruthy();

        const updatedGroups = emitted![0][0] as FiltersModalFilterGroup[];
        const statusGroup = updatedGroups.find(g => g.name === 'status');

        expect(statusGroup?.type).toBe('checkboxes');
        if (statusGroup?.type === 'checkboxes') {
            // Now expect the checkbox to be unchecked (false)
            expect(statusGroup.data[0].checked).toBe(false);
            expect((statusGroup.data[0] as any).value || statusGroup.data[0].name).toBe('active');
        }
    });

    it('emits close event when close button is clicked', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        const closeButton = wrapper.find('.filters-modal-header-close');
        await closeButton.trigger('click');
        await wrapper.vm.$nextTick();

        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits confirm event with updated filters when apply button is clicked', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        // Change a filter so the confirm button is enabled
        const checkbox = wrapper.findComponent(globalStubs.CheckboxInput);
        await checkbox.vm.$emit('update:modelValue', false);
        await wrapper.vm.$nextTick();

        const confirmButton = wrapper.find('.filters-modal-confirm');
        await confirmButton.trigger('click');
        await wrapper.vm.$nextTick();

        const emitted = wrapper.emitted('confirm');
        expect(emitted).toBeTruthy();
        expect(emitted![0][0]).toEqual(expect.any(Array));
    });

    it('resets all filters when clear all button is clicked', async () => {
        const wrapper = createWrapper();
        await wrapper.vm.$nextTick();
        await wrapper.setProps({ isOpen: true });
        await wrapper.vm.$nextTick();

        const rangeSlider = wrapper.findComponent(globalStubs.RangeSlider);
        const checkbox = wrapper.findComponent(globalStubs.CheckboxInput);
        expect(rangeSlider.exists()).toBe(true);
        expect(checkbox.exists()).toBe(true);

        await rangeSlider.vm.$emit('change', { bottom: 100, top: 500 });
        await checkbox.vm.$emit('update:modelValue', false);
        await wrapper.vm.$nextTick();

        const clearButton = wrapper.find('.filters-modal-clear');
        await clearButton.trigger('click');
        await wrapper.vm.$nextTick();

        const emitted = wrapper.emitted('confirm');
        expect(emitted).toBeTruthy();

        const updatedGroups = emitted![0][0] as FiltersModalFilterGroup[];
        const rangeGroup = updatedGroups.find(g => g.name === 'price');
        const checkboxGroup = updatedGroups.find(g => g.name === 'status');

        expect(rangeGroup?.type).toBe('range');
        expect(checkboxGroup?.type).toBe('checkboxes');

        if (rangeGroup?.type === 'range') {
            expect(rangeGroup.data.bottom).toBe(rangeGroup.data.min);
            expect(rangeGroup.data.top).toBe(rangeGroup.data.max);
        }

        if (checkboxGroup?.type === 'checkboxes') {
            expect(checkboxGroup.data[0].checked).toBe(true);
        }
    });
});
