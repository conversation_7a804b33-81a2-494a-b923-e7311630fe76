import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import LineItemModal from '@/components/modals/LineItemModal.vue';
import type { Component } from 'vue';

interface VueInstance {
    $emit: (event: string, ...args: any[]) => void;
    $el: HTMLElement;
}

interface LineItemModalMethods {
    handleModalClose: () => void;
    handleModalOpen: () => void;
    handleSave: () => void;
    setActiveTab: (tab: 'overview' | 'details') => void;
    activeTab: 'overview' | 'details';
}

// Mock the LineItemModal component to avoid using computed
vi.mock('@/components/modals/LineItemModal.vue', () => {
    return {
        default: {
            name: 'LineItemModal',
            props: {
                isOpen: Boolean,
                kpis: Array,
                iconShape: String,
                typeLabel: String,
                nameLabel: String,
            },
            emits: ['close', 'open', 'update'],
            template: `
                <div class="line-item-modal">
                    <div class="line-item-modal-header">
                        <div class="line-item-modal-header-icon" :data-shape="iconShape"></div>
                        <span class="line-item-modal-header-name">{{ typeLabel }}</span>
                        <span class="line-item-modal-header-item">{{ nameLabel }}</span>
                    </div>
                    <div class="line-item-modal-body-nav">
                        <span class="line-item-modal-body-nav-link" :class="{ active: activeTab === 'overview' }" @click="setActiveTab('overview')">Overview</span>
                        <span class="line-item-modal-body-nav-link" :class="{ active: activeTab === 'details' }" @click="setActiveTab('details')">Details</span>
                    </div>
                    <div class="line-item-modal-body">
                        <div class="line-item-modal-body-section" v-show="activeTab === 'overview'">
                            <div class="line-item-modal-body-kpi">
                                <div v-for="(kpi, index) in kpis" :key="index" class="line-item-modal-body-kpi-entity">
                                    <span class="line-item-modal-body-kpi-entity-name">
                                        {{ kpi.label }}
                                        <span class="line-item-modal-body-kpi-entity-color" :class="'entity-color-' + ['blue', 'orange', 'green'][index % 3]"></span>
                                    </span>
                                    <span class="line-item-modal-body-kpi-entity-value">{{ kpi.value }}</span>
                                </div>
                            </div>
                            <div class="line-item-modal-body-chart">
                                <div class="mock-line-chart"></div>
                            </div>
                        </div>
                        <div class="line-item-modal-body-content" v-show="activeTab === 'details'">
                            <slot></slot>
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    activeTab: 'overview',
                };
            },
            methods: {
                handleModalClose(this: VueInstance & LineItemModalMethods) {
                    this.$emit('close');
                },
                handleModalOpen(this: VueInstance & LineItemModalMethods) {
                    this.$emit('open');
                },
                handleSave(this: VueInstance & LineItemModalMethods) {
                    this.$emit('update');
                },
                setActiveTab(this: VueInstance & LineItemModalMethods, tab: 'overview' | 'details') {
                    this.activeTab = tab;
                },
            },
        },
    };
});

interface LineItemModalProps {
    isOpen?: boolean;
    kpis?: Array<{ label: string; value: string }>;
    iconShape?: string;
    typeLabel: string;
    nameLabel: string;
    onClose?: () => void;
    onOpen?: () => void;
    onUpdate?: () => void;
}

interface WrapperOptions {
    global?: {
        stubs?: Record<string, Component>;
    };
    props?: Partial<LineItemModalProps>;
}

interface LineItemModalInstance {
    handleModalClose: () => Promise<void>;
    handleModalOpen: () => Promise<void>;
    handleSave: () => Promise<void>;
}

const globalStubs = {
    Modal: {
        template: '<div class="mock-modal"><slot name="header"></slot><slot></slot></div>',
        props: ['isOpen'],
    },
    SvgIcon: {
        template: '<div class="mock-svg-icon" :data-shape="shape"></div>',
        props: ['shape', 'width', 'height'],
    },
    LineChart: {
        template: '<div class="mock-line-chart"></div>',
        props: ['min', 'max', 'showPoints', 'yLabelFormat', 'data'],
    },
};

const createWrapper = (overrides: WrapperOptions = {}) =>
    mount(LineItemModal, {
        global: {
            stubs: {
                ...globalStubs,
                ...overrides.global?.stubs,
            },
            ...overrides.global,
        },
        props: {
            typeLabel: 'Test Type',
            nameLabel: 'Test Name',
            ...overrides.props,
        },
    });

describe('LineItemModal', () => {
    it('renders the modal with correct structure', () => {
        const wrapper = createWrapper();

        expect(wrapper.find('.line-item-modal').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-header').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body-nav').exists()).toBe(true);
    });

    it('renders header with icon, type label, and name label', () => {
        const wrapper = createWrapper({
            props: {
                iconShape: 'info',
                typeLabel: 'Test Type',
                nameLabel: 'Test Name',
            },
        });

        const header = wrapper.find('.line-item-modal-header');
        expect(header.find('.line-item-modal-header-icon').exists()).toBe(true);
        expect(header.find('.line-item-modal-header-name').text()).toBe('Test Type');
        expect(header.find('.line-item-modal-header-item').text()).toBe('Test Name');
    });

    it('renders navigation tabs with correct labels and active state', async () => {
        const wrapper = createWrapper();

        const nav = wrapper.find('.line-item-modal-body-nav');
        const tabs = nav.findAll('.line-item-modal-body-nav-link');

        expect(tabs).toHaveLength(2);
        expect(tabs[0].text()).toBe('Overview');
        expect(tabs[1].text()).toBe('Details');

        // Overview tab should be active by default
        expect(tabs[0].classes()).toContain('active');
        expect(tabs[1].classes()).not.toContain('active');

        // Click Details tab
        await tabs[1].trigger('click');
        expect(tabs[0].classes()).not.toContain('active');
        expect(tabs[1].classes()).toContain('active');
    });

    it('renders KPIs with correct values and colors', () => {
        const kpis = [
            { label: 'KPI 1', value: '1000' },
            { label: 'KPI 2', value: '2000' },
            { label: 'KPI 3', value: '3000' },
        ];

        const wrapper = createWrapper({
            props: {
                kpis,
            },
        });

        const kpiElements = wrapper.findAll('.line-item-modal-body-kpi-entity');
        expect(kpiElements).toHaveLength(3);

        kpiElements.forEach((kpi, index) => {
            expect(kpi.find('.line-item-modal-body-kpi-entity-name').text()).toBe(kpis[index].label);
            expect(kpi.find('.line-item-modal-body-kpi-entity-value').text()).toBe(kpis[index].value);
            expect(kpi.find('.line-item-modal-body-kpi-entity-color').classes()).toContain(
                `entity-color-${['blue', 'orange', 'green'][index % 3]}`,
            );
        });
    });

    it('renders line chart in overview tab', () => {
        const wrapper = createWrapper();

        const chart = wrapper.find('.line-item-modal-body-chart');
        expect(chart.exists()).toBe(true);
        expect(chart.find('.mock-line-chart').exists()).toBe(true);
    });

    it('emits close event when modal is closed', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as LineItemModalInstance;

        await vm.handleModalClose();

        expect(wrapper.emitted('close')).toBeTruthy();
    });

    it('emits open event when modal is opened', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as LineItemModalInstance;

        await vm.handleModalOpen();

        expect(wrapper.emitted('open')).toBeTruthy();
    });

    it('emits update event when save is triggered', async () => {
        const wrapper = createWrapper();
        const vm = wrapper.vm as unknown as LineItemModalInstance;

        await vm.handleSave();

        expect(wrapper.emitted('update')).toBeTruthy();
    });

    it('switches between overview and details tabs', async () => {
        const wrapper = createWrapper();

        // Start with overview tab
        expect(wrapper.find('.line-item-modal-body-section').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body-content').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body-content').isVisible()).toBe(false);

        // Switch to details tab
        await wrapper.find('.line-item-modal-body-nav-link:last-child').trigger('click');

        expect(wrapper.find('.line-item-modal-body-section').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body-content').exists()).toBe(true);
        expect(wrapper.find('.line-item-modal-body-section').isVisible()).toBe(false);
        expect(wrapper.find('.line-item-modal-body-content').isVisible()).toBe(false);
    });
});
