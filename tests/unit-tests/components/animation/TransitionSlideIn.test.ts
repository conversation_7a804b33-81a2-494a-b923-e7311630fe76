import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import TransitionSlideIn from '@/components/animation/TransitionSlideIn.vue';

describe('TransitionSlideIn', () => {
    let wrapper: ReturnType<typeof mount>;

    beforeEach(() => {
        wrapper = mount(TransitionSlideIn, {
            props: {
                isVisible: true,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });
    });

    it('renders correctly when isVisible is true', () => {
        expect(wrapper.find('.transition-slide-in').exists()).toBe(true);
        expect(wrapper.find('.transition-slide-in-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').text()).toBe('Test Content');
    });

    it('does not render content when isVisible is false', () => {
        wrapper = mount(TransitionSlideIn, {
            props: {
                isVisible: false,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-in').exists()).toBe(false);
    });

    it('does not render content when isVisible is null', () => {
        wrapper = mount(TransitionSlideIn, {
            props: {
                isVisible: null,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-in').exists()).toBe(false);
    });

    it('does not render content when isVisible is undefined', () => {
        wrapper = mount(TransitionSlideIn, {
            props: {},
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-in').exists()).toBe(false);
    });

    it('applies correct CSS classes for transitions', () => {
        const component = wrapper.find('.transition-slide-in');
        expect(component.classes()).toContain('transition-slide-in');
    });

    it('has the correct structure with content wrapper', () => {
        const component = wrapper.find('.transition-slide-in');
        const contentWrapper = component.find('.transition-slide-in-content');

        expect(contentWrapper.exists()).toBe(true);
        expect(contentWrapper.find('.test-content').exists()).toBe(true);
    });
});
