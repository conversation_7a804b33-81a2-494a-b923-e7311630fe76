import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import TransitionSlideDown from '@/components/animation/TransitionSlideDown.vue';

describe('TransitionSlideDown', () => {
    let wrapper: ReturnType<typeof mount>;

    beforeEach(() => {
        wrapper = mount(TransitionSlideDown, {
            props: {
                isVisible: true,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });
    });

    it('renders correctly when isVisible is true', () => {
        expect(wrapper.find('.transition-slide-down').exists()).toBe(true);
        expect(wrapper.find('.test-content').exists()).toBe(true);
        expect(wrapper.find('.test-content').text()).toBe('Test Content');
    });

    it('does not render content when isVisible is false', () => {
        wrapper = mount(TransitionSlideDown, {
            props: {
                isVisible: false,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-down').exists()).toBe(false);
    });

    it('does not render content when isVisible is null', () => {
        wrapper = mount(TransitionSlideDown, {
            props: {
                isVisible: null,
            },
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-down').exists()).toBe(false);
    });

    it('does not render content when isVisible is undefined', () => {
        wrapper = mount(TransitionSlideDown, {
            props: {},
            slots: {
                default: '<div class="test-content">Test Content</div>',
            },
        });

        expect(wrapper.find('.transition-slide-down').exists()).toBe(false);
    });

    it('applies correct CSS classes for transitions', () => {
        const component = wrapper.find('.transition-slide-down');
        expect(component.classes()).toContain('transition-slide-down');
    });
});
