import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import ToggleTag from '../../../../components/elements/ToggleTag.vue';

describe('ToggleTag.vue', () => {
    it('renders with default props', () => {
        const wrapper = mount(ToggleTag, {
            props: {
                label: 'Test Tag',
                isSelected: false,
            },
        });
        expect(wrapper.find('.toggle-tag').exists()).toBe(true);
        expect(wrapper.text()).toContain('Test Tag');
    });

    it('applies selected class when isSelected is true', () => {
        const wrapper = mount(ToggleTag, {
            props: {
                label: 'Test Tag',
                isSelected: true,
            },
        });
        expect(wrapper.find('.toggle-tag').classes()).toContain('selected');
    });

    it('displays count when provided', () => {
        const wrapper = mount(ToggleTag, {
            props: {
                label: 'Test Tag',
                count: 5,
                isSelected: false,
            },
        });
        expect(wrapper.find('.toggle-tag-count').exists()).toBe(true);
        expect(wrapper.find('.toggle-tag-count').text()).toBe('5');
    });

    it('emits click event when clicked', async () => {
        const wrapper = mount(ToggleTag, {
            props: {
                label: 'Test Tag',
                isSelected: false,
            },
        });
        await wrapper.trigger('click');
        expect(wrapper.emitted('click')).toBeTruthy();
    });

    it('uses default label when not provided', () => {
        const wrapper = mount(ToggleTag, {
            props: {
                label: 'Default Tag',
                isSelected: false,
            },
        });
        expect(wrapper.text()).toContain('Default Tag');
    });
});
