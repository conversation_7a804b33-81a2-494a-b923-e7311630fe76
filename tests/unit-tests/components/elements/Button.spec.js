import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach } from 'vitest';
import Button from '../../../../components/elements/Button.vue';

// Mock NuxtLink component
const NuxtLinkStub = {
    name: 'NuxtLink',
    template: '<a><slot /></a>',
    props: ['to'],
};

describe('Button.vue', () => {
    it('renders button with default props', () => {
        const wrapper = mount(Button, {
            slots: {
                default: 'Click me',
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                },
            },
        });
        expect(wrapper.find('button').exists()).toBe(true);
        expect(wrapper.text()).toContain('Click me');
    });

    it('renders as NuxtLink when to prop is provided', () => {
        const wrapper = mount(Button, {
            props: {
                to: '/some-path',
            },
            slots: {
                default: 'Link Button',
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                },
            },
        });
        expect(wrapper.find('a').exists()).toBe(true);
        expect(wrapper.text()).toContain('Link Button');
    });

    it('applies correct classes based on props', () => {
        const wrapper = mount(Button, {
            props: {
                color: 'primary',
                variant: 'outline',
                size: 'sm',
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                },
            },
        });
        const button = wrapper.find('button');
        expect(button.classes()).toContain('button');
        expect(button.classes()).toContain('button-c-custom');
        expect(button.classes()).toContain('button-c-primary');
        expect(button.classes()).toContain('button-v-outline');
        expect(button.classes()).toContain('button-s-sm');
    });

    it('disables button when disabled prop is true', () => {
        const wrapper = mount(Button, {
            props: {
                disabled: true,
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                },
            },
        });
        expect(wrapper.find('button').attributes('disabled')).toBeDefined();
    });

    it('disables button when loading prop is true', () => {
        const wrapper = mount(Button, {
            props: {
                loading: true,
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                },
            },
        });
        expect(wrapper.find('button').attributes('disabled')).toBeDefined();
    });

    it('renders icon when svgIcon prop is provided', () => {
        const wrapper = mount(Button, {
            props: {
                svgIcon: {
                    shape: 'check',
                    position: 'right',
                    color: 'primary',
                },
            },
            global: {
                stubs: {
                    NuxtLink: NuxtLinkStub,
                    SvgIcon: true,
                },
            },
        });
        expect(wrapper.find('.button-icon').exists()).toBe(true);
        expect(wrapper.find('.button-icon-right').exists()).toBe(true);
        expect(wrapper.find('.button-icon-c-primary').exists()).toBe(true);
    });
});
