import { mount } from '@vue/test-utils';
import { describe, it, expect } from 'vitest';
import SortSelector from '../../../../components/elements/SortSelector.vue';

describe('SortSelector.vue', () => {
    it('renders with default selected option', () => {
        const wrapper = mount(SortSelector);
        expect(wrapper.find('.sort-selector').exists()).toBe(true);
        expect(wrapper.find('.sort-selector-select').text()).toContain('Date');
    });

    it('toggles dropdown when clicked', async () => {
        const wrapper = mount(SortSelector);

        // Initially dropdown should be closed
        expect(wrapper.find('.sort-selector-dropdown').exists()).toBe(false);

        // Click to open dropdown
        await wrapper.find('.sort-selector-select').trigger('click');
        expect(wrapper.find('.sort-selector-dropdown').exists()).toBe(true);
        expect(wrapper.find('.sort-selector-select').classes()).toContain('active');

        // Click again to close dropdown
        await wrapper.find('.sort-selector-select').trigger('click');
        expect(wrapper.find('.sort-selector-dropdown').exists()).toBe(false);
        expect(wrapper.find('.sort-selector-select').classes()).not.toContain('active');
    });

    it('displays all options in dropdown', async () => {
        const wrapper = mount(SortSelector);

        // Open dropdown
        await wrapper.find('.sort-selector-select').trigger('click');

        // Check all options are displayed
        const options = wrapper.findAll('.sort-selector-option');
        expect(options.length).toBe(4);
        expect(options[0].text()).toBe('Date added');
        expect(options[1].text()).toBe('Net Worth');
        expect(options[2].text()).toBe('Liabilities');
        expect(options[3].text()).toBe('Expenses');
    });

    it('selects option when clicked', async () => {
        const wrapper = mount(SortSelector);

        // Open dropdown
        await wrapper.find('.sort-selector-select').trigger('click');

        // Select an option
        await wrapper.findAll('.sort-selector-option')[1].trigger('click');

        // Check selected option is updated
        expect(wrapper.find('.sort-selector-select').text()).toBe('Net Worth');

        // Dropdown should be closed
        expect(wrapper.find('.sort-selector-dropdown').exists()).toBe(false);
    });
});
