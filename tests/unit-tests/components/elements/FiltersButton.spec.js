import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import FiltersButton from '@/components/elements/FiltersButton.vue';
import Button from '@/components/elements/Button.vue';
import FiltersModal from '@/components/modals/FiltersModal.vue';

// Mock the FiltersModal component
const FiltersModalStub = {
    template: '<div class="filters-modal"></div>',
    props: ['isOpen', 'filterGroups'],
};

describe('FiltersButton', () => {
    let wrapper;
    const defaultProps = {
        data: [
            {
                name: 'Status',
                type: 'checkboxes',
                data: [
                    { label: 'Active', value: 'active', checked: false },
                    { label: 'Inactive', value: 'inactive', checked: false },
                ],
            },
            {
                name: 'Amount',
                type: 'range',
                data: {
                    min: 0,
                    max: 100,
                    bottom: 0,
                    top: 100,
                },
            },
        ],
    };

    beforeEach(() => {
        wrapper = mount(FiltersButton, {
            props: defaultProps,
            global: {
                stubs: {
                    FiltersModal: FiltersModalStub,
                },
            },
        });
    });

    it('renders the button with "Filters" text', () => {
        expect(wrapper.findComponent(Button).exists()).toBe(true);
        expect(wrapper.text()).toContain('Filters');
    });

    it('does not show count when no filters are active', () => {
        expect(wrapper.find('.filters-button-count').exists()).toBe(false);
    });

    it('shows count when checkbox filters are active', async () => {
        await wrapper.setProps({
            data: [
                {
                    ...defaultProps.data[0],
                    data: [
                        { label: 'Active', value: 'active', checked: true },
                        { label: 'Inactive', value: 'inactive', checked: false },
                    ],
                },
            ],
        });

        expect(wrapper.find('.filters-button-count').exists()).toBe(true);
        expect(wrapper.find('.filters-button-count').text()).toBe('1');
    });

    it('shows count when range filter is active', async () => {
        await wrapper.setProps({
            data: [
                {
                    ...defaultProps.data[1],
                    data: {
                        min: 0,
                        max: 100,
                        bottom: 20,
                        top: 100,
                    },
                },
            ],
        });

        expect(wrapper.find('.filters-button-count').exists()).toBe(true);
        expect(wrapper.find('.filters-button-count').text()).toBe('1');
    });

    it('opens modal when button is clicked', async () => {
        await wrapper.findComponent(Button).trigger('click');
        expect(wrapper.findComponent(FiltersModalStub).props('isOpen')).toBe(true);
    });

    it('emits change event when filters are confirmed', async () => {
        const updatedFilters = [
            {
                name: 'Status',
                type: 'checkboxes',
                data: [
                    { label: 'Active', value: 'active', checked: true },
                    { label: 'Inactive', value: 'inactive', checked: false },
                ],
            },
        ];

        await wrapper.vm.handleModalConfirm(updatedFilters);

        expect(wrapper.emitted('change')).toBeTruthy();
        expect(wrapper.emitted('change')[0][0]).toEqual(updatedFilters);
        expect(wrapper.vm.modalIsOpen).toBe(false);
    });

    it('closes modal when hide modal is triggered', async () => {
        await wrapper.vm.handleShowModal();
        expect(wrapper.vm.modalIsOpen).toBe(true);

        await wrapper.vm.handleHideModal();
        expect(wrapper.vm.modalIsOpen).toBe(false);
    });
});
