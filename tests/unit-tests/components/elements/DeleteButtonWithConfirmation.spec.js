import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import DeleteButtonWithConfirmation from '@/components/elements/DeleteButtonWithConfirmation.vue';

describe('DeleteButtonWithConfirmation.vue', () => {
    it('renders the button', () => {
        const wrapper = mount(DeleteButtonWithConfirmation, {
            props: { label: 'Test Item' },
        });
        expect(wrapper.find('.delete-button-with-confirmation-button').exists()).toBe(true);
    });

    it('shows the label in the popover when open', async () => {
        const wrapper = mount(DeleteButtonWithConfirmation, {
            props: { label: 'Test Item' },
        });
        await wrapper.find('.delete-button-with-confirmation-button').trigger('click');
        expect(wrapper.text()).toContain('Test Item');
    });

    it('opens the popover when button is clicked', async () => {
        const wrapper = mount(DeleteButtonWithConfirmation, {
            props: { label: 'Test Item' },
        });
        expect(wrapper.findComponent({ name: 'PopoverModal' }).props('isOpen')).toBe(false);
        await wrapper.find('.delete-button-with-confirmation-button').trigger('click');
        // Popover should now be open
        expect(wrapper.vm.popoverIsOpen).toBe(true);
    });

    it('emits confirm when Yes, Delete is clicked', async () => {
        const wrapper = mount(DeleteButtonWithConfirmation, {
            props: { label: 'Test Item' },
        });
        // Open the popover
        await wrapper.find('.delete-button-with-confirmation-button').trigger('click');
        // Find the Yes, Delete button and click it
        const yesButton = wrapper.findAll('button').find(btn => btn.text().includes('Yes, Delete'));
        expect(yesButton).toBeTruthy();
        await yesButton.trigger('click');
        expect(wrapper.emitted('confirm')).toBeTruthy();
    });

    it('closes the popover when Cancel is clicked', async () => {
        const wrapper = mount(DeleteButtonWithConfirmation, {
            props: { label: 'Test Item' },
        });
        // Open the popover
        await wrapper.find('.delete-button-with-confirmation-button').trigger('click');
        // Find the Cancel button and click it
        const cancelButton = wrapper.findAll('button').find(btn => btn.text().includes('Cancel'));
        expect(cancelButton).toBeTruthy();
        await cancelButton.trigger('click');
        expect(wrapper.vm.popoverIsOpen).toBe(false);
    });
});
