import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import EntryModeSwitcher from '@/components/situations/EntryModeSwitcher.vue';

// Mock components
vi.mock('@/components/elements/Button.vue', () => ({
    default: {
        template: '<button><slot></slot></button>',
        props: ['size', 'class'],
    },
}));

vi.mock('@/components/modals/PopoverModal.vue', () => ({
    default: {
        template:
            '<div class="popover-modal"><div v-if="isOpen"><slot></slot><button class="entry-mode-switcher-popover-button" @click="$emit(\'change\', \'simple\', false)">Set Mode</button></div></div>',
        props: ['isOpen', 'dynamicPlacement', 'withOverlay'],
    },
}));

vi.mock('@/components/modals/PopoverModalContent.vue', () => ({
    default: {
        template:
            '<div class="popover-modal-content"><slot name="header"></slot><slot></slot><slot name="footer"></slot></div>',
    },
}));

vi.mock('@/components/forms/CheckboxGroup.vue', () => ({
    default: {
        template: '<div class="checkbox-group"><slot></slot></div>',
        props: ['options', 'selected', 'size', 'mode'],
    },
}));

vi.mock('@/components/forms/CheckboxInput.vue', () => ({
    default: {
        template: '<div class="checkbox-input"><slot></slot></div>',
        props: ['modelValue', 'size', 'label'],
    },
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        template: '<svg class="svg-icon"></svg>',
        props: ['shape'],
    },
}));

// Mock utils
vi.mock('@/utils', () => ({
    capitalize: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
    pluralize: (str: string) => str + 's',
    toLowerCase: (str: string) => str.toLowerCase(),
}));

describe('EntryModeSwitcher', () => {
    it('renders correctly with default props', () => {
        const wrapper = mount(EntryModeSwitcher, {
            props: {
                active: 'simple',
            },
        });

        expect(wrapper.find('.entry-mode-switcher').exists()).toBe(true);
        expect(wrapper.find('.entry-mode-switcher-mode').text()).toBe('MODE');
        expect(wrapper.find('.entry-mode-switcher-active').text()).toBe('Simple');
    });

    it('renders with label', () => {
        const wrapper = mount(EntryModeSwitcher, {
            props: {
                active: 'simple',
                label: 'Test Label',
            },
        });

        expect(wrapper.find('.entry-mode-switcher').exists()).toBe(true);
        expect(wrapper.find('.entry-mode-switcher-active').text()).toBe('Simple');
    });

    it('opens popover when button is clicked', async () => {
        const wrapper = mount(EntryModeSwitcher, {
            props: {
                active: 'simple',
            },
        });

        await wrapper.find('.entry-mode-switcher-button').trigger('click');
        expect(wrapper.find('.popover-modal').exists()).toBe(true);
    });

    it('handles custom descriptions', () => {
        const wrapper = mount(EntryModeSwitcher, {
            props: {
                active: 'simple',
                standardDescription: 'Custom standard description',
                simpleDescription: 'Custom simple description',
            },
        });

        expect(wrapper.find('.entry-mode-switcher').exists()).toBe(true);
    });
});
