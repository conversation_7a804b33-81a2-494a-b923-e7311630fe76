import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import SituationCard from '@/components/situations/SituationCard.vue';

// Mock components
vi.mock('@/components/images/BigIcon.vue', () => ({
    default: {
        template: '<div class="big-icon"></div>',
        props: ['shape'],
    },
}));

// Mock utils
vi.mock('@/utils', () => ({
    numberToUSD: (value: number) => `$${value}`,
}));

// Mock useId
vi.mock('@/composables/useId', () => ({
    useId: () => 'test-id',
}));

describe('SituationCard', () => {
    const defaultProps = {
        totalSteps: 3,
        completedSteps: 2,
        kind: 'Test Kind',
        title: 'Test Title',
        rows: [
            {
                label: 'Test Row',
                value: 100,
                suffix: 'test',
            },
        ],
        footerRow: {
            label: 'Test Footer',
            value: 200,
        },
        iconShape: 'test-shape',
        iconBadge: 'test-badge',
    };

    it('renders correctly with default props', () => {
        const wrapper = mount(SituationCard, {
            props: defaultProps,
        });

        expect(wrapper.find('.situation-card').exists()).toBe(true);
        expect(wrapper.find('h5').text()).toBe('Test Kind');
        expect(wrapper.find('h4').text()).toBe('Test Title');
        expect(wrapper.find('.situation-card-badge-content').text()).toBe('test-badge');
    });

    it('renders progress bar correctly', () => {
        const wrapper = mount(SituationCard, {
            props: defaultProps,
        });

        const steps = wrapper.findAll('.situation-card-progress-bar-step');
        expect(steps.length).toBe(3);
        expect(steps.filter(step => step.classes('completed')).length).toBe(2);
    });

    it('renders rows correctly', () => {
        const wrapper = mount(SituationCard, {
            props: defaultProps,
        });

        const rows = wrapper.findAll('dl');
        expect(rows.length).toBe(2); // One for the main row, one for footer
        expect(rows[0].find('dt').text()).toBe('Test Row');
        expect(rows[0].find('dd').text()).toContain('$100');
        expect(rows[0].find('dd').text()).toContain('test');
    });

    it('renders footer correctly', () => {
        const wrapper = mount(SituationCard, {
            props: defaultProps,
        });

        const footer = wrapper.find('.situation-card-footer');
        expect(footer.find('dt').text()).toBe('Test Footer');
        expect(footer.find('dd').text()).toContain('$200');
    });

    it('emits click event when clicked', async () => {
        const wrapper = mount(SituationCard, {
            props: defaultProps,
        });

        await wrapper.find('.situation-card').trigger('click');
        expect(wrapper.emitted('click')).toBeTruthy();
    });

    it('renders in pending mode', () => {
        const wrapper = mount(SituationCard, {
            props: {
                ...defaultProps,
                mode: 'pending',
            },
        });

        expect(wrapper.find('.situation-card-pending-content').exists()).toBe(true);
        expect(wrapper.find('.situation-card-pending-cta h6').text()).toBe('Please complete');
        expect(wrapper.find('.situation-card-pending-cta h4').text()).toBe('Test Kind');
    });

    it('handles missing values', () => {
        const wrapper = mount(SituationCard, {
            props: {
                totalSteps: 3,
                completedSteps: 2,
                rows: [
                    {
                        label: 'Test Row',
                        value: null,
                    },
                ],
            },
        });

        expect(wrapper.find('.fade').text()).toBe('–');
    });
});
