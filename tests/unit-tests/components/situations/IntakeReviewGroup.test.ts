import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import IntakeReviewGroup from '@/components/situations/IntakeReviewGroup.vue';
import type { IntakeReviewGroupProps } from '@/components/situations/IntakeReviewGroup.vue';
import type { SituationInputGroupProps } from '@/components/situations/SituationInputGroup.vue';

// Mock components
vi.mock('@/components/images/BigIcon.vue', () => ({
    default: {
        template: '<div class="big-icon"></div>',
        props: ['shape', 'width', 'height'],
    },
}));

vi.mock('@/components/situations/SituationItem.vue', () => ({
    default: {
        template: '<div class="situation-item"><slot></slot></div>',
        props: ['title', 'displayMode'],
    },
}));

vi.mock('@/components/situations/SituationLineItem.vue', () => ({
    default: {
        template: '<div class="situation-line-item"><slot></slot></div>',
    },
}));

// Mock useId
vi.mock('@/composables/useId', () => ({
    useId: () => 'test-id',
}));

describe('IntakeReviewGroup', () => {
    const defaultProps: IntakeReviewGroupProps = {
        svgShape: 'test-shape',
        label: 'Test Group',
        instructions: 'Test instructions',
        completion: 50,
        situations: [
            {
                kind: 'Test Situation',
                entryMode: 'simple',
                category: 'Asset',
                itemsKind: 'test-kind',
                totalSteps: 1,
                completedSteps: 0,
                simple: [
                    {
                        name: 'test-field',
                        label: 'Test Field',
                        type: 'text',
                        required: true,
                        value: 'test value',
                    },
                ],
                standard: {
                    'test-id': {
                        id: 'test-id',
                        isComplete: false,
                        fields: [
                            {
                                name: 'test-field',
                                label: 'Test Field',
                                type: 'text',
                                required: true,
                                value: 'test value',
                            },
                        ],
                    },
                },
                template: [
                    {
                        name: 'test-field',
                        label: 'Test Field',
                        type: 'text',
                        required: true,
                        value: 'test value',
                    },
                ],
            },
        ],
    };

    it('renders correctly with default props', () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        expect(wrapper.find('.intake-review-group').exists()).toBe(true);
        expect(wrapper.find('h4').text()).toBe('Test Group');
        expect(wrapper.find('.intake-review-group-progress p').text()).toBe('50% Complete');
        expect(wrapper.find('.intake-review-group-instructions').text()).toBe('Test instructions');
    });

    it('displays correct completion count', () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        expect(wrapper.find('.intake-review-group-count').text()).toContain('1 / 1 Selections complete');
    });

    it('emits simple-change event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('simple-change', {
            fieldName: 'test-field',
            value: 'test-value',
        });
        expect(wrapper.emitted('simple-change')).toBeTruthy();
    });

    it('emits standard-change event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('standard-change', {
            itemId: 'test-id',
            fieldName: 'test-field',
            value: 'test-value',
        });
        expect(wrapper.emitted('standard-change')).toBeTruthy();
    });

    it('emits entry-mode-change event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('entry-mode-change', {
            newMode: 'standard',
            useAsDefault: false,
        });
        expect(wrapper.emitted('entry-mode-change')).toBeTruthy();
    });

    it('emits remove-item event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('remove-item', {
            itemId: 'test-id',
        });
        expect(wrapper.emitted('remove-item')).toBeTruthy();
    });

    it('emits add-item event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('add-item');
        expect(wrapper.emitted('add-item')).toBeTruthy();
    });

    it('emits remove-situation event', async () => {
        const wrapper = mount(IntakeReviewGroup, {
            props: defaultProps,
        });

        await wrapper.find('.situation-item').trigger('remove-situation');
        expect(wrapper.emitted('remove-situation')).toBeTruthy();
    });
});
