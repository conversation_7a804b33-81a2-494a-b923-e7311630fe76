vi.mock('~/stores/intake', () => ({
    prepend: vi.fn(),
    reduceSituationFieldsToTotal: vi.fn(),
    useRequestFetch: vi.fn(() => () => Promise.resolve({ data: 'mocked data' })),
    nanoid: vi.fn().mockReturnValue('mocked-id'),
    useIntakeStore: vi.fn(() => ({
        assets: [],
        liabilities: [],
        expenses: [],
        income: [],
        insurance: [],
        owners: [],
        data: {
            value: {
                name: {
                    firstName: '',
                    middleName: '',
                    lastName: '',
                },
                spouse: {},
                dependents: [],
                portfolio: {
                    owners: [],
                    assets: [],
                    expenses: [],
                    income: [],
                    insurance: [],
                    liabilities: [],
                },
                intakeCompleted: false,
                home: {},
                situations: [],
                incomeTypes: [],
            },
        },
        progress: {
            steps: [],
            activeStep: 'personal',
            completion: 0,
        },
        totals: {
            assets: 0,
            liabilities: 0,
            income: 0,
            expenses: 0,
            netWorth: 0,
            netIncome: 0,
        },
        fetch: vi.fn(),
        update: vi.fn(),
        getItemAssociatedItems: vi.fn(() => ({
            assets: [],
            liabilities: [],
            expenses: [],
            income: [],
        })),
    })),
}));

import { describe, it, expect, vi } from 'vitest';
import { shallowMount, flushPromises } from '@vue/test-utils';
import { defineComponent } from 'vue';
import SituationItem from '~/components/situations/SituationItem.vue';
import type { IntakeStandardProperty } from '~/stores/intake';

vi.mock('~/components/situations/SituationLineItem.vue', () => ({
    default: defineComponent({
        name: 'SituationLineItem',
        props: ['title', 'iconShape', 'footerRow'],
        emits: ['click'],
        template: '<div class="mock-situation-line-item">Mock Line Item</div>',
    }),
    __esModule: true,
}));

vi.mock('~/components/situations/SituationCard.vue', () => ({
    default: defineComponent({
        name: 'SituationCard',
        props: ['title', 'iconShape', 'footerRow', 'mode'],
        emits: ['click'],
        template: '<div class="mock-situation-card">Mock Card</div>',
    }),
    __esModule: true,
}));

vi.mock('~/components/situations/SituationInputGroup.vue', () => ({
    default: defineComponent({
        name: 'SituationInputGroup',
        props: ['label', 'name', 'required', 'value', 'description', 'autoFocus', 'footerDisclaimer'],
        emits: ['change'],
        template: '<div class="mock-situation-input-group">Mock Input Group</div>',
    }),
    __esModule: true,
}));

vi.mock('~/components/situations/SituationItemAssociatedItems.vue', () => ({
    default: defineComponent({
        name: 'SituationItemAssociatedItems',
        props: ['item', 'itemKind', 'kind'],
        template: '<div class="mock-associated-items">Mock Associated Items</div>',
    }),
    __esModule: true,
}));

vi.mock('~/components/modals/Modal.vue', () => ({
    default: defineComponent({
        name: 'Modal',
        props: ['isOpen'],
        emits: ['close'],
        template: '<div class="mock-modal"><slot name="header"></slot><slot></slot></div>',
    }),
    __esModule: true,
}));

vi.mock('~/components/modals/ModalHeaderRow.vue', () => ({
    default: defineComponent({
        name: 'ModalHeaderRow',
        props: ['activeMode', 'modeTypeLabel', 'showModeSwitcher', 'canDelete'],
        emits: ['close', 'delete', 'back', 'entryModeChange'],
        template: '<div class="mock-modal-header">Mock Header</div>',
    }),
    __esModule: true,
}));

describe('SituationItem.vue', () => {
    const defaultProps = {
        title: 'Test Situation',
        simple: [
            {
                type: 'number',
                label: 'Test Field',
                name: 'test-field',
                required: true,
            },
        ],
        standard: {
            '1': {
                id: '1',
                fields: [
                    {
                        type: 'text',
                        label: 'Standard Field',
                        name: 'standard-field',
                        required: true,
                    },
                ],
            },
        } as IntakeStandardProperty,
        template: [],
    };

    it('renders SituationLineItem when displayMode is line', async () => {
        const wrapper = shallowMount(SituationItem, {
            props: {
                ...defaultProps,
                displayMode: 'line',
            },
        });

        await flushPromises();
        expect(wrapper.findComponent({ name: 'SituationLineItem' }).exists()).toBe(true);
        expect(wrapper.findComponent({ name: 'SituationCard' }).exists()).toBe(false);
    });

    it('renders SituationCard when displayMode is card', async () => {
        const wrapper = shallowMount(SituationItem, {
            props: {
                ...defaultProps,
                displayMode: 'card',
            },
        });

        await flushPromises();
        expect(wrapper.findComponent({ name: 'SituationCard' }).exists()).toBe(true);
        expect(wrapper.findComponent({ name: 'SituationLineItem' }).exists()).toBe(false);
    });
});
