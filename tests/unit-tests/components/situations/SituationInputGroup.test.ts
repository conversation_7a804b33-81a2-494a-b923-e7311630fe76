import { describe, it, expect, vi } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { defineComponent } from 'vue';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';

// ✅ Mock NumberSituationInputGroup component
vi.mock('~/components/situations/input-groups/NumberSituationInputGroup.vue', () => {
    return {
        default: defineComponent({
            name: 'NumberSituationInputGroup',
            props: ['label', 'name', 'required', 'value', 'description'],
            emits: ['change'],
            template: `<div class="mock-number-input">Mock Number Input</div>`,
        }),
        __esModule: true,
    };
});

// ✅ Mock TextSituationInputGroup component
vi.mock('~/components/situations/input-groups/TextSituationInputGroup.vue', () => {
    return {
        default: defineComponent({
            name: 'TextSituationInputGroup',
            props: ['label', 'name', 'required', 'value', 'description'],
            emits: ['change'],
            template: `<div class="mock-text-input">Mock Text Input</div>`,
        }),
        __esModule: true,
    };
});

describe('SituationInputGroup', () => {
    it('renders correct input type component', async () => {
        const wrapper = mount(SituationInputGroup, {
            props: {
                type: 'number',
                label: 'My Number Input',
                name: 'my-number',
            },
        });

        await flushPromises();

        expect(wrapper.find('.mock-number-input').exists()).toBe(true);
    });

    it('renders correct input type component for text', async () => {
        const wrapper = mount(SituationInputGroup, {
            props: {
                type: 'text',
                label: 'My Text Input',
                name: 'my-text',
            },
        });

        await flushPromises();

        expect(wrapper.find('.mock-text-input').exists()).toBe(true);
    });

    it('emits change event from child', async () => {
        const wrapper = mount(SituationInputGroup, {
            props: {
                type: 'number',
                label: 'Emit Test',
                name: 'emit-input',
            },
        });

        await flushPromises();

        const numberInput = wrapper.findComponent({ name: 'NumberSituationInputGroup' });
        expect(numberInput.exists()).toBe(true);

        numberInput.vm?.$emit('change', 'new-value');
        await flushPromises();

        expect(wrapper.emitted('change')).toBeTruthy();
        expect(wrapper.emitted('change')![0]).toEqual(['new-value']);
    });
});
