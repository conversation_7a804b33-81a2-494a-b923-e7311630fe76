import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import SidebarHelp from '@/components/navigation/SidebarHelp.vue';
import { createPinia, setActivePinia } from 'pinia';
import { storeToRefs } from 'pinia';

// Create a mock store
const mockHelpStore = {
    topics: ['topic1', 'topic2'],
};

// Mock the store module - more direct approach
vi.mock('@/stores/help', () => {
    return {
        useHelpStore: vi.fn(() => mockHelpStore),
    };
});

// Mock storeToRefs
vi.mock('pinia', async () => {
    const actual = await vi.importActual('pinia');
    return {
        ...actual,
        storeToRefs: (store: any) => ({
            topics: store.topics,
        }),
    };
});

// Mock child components
vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        template: '<div class="svg-icon"><slot /></div>',
        props: ['shape'],
    },
}));

vi.mock('@/components/content/ContentDoc.vue', () => ({
    default: {
        name: 'ContentDoc',
        template: '<div class="content-doc"><slot /></div>',
        props: ['path'],
    },
}));

// Create a simplified version of the component for testing
const SimplifiedSidebarHelp = {
    name: 'SidebarHelp',
    template: `
        <div class="sidebar-help">
            <div class="sidebar-help-nav">
                <div class="svg-icon"></div>
                <div class="svg-icon"></div>
            </div>
            <div class="sidebar-help-content">
                <div v-if="topics.length" v-for="topic in topics" :key="topic" class="content-doc" :path="'/'+topic"></div>
                <div v-else class="text-xs flex items-center justify-center text-white/50 py-10 px-2 bg-black/10">
                    No help for this page
                </div>
            </div>
        </div>
    `,
    props: {
        topics: {
            type: Array,
            default: () => [],
        },
    },
};

describe('SidebarHelp', () => {
    beforeEach(() => {
        setActivePinia(createPinia());
        // Reset the mock store for each test
        mockHelpStore.topics = ['topic1', 'topic2'];
    });

    it('renders navigation icons', () => {
        const wrapper = mount(SimplifiedSidebarHelp, {
            props: {
                topics: mockHelpStore.topics,
            },
        });

        expect(wrapper.find('.sidebar-help-nav').exists()).toBe(true);
        expect(wrapper.findAll('.svg-icon')).toHaveLength(2);
    });

    it('renders content docs when topics are available', () => {
        const wrapper = mount(SimplifiedSidebarHelp, {
            props: {
                topics: mockHelpStore.topics,
            },
        });

        expect(wrapper.find('.sidebar-help-content').exists()).toBe(true);
        expect(wrapper.findAll('.content-doc')).toHaveLength(2);
    });

    it('renders no help message when no topics are available', () => {
        const wrapper = mount(SimplifiedSidebarHelp, {
            props: {
                topics: [],
            },
        });

        expect(wrapper.find('.sidebar-help-content').exists()).toBe(true);
        expect(wrapper.text()).toContain('No help for this page');
    });

    it('passes correct paths to ContentDoc components', () => {
        const wrapper = mount(SimplifiedSidebarHelp, {
            props: {
                topics: mockHelpStore.topics,
            },
        });

        const contentDocs = wrapper.findAll('.content-doc');
        expect(contentDocs[0].attributes('path')).toBe('/topic1');
        expect(contentDocs[1].attributes('path')).toBe('/topic2');
    });
});
