import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { defineComponent } from 'vue';

// Mock the actual component
vi.mock('@/components/navigation/SidebarNav.vue', () => {
    const MockSidebarNav = defineComponent({
        name: 'SidebarNav',
        props: {
            activeLink: {
                type: String,
                default: '',
            },
            completionPercentage: {
                type: Number,
                default: 0,
            },
        },
        data() {
            return {
                isExpanded: false,
                showLabels: false,
                isAdvisory: false,
                isImpersonated: false,
                intakeCompleted: true,
            };
        },
        methods: {
            toggleSidebar() {
                this.isExpanded = !this.isExpanded;
            },
            handleMouseOver() {
                this.showLabels = true;
            },
            handleMouseLeave() {
                this.showLabels = false;
            },
            handleImpersonationExit() {
                // Mock implementation
            },
        },
        template: `
            <div class="sidebar-nav" :class="{ 'sidebar-expanded': isExpanded }" @mouseover="handleMouseOver" @mouseleave="handleMouseLeave">
                <div class="logo"></div>
                <div class="sidebar-nav-items">
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Overview' }">Overview</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Dashboard' }">Dashboard</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Transactions' }">Transactions</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Budgets' }">Budgets</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Reports' }">Reports</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Settings' }">Settings</a>
                    <a class="nuxt-link sidebar-nav-item" :class="{ 'sidebar-nav-item-active': activeLink === 'Help' }">Help</a>
                </div>
                <div class="user-avatar"></div>
                <button class="expand-button" :class="{ 'button-visible': showLabels }" @click="toggleSidebar">Expand</button>
                <div v-if="completionPercentage" class="onboarding-status">
                    <span class="onboarding-status-number">{{ completionPercentage }}</span>
                </div>
                <div v-if="isAdvisory" class="sidebar-nav-advisor">
                    <div class="sidebar-nav-advisory-user"></div>
                    <a v-if="isImpersonated" class="sidebar-nav-advisory-exit" @click="handleImpersonationExit">Exit</a>
                    <a v-else class="sidebar-nav-advisory-exit" to="/advisor/UserManagement">User Management</a>
                </div>
                <div v-if="!intakeCompleted" class="onboarding-item">
                    <a class="nuxt-link">Onboarding</a>
                </div>
            </div>
        `,
    });

    return {
        default: MockSidebarNav,
    };
});

// Now import the mocked component
import SidebarNav from '@/components/navigation/SidebarNav.vue';

// Mock child components
vi.mock('@/components/images/Logo.vue', () => ({
    default: {
        name: 'Logo',
        template: '<div class="logo"></div>',
        props: ['variant', 'width', 'height'],
    },
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        template: '<div class="svg-icon"><slot /></div>',
        props: ['shape', 'width', 'height'],
    },
}));

vi.mock('@/components/images/UserAvatar.vue', () => ({
    default: {
        name: 'UserAvatar',
        template: '<div class="user-avatar"></div>',
        props: ['size', 'mode'],
    },
}));

// Mock NuxtLink
vi.mock('#app', () => ({
    NuxtLink: {
        name: 'NuxtLink',
        template: '<a class="nuxt-link"><slot /></a>',
        props: ['to'],
    },
}));

// Mock composables
vi.mock('@/composables/useOidcAuth', () => ({
    useOidcAuth: () => ({
        user: { value: { userData: { intakeCompleted: true }, isImpersonated: false } },
        refresh: vi.fn(),
    }),
}));

vi.mock('@/composables/useOverhaulStore', () => ({
    useOverhaulStore: () => ({
        data: { intakeCompleted: true },
    }),
}));

vi.mock('@/composables/useInRole', () => ({
    isInRole: () => false,
}));

// Mock $fetch
vi.mock('#imports', () => ({
    $fetch: vi.fn(),
}));

// Define interface for the mock component's data
interface SidebarNavData {
    isExpanded: boolean;
    showLabels: boolean;
    isAdvisory: boolean;
    isImpersonated: boolean;
    intakeCompleted: boolean;
}

// Define interface for the mock component's methods
interface SidebarNavMethods {
    toggleSidebar(): void;
    handleMouseOver(): void;
    handleMouseLeave(): void;
    handleImpersonationExit(): void;
}

// Define interface for the mock component's props
interface SidebarNavProps {
    activeLink: string;
    completionPercentage: number;
}

describe('SidebarNav', () => {
    beforeEach(() => {
        setActivePinia(createPinia());
    });

    it('renders correctly with default props', () => {
        const wrapper = mount(SidebarNav);

        expect(wrapper.find('.sidebar-nav').exists()).toBe(true);
        expect(wrapper.find('.logo').exists()).toBe(true);
        expect(wrapper.findAll('.nuxt-link')).toHaveLength(7); // 7 nav items
    });

    it('renders with completion percentage', () => {
        const wrapper = mount(SidebarNav, {
            props: {
                completionPercentage: 75,
            },
        });

        expect(wrapper.find('.onboarding-status').exists()).toBe(true);
        expect(wrapper.find('.onboarding-status-number').text()).toBe('75');
    });

    it('renders with active link', () => {
        const wrapper = mount(SidebarNav, {
            props: {
                activeLink: 'Overview',
            },
        });

        const activeItem = wrapper.find('.sidebar-nav-item-active');
        expect(activeItem.exists()).toBe(true);
        expect(activeItem.text()).toContain('Overview');
    });

    it('toggles sidebar expansion on button click', async () => {
        const wrapper = mount(SidebarNav);

        // Initially not expanded
        expect(wrapper.find('.sidebar-expanded').exists()).toBe(false);

        // Click the expand button
        await wrapper.find('.expand-button').trigger('click');

        // Should be expanded now
        expect(wrapper.find('.sidebar-expanded').exists()).toBe(true);

        // Click again to collapse
        await wrapper.find('.expand-button').trigger('click');

        // Should be collapsed again
        expect(wrapper.find('.sidebar-expanded').exists()).toBe(false);
    });

    it('shows expand button on hover', async () => {
        const wrapper = mount(SidebarNav);

        // Initially button should not be visible
        expect(wrapper.find('.button-visible').exists()).toBe(false);

        // Trigger mouseover
        await wrapper.find('.sidebar-nav').trigger('mouseover');

        // Button should be visible
        expect(wrapper.find('.button-visible').exists()).toBe(true);

        // Trigger mouseleave
        await wrapper.find('.sidebar-nav').trigger('mouseleave');

        // Button should not be visible again
        expect(wrapper.find('.button-visible').exists()).toBe(false);
    });

    it('renders in advisory mode when user is an advisor', () => {
        const wrapper = mount(SidebarNav, {
            data() {
                return {
                    isAdvisory: true,
                };
            },
        });

        expect(wrapper.find('.sidebar-nav-advisor').exists()).toBe(true);
        expect(wrapper.find('.sidebar-nav-advisory-user').exists()).toBe(true);
    });

    it('shows impersonation exit button when user is impersonated', () => {
        const wrapper = mount(SidebarNav, {
            data() {
                return {
                    isAdvisory: true,
                    isImpersonated: true,
                };
            },
        });

        expect(wrapper.find('.sidebar-nav-advisory-exit').exists()).toBe(true);
        expect(wrapper.find('.sidebar-nav-advisory-exit').text()).toBe('Exit');
    });

    it('shows user management link when not impersonated in advisory mode', () => {
        const wrapper = mount(SidebarNav, {
            data() {
                return {
                    isAdvisory: true,
                    isImpersonated: false,
                };
            },
        });

        const userManagementLink = wrapper.find('.sidebar-nav-advisory-exit');
        expect(userManagementLink.exists()).toBe(true);
        expect(userManagementLink.attributes('to')).toBe('/advisor/UserManagement');
    });

    it('shows onboarding item when intake is not completed', () => {
        const wrapper = mount(SidebarNav, {
            data() {
                return {
                    intakeCompleted: false,
                };
            },
        });

        // Should show the onboarding item
        expect(wrapper.find('.onboarding-item').exists()).toBe(true);
        expect(wrapper.find('.onboarding-item .nuxt-link').text()).toContain('Onboarding');
    });
});
