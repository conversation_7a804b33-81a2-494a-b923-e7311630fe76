import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { defineComponent } from 'vue';

// Mock the TabBarTab component
vi.mock('@/components/navigation/TabBarTab.vue', () => {
    const MockTabBarTab = defineComponent({
        name: 'TabBarTab',
        props: {
            label: {
                type: String,
                required: true,
            },
            name: {
                type: [String, Number],
                required: true,
            },
            completion: {
                type: Number,
                default: 0,
            },
            iconShape: {
                type: String,
                default: '',
            },
            active: {
                type: Boolean,
                default: false,
            },
        },
        template: `
      <button
        class="tab-bar-tab"
        :class="{'tab-bar-tab-active': active}"
        @click="$emit('clicked', name)"
      >
        <span v-if="iconShape" class="tab-bar-tab-icon">Icon</span>
        <span class="tab-bar-tab-label">{{ label }}</span>
        <div class="tab-bar-progress">
          <div class="tab-bar-progress-bar" :style="{ width: completion + '%' }"></div>
        </div>
      </button>
    `,
    });

    return {
        default: MockTabBarTab,
    };
});

// Now import the TabBar component
import TabBar from '@/components/navigation/TabBar.vue';

describe('TabBar', () => {
    const mockTabs = [
        {
            label: 'Tab 1',
            name: 'tab-1',
            completion: 100,
        },
        {
            label: 'Tab 2',
            name: 'tab-2',
            completion: 50,
            iconShape: 'info',
        },
        {
            label: 'Tab 3',
            name: 'tab-3',
            completion: 0,
        },
    ];

    it('renders correctly with default props', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
            },
        });

        expect(wrapper.find('.tab-bar').exists()).toBe(true);
        expect(wrapper.findAll('.tab-bar-tab')).toHaveLength(3);
        expect(wrapper.findAll('.tab-bar-tab-label')).toHaveLength(3);
        expect(wrapper.findAll('.tab-bar-tab-label')[0].text()).toBe('Tab 1');
        expect(wrapper.findAll('.tab-bar-tab-label')[1].text()).toBe('Tab 2');
        expect(wrapper.findAll('.tab-bar-tab-label')[2].text()).toBe('Tab 3');
    });

    it('renders with active tab', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
                activeTab: 'tab-2',
            },
        });

        const activeTab = wrapper.find('.tab-bar-tab-active');
        expect(activeTab.exists()).toBe(true);
        expect(activeTab.find('.tab-bar-tab-label').text()).toBe('Tab 2');
    });

    it('emits clicked event when a tab is clicked', async () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
            },
        });

        // Click the first tab
        await wrapper.findAll('.tab-bar-tab')[0].trigger('click');

        // Check that the clicked event was emitted with the correct tab name
        expect(wrapper.emitted('clicked')).toBeTruthy();
        expect(wrapper.emitted('clicked')![0]).toEqual(['tab-1']);
    });

    it('renders tabs with completion progress', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
            },
        });

        // Check that the progress bars are rendered with the correct width
        const progressBars = wrapper.findAll('.tab-bar-progress-bar');
        expect(progressBars[0].attributes('style')).toContain('width: 100%');
        expect(progressBars[1].attributes('style')).toContain('width: 50%');
        expect(progressBars[2].attributes('style')).toContain('width: 0%');
    });

    it('renders tabs with icons when iconShape is provided', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
            },
        });

        // Check that the icon is rendered for the second tab
        const icons = wrapper.findAll('.tab-bar-tab-icon');
        expect(icons).toHaveLength(1);
        expect(icons[0].text()).toBe('Icon');
    });

    it('handles empty tabs array', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: [],
            },
        });

        expect(wrapper.find('.tab-bar').exists()).toBe(true);
        expect(wrapper.findAll('.tab-bar-tab')).toHaveLength(0);
    });

    it('handles null activeTab', () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
                activeTab: null,
            },
        });

        expect(wrapper.find('.tab-bar').exists()).toBe(true);
        expect(wrapper.find('.tab-bar-tab-active').exists()).toBe(false);
    });

    it('updates active tab when activeTab prop changes', async () => {
        const wrapper = mount(TabBar, {
            props: {
                tabs: mockTabs,
                activeTab: 'tab-1',
            },
        });

        // Initially tab-1 should be active
        expect(wrapper.find('.tab-bar-tab-active').find('.tab-bar-tab-label').text()).toBe('Tab 1');

        // Change active tab to tab-3
        await wrapper.setProps({ activeTab: 'tab-3' });

        // Now tab-3 should be active
        expect(wrapper.find('.tab-bar-tab-active').find('.tab-bar-tab-label').text()).toBe('Tab 3');
    });
});
