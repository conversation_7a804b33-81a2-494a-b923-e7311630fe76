import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { defineComponent } from 'vue';

// Mock the SvgIcon component
vi.mock('@/components/images/SvgIcon.vue', () => {
    const MockSvgIcon = defineComponent({
        name: 'SvgIcon',
        props: {
            shape: {
                type: String,
                required: true,
            },
            width: {
                type: Number,
                default: 20,
            },
            height: {
                type: Number,
                default: 20,
            },
        },
        template: `<div class="svg-icon" :data-shape="shape" :style="{ width: width + 'px', height: height + 'px' }">Icon</div>`,
    });

    return {
        default: MockSvgIcon,
    };
});

// Now import the TabBarTab component
import TabBarTab from '@/components/navigation/TabBarTab.vue';

describe('TabBarTab', () => {
    it('renders correctly with required props', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 75,
            },
        });

        expect(wrapper.find('.tab-bar-tab').exists()).toBe(true);
        expect(wrapper.find('.tab-bar-tab-label').text()).toBe('Test Tab');
        expect(wrapper.find('.tab-bar-progress-bar').attributes('style')).toContain('width: 75%');
        expect(wrapper.find('.tab-bar-tab-active').exists()).toBe(false);
        expect(wrapper.find('.tab-bar-tab-icon').exists()).toBe(false);
    });

    it('renders with active state', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 75,
                active: true,
            },
        });

        expect(wrapper.find('.tab-bar-tab-active').exists()).toBe(true);
    });

    it('renders with icon when iconShape is provided', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 75,
                iconShape: 'info',
            },
        });

        expect(wrapper.find('.tab-bar-tab-icon').exists()).toBe(true);
        expect(wrapper.find('.svg-icon').exists()).toBe(true);
        expect(wrapper.find('.svg-icon').attributes('data-shape')).toBe('info');
    });

    it('emits clicked event with tab name when clicked', async () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 75,
            },
        });

        await wrapper.find('.tab-bar-tab').trigger('click');

        expect(wrapper.emitted('clicked')).toBeTruthy();
        expect(wrapper.emitted('clicked')![0]).toEqual(['test-tab']);
    });

    it('handles numeric name prop', async () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 123,
                completion: 75,
            },
        });

        await wrapper.find('.tab-bar-tab').trigger('click');

        expect(wrapper.emitted('clicked')).toBeTruthy();
        expect(wrapper.emitted('clicked')![0]).toEqual([123]);
    });

    it('handles zero completion', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 0,
            },
        });

        expect(wrapper.find('.tab-bar-progress-bar').attributes('style')).toContain('width: 0%');
    });

    it('handles 100% completion', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 100,
            },
        });

        expect(wrapper.find('.tab-bar-progress-bar').attributes('style')).toContain('width: 100%');
    });

    it('handles completion greater than 100%', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 150,
            },
        });

        expect(wrapper.find('.tab-bar-progress-bar').attributes('style')).toContain('width: 150%');
    });

    it('handles negative completion', () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: -10,
            },
        });

        expect(wrapper.find('.tab-bar-progress-bar').attributes('style')).toContain('width: -10%');
    });

    it('prevents default click behavior', async () => {
        const wrapper = mount(TabBarTab, {
            props: {
                label: 'Test Tab',
                name: 'test-tab',
                completion: 75,
            },
        });

        const clickEvent = {
            preventDefault: vi.fn(),
        };

        await wrapper.find('.tab-bar-tab').trigger('click', clickEvent);

        expect(clickEvent.preventDefault).toHaveBeenCalled();
    });
});
