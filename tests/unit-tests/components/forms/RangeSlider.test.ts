import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import { nextTick } from 'vue';
import RangeSlider from '@/components/forms/RangeSlider.vue';
import type { ComponentPublicInstance } from 'vue';

interface RangeSliderInstance extends ComponentPublicInstance {
    $refs: {
        range: any;
    };
}

// Mock noUiSlider
const mockSlider = {
    on: vi.fn(),
    set: vi.fn(),
    get: vi.fn(() => [0, 100]),
    destroy: vi.fn(),
};

vi.mock('nouislider', () => {
    return {
        default: {
            create: vi.fn(() => mockSlider),
        },
    };
});

// Mock TextInput component
vi.mock('@/components/forms/TextInput.vue', () => ({
    default: {
        name: 'TextInput',
        props: ['modelValue', 'numberFormatting', 'svgIcon', 'size', 'inputmode', 'min', 'max'],
        template: '<input :value="modelValue" @input="$emit(\'update:modelValue\', $event.target.value)" />',
    },
}));

describe('RangeSlider', () => {
    const defaultProps = {
        min: 0,
        max: 100,
        bottom: 20,
        top: 80,
        mode: null,
    };

    beforeEach(() => {
        vi.clearAllMocks();
        // Reset mock slider
        mockSlider.get.mockReturnValue([20, 80]);
    });

    it('renders properly with default props', () => {
        const wrapper = mount(RangeSlider, {
            props: defaultProps,
        });
        expect(wrapper.find('.range-slider').exists()).toBe(true);
        expect(wrapper.find('.range-slider-range').exists()).toBe(true);
        expect(wrapper.find('.range-slider-values').exists()).toBe(true);
    });

    it('initializes with default values when bottom and top are not provided', () => {
        const wrapper = mount(RangeSlider, {
            props: {
                min: 0,
                max: 100,
            },
        });

        // Check that the TextInput components have the correct initial values
        const inputs = wrapper.findAllComponents({ name: 'TextInput' });
        expect(inputs[0].props('modelValue')).toBe(0); // bottom value
        expect(inputs[1].props('modelValue')).toBe(100); // top value
    });

    it('initializes with provided bottom and top values', () => {
        const wrapper = mount(RangeSlider, {
            props: defaultProps,
        });

        // Check that the TextInput components have the correct initial values
        const inputs = wrapper.findAllComponents({ name: 'TextInput' });
        expect(inputs[0].props('modelValue')).toBe(20); // bottom value
        expect(inputs[1].props('modelValue')).toBe(80); // top value
    });

    it('renders TextInput components with correct props for currency mode', () => {
        const wrapper = mount(RangeSlider, {
            props: {
                ...defaultProps,
                mode: 'currency',
            },
        });

        const inputs = wrapper.findAllComponents({ name: 'TextInput' });
        expect(inputs).toHaveLength(2);

        // Check first input (bottom value)
        expect(inputs[0].props('modelValue')).toBe(20);
        expect(inputs[0].props('numberFormatting')).toBe(true);
        expect(inputs[0].props('svgIcon')).toEqual({ shape: 'dollar' });
        expect(inputs[0].props('size')).toBe('sm');
        expect(inputs[0].props('inputmode')).toBe('numeric');
        expect(inputs[0].props('min')).toBe(0);
        expect(inputs[0].props('max')).toBe(100);

        // Check second input (top value)
        expect(inputs[1].props('modelValue')).toBe(80);
        expect(inputs[1].props('numberFormatting')).toBe(true);
        expect(inputs[1].props('svgIcon')).toEqual({ shape: 'dollar' });
        expect(inputs[1].props('size')).toBe('sm');
        expect(inputs[1].props('inputmode')).toBe('numeric');
        expect(inputs[1].props('min')).toBe(0);
        expect(inputs[1].props('max')).toBe(100);
    });

    it('renders TextInput components with correct props for year mode', () => {
        const wrapper = mount(RangeSlider, {
            props: {
                ...defaultProps,
                mode: 'year',
            },
        });

        const inputs = wrapper.findAllComponents({ name: 'TextInput' });
        expect(inputs).toHaveLength(2);

        // Check first input (bottom value)
        expect(inputs[0].props('modelValue')).toBe(20);
        expect(inputs[0].props('numberFormatting')).toBe(false);
        expect(inputs[0].props('svgIcon')).toBeNull();
        expect(inputs[0].props('size')).toBe('sm');
        expect(inputs[0].props('inputmode')).toBe('numeric');
        expect(inputs[0].props('min')).toBe(0);
        expect(inputs[0].props('max')).toBe(100);

        // Check second input (top value)
        expect(inputs[1].props('modelValue')).toBe(80);
        expect(inputs[1].props('numberFormatting')).toBe(false);
        expect(inputs[1].props('svgIcon')).toBeNull();
        expect(inputs[1].props('size')).toBe('sm');
        expect(inputs[1].props('inputmode')).toBe('numeric');
        expect(inputs[1].props('min')).toBe(0);
        expect(inputs[1].props('max')).toBe(100);
    });
});
