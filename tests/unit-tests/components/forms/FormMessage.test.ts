import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import FormMessage from '@/components/forms/FormMessage.vue';

describe('FormMessage', () => {
    it('renders properly with default props', () => {
        const wrapper = mount(FormMessage, {
            props: {
                message: 'Test message',
                type: 'info',
            },
        });

        // Check that the component renders with the correct structure
        expect(wrapper.find('.form-message').exists()).toBe(true);
        expect(wrapper.find('.form-message-info').exists()).toBe(true);

        // Check that the message is rendered
        expect(wrapper.text()).toContain('Test message');
    });

    it('renders with different message types', () => {
        // Test error type
        const errorWrapper = mount(FormMessage, {
            props: {
                message: 'Error message',
                type: 'error',
            },
        });
        expect(errorWrapper.find('.form-message-error').exists()).toBe(true);

        // Test success type
        const successWrapper = mount(FormMessage, {
            props: {
                message: 'Success message',
                type: 'success',
            },
        });
        expect(successWrapper.find('.form-message-success').exists()).toBe(true);

        // Test warning type
        const warningWrapper = mount(FormMessage, {
            props: {
                message: 'Warning message',
                type: 'warning',
            },
        });
        expect(warningWrapper.find('.form-message-warning').exists()).toBe(true);

        // Test info type (default)
        const infoWrapper = mount(FormMessage, {
            props: {
                message: 'Info message',
                type: 'info',
            },
        });
        expect(infoWrapper.find('.form-message-info').exists()).toBe(true);
    });

    it('handles null message and type', () => {
        const wrapper = mount(FormMessage, {
            props: {
                message: null,
                type: null,
            },
        });

        // Component should still render but without content
        expect(wrapper.find('.form-message').exists()).toBe(true);
        expect(wrapper.text()).toBe('');
    });

    it('renders HTML content in message', () => {
        const wrapper = mount(FormMessage, {
            props: {
                message: '<strong>Bold</strong> and <em>italic</em> text',
                type: 'info',
            },
        });

        // Check that HTML content is rendered
        expect(wrapper.find('strong').exists()).toBe(true);
        expect(wrapper.find('strong').text()).toBe('Bold');
        expect(wrapper.find('em').exists()).toBe(true);
        expect(wrapper.find('em').text()).toBe('italic');
    });

    it('applies animation classes when transitioning', () => {
        const wrapper = mount(FormMessage, {
            props: {
                message: 'Test message',
                type: 'info',
            },
        });

        // Check that animation classes are present
        expect(wrapper.find('.form-message-anim-enter-active').exists()).toBe(false);
        expect(wrapper.find('.form-message-anim-leave-active').exists()).toBe(false);
        expect(wrapper.find('.form-message-anim-enter-from').exists()).toBe(false);
        expect(wrapper.find('.form-message-anim-leave-to').exists()).toBe(false);
        expect(wrapper.find('.form-message-anim-enter-to').exists()).toBe(false);
        expect(wrapper.find('.form-message-anim-leave-from').exists()).toBe(false);
    });
});
