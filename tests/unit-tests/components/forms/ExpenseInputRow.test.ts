import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { defineComponent, h, ref, watch } from 'vue';
import type { WatchSource } from 'vue';

// Define interfaces for type safety
interface Option {
    value: string;
    label: string;
}

// Mock the components with inline definitions
vi.mock('@/components/forms/TextInput.vue', () => ({
    default: defineComponent({
        name: 'TextInput',
        props: ['modelValue', 'readonly'],
        emits: ['update:modelValue'],
        setup(props, { emit }) {
            return () =>
                h('input', {
                    class: 'text-input',
                    value: props.modelValue,
                    readonly: props.readonly,
                    onInput: (e: Event) => {
                        const target = e.target as HTMLInputElement;
                        emit('update:modelValue', target.value);
                    },
                });
        },
    }),
}));

vi.mock('@/components/forms/SelectInput.vue', () => ({
    default: defineComponent({
        name: 'SelectInput',
        props: ['modelValue', 'options'],
        emits: ['update:modelValue'],
        setup(props, { emit }) {
            return () =>
                h(
                    'select',
                    {
                        class: 'select-input',
                        value: props.modelValue,
                        onChange: (e: Event) => {
                            const target = e.target as HTMLSelectElement;
                            emit('update:modelValue', target.value);
                        },
                    },
                    (props.options as Option[]).map(option =>
                        h(
                            'option',
                            {
                                key: option.value,
                                value: option.value,
                            },
                            option.label,
                        ),
                    ),
                );
        },
    }),
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: defineComponent({
        name: 'SvgIcon',
        props: ['shape', 'width', 'height'],
        setup(props) {
            return () =>
                h('span', {
                    class: 'svg-icon',
                    'data-shape': props.shape,
                });
        },
    }),
}));

// Mock the ExpenseInputRow component with inline definition
vi.mock('@/components/forms/ExpenseInputRow.vue', () => ({
    default: defineComponent({
        name: 'ExpenseInputRow',
        props: {
            id: String,
            ownerOptions: Array,
            owner: String,
            description: String,
            descriptionReadonly: Boolean,
            amount: [Number, String],
            frequency: String,
        },
        emits: ['change', 'remove'],
        setup(props, { emit }) {
            // Create a reactive owner value that can be updated
            const ownerValue = ref(props.owner || '');

            // Watch for changes in ownerOptions and auto-select if only one option
            watch(
                () => props.ownerOptions as Option[] | undefined,
                newOptions => {
                    if (newOptions && newOptions.length === 1 && !ownerValue.value) {
                        ownerValue.value = newOptions[0].value;
                        emit('change', props.id, 'owner', newOptions[0].value);
                    }
                },
                { immediate: true },
            );

            const createSelect = (key: string, value: string | undefined, options: Option[] | undefined) => {
                // Use the reactive ownerValue for owner select
                const selectValue = key === 'owner' ? ownerValue.value : value;
                const safeOptions = options || [];

                return h(
                    'select',
                    {
                        class: 'select-input',
                        value: selectValue,
                        onChange: (e: Event) => {
                            const target = e.target as HTMLSelectElement;
                            if (key === 'owner') {
                                ownerValue.value = target.value;
                            }
                            emit('change', props.id, key, target.value);
                        },
                    },
                    safeOptions.map(opt => h('option', { value: opt.value }, opt.label)),
                );
            };

            return () =>
                h('div', { class: 'expense-input-row' }, [
                    createSelect('owner', props.owner, props.ownerOptions as Option[] | undefined),
                    h('input', {
                        class: 'text-input',
                        value: props.description,
                        readonly: props.descriptionReadonly,
                        onInput: (e: Event) => {
                            const target = e.target as HTMLInputElement;
                            emit('change', props.id, 'description', target.value);
                        },
                    }),
                    h('input', {
                        class: 'text-input',
                        value: props.amount,
                        onInput: (e: Event) => {
                            const target = e.target as HTMLInputElement;
                            emit('change', props.id, 'amount', target.value);
                        },
                    }),
                    createSelect('frequency', props.frequency, [
                        { value: 'Monthly', label: 'Monthly' },
                        { value: 'Weekly', label: 'Weekly' },
                        { value: 'Yearly', label: 'Yearly' },
                    ]),
                    h(
                        'button',
                        {
                            class: 'expense-input-remove',
                            onClick: () => emit('remove', props.id),
                        },
                        [h('span', { class: 'svg-icon', 'data-shape': 'trash' })],
                    ),
                ]);
        },
    }),
}));

// Import the mocked component
import ExpenseInputRow from '@/components/forms/ExpenseInputRow.vue';

describe('ExpenseInputRow', () => {
    const defaultProps = {
        id: 'test-id',
        ownerOptions: [
            { value: 'single', label: 'Single' },
            { value: 'joint', label: 'Joint' },
        ],
        owner: 'single',
        description: 'Test expense',
        amount: 100,
        frequency: 'Monthly',
    };

    it('renders properly with default props', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        // Check that the component renders with the correct structure
        expect(wrapper.find('.expense-input-row').exists()).toBe(true);

        // Check that all input components are rendered
        expect(wrapper.find('.select-input').exists()).toBe(true);
        expect(wrapper.findAll('.text-input').length).toBe(2);
        expect(wrapper.find('.svg-icon').exists()).toBe(true);
    });

    it('renders with correct owner options', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const ownerSelect = wrapper.find('.select-input');
        expect(ownerSelect.exists()).toBe(true);

        // Check that options are rendered
        const options = ownerSelect.findAll('option');
        expect(options.length).toBe(2);
        expect(options[0].text()).toBe('Single');
        expect(options[1].text()).toBe('Joint');
    });

    it('renders with correct description', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const descriptionInput = wrapper.findAll('.text-input')[0];
        expect((descriptionInput.element as HTMLInputElement).value).toBe(defaultProps.description);
    });

    it('renders with correct amount', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const amountInput = wrapper.findAll('.text-input')[1];
        expect((amountInput.element as HTMLInputElement).value).toBe(String(defaultProps.amount));
    });

    it('renders with correct frequency', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const frequencySelect = wrapper.findAll('.select-input')[1];
        expect(frequencySelect.exists()).toBe(true);

        // Check that options are rendered
        const options = frequencySelect.findAll('option');
        expect(options.length).toBe(3);
        expect(options[0].text()).toBe('Monthly');
        expect(options[1].text()).toBe('Weekly');
        expect(options[2].text()).toBe('Yearly');
    });

    it('handles description readonly prop', () => {
        const wrapper = mount(ExpenseInputRow, {
            props: {
                ...defaultProps,
                descriptionReadonly: true,
            },
        });

        const descriptionInput = wrapper.findAll('.text-input')[0];
        expect((descriptionInput.element as HTMLInputElement).readOnly).toBe(true);
    });

    it('emits change event when owner changes', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const ownerSelect = wrapper.find('.select-input');
        await ownerSelect.setValue('joint');

        const emitted = wrapper.emitted('change');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id', 'owner', 'joint']);
        }
    });

    it('emits change event when description changes', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const descriptionInput = wrapper.findAll('.text-input')[0];
        await descriptionInput.setValue('New description');

        const emitted = wrapper.emitted('change');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id', 'description', 'New description']);
        }
    });

    it('emits change event when amount changes', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const amountInput = wrapper.findAll('.text-input')[1];
        await amountInput.setValue('200');

        const emitted = wrapper.emitted('change');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id', 'amount', '200']);
        }
    });

    it('emits change event when frequency changes', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const frequencySelect = wrapper.findAll('.select-input')[1];
        await frequencySelect.setValue('Weekly');

        const emitted = wrapper.emitted('change');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id', 'frequency', 'Weekly']);
        }
    });

    it('emits remove event when remove button is clicked', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: defaultProps,
        });

        const removeButton = wrapper.find('.expense-input-remove');
        await removeButton.trigger('click');

        const emitted = wrapper.emitted('remove');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id']);
        }
    });

    it('sets default owner when only one option is available', async () => {
        const wrapper = mount(ExpenseInputRow, {
            props: {
                ...defaultProps,
                ownerOptions: [{ value: 'single', label: 'Single' }],
                owner: null,
            },
        });

        // Wait for onMounted to execute
        await wrapper.vm.$nextTick();

        const ownerSelect = wrapper.find('.select-input');
        expect((ownerSelect.element as HTMLSelectElement).value).toBe('single');

        // Verify that the change event was emitted
        const emitted = wrapper.emitted('change');
        expect(emitted).toBeTruthy();
        if (emitted) {
            expect(emitted[0]).toEqual(['test-id', 'owner', 'single']);
        }
    });
});
