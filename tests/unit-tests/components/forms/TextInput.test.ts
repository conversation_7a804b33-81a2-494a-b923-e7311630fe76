import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import TextInput from '@/components/forms/TextInput.vue';
import type { TextInputProps } from '@/components/forms/TextInput.vue';

describe('TextInput', () => {
    it('renders properly with default props', () => {
        const wrapper = mount(TextInput);
        expect(wrapper.find('.text-input').exists()).toBe(true);
        expect(wrapper.find('input').exists()).toBe(true);
    });

    it('renders with label when provided', () => {
        const label = 'Test Label';
        const wrapper = mount(TextInput, {
            props: { label } as TextInputProps,
        });
        expect(wrapper.find('.text-input-label').text()).toBe(label);
    });

    it('renders with placeholder when no label is provided', () => {
        const placeholder = 'Test Placeholder';
        const wrapper = mount(TextInput, {
            props: { placeholder } as TextInputProps,
        });
        expect(wrapper.find('input').attributes('placeholder')).toBe(placeholder);
    });

    it('does not render placeholder when label is provided', () => {
        const wrapper = mount(TextInput, {
            props: {
                label: 'Test Label',
                placeholder: 'Test Placeholder',
            } as TextInputProps,
        });
        expect(wrapper.find('input').attributes('placeholder')).toBeUndefined();
    });

    it('renders with icon when svgIcon prop is provided', () => {
        const wrapper = mount(TextInput, {
            props: {
                svgIcon: {
                    shape: 'test',
                    position: 'left',
                },
            } as TextInputProps,
        });
        expect(wrapper.find('.text-input-icon').exists()).toBe(true);
    });

    it('renders with suffix when provided', () => {
        const suffix = 'Test Suffix';
        const wrapper = mount(TextInput, {
            props: { suffix } as TextInputProps,
        });
        expect(wrapper.find('.text-input-suffix').text()).toBe(suffix);
    });

    it('applies validation state classes correctly', () => {
        const wrapper = mount(TextInput, {
            props: { validationState: 'error' } as TextInputProps,
        });
        expect(wrapper.classes()).toContain('text-input-v-error');
    });

    it('applies size classes correctly', () => {
        const wrapper = mount(TextInput, {
            props: { size: 'sm' } as TextInputProps,
        });
        expect(wrapper.classes()).toContain('text-input-s-sm');
    });

    it('handles disabled state correctly', () => {
        const wrapper = mount(TextInput, {
            props: { disabled: true } as TextInputProps,
        });
        expect(wrapper.find('input').attributes('disabled')).toBeDefined();
    });

    it('handles readonly state correctly', () => {
        const wrapper = mount(TextInput, {
            props: { readonly: true } as TextInputProps,
        });
        expect(wrapper.find('input').attributes('readonly')).toBeDefined();
        expect(wrapper.classes()).toContain('text-input-readonly');
    });

    it('toggles password visibility when type is password', async () => {
        const wrapper = mount(TextInput, {
            props: { type: 'password' } as TextInputProps,
        });

        const toggleButton = wrapper.find('.text-input-password-toggle');
        expect(toggleButton.exists()).toBe(true);

        const input = wrapper.find('input');
        expect(input.attributes('type')).toBe('password');

        await toggleButton.trigger('click');
        expect(input.attributes('type')).toBe('text');

        await toggleButton.trigger('click');
        expect(input.attributes('type')).toBe('password');
    });

    it('applies withBorder class when withBorder prop is true', () => {
        const wrapper = mount(TextInput, {
            props: { withBorder: true } as TextInputProps,
        });
        expect(wrapper.classes()).toContain('text-input-with-border');
    });

    it('does not apply withBorder class when withBorder prop is false', () => {
        const wrapper = mount(TextInput, {
            props: { withBorder: false } as TextInputProps,
        });
        expect(wrapper.classes()).not.toContain('text-input-with-border');
    });

    it('handles v-model correctly', async () => {
        const wrapper = mount(TextInput, {
            props: {
                modelValue: 'initial value',
                'onUpdate:modelValue': (e: string) => wrapper.setProps({ modelValue: e }),
            } as TextInputProps,
        });

        const input = wrapper.find('input');
        expect(input.element.value).toBe('initial value');

        await input.setValue('new value');
        expect(wrapper.props('modelValue')).toBe('new value');
    });
});
