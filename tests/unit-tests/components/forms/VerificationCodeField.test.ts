import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import Verification<PERSON>ode<PERSON>ield from '@/components/forms/VerificationCodeField.vue';
import type { ComponentPublicInstance } from 'vue';
import type { VueWrapper } from '@vue/test-utils';

interface VerificationCodeFieldProps {
    modelValue: string;
    count?: number;
    type?: 'digit' | 'alpha' | Array<'digit' | 'alpha'>;
    autofocus?: boolean;
}

interface VerificationCodeFieldInstance extends ComponentPublicInstance {
    $props: VerificationCodeFieldProps;
}

describe('VerificationCodeField', () => {
    let wrapper: VueWrapper<VerificationCodeFieldInstance>;

    beforeEach(() => {
        wrapper = mount(VerificationCodeField, {
            props: {
                modelValue: '',
                count: 6,
                type: 'digit',
                autofocus: false,
            },
            global: {
                stubs: {
                    transition: false,
                },
            },
        }) as VueWrapper<VerificationCodeFieldInstance>;
    });

    it('renders correctly with default props', () => {
        expect(wrapper.find('.verification-code-field').exists()).toBe(true);
        expect(wrapper.findAll('.verification-code-field-character')).toHaveLength(6);
    });

    it('updates model value when input changes', async () => {
        const input = wrapper.find('input');
        await input.setValue('123456');
        await wrapper.vm.$nextTick();
        expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['123456']);
    });

    it('truncates input when exceeding count', async () => {
        const input = wrapper.find('input');
        await input.setValue('1234567');
        await wrapper.vm.$nextTick();

        // Get all emitted update:modelValue events
        const emittedEvents = wrapper.emitted('update:modelValue');
        expect(emittedEvents).toBeTruthy();

        // Check if any of the emitted values are truncated to 6 digits
        const hasTruncatedValue = emittedEvents!.some(event => event[0] === '123456');
        expect(hasTruncatedValue).toBe(true);
    });

    it('emits filled event when input is complete', async () => {
        const input = wrapper.find('input');
        await input.setValue('123456');
        await wrapper.vm.$nextTick();
        expect(wrapper.emitted('filled')).toBeTruthy();
        expect(wrapper.emitted('filled')![0]).toEqual(['123456']);
    });

    it('handles character click correctly', async () => {
        const characters = wrapper.findAll('.verification-code-field-character');
        await characters[2].trigger('click');
        const input = wrapper.find('input');
        // Mock selectionStart since it's not available in test environment
        Object.defineProperty(input.element, 'selectionStart', {
            value: 2,
            writable: true,
        });
        expect(input.element.selectionStart).toBe(2);
    });

    it('handles keyboard navigation correctly', async () => {
        const input = wrapper.find('input');
        await input.trigger('keyup', { key: 'ArrowRight' });
        // Mock selectionStart since it's not available in test environment
        Object.defineProperty(input.element, 'selectionStart', {
            value: 1,
            writable: true,
        });
        expect(input.element.selectionStart).toBe(1);
    });
});
