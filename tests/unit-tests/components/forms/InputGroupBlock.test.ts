import { describe, it, expect, vi } from 'vitest';
import { mount, VueWrapper } from '@vue/test-utils';
import InputGroupBlock from '@/components/forms/InputGroupBlock.vue';
import type { ComponentPublicInstance } from 'vue';

type Layout = 'horizontal' | 'vertical';
type ValidationState = 'none' | 'error';

interface InputGroupBlockProps {
    title: string;
    description?: string;
    layout?: Layout;
    validationState?: ValidationState;
    labelFor?: string;
    multiRow?: boolean;
}

interface InputGroupBlockInstance extends ComponentPublicInstance {
    $refs: {
        range: any;
    };
}

describe('InputGroupBlock', () => {
    const defaultProps: InputGroupBlockProps = {
        title: 'Test Title',
        description: 'Test Description',
        layout: 'horizontal',
        validationState: 'none',
        labelFor: 'test-input',
        multiRow: false,
    };

    it('renders properly with default props', () => {
        const wrapper = mount(InputGroupBlock, {
            props: defaultProps,
        });

        // Check that the component renders with the correct structure
        expect(wrapper.find('.input-group-block').exists()).toBe(true);
        expect(wrapper.find('.input-group-block-label').exists()).toBe(true);
        expect(wrapper.find('.input-group-block-fields').exists()).toBe(true);

        // Check that the title and description are rendered
        expect(wrapper.find('label').text()).toContain('Test Title');
        expect(wrapper.find('.input-group-block-description').text()).toContain('Test Description');

        // Check that the label has the correct 'for' attribute
        expect(wrapper.find('label').attributes('for')).toBe('test-input');
    });

    it('renders without description when not provided', () => {
        const { description, ...propsWithoutDescription } = defaultProps;

        const wrapper = mount(InputGroupBlock, {
            props: propsWithoutDescription,
        });

        // Check that the description is not rendered
        expect(wrapper.find('.input-group-block-description').exists()).toBe(false);
    });

    it('applies the correct layout class', () => {
        // Test horizontal layout (default)
        const horizontalWrapper = mount(InputGroupBlock, {
            props: defaultProps,
        });
        expect(horizontalWrapper.find('.input-group-block-l-horizontal').exists()).toBe(true);

        // Test vertical layout
        const verticalWrapper = mount(InputGroupBlock, {
            props: { ...defaultProps, layout: 'vertical' as Layout },
        });
        expect(verticalWrapper.find('.input-group-block-l-vertical').exists()).toBe(true);
    });

    it('applies the correct validation state class', () => {
        // Test none validation state (default)
        const noneWrapper = mount(InputGroupBlock, {
            props: defaultProps,
        });
        expect(noneWrapper.find('.input-group-block-v-none').exists()).toBe(true);

        // Test error validation state
        const errorWrapper = mount(InputGroupBlock, {
            props: { ...defaultProps, validationState: 'error' as ValidationState },
        });
        expect(errorWrapper.find('.input-group-block-v-error').exists()).toBe(true);
    });

    it('applies the correct field class based on multiRow prop', () => {
        // Test single row (default)
        const singleRowWrapper = mount(InputGroupBlock, {
            props: defaultProps,
        });
        expect(singleRowWrapper.find('.input-group-block-fields-multiple').exists()).toBe(false);

        // Test multiple rows
        const multiRowWrapper = mount(InputGroupBlock, {
            props: { ...defaultProps, multiRow: true },
        });
        expect(multiRowWrapper.find('.input-group-block-fields-multiple').exists()).toBe(true);
    });

    it('renders slot content', () => {
        const wrapper = mount(InputGroupBlock, {
            props: defaultProps,
            slots: {
                default: '<div class="test-slot">Slot Content</div>',
            },
        });

        // Check that the slot content is rendered
        expect(wrapper.find('.test-slot').exists()).toBe(true);
        expect(wrapper.find('.test-slot').text()).toBe('Slot Content');
    });

    it('renders HTML content in title and description', () => {
        const wrapper = mount(InputGroupBlock, {
            props: {
                ...defaultProps,
                title: '<strong>Bold Title</strong>',
                description: '<em>Italic Description</em>',
            },
        });

        // Check that the HTML content is rendered
        expect(wrapper.find('label strong').exists()).toBe(true);
        expect(wrapper.find('label strong').text()).toBe('Bold Title');

        expect(wrapper.find('.input-group-block-description em').exists()).toBe(true);
        expect(wrapper.find('.input-group-block-description em').text()).toBe('Italic Description');
    });
});
