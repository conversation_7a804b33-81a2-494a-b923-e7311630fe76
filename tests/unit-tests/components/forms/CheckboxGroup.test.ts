import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import CheckboxGroup from '@/components/forms/CheckboxGroup.vue';
import type { CheckboxGroupOption } from '@/components/forms/CheckboxGroup.vue';

describe('CheckboxGroup', () => {
    const defaultOptions: CheckboxGroupOption[] = [
        {
            label: 'Option 1',
            name: 'option1',
            value: false,
        },
        {
            label: 'Option 2',
            name: 'option2',
            value: true,
        },
    ];

    it('renders with default props', () => {
        const wrapper = mount(CheckboxGroup, {
            props: {
                options: defaultOptions,
                mode: 'checkbox',
            },
        });

        expect(wrapper.find('.checkbox-group').exists()).toBe(true);
        expect(wrapper.findAll('.checkbox-group-option')).toHaveLength(2);
    });

    it('renders in radio mode', () => {
        const wrapper = mount(CheckboxGroup, {
            props: {
                options: defaultOptions,
                mode: 'radio',
            },
        });

        expect(wrapper.find('input[type="radio"]').exists()).toBe(true);
        expect(wrapper.find('.checkbox-group-faux-radio').exists()).toBe(true);
    });

    it('renders in checkbox mode', () => {
        const wrapper = mount(CheckboxGroup, {
            props: {
                options: defaultOptions,
                mode: 'checkbox',
            },
        });

        expect(wrapper.find('input[type="checkbox"]').exists()).toBe(true);
        expect(wrapper.find('.checkbox-group-faux-checkbox').exists()).toBe(true);
    });

    it('renders with different sizes', () => {
        const sizes = ['sm', 'lg'] as const;

        sizes.forEach(size => {
            const wrapper = mount(CheckboxGroup, {
                props: {
                    options: defaultOptions,
                    mode: 'checkbox',
                    size,
                },
            });

            expect(wrapper.find(`.checkbox-group-s-${size}`).exists()).toBe(true);
        });
    });

    it('renders with different variants', () => {
        const variants = ['buttons', 'list'] as const;

        variants.forEach(variant => {
            const wrapper = mount(CheckboxGroup, {
                props: {
                    options: defaultOptions,
                    mode: 'checkbox',
                    variant,
                },
            });

            expect(wrapper.find(`.checkbox-group-${variant}`).exists()).toBe(true);
        });
    });

    it('renders filter bar when filterable is true', () => {
        const wrapper = mount(CheckboxGroup, {
            props: {
                options: defaultOptions,
                mode: 'checkbox',
                filterable: true,
            },
        });

        expect(wrapper.find('.checkbox-group-filter-bar').exists()).toBe(true);
        expect(wrapper.find('.checkbox-group-filter-input').exists()).toBe(true);
        expect(wrapper.find('.checkbox-group-filter-clear').exists()).toBe(true);
    });

    it('filters options when search text is entered', async () => {
        const options: CheckboxGroupOption[] = [
            {
                label: 'Apple',
                name: 'apple',
                value: false,
            },
            {
                label: 'Banana',
                name: 'banana',
                value: false,
            },
            {
                label: 'Orange',
                name: 'orange',
                value: false,
            },
        ];

        const wrapper = mount(CheckboxGroup, {
            props: {
                options,
                mode: 'checkbox',
                filterable: true,
            },
        });

        const filterInput = wrapper.find('.checkbox-group-filter-input');
        await filterInput.setValue('app');

        expect(wrapper.findAll('.checkbox-group-option')).toHaveLength(1);
        expect(wrapper.find('.checkbox-group-option-label').text()).toBe('Apple');
    });

    it('clears filter when clear button is clicked', async () => {
        const options: CheckboxGroupOption[] = [
            {
                label: 'Apple',
                name: 'apple',
                value: false,
            },
            {
                label: 'Banana',
                name: 'banana',
                value: false,
            },
        ];

        const wrapper = mount(CheckboxGroup, {
            props: {
                options,
                mode: 'checkbox',
                filterable: true,
            },
        });

        const filterInput = wrapper.find('.checkbox-group-filter-input');
        await filterInput.setValue('app');
        expect(wrapper.findAll('.checkbox-group-option')).toHaveLength(1);

        await wrapper.find('.checkbox-group-filter-clear').trigger('click');
        expect(wrapper.findAll('.checkbox-group-option')).toHaveLength(2);
    });

    it('updates selected value in radio mode', async () => {
        const wrapper = mount(CheckboxGroup, {
            props: {
                options: defaultOptions,
                mode: 'radio',
            },
        });

        const radio = wrapper.find('input[type="radio"]');
        await radio.setValue('option1');

        expect(wrapper.emitted()['update:selected']).toBeTruthy();
        expect(wrapper.emitted()['update:selected'][0]).toEqual(['option1']);
    });

    it('renders descriptions when provided', () => {
        const options: CheckboxGroupOption[] = [
            {
                label: 'Option 1',
                name: 'option1',
                value: false,
                description: 'Description 1',
            },
        ];

        const wrapper = mount(CheckboxGroup, {
            props: {
                options,
                mode: 'checkbox',
            },
        });

        expect(wrapper.find('.checkbox-group-option-description').exists()).toBe(true);
        expect(wrapper.find('.checkbox-group-option-description').text()).toBe('Description 1');
    });
});
