import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import CheckboxInput from '@/components/forms/CheckboxInput.vue';

describe('CheckboxInput', () => {
    it('renders with default props', () => {
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
            },
        });

        expect(wrapper.find('input[type="checkbox"]').exists()).toBe(true);
        expect(wrapper.find('.checkbox-input').exists()).toBe(true);
    });

    it('renders with a label', () => {
        const label = 'Test Label';
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
                label,
            },
        });

        expect(wrapper.find('.checkbox-input-option-label').text()).toBe(label);
    });

    it('renders with different sizes', () => {
        const sizes = ['xs', 'sm', 'lg'] as const;

        sizes.forEach(size => {
            const wrapper = mount(CheckboxInput, {
                props: {
                    modelValue: false,
                    size,
                },
            });

            expect(wrapper.find(`.checkbox-input-s-${size}`).exists()).toBe(true);
        });
    });

    it('updates model value when clicked', async () => {
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
            },
        });

        const input = wrapper.find('input');
        await input.setValue(true);
        expect(wrapper.emitted()['update:modelValue'][0]).toEqual([true]);

        await input.setValue(false);
        expect(wrapper.emitted()['update:modelValue'][1]).toEqual([false]);
    });

    it('generates unique element ID', () => {
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
            },
        });

        const input = wrapper.find('input');
        expect(input.attributes('id')).toMatch(/^checkbox-input-/);
    });

    it('uses provided ID when specified', () => {
        const customId = 'custom-checkbox-id';
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
                id: customId,
            },
        });

        const input = wrapper.find('input');
        expect(input.attributes('id')).toBe(customId);
    });

    it('uses provided name when specified', () => {
        const customName = 'custom-checkbox-name';
        const wrapper = mount(CheckboxInput, {
            props: {
                modelValue: false,
                name: customName,
            },
        });

        const input = wrapper.find('input');
        expect(input.attributes('name')).toBe(customName);
    });
});
