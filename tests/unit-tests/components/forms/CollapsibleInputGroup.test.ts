import { describe, it, expect, vi } from 'vitest';
import { mount, flushPromises } from '@vue/test-utils';
import { defineComponent, h, ref } from 'vue';
import type { TSvgIconShapeKey } from '@/components/images/SvgIconShapes';

vi.mock('@vueuse/core', () => ({
    useId: () => 'test-id',
    onKeyStroke: () => () => {},
    onClickOutside: () => () => {},
}));

vi.mock('@/composables/useElementBlur', () => ({
    default: () => ({ listener: () => {} }),
}));

vi.mock('@floating-ui/vue', () => ({
    useFloating: () => ({ floatingStyles: ref({}) }),
    autoUpdate: () => {},
    autoPlacement: () => {},
    flip: () => {},
    offset: () => {},
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: defineComponent({
        name: 'SvgIcon',
        props: ['shape', 'width', 'height'],
        setup(props) {
            return () => h('span', { class: 'svg-icon', 'data-shape': props.shape });
        },
    }),
}));

vi.mock('@/components/elements/Button.vue', () => ({
    default: defineComponent({
        name: 'Button',
        props: ['variant', 'size', 'svg-icon', 'tabindex'],
        setup(props, { slots }) {
            return () =>
                h(
                    'button',
                    {
                        class: ['button', props.variant, props.size].filter(Boolean).join(' '),
                        tabindex: props.tabindex,
                    },
                    slots.default?.(),
                );
        },
    }),
}));

import CollapsibleInputGroup from '@/components/forms/CollapsibleInputGroup.vue';

describe('CollapsibleInputGroup', () => {
    const defaultProps = {
        label: 'Test Label',
        description: 'Test Description',
        formattedValue: 'Test Value',
        validationState: 'good' as const,
        autoFocus: false,
        expandMode: 'inline' as const,
        collapseOnBlur: true,
        collapseOnClickOutside: true,
        showDescriptionOnEdit: true,
        isPulsing: false,
        footerButtons: true,
        footerDisclaimer: 'Test Disclaimer',
        confirmButtonText: 'Done',
        confirmButtonIcon: 'check' as TSvgIconShapeKey,
    };

    it('renders properly with default props', () => {
        const wrapper = mount(CollapsibleInputGroup, {
            props: defaultProps,
            slots: {
                default: () => h('div', { class: 'test-slot' }, 'Test Slot Content'),
            },
        });

        expect(wrapper.find('.collapsible-input-group').exists()).toBe(true);
        expect(wrapper.find('.collapsible-input-group-label').text()).toContain('Test Label');
        expect(wrapper.find('.collapsible-input-group-description').text()).toContain('Test Value');
        expect(wrapper.find('.collapsible-input-group-indicator-good').exists()).toBe(true);
        expect(wrapper.find('.collapsible-input-group-edit-icon').exists()).toBe(true);
    });
});
