import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import TextareaInput from '@/components/forms/TextareaInput.vue';
import type { ComponentPublicInstance } from 'vue';

describe('TextareaInput', () => {
    it('renders properly with default props', () => {
        const wrapper = mount(TextareaInput);
        expect(wrapper.find('.textarea-input').exists()).toBe(true);
        expect(wrapper.find('textarea').exists()).toBe(true);
    });

    it('renders with label when provided', () => {
        const label = 'Test Label';
        const wrapper = mount(TextareaInput, {
            props: { label },
        });
        expect(wrapper.find('.textarea-input-label').text()).toBe(label);
    });

    it('renders with placeholder when no label is provided', () => {
        const placeholder = 'Test Placeholder';
        const wrapper = mount(TextareaInput, {
            props: { placeholder },
        });
        expect(wrapper.find('textarea').attributes('placeholder')).toBe(placeholder);
    });

    it('does not render placeholder when label is provided', () => {
        const wrapper = mount(TextareaInput, {
            props: {
                label: 'Test Label',
                placeholder: 'Test Placeholder',
            },
        });
        expect(wrapper.find('textarea').attributes('placeholder')).toBeUndefined();
    });

    it('applies validation state classes correctly', () => {
        const wrapper = mount(TextareaInput, {
            props: { validationState: 'error' },
        });
        expect(wrapper.classes()).toContain('textarea-input-v-error');
    });

    it('applies size classes correctly', () => {
        const wrapper = mount(TextareaInput, {
            props: { size: 'sm' },
        });
        expect(wrapper.classes()).toContain('textarea-input-s-sm');
    });

    it('handles disabled state correctly', () => {
        const wrapper = mount(TextareaInput, {
            props: { disabled: true },
        });
        expect(wrapper.find('textarea').attributes('disabled')).toBeDefined();
    });

    it('applies filled class when model has value', () => {
        const wrapper = mount(TextareaInput, {
            props: {
                modelValue: 'test value',
            },
        });
        expect(wrapper.classes()).toContain('textarea-input-filled');
    });

    it('does not apply filled class when model is empty', () => {
        const wrapper = mount(TextareaInput, {
            props: {
                modelValue: '',
            },
        });
        expect(wrapper.classes()).not.toContain('textarea-input-filled');
    });

    it('handles v-model correctly', async () => {
        const wrapper = mount(TextareaInput, {
            props: {
                modelValue: 'initial value',
                'onUpdate:modelValue': (value: string | null | undefined): void => {
                    wrapper.setProps({ modelValue: value });
                },
            },
        });

        const textarea = wrapper.find('textarea');
        expect(textarea.element.value).toBe('initial value');

        await textarea.setValue('new value');
        expect(wrapper.props('modelValue')).toBe('new value');
    });

    it('renders spacer div with model value', () => {
        const testValue = 'test value';
        const wrapper = mount(TextareaInput, {
            props: {
                modelValue: testValue,
            },
        });
        expect(wrapper.find('.textarea-input-spacer').text()).toBe(testValue);
    });

    it('generates unique element ID and associates label correctly', () => {
        const wrapper = mount(TextareaInput, {
            props: {
                label: 'Test Label',
            },
        });
        const textarea = wrapper.find('textarea');
        const label = wrapper.find('.textarea-input-label');

        expect(textarea.exists()).toBe(true);
        expect(label.exists()).toBe(true);

        const textareaId = textarea.attributes('id');
        expect(textareaId).toMatch(/^textarea-input-/);
        expect(label.attributes('for')).toBe(textareaId);
    });
});
