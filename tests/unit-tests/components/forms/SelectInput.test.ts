import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import SelectInput from '@/components/forms/SelectInput.vue';

describe('SelectInput', () => {
    const mockOptions = [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
    ];

    it('renders properly with default props', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
            },
        });
        expect(wrapper.find('.select-input').exists()).toBe(true);
        expect(wrapper.find('.select-input-select').exists()).toBe(true);
        expect(wrapper.find('.select-input-caret').exists()).toBe(true);
    });

    it('renders label when provided', () => {
        const label = 'Test Label';
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                label,
            },
        });
        const labelElement = wrapper.find('.select-input-label');
        expect(labelElement.exists()).toBe(true);
        expect(labelElement.text()).toBe(label);
    });

    it('does not render label when not provided', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
            },
        });
        expect(wrapper.find('.select-input-label').exists()).toBe(false);
    });

    it('renders all options correctly', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
            },
        });
        const options = wrapper.findAll('option');
        expect(options).toHaveLength(mockOptions.length);
        options.forEach((option, index) => {
            expect(option.text()).toBe(mockOptions[index].label);
            expect(option.attributes('value')).toBe(mockOptions[index].value);
        });
    });

    it('renders placeholder when provided', () => {
        const placeholder = 'Select an option';
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                placeholder,
            },
        });
        const placeholderOption = wrapper.find('option[disabled]');
        expect(placeholderOption.exists()).toBe(true);
        expect(placeholderOption.text()).toBe(placeholder);
        expect(placeholderOption.attributes('selected')).toBeDefined();
    });

    it('updates v-model value when selection changes', async () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                modelValue: null,
                'onUpdate:modelValue': (e: any) => wrapper.setProps({ modelValue: e }),
            },
        });
        const select = wrapper.find('select');
        await select.setValue('option2');
        expect(wrapper.props('modelValue')).toBe('option2');
    });

    it('applies validation state classes', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                validationState: 'error',
            },
        });
        expect(wrapper.classes()).toContain('select-input-v-error');
    });

    it('applies size variant classes', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                size: 'sm',
            },
        });
        expect(wrapper.classes()).toContain('select-input-s-sm');
    });

    it('applies alignment classes', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                alignment: 'right',
            },
        });
        expect(wrapper.classes()).toContain('select-input-align-right');
    });

    it('sets readonly attribute when readonly prop is true', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                readonly: true,
            },
        });
        expect(wrapper.find('select').attributes('readonly')).toBeDefined();
    });

    it('sets aria attributes correctly', () => {
        const ariaLabel = 'Test Aria Label';
        const ariaLabelledby = 'test-label-id';
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                ariaLabel,
                ariaLabelledby,
            },
        });
        const select = wrapper.find('select');
        expect(select.attributes('aria-label')).toBe(ariaLabel);
        expect(select.attributes('aria-labelledby')).toBe(ariaLabelledby);
    });

    it('applies filled class when value is selected', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                modelValue: 'option1',
            },
        });
        expect(wrapper.classes()).toContain('select-input-filled');
    });

    it('applies empty class when no value is selected', () => {
        const wrapper = mount(SelectInput, {
            props: {
                options: mockOptions,
                modelValue: null,
            },
        });
        expect(wrapper.classes()).toContain('select-input-empty');
    });
});
