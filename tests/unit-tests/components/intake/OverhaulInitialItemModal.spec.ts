// OverhaulInitialItemModal.spec.ts

// Mock ResizeObserver before anything else
class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
}

global.ResizeObserver = ResizeObserver;

import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import OverhaulInitialItemModal from '@/components/intake/OverhaulInitialItemModal.vue';

// Mock useRequestFetch
vi.mock('#app', () => ({
    useRequestFetch: vi.fn(() => ({
        fetch: vi.fn(),
    })),
}));

// Mock the overhaul store
vi.mock('@/stores/overhaul', () => ({
    useOverhaulStore: vi.fn(() => ({
        updateItemField: vi.fn(),
        deleteItem: vi.fn(),
        upsert1099: vi.fn(),
        addItem: vi.fn(),
        assetItemsArr: [],
        liabilityItemsArr: [],
        incomeItemsArr: [],
        expenseItemsArr: [],
        insuranceItemsArr: [],
    })),
}));

describe('OverhaulInitialItemModal', () => {
    it('renders successfully', () => {
        const wrapper = mount(OverhaulInitialItemModal, {
            props: {
                isOpen: true,
                category: 'assets',
                selectedItemId: null,
                entryMode: 'standard',
            },
        });
        expect(wrapper.exists()).toBe(true);
    });
});
