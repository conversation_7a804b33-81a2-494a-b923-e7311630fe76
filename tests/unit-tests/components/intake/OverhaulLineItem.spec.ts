import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import OverhaulLineItem from '@/components/intake/OverhaulLineItem.vue';
import BigIcon from '@/components/images/BigIcon.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';
import { useOverhaulStore } from '@/stores/overhaul';

vi.mock('@/components/images/BigIcon.vue', () => ({
    default: {
        name: 'BigIcon',
        template: '<div class="big-icon-mock"></div>',
    },
}));

vi.mock('@/components/images/SvgIcon.vue', () => ({
    default: {
        name: 'SvgIcon',
        template: '<div class="svg-icon-mock"></div>',
    },
}));

vi.mock('@/stores/overhaul', () => ({
    useOverhaulStore: vi.fn(() => ({
        updateItemField: vi.fn(),
        deleteItem: vi.fn(),
        upsert1099: vi.fn(),
        addItem: vi.fn(),
        assetItemsArr: [],
        liabilityItemsArr: [],
        incomeItemsArr: [],
        expenseItemsArr: [],
        insuranceItemsArr: [],
    })),
}));

describe('OverhaulLineItem', () => {
    it('renders successfully', () => {
        const wrapper = mount(OverhaulLineItem, {
            props: {
                id: 'test-id',
                kind: 'test-kind',
                kindLabel: 'Test Kind',
                status: 'not-started',
                entryMode: 'Standard',
                draggable: true,
                iconShape: 'bungalow',
            },
        });
        expect(wrapper.exists()).toBe(true);
    });

    const defaultProps = {
        id: '1',
        kindLabel: 'Test Item',
        kind: 'TestKind',
        description: 'Test Description',
        status: 'not-started' as const,
        draggable: true,
        iconShape: 'bungalow' as const,
    };

    it('renders correctly with default props', () => {
        const wrapper = mount(OverhaulLineItem, {
            props: defaultProps,
        });

        expect(wrapper.exists()).toBe(true);
        expect(wrapper.find('.overhaul-line-item').exists()).toBe(true);
    });

    it('displays the kind label and description', () => {
        const wrapper = mount(OverhaulLineItem, {
            props: defaultProps,
        });

        expect(wrapper.text()).toContain('Test Item');
        expect(wrapper.text()).toContain('Test Description');
    });

    it('shows the correct status', () => {
        const wrapper = mount(OverhaulLineItem, {
            props: {
                ...defaultProps,
                status: 'in-progress',
                progress: {
                    percentage: 0.5,
                    fields: 10,
                    required: 5,
                    requiredCompleted: 3,
                    completed: 5,
                },
            },
        });

        expect(wrapper.text()).toContain('50% Complete');
        expect(wrapper.find('.overhaul-line-item-progress-bar-fill').exists()).toBe(true);
    });

    it('emits clicked event when clicked', async () => {
        const wrapper = mount(OverhaulLineItem, {
            props: defaultProps,
        });

        await wrapper.find('.overhaul-line-item-click-target').trigger('click');
        expect(wrapper.emitted('clicked')).toBeTruthy();
    });

    it('shows KPIs when status is complete', () => {
        const wrapper = mount(OverhaulLineItem, {
            props: {
                id: '1',
                kindLabel: 'Test Item',
                kind: 'TestKind',
                name: 'test-item',
                category: 'assets',
                entryMode: 'Standard',
                description: 'Test Description',
                fields: [],
                status: 'complete',
                kpis: [
                    {
                        label: 'Value',
                        value: 100000,
                        formatCurrency: true,
                    },
                ],
            },
        });

        expect(wrapper.text()).toContain('$100000');
    });
});
