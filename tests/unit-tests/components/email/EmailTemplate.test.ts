import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { config } from '@vue/test-utils';
import EmailTemplate from '@/components/email/EmailTemplate.vue';

// Configure Vue Test Utils to treat MJML components as custom elements
const mjmlElements = [
    'mjml',
    'mj-head',
    'mj-title',
    'mj-preview',
    'mj-font',
    'mj-attributes',
    'mj-style',
    'mj-section',
    'mj-column',
    'mj-text',
    'mj-image',
    'mj-button',
    'mj-table',
    'mj-wrapper',
    'mj-group',
    'mj-all',
    'mj-class',
    'mj-html',
    'mj-raw',
    'mj-social',
    'mj-social-element',
    'mj-navbar',
    'mj-navbar-link',
    'mj-divider',
    'mj-spacer',
    'mj-accordion',
    'mj-accordion-element',
    'mj-accordion-title',
    'mj-accordion-text',
    'mj-carousel',
    'mj-carousel-image',
    'mj-hero',
    'mj-wrapper',
    'mj-body',
];

// Initialize compiler options if they don't exist
if (!config.global.config.compilerOptions) {
    config.global.config.compilerOptions = {};
}

// Update the isCustomElement function to properly handle MJML components
config.global.config.compilerOptions.isCustomElement = (tag: string) => {
    return mjmlElements.includes(tag.toLowerCase());
};

// Create a mock component for each MJML element
const mjmlStubs = Object.fromEntries(
    mjmlElements.map(tag => [
        tag,
        {
            template: `<div class="${tag}" :class="$attrs['mj-class']"><slot /></div>`,
            inheritAttrs: true,
        },
    ]),
);

describe('EmailTemplate', () => {
    it('renders with the correct title', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email Title',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        const title = wrapper.find('.mj-title');
        expect(title.exists()).toBe(true);
        expect(title.text()).toBe('Test Email Title');
    });

    it('renders header slot content', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            slots: {
                header: 'This is the header content',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        const preview = wrapper.find('.mj-preview');
        const heading = wrapper.find('.mj-text');

        expect(preview.exists()).toBe(true);
        expect(preview.text()).toBe('This is the header content');
        expect(heading.exists()).toBe(true);
        expect(heading.text()).toBe('This is the header content');
    });

    it('renders body slot content', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            slots: {
                body: 'This is the body content',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        // Find the mj-column that contains the body slot
        const column = wrapper.find('.mj-column');
        expect(column.exists()).toBe(true);

        // The body content should be directly in the column
        expect(column.text()).toContain('This is the body content');
    });

    it('renders with default MJML structure', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        expect(wrapper.find('.mjml').exists()).toBe(true);
        expect(wrapper.find('.mj-head').exists()).toBe(true);
        expect(wrapper.find('.mj-body').exists()).toBe(true);
    });

    it('includes required font and styling elements', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        const font = wrapper.find('.mj-font');
        expect(font.exists()).toBe(true);
        expect(font.attributes('href')).toBe(
            'https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap',
        );

        const attributes = wrapper.find('.mj-attributes');
        expect(attributes.exists()).toBe(true);
    });

    it('renders footer content correctly', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            slots: {
                footer: '© 2024 Company Name',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        const socialElements = wrapper.findAll('.mj-social-element');
        expect(socialElements.length).toBe(4);

        // Find all mj-text elements and check if any contain the copyright symbol
        const textElements = wrapper.findAll('.mj-text');
        const copyrightText = textElements.find(el => el.text().includes('©'));

        expect(copyrightText).toBeTruthy();
        expect(copyrightText?.text()).toContain('©');
    });

    it('applies correct styling classes', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        // Find all mj-section elements
        const sections = wrapper.findAll('.mj-section');
        expect(sections.length).toBeGreaterThan(0);

        // Find all mj-text elements
        const textElements = wrapper.findAll('.mj-text');
        expect(textElements.length).toBeGreaterThan(0);
    });

    it('renders with responsive design attributes', () => {
        const wrapper = mount(EmailTemplate, {
            props: {
                title: 'Test Email',
            },
            global: {
                stubs: mjmlStubs,
            },
        });

        const wrapperElement = wrapper.find('.mj-wrapper');
        expect(wrapperElement.exists()).toBe(true);
        expect(wrapperElement.attributes('full-width')).toBe('full-width');

        const body = wrapper.find('.mj-body');
        expect(body.exists()).toBe(true);
        expect(body.attributes('background-color')).toBe('#0D182A');
    });
});
