import { vi } from 'vitest';

// Mock Vue lifecycle hooks
export const onMounted = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the mounted lifecycle
    return fn();
});

export const onUnmounted = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the unmounted lifecycle
    return fn();
});

export const onBeforeMount = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the beforeMount lifecycle
    return fn();
});

export const onBeforeUnmount = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the beforeUnmount lifecycle
    return fn();
});

export const onUpdated = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the updated lifecycle
    return fn();
});

export const onBeforeUpdate = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the beforeUpdate lifecycle
    return fn();
});

export const onErrorCaptured = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the errorCaptured lifecycle
    return fn();
});

export const onActivated = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the activated lifecycle
    return fn();
});

export const onDeactivated = vi.fn((fn: () => void) => {
    // Execute the function immediately to simulate the deactivated lifecycle
    return fn();
});

// Mock other Vue functions that might be needed
export const ref = vi.fn((value: any) => ({
    value,
    __v_isRef: true,
}));

export const computed = vi.fn((getter: () => any) => ({
    value: getter(),
    __v_isRef: true,
}));

export const watch = vi.fn((source: any, callback: (newValue: any, oldValue: any) => void) => {
    // Simple mock implementation
    return () => {};
});

export const watchEffect = vi.fn((effect: () => void) => {
    // Simple mock implementation
    effect();
    return () => {};
});

export const nextTick = vi.fn((fn?: () => void) => {
    if (fn) {
        fn();
    }
    return Promise.resolve();
});

export const getCurrentInstance = vi.fn(() => ({
    proxy: {},
    data: {},
    props: {},
    attrs: {},
    slots: {},
    emit: vi.fn(),
    expose: vi.fn(),
    options: {},
    parent: null,
    root: null,
    refs: {},
    setupState: {},
    ctx: {},
    isMounted: true,
    isUnmounted: false,
    isDeactivated: false,
    vnode: {},
    type: {},
    subTree: {},
    update: vi.fn(),
    forceUpdate: vi.fn(),
    render: vi.fn(),
}));
