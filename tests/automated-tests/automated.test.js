const { Builder, By, until } = require('selenium-webdriver');

describe('Login and Register Tests', () => {
    it('should login successfully', async () => {
        const driver = await new Builder().forBrowser('chrome').build();
        try {
            await driver.get('http://localhost:3000/login');

            const emailInputElement = await driver.wait(
                until.elementLocated(By.xpath('/html/body/div[1]/section/div/form/div[2]/div[1]/label/input')),
            );
            await emailInputElement.sendKeys('<EMAIL>');

            const passwordInputElement = await driver.wait(
                until.elementLocated(By.xpath('/html/body/div[1]/section/div/form/div[2]/div[2]/label/input')),
            );
            await passwordInputElement.sendKeys('password');

            const submitButtonElement = await driver.wait(
                until.elementLocated(By.xpath('/html/body/div[1]/section/div/form/div[3]/button')),
            );
            await submitButtonElement.click();
        } finally {
            await driver.quit();
        }
    });

    it('should register successfully', async () => {
        const driver = await new Builder().forBrowser('chrome').build();
        try {
            await driver.get('http://localhost:3000/register');

            await driver
                .findElement(By.xpath('/html/body/div[1]/section/div/form/div[2]/div/label'))
                .sendKeys('<EMAIL>');

            await driver.findElement(By.xpath('/html/body/div[1]/section/div/form/div[3]/a')).click();
        } finally {
            await driver.quit();
        }
    });
});
