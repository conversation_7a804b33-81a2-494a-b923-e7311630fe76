// noinspection JSUnusedGlobalSymbols

import { format, parse, isValid } from 'date-fns';
import lowerCase from 'lodash/lowerCase';
import cloneDeep from 'lodash/cloneDeep';
import pluralizePackage from 'pluralize';
import { Frequency } from '~/types/base';
import type { UserSession } from '#nuxt-oidc';
import type { UserGroup } from '#nuxt-oidc/types';
import type { MaybeRefOrGetter } from '@vue/reactivity';
import humanFormat from 'human-format';
import type { TPopulatedItem, TOverhaulLineItem } from '~/stores/overhaul';

//   ____        _          _______ _
//  |  _ \  __ _| |_ ___   / /_   _(_)_ __ ___   ___
//  | | | |/ _` | __/ _ \ / /  | | | | '_ ` _ \ / _ \
//  | |_| | (_| | ||  __// /   | | | | | | | | |  __/
//  |____/ \__,_|\__\___/_/    |_| |_|_| |_| |_|\___|
//

/**
 * Format a string to a specific date format using this pattern:
 * https://date-fns.org/v2.29.3/docs/format
 * Defaults to format like April 29, 2021
 */
export const formatDate = (date: Date | string | null, dateFormat?: string) => {
    if (!date) return '';
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    dateFormat = dateFormat ? dateFormat : 'PPP';
    return format(date, dateFormat);
};

/**
 * Format a string to a date as MM/DD/YYYY or MM/DD
 */
export const dateToSlash = (date: Date | string | null, excludeYear?: boolean, slim?: boolean) => {
    let format: string;
    if (slim) {
        format = excludeYear ? 'L/d' : 'L/d/yy';
    } else {
        format = excludeYear ? 'MM/dd' : 'P';
    }
    return formatDate(date, format);
};

// Convert a date/string to string of format 2024-12-31 13:59:59
export const dateToDateTimeString = (date: Date | string) => {
    return formatDate(date, 'yyyy-MM-dd HH:mm:ss');
};

// Convert a time string to a Date object
export const parseTimeString = (timeString: string | null) => {
    if (!timeString) return undefined;
    timeString = timeString.replace(/\s/g, '');

    const formats = ['HH:mm', 'H:mm', 'h:mma', 'h:mmaaa', 'Hmm', 'hmmaaa', 'hmma'];
    let result;

    for (let i = 0; i < formats.length; i++) {
        result = parse(timeString, formats[i], new Date());
        if (isValid(result)) {
            return result;
        }
    }

    return undefined;
};

export const parseMonthYearString = (monthYearString: string | null) => {
    if (!monthYearString) return undefined;

    const formats = [
        'yyyy',
        'yyyy-MM',
        'MMMM yyyy',
        'MMM yyyy',
        'MM/yyyy',
        'M/yyyy',
        'yyyy/MM',
        'MM-yyyy',
        'M-yyyy',
        'MMMM-yyyy',
        'MMM-yyyy',
        'yyyy.MM',
        'yyyy MM',
        'yyyy/MMM',
        'yyyy MMM',
    ];
    let result;

    for (let i = 0; i < formats.length; i++) {
        result = parse(monthYearString, formats[i], new Date());
        if (isValid(result)) {
            return result;
        }
    }

    return undefined;
};

export const isValidDate = (dateString: string | null) => {
    console.log(dateString, dateString ? isValid(dateString) : false);
    return dateString ? isValid(dateString) : false;
};

export const dateIsPast = (date: string | Date | null | undefined) => {
    if (!date) {
        return false;
    }
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    const currentDate = new Date();

    return date.getTime() < currentDate.getTime();
};

// Take a number of seconds and turn it into a string of the format HH:MM:SS
export const secondsToString = (seconds: number) => {
    const isNegative = seconds < 0;
    if (isNegative) {
        seconds = Math.abs(seconds);
    }
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    let arr = [secs < 10 ? '0' + secs : secs];
    arr.unshift(minutes);
    if (hours > 0) {
        arr[0] = minutes < 10 ? '0' + minutes : minutes;
        arr.unshift(hours);
    }

    return isNegative ? '-' + arr.join(':') : arr.join(':');
};

export function distanceToNow(dt?: Date) {
    if (!dt) return '';
    const timeScale = new humanFormat.Scale({
        yrs: 31557600000,
        mos: 2629800000,
        wks: 604800000,
        days: 86400000,
        hrs: 3600000,
        mins: 60000,
        secs: 1000,
    });

    const dur = Date.now() - dt.getTime();
    if (dur > 31557600000) {
        return humanFormat(dur, { maxDecimals: 1, scale: timeScale, separator: '' });
    }
    return humanFormat(dur, { maxDecimals: 0, scale: timeScale, separator: '' });
}

//   ____  _        _               _____                          _   _
//  / ___|| |_ _ __(_)_ __   __ _  |  ___|__  _ __ _ __ ___   __ _| |_| |_ ___ _ __ ___
//  \___ \| __| '__| | '_ \ / _` | | |_ / _ \| '__| '_ ` _ \ / _` | __| __/ _ \ '__/ __|
//   ___) | |_| |  | | | | | (_| | |  _| (_) | |  | | | | | | (_| | |_| ||  __/ |  \__ \
//  |____/ \__|_|  |_|_| |_|\__, | |_|  \___/|_|  |_| |_| |_|\__,_|\__|\__\___|_|  |___/
//                          |___/

/**
 * Make the first letter of a string uppercase
 */
export function capitalize<T extends string>(str: T): Capitalize<T> {
    return (str.charAt(0).toUpperCase() + str.slice(1)) as Capitalize<T>;
}

/** Make a string all lowercase */
export const toLowerCase = (str: string | null) => {
    return str ? lowerCase(str) : '';
};

export function prepend<TP extends string, TT extends string>(prefix: TP, str: TT): `${TP}${Capitalize<TT>}` {
    return `${prefix}${capitalize(str)}`;
}

export const pluralize = (word?: string, count?: number, includeCount?: boolean) => {
    return word ? pluralizePackage(word, count, includeCount) : '';
};

export const singularize = (word?: string) => {
    return word ? pluralizePackage.singular(word) : '';
};

/**
 * Convert snake_case to Snake case
 */
export const snakeToText = (text: string) => {
    const str = lowerCase(text);
    return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Convert a number to a USD formatted string
 */
export const numberToUSD = (money: number | string, includeDecimals?: boolean, includeDollarSign?: boolean): string => {
    if (typeof money === 'string') return money;
    const decimals = includeDecimals ?? true; // Default to true unless "false" explicitly passed in
    const dollarSign = includeDollarSign ?? true; // Default to true unless "false" explicitly passed in
    return new Intl.NumberFormat('en-US', {
        style: dollarSign ? 'currency' : undefined,
        currency: dollarSign ? 'USD' : undefined,
        minimumFractionDigits: decimals ? 2 : 0,
        maximumFractionDigits: decimals ? 2 : 0,
    }).format(money);
};

export function formatAmount(num?: number) {
    if (!num) return '-';
    return `$${humanFormat(num, { separator: '', maxDecimals: 2 })}`;
}

export const ordinal = (n: number) => {
    let ord = 'th';

    if (n % 10 == 1 && n % 100 != 11) {
        ord = 'st';
    } else if (n % 10 == 2 && n % 100 != 12) {
        ord = 'nd';
    } else if (n % 10 == 3 && n % 100 != 13) {
        ord = 'rd';
    }
    return Math.floor(n).toString() + ord;
};

/**
 * Format a number to a succinct display value (e.g. 123k, 14M). Optionally with a leading dollar sign
 */
export function abbreviateNumber(
    total: number,
    leadingDollar?: boolean,
    asArray?: false | undefined | null,
    strDivider?: string,
): string;
export function abbreviateNumber(total: number, leadingDollar?: boolean, asArray?: true, strDivider?: string): string[];
export function abbreviateNumber(
    total: number,
    leadingDollar?: boolean,
    asArray?: boolean | null,
    strDivider?: string,
): string | string[] {
    let arr = leadingDollar || leadingDollar === undefined ? ['$'] : [];
    if (isNaN(total)) {
        arr.push('0');
    } else if (total > 1000000000) {
        arr.push(String(Math.round((total / 1000000000) * 10) / 10));
        arr.push('B');
    } else if (total > 1000000) {
        arr.push(String(Math.round((total / 1000000) * 10) / 10));
        arr.push('M');
    } else if (total > 1000) {
        arr.push(String(Math.floor(total / 1000)));
        arr.push('k');
    } else {
        arr.push(String(Math.floor(total)));
    }
    return asArray ? arr : arr.join(strDivider ?? '');
}

export const arrayToCommaSeparatedString = (arr: Array<string | number>) => {
    if (!arr.length) return '';
    if (arr.length === 1) return arr[0];
    return arr.slice(0, -1).join(', ') + ' and ' + arr.slice(-1);
};

//   _____                                              ____                              _
//  |  ___| __ ___  __ _ _   _  ___ _ __   ___ _   _   / ___|___  _ ____   _____ _ __ ___(_) ___  _ __
//  | |_ | '__/ _ \/ _` | | | |/ _ \ '_ \ / __| | | | | |   / _ \| '_ \ \ / / _ \ '__/ __| |/ _ \| '_ \
//  |  _|| | |  __/ (_| | |_| |  __/ | | | (__| |_| | | |__| (_) | | | \ V /  __/ |  \__ \ | (_) | | | |
//  |_|  |_|  \___|\__, |\__,_|\___|_| |_|\___|\__, |  \____\___/|_| |_|\_/ \___|_|  |___/_|\___/|_| |_|
//                    |_|                      |___/

export const frequencyToInt = {
    Daily: 365,
    Weekly: 52,
    Biweekly: 26,
    Semimonthly: 24,
    Monthly: 12,
    Quarterly: 4,
    Yearly: 1,
};

export const getNormalizeFrequencyMultiplier = (from: keyof typeof frequencyToInt, to: keyof typeof frequencyToInt) => {
    const fromInt = frequencyToInt[from] ?? null;
    const toInt = frequencyToInt[to] ?? null;

    return fromInt && toInt ? fromInt / toInt : null;
};

const FrequencyMultiplier: Record<Frequency, number> = {
    Yearly: 1, // Oct 31: I'm still seeing some items listed as "Yearly" instead of "Annually" -Caleb
    Annually: 1,
    Semiannually: 2,
    Trimesterly: 3,
    Quarterly: 4,
    Bimonthly: 6,
    Monthly: 12,
    Semimonthly: 24,
    Biweekly: 26,
    Weekly: 52,
    Daily: 365,
    Other: 1,
};
export function NormalizeToFrequency(value: number, from: Frequency, to: Frequency): number {
    return (value * FrequencyMultiplier[from]) / FrequencyMultiplier[to];
}

export function abbreviateFrequency(frequency: string): string {
    switch (frequency) {
        case 'Yearly':
        case 'Annually':
            return 'yr';
        case 'Semiannually':
            return '6mo';
        case 'Trimesterly':
        case 'Quarterly':
            return '3mo';
        case 'Bimonthly':
            return '2mo';
        case 'Monthly':
            return 'mo';
        case 'Semimonthly':
            return 'half mo';
        case 'Biweekly':
            return '2wk';
        case 'Weekly':
            return 'wk';
        case 'Daily':
            return 'day';
        default:
            return 'other';
    }
}

// A callback to pass into a reduce function to sum up the values of a list of fields
export const reduceSituationFieldsToTotal = (
    acc: number,
    field: any,
    currentIndex: number,
    array: any[],
    normalizeFrequency: 'monthly' | 'annual' = 'annual',
): number => {
    // Built this in the rest of the logic, but not sure the best way to pass that in via the reducer
    let value;
    if (['number-frequency', 'currency-frequency'].includes(field.type)) {
        const frequencyMultiplier = getNormalizeFrequencyMultiplier(
            field.value?.frequency,
            normalizeFrequency === 'monthly' ? 'Monthly' : 'Yearly',
        );
        value = (field.value?.number ?? 0) * (frequencyMultiplier ?? 1);
    } else {
        value = field.value ?? 0;
    }

    return isNaN(value) ? acc : acc + value * (field.affectSign === 'negative' ? -1 : 1);
};

export function isInRole(user: MaybeRefOrGetter<UserSession | undefined>, allowedRoles: UserGroup[]) {
    return !!toValue(user)?.groups?.some(group => allowedRoles.includes(group));
}
export function getUserRole(user: MaybeRefOrGetter<UserSession | undefined>) {
    const u = toValue(user);
    if (u?.groups?.includes('Administrator')) return 'Administrator';
    if (u?.groups?.includes('Advisor')) return 'Advisor';
    return 'Client';
}
export function cloneObj<T>(obj: T): T {
    return cloneDeep(obj);
}
export function getInitials(user: MaybeRefOrGetter<{ firstName?: string; lastName?: string } | undefined>) {
    const u = toValue(user);
    if (!u) return '';
    return `${u.firstName?.charAt(0).toUpperCase() ?? ''}${u.lastName?.charAt(0).toUpperCase() ?? ''}`;
}

// Given a TPopulatedItem, transform it to a TOverhaulLineItem
// Primarily used as the callback in map() to transform a group of TPopulatedItems into TOverhaulLineItems
export function transformPopulatedItemToLineItem(item: TPopulatedItem): TOverhaulLineItem {
    const lineItem = {
        kind: item.kind,
        name: item.name,
        kindLabel: item.kindLabel,
        iconShape: item.iconShape,
        progress: item.progress,
        entryMode: item.entryMode,
        id: item.id,
    } as Partial<TOverhaulLineItem>;

    if (item.progress?.percentage) {
        if (item.progress.percentage < 1) {
            lineItem.status = 'in-progress';
        } else {
            lineItem.status = 'complete';
        }
    } else {
        lineItem.status = 'not-started';
    }

    if (lineItem.status === 'complete') {
        const kpis: TOverhaulLineItem['kpis'] = [];

        const interestStat = item.stats?.find(stat => stat.kind === 'interestRate');
        if (interestStat && typeof interestStat.value === 'number' && !isNaN(interestStat.value)) {
            kpis.push({
                label: 'Interest',
                formattedValue: Math.round(interestStat.value).toString(),
                value: interestStat.value,
                formatCurrency: false,
                suffix: '%',
            });
        }

        // This is currently only "minimumPayment" fields on liabilities
        const paymentField =
            item.category === 'Liability'
                ? item.fields?.find(field => field.name === 'minimumPayment' && field.value.hasOwnProperty('number'))
                : null;
        if (paymentField) {
            kpis.push({
                label: 'Payment',
                formattedValue: abbreviateNumber(Math.abs(paymentField.value.number), false, false, ' '),
                value: paymentField.value.number,
                formatCurrency: true,
                direction: 'pos',
                suffix: paymentField.value.frequency
                    ? '/' + abbreviateFrequency(paymentField.value.frequency)
                    : undefined,
            });
        }

        const assetValue = item.stats?.find(stat => stat.kind === 'asset')?.value;
        if (assetValue) {
            kpis.push({
                label: 'Value',
                formattedValue: abbreviateNumber(Math.abs(assetValue), false, false, ' '),
                value: assetValue,
                formatCurrency: true,
                direction: assetValue >= 0 ? 'pos' : 'neg',
            });
        }

        const liabilityValue = item.stats?.find(stat => stat.kind === 'liability')?.value;
        if (liabilityValue) {
            kpis.push({
                label: item.category === 'Liability' ? 'Balance' : 'Liability',
                formattedValue: abbreviateNumber(Math.abs(liabilityValue), false, false, ' '),
                value: liabilityValue,
                formatCurrency: true,
                direction: liabilityValue >= 0 ? 'pos' : 'neg',
            });
        }

        // If there is already a "Payment" stat, skip the expense stat
        const expenseStat = item.stats?.find(stat => stat.kind === 'expense');
        if (expenseStat && !paymentField) {
            kpis.push({
                label: 'Expense',
                formattedValue: abbreviateNumber(Math.abs(expenseStat.value), false, false, ' '),
                value: expenseStat.value,
                formatCurrency: true,
                direction: expenseStat.value >= 0 ? 'pos' : 'neg',
                suffix: expenseStat.frequency ?? '/mo',
            });
        }

        const incomeStat = item.stats?.find(stat => stat.kind === 'income');
        if (incomeStat) {
            kpis.push({
                label: 'Income',
                formattedValue: abbreviateNumber(Math.abs(incomeStat.value), false, false, ' '),
                value: incomeStat.value,
                formatCurrency: true,
                direction: incomeStat.value >= 0 ? 'pos' : 'neg',
                suffix: incomeStat.frequency ?? '/mo',
            });
        }

        if (assetValue || liabilityValue) {
            const netWorth = (assetValue ?? 0) - (liabilityValue ?? 0);
            kpis.push({
                label: 'Net Worth',
                formattedValue: abbreviateNumber(Math.abs(netWorth), false, false, ' '),
                value: netWorth,
                direction: netWorth >= 0 ? 'pos' : 'neg',
                formatCurrency: true,
            });
        }

        lineItem.kpis = kpis;
    }

    if (lineItem.status !== 'not-started') {
        lineItem.description = item.description ?? undefined;
    }

    return lineItem as TOverhaulLineItem;
}
