import Chart from 'chart.js/auto';
import { TreemapController, TreemapElement } from 'chartjs-chart-treemap';

export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.mixin({
        mounted () {
            Chart.register(TreemapController, TreemapElement);
            Chart.defaults.color = '#97ABCC';

            Chart.defaults.borderColor = '#57719C';
            Chart.defaults.backgroundColor = '#57719C';
            Chart.defaults.plugins.colors.enabled = false;

            Chart.defaults.font.size = 10;
            Chart.defaults.font.family = '"Oxygen", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"';
        }
    });
});
