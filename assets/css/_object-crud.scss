.page-heading {
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
        font-size: 18px;
        font-weight: 700;
        line-height: 1.66666667em;
        letter-spacing: -0.5px;
    }
    .page-heading-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }
}

.spark-kpi-row {
    display: grid;
    gap: 10px;
    @for $i from 2 through 6 {
        &:has(> :last-child:nth-child(#{$i})) {
            grid-template-columns: repeat(#{$i}, 1fr);
        }
    }
}

.situation-crud-items {
    margin-top: 20px;
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 1px;
}

.expenses-object-content {
    margin-top:20px;
}

.situation-crud-placeholder {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    padding: 20px;
    background-color: rgba(var(--color-blue-grey-rgb),.1);
    color: rgba(var(--color-grey-rgb), .5);
    margin-top: 10px;
    font-size: 12px;
    border-radius:8px;
}

.dashboard-tag-filters {
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap:wrap;
    gap: 4px;
    width: auto;
}