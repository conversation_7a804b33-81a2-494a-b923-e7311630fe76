/**
 * This file should *only* contain SCSS variables, functions and mixins
 * It is imported into most component SCSS files, so no raw CSS should be included.
 */

$variantColors: (
    'dark-blue': --color-primary-dark-blue,
    'blue': --color-primary-blue,
    'purple': --color-support-purple,
    'green': --color-support-green,
    'yellow': --color-support-yellow,
    'orange': --color-support-orange,
    'red': --color-support-red,
    'pink': --color-support-pink,
    'teal': --color-support-teal,
);

$breakpoints: (
    'xs': 480px,
    'sm': 640px,
    'md': 768px,
    'lg': 1024px,
    'xl': 1280px,
);

@mixin mq($width, $type: max) {
	@if map_has_key($breakpoints, $width) {
		$width: map_get($breakpoints, $width);
        @if $type == max {
			$width: $width - 1px;
		}
		@media only screen and (#{$type}-width: $width) {
			@content;
		}
	}
}

@mixin placeholder {
    &::-webkit-input-placeholder {@content}
    &:-moz-placeholder           {@content}
    &::-moz-placeholder          {@content}
    &:-ms-input-placeholder      {@content}  
}

@mixin grid-columns($min: 2, $max: 6) {
    @for $i from $min through $max {
        &:has(> :last-child:nth-child(#{$i})) {
            grid-template-columns: repeat(#{$i}, 1fr);
        }
    }
}
