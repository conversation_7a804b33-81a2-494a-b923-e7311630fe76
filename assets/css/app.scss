@tailwind base;
@tailwind components;

@import 'mixins';
@import 'fonts';
@import 'colors';

body {
    background: var(--color-dark-grey);
}

:root {
    color-scheme: dark;
}

@import 'layouts';
@import 'intake';
@import 'object-crud';
@import 'dashboards';

// Glob import of component-specific .scss files
// This format only imports from the root directory so any nested directories need to be added
// @import '../../components/**/*.scss';
// @import '../../components/forms/**/*.scss';

@tailwind utilities;
