.kpi-groups {
    display: flex;
    align-items: flex-start;
    justify-content: stretch;
    flex-direction: column;
    gap: 10px;

    .kpi-groups-row {
        display: grid;
        width: 100%;
        gap: 10px;
        @for $i from 2 through 6 {
            &:has(> :last-child:nth-child(#{$i})) {
                grid-template-columns: repeat(#{$i}, 1fr);
            }
        }
    }
}

.kpi-groups-add-button {
    width: 100%;
    height: 148px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    gap: 3px;

    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    border-radius: 4px;

    transition: background .2s ease-in-out;
    background: rgba(var(--color-blue-rgb), 0.5);
    color: var(--color-white);

    &:hover {
        background: rgba(var(--color-blue-rgb), 0.8);
    }
}

.dashboard-custom-widget-row {
    display: flex;
    align-items: center;
    justify-content: stretch;
    width: 100%;
}

.dashboard-delete-selected-button {
    margin-top: 16px;
    text-align: center;
}

.dashboard-delete-count {
    color: var(--color-red);
}

.dashboard-draggable-wrapper {
    display: flex;
    align-items: flex-start;
    justify-content: stretch;
    flex-direction: column;
    gap: 10px;
    width: 100%;

    .kpi-groups-row {
        display: grid;
        width: 100%;
        gap: 10px;
        @for $i from 2 through 6 {
            &:has(> :last-child:nth-child(#{$i})) {
                grid-template-columns: repeat(#{$i}, 1fr);
            }
        }
    }
}