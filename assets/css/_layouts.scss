.layout-auth {
    display: flex;
    align-items: stretch;
    justify-content: flex-end;
    position: fixed;
    height: 100dvh;
    width: 100dvw;

    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;

    .auth-panel {
        width:500px;
        flex:none;
    }
}

.layout-minimal {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction:column;
    gap: 40px;
    padding: 40px;
    height: 100vh;
    width: 100dvw;
    .layout-minimal-logo {
        flex: none;
        width: 130px;
        color: var(--color-white);
        margin-bottom: auto;
        svg, img {
            width: 100%;
            height: auto;
        }
    }

    .layout-minimal-legal {
        font-weight: 400;
        font-size: 12px;
        line-height: 1.5px;

        text-align: center;
        color: var(--color-blue-grey);

        margin-top: auto;
    }
}

.layout-app {
    display: flex;
    align-items: stretch;
    justify-content: center;
    padding-right: 260px;
    min-height:100%;
    .layout-app-sidebar {
        position:fixed;
        left:0;
        top:0;
        height:100dvh;
        width: 80px;

        .sidebar-nav {
            height: 100%;
        }
    }

    .layout-app-content {
        display: flex;
        flex-direction: row;
        gap: 40px;
        width: 100%;
        padding: 0 40px 0 0;
        transition: margin-left 0.3s ease;
    }

    .layout-app-help-bar {
        width: 300px;
        background: var(--color-sidebar);
        position: fixed;
        right: 0;
        height: 100dvh;
        padding: 0 0 0 40px;
        &:before {
            content: '';
            display: block;
            position: absolute;
            top:0;
            left:0;
            width: 40px;
            height: 100%;
            background: var(--color-dark-grey);
            border-top-right-radius: 40px;
        }
    }
}
