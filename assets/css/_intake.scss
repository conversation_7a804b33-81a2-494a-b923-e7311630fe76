.intake-layout {
    color: var(--color-white);
    width: 100%;
    padding-top: 40px;
    .intake-layout-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        gap: 40px;
    }
    .intake-layout-desc {
        flex: 1 1 auto;
        h3 {
            font-size: 1rem;
            font-weight: 700;
            line-height: 1.25em;
            margin-bottom: 10px;
        }
        p {
            font-size: 0.875rem;
            font-weight: 400;
            line-height: 1.45em;
            color: var(--color-grey);
            strong {
                font-weight: 700;
                color: var(--color-white);
            }
        }
    }
    .intake-layout-kpis {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        flex: none;
        .top-kpi {
            width: 120px;
            z-index: 0;
        }
    }
    .intake-layout-progress {
        margin-top: 40px;
        margin-bottom: 20px;
    }

    .intake-layout-content {
        width: 100%;
        padding-bottom: 40px;
        .intake-layout-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 40px;
            h3 {
                font-size: 1.5rem;
                font-weight: 700;
                line-height: 1.25em;
                letter-spacing: -0.5px;
                em {
                    font-style: normal;
                    color: var(--color-grey);
                
                }
            }
            .intake-layout-title-actions {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 10px;
            }
        }
    }

    .intake-layout-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;

        .intake-layout-grid-item {
            > * {
                width: 100%;
            }
        }
    }

    .intake-layout-list {
        display: flex;
        align-items: stretch;
        flex-direction: column;
        gap:4px;
    }

    .intake-layout-two-col {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }
}

.intake-layout-grid-add-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-top:10px;
    padding: 24px 12px;

    --add-button-color: var(--color-blue-grey);
    border-radius: 4px;
    border: 1px solid var(--add-button-color);

    color: var(--add-button-color);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.45em;

    transition: border-color .15s ease-in, color .15s ease-in, background .15s ease-in;

    &:hover {
        --add-button-color: var(--color-grey);
        background: rgba(var(--color-white-rgb), .02);
    }
}

.situation-card-grid {
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(3, 1fr);

    > * {
        min-height: 220px;
    }
}
.situation-card-grid-add-button {
    --add-button-color: var(--color-blue-grey);
    border-radius: 4px;
    border: 1px solid var(--add-button-color);

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;

    color: var(--add-button-color);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.45em;

    transition: border-color .15s ease-in, color .15s ease-in, background .15s ease-in;

    &:hover {
        --add-button-color: var(--color-grey);
        background: rgba(var(--color-white-rgb), .02);
    }
}

.situation-modal {
    .modal-dialog-panel {
        width: 100%;
        max-width: 1020px;
        height: 100%;
    }
    .situation-modal-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }

    .situation-modal-tabs-column {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .situation-modal-tabs {
        border-radius: 4px;
        overflow:hidden;
        width: 100%;
        align-self: flex-start;
        .situation-modal-tab {
            width: 100%;
            display:flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            padding: 20px;

            color: var(--color-grey);
            background: rgba(var(--color-white-rgb), 0.05);
            transition: color .2s ease-in-out, background .2s ease-in-out;

            font-size: 14px;
            font-weight: 700;
            line-height: 1.45em;
            letter-spacing: -0.28px;

            &.active {
                background: var(--color-blue-grey);
                color: var(--color-white);
            }

            &:hover:not(.active) {
                background: rgba(var(--color-white-rgb), .1);
            } 
        }
    }

    .situation-modal-associated-link {
        position: relative;
        background: rgba(var(--color-white-rgb), 0.05);
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
        transition: background .2s ease-in-out;
        cursor: pointer;

        .situation-modal-associated-link-label {
            padding: 20px 20px 0;
            font-size: 14px;
            font-weight: 700;
            line-height: 1.45em;
            color: var(--color-grey);
            transition: color .2s ease-in-out;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 2px;
            &:last-child {
                padding-bottom: 20px;
            }
            .associated-link-count {
                font-size: 12px;
                font-weight: 400;
                line-height: 1.45em;
                border-radius: 9999em;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                min-height: 18px;
                margin-left: 6px;
                width: 18px;
                background: rgba(var(--color-primary-black-rgb), .2);
                color: rgba(var(--color-white-rgb), .5);

                em {
                    color: var(--color-white);
                    font-style: normal;
                    margin: 0 4px;
                    &:first-child, & + em {
                        margin-left: 0;
                    }
                }
            }
        }

        .situation-modal-associated-link-description {
            padding: 0 20px 20px;
            font-size: 14px;
            font-weight: 400;
            line-height: 1.45em;
            color: var(--color-blue-grey);
            transition: color .2s ease-in-out;
            user-select: none;
    
            strong {
                color: var(--color-white);
            }
    
            em {
                color: var(--color-grey);
                font-style: normal;
            }
        }

        .situation-modal-associated-link-arrow {
            position: absolute;
            top: 50%;
            margin-top: -12px;
            right: 20px;
            color: var(--color-blue-grey);
            transition: color .2s ease-in-out, transform .2s ease-in-out;
        }

        &:hover {
            background: rgba(var(--color-white-rgb), 0.10);
            .situation-modal-associated-link-label {
                color: var(--color-white);
            }
            .situation-modal-associated-link-description {
                color: var(--color-grey);
                strong {
                    color: var(--color-white);
                }
            }
            .situation-modal-associated-link-arrow {
                color: var(--color-grey);
                transform: translateX(4px);
            }
        }
    }

    .situation-modal-disclaimer {
        color: var(--color-blue-grey);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 1.45em;
        margin-top:30px;
    }

    .situation-modal-input-groups {
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        gap: 1px;
        flex-direction: column;
        &.hidden {
            display:none;
        }

        .situation-modal-delete-item {
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-grey);
            font-size: 14px;
            font-weight: 400;
            line-height: 1.45em;
            gap: 10px;
            padding: 20px;
            opacity: .5;
            transition: opacity .15s;
            &:hover {
                opacity: .8;
            }
        }
    }

    .situation-modal-mode-info {
        padding: 20px;
        display: flex;
        flex-direction: column;
        gap: 20px;
        border: 1px solid var(--color-blue-grey);
        border-radius: 4px;

        .situation-modal-mode-info-heading {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 10px;

            .situation-modal-mode-info-icon {
                background: var(--color-blue-grey);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                border-radius: 10px;
            }

            h5 {
                color: var(--color-grey);
                font-size: 14px;
                font-weight: 700;
                line-height: 1.45em;

                strong {
                    color: var(--color-white);
                    font-weight: 700;
                }
            }

            .situation-modal-mode-info-toggle {
                margin-left: auto;
                color: var(--color-grey);
            }
        }

        .situation-modal-mode-info-content {
            display: none;
            flex-direction: column;
            gap: 20px;

            p {
                color: var(--color-grey);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.45em;
                strong {
                    font-weight: 400;
                    color: var(--color-white);
                }
                &.fade {
                    color: var(--color-blue-grey);
                }
            }
        }
        .situation-modal-mode-info-items {
            width: 100%;
            align-self: flex-start;
            display: flex;
            align-items: stretch;
            justify-content: flex-start;
            flex-direction: column;
            gap: 1px;
        }
        .situation-modal-mode-info-item {
            width: 100%;
            display:flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            padding: 20px;
            border-radius: 4px;

            color: var(--color-grey);
            background: rgba(var(--color-white-rgb), 0.05);
            transition: color .2s ease-in-out, background .2s ease-in-out;

            font-size: 14px;
            font-weight: 700;
            line-height: 1.45em;
            letter-spacing: -0.28px;
        }

        &.info-expanded {
            .situation-modal-mode-info-toggle {
                transform: rotate(180deg);
            }
            .situation-modal-mode-info-content {
                display: flex;
            }
        }
    }
}

.intake-layout-title + .intake-review-page {
    margin-top: -20px;
}

.intake-review-page {
    .intake-review-kpis {
        display: grid;
        gap: 10px;
        @for $i from 2 through 6 {
            &:has(> :last-child:nth-child(#{$i})) {
                grid-template-columns: repeat(#{$i}, 1fr);
            }
        }
    }

    .intake-review-situations {
        padding-top: 10px;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
    }
}

.expense-panels {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    .expense-panel {
        max-width: 100%;
    }
}

.page-container {
    display: flex;
    align-items: stretch;
    width: 100%;
    padding-top: 40px;
    padding-bottom: 40px;
    justify-content: flex-start;
    flex-direction: column;
}
