﻿// noinspection JSUnusedGlobalSymbols,ES6UnusedImports

import { UserSession } from '#nuxt-oidc/types';

declare module '#nuxt-oidc/types' {
    interface UserSession {
        isImpersonated?: boolean;
        originalUser?: string;
        groups?: UserGroup[];
        userData: UserData;
    }

    interface UserData {
        _id: string;
        authId: string;
        email: string;
        firstName: string;
        lastName: string;
        joinDate: Date;
        profileImage?: string;
        intakeCompleted?: boolean;
        isDisabled: boolean;
    }

    type UserGroup = 'Client' | 'Advisor' | 'Administrator' | 'Denied' | 'Deleted';
}
