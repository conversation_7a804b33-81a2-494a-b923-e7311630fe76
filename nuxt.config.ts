// https://nuxt.com/docs/api/configuration/nuxt-config
// noinspection JSUnusedGlobalSymbols

import { defineNuxtConfig } from 'nuxt/config';
import sassGlobImports from 'vite-plugin-sass-glob-import';
import svgLoader from 'vite-svg-loader';
import { createResolver } from '@nuxt/kit';
import defaultTheme from 'tailwindcss/defaultTheme';
import vuePlugin from '@vitejs/plugin-vue';

const { resolve } = createResolver(import.meta.url);

export default defineNuxtConfig({
    compatibilityDate: '2024-07-05',
    telemetry: false,
    $production: {
        modules: [['nuxt-applicationinsights', { applicationinsights: { clientEnabled: true } }]],
    },
    $test: {
        modules: [['nuxt-applicationinsights', { applicationinsights: { clientEnabled: true } }]],
        sourcemap: {
            client: true,
            server: true,
        },
    },
    app: {
        head: {
            meta: [{ name: 'color-scheme', content: 'dark' }],
        },
    },
    modules: ['@nuxtjs/tailwindcss', '@pinia/nuxt', '@nuxt/content', 'nuxt-oidc-auth', 'nuxt-file-storage'],
    routeRules: {
        '/proxy/**': { proxy: '/api/**' },
        // This is stupid, but SWA only allows POST to /api routes
        '/api/auth/**': { proxy: '/auth/**' },
        '/client/**': { appMiddleware: 'require-client' },
        '/advisor/**': { appMiddleware: 'require-advisor' },
        '/administrator/**': { appMiddleware: 'require-administrator' },
    },
    runtimeConfig: {
        emailBaseUrl: '', // Set the base url to use for emails/etc.
        sendEmail: true, // Controls whether we even try to send an email
        emailSender: '<EMAIL>',
        emailConnectionString: '', // Connection string for Azure email SDK
        mongodb: {
            dataUri: '', // Connection string for where app data is stored
            authUri: '', // Connection string for where OIDC auth data is stored
        },
        groupMap: {
            // Maps Auth0 group ids to fin-pro group
            '3d672809-edb5-44ec-8c15-227588d63070': 'Advisor',
            '7ea58576-be46-4155-b366-e4054788f540': 'Administrator',
        },
        applicationinsights: { connectionString: '' },
        azureStorageAccountName: '',
        azureStorageAccountKey: '',
        azureDocumentIntelligenceEndpoint: '',
        azureDocumentIntelligenceKey: '',
    },
    fileStorage: {
        version: '0.2.9',
    },
    oidc: {
        defaultProvider: 'auth0',
        providers: {
            auth0: {
                audience: 'FinPro Access',
                redirectUri: '',
                baseUrl: '',
                clientId: '',
                clientSecret: '',
                scope: ['openid', 'offline_access', 'profile', 'email'],
                additionalTokenParameters: {
                    // In case you need access to an API registered in Auth0
                    audience: 'FinPro Access',
                },
                additionalAuthParameters: {
                    // In case you need access to an API registered in Auth0
                    audience: 'FinPro Access',
                    prompt: 'login', // Disable SSO and force the user to login
                },
            },
        },
        session: {
            expirationCheck: true,
            automaticRefresh: true,
            expirationThreshold: 3600,
        },
        middleware: {
            globalMiddlewareEnabled: false,
        },
    },
    css: ['~/assets/css/app.scss'],
    devtools: {
        enabled: true,

        timeline: {
            enabled: true,
        },
    },
    vite: {
        plugins: [sassGlobImports(), svgLoader()],
        // server {
        //     hmr {
        //         overlay: false
        //     }
        // },
        css: {
            preprocessorOptions: {
                scss: {
                    api: 'modern-compiler',
                    silenceDeprecations: ['import'],
                },
            },
        },
    },
    tailwindcss: {
        config: {
            content: ['./components/**/*.vue'],
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['"Oxygen"', ...defaultTheme.fontFamily.sans],
                    },
                },
            },
            plugins: [],
        },
        cssPath: '~/assets/css/app.scss',
    },
    postcss: {
        plugins: {
            tailwindcss: {},
            autoprefixer: {},
        },
    },
    alias: {
        '#nuxt-oidc': resolve('./node_modules/nuxt-oidc-auth/dist/runtime/types'),
        '#nuxt-oidc/types': resolve('./node_modules/nuxt-oidc-auth/dist/runtime/types'),
    },
    nitro: {
        storage: {
            // Uncomment this to render emails to the .data/email folder
            // when the azure email connection string is not configured
            // Otherwise, emails are still rendered, but stored in memory only.
            // email: {
            //     driver: 'fs',
            //     base: './.data/email'
            // },
            // Uncomment this to store OIDC info in .data/oidc when the
            // Mongodb.authUri is not configured.
            // Otherwise, this data is stored in memory only and users
            // might need to reauthenticate if the server restarts.
            // oidc: {
            //     driver: 'fs',
            //     base: './.data/oidc',
            // },
        },
        esbuild: {
            options: {
                target: 'esnext',
            },
        },
        alias: {
            '#nuxt-oidc': resolve('./node_modules/nuxt-oidc-auth/dist/runtime'),
        },
        azure: {
            config: {
                platform: {
                    apiRuntime: 'node:20',
                },
            },
        },
        rollupConfig: {
            // @ts-expect-error
            plugins: [
                vuePlugin({
                    template: {
                        compilerOptions: {
                            // Registers mjml elements so vue doesn't try to process them and renders them as-is
                            isCustomElement: tag => tag === 'mjml' || tag.startsWith('mj-'),
                        },
                    },
                }),
            ],
        },
    },
});
