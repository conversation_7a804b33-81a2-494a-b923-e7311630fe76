import { exec } from "node:child_process";
import directory from 'inquirer-select-directory';

import fs from 'fs';
import path from 'path';

// Plop commands to generate and/or open component files for editing
export default function (plop) {
    // Include the "directory" plugin for inquirer.js
	plop.setPrompt('directory', directory);

    // Open newly gnerated files in IDE
    plop.setActionType('handleOpen', function (answers, config, plop) {
        if (answers.openide ?? null) {
            exec(`code ${answers.from}/${answers.name}.vue`);
            exec(`code ${answers.from}/${answers.name}.stories.ts`);
            if (answers.css ?? null) {
                exec(`code ${answers.from}/${answers.name}.scss`);
            }
        }
    });

    // Define the "component" generator for generating new Vue components, stories and (optionally) SCSS
	plop.setGenerator('component', {
        description: 'new component and storybook story',
        prompts: [{
            type: 'input',
            name: 'name',
            message: 'Component name (e.g. TextInput)'
        },{
            type: 'directory',
            name: 'from',
            message: 'Where you like to put this component?',
            basePath: './components'
        }, {
            type: 'confirm',
            name: 'css',
            message: 'Include a separate SCSS file?',
        }, {
            type: 'confirm',
            name: 'openide',
            message: 'Open generated files in your configured editor?',
        }],
        actions: function(data) {
            const actions = [
                {
                    type: 'add',
                    path: '{{ from }}/{{ pascalCase name }}.vue',
                    templateFile: '.stubs/stub.vue',
                    transform: (content) => {
                        return (data.css)
                            ?  content
                                    .replace(/\/\/ Inline CSS\n<style lang="scss">\n\.(.)*\{\n(.)*\n\}\n<\/style>/g, '')
                                    .replace(/\n\/\/ Imported CSS\n/g, '')
                            : content
                                    .replace(/\/\/ Imported CSS\n<style lang="scss">\n(.)*\n<\/style>/g, '')
                                    .replace(/\/\/ Inline CSS\n/g, '');
                    }
                },
                {
                    type: 'add',
                    path: '{{ from }}/{{ pascalCase name }}.stories.ts',
                    templateFile: '.stubs/stub.stories.ts'
                }
            ];
            
            if(data.css) {
                actions.push({
                    type: 'add',
                    path: '{{ from }}/{{ pascalCase name }}.scss',
                    templateFile: '.stubs/stub.scss'
                });
            }

            if(data.openide) {
                const paths = [
                    '{{ from }}/{{ pascalCase name }}.vue',
                    '{{ from }}/{{ pascalCase name }}.stories.ts'
                ];

                if (data.css) {
                    paths.push('{{ from }}/{{ pascalCase name }}.scss');
                }
                actions.push({
                    type: 'handleOpen',
                    paths: paths
                });
            }

            return actions;
        }
    });

    // Helper function to search through a directory tree for a specific filename
    function searchFile(dir, fileName) {
        // read the contents of the directory
        const files = fs.readdirSync(dir);

        // search through the files
        for (const file of files) {
            // build the full path of the file
            const filePath = path.join(dir, file);

            // get the file stats
            const fileStat = fs.statSync(filePath);

            // if the file is a directory, recursively search the directory
            if (fileStat.isDirectory()) {
                const result = searchFile(filePath, fileName);
                if (result) {
                    return result;
                }
            } else if (file.endsWith(fileName)) {
                // if the file is a match, print it
                return filePath;
            }
        }
    }

    // Search through the components directory for a component with the given name and open it in the IDE
    plop.setActionType('open component', function (answers, config, plop) {
        const result = searchFile('components', answers.name+'.vue');
        if (result) {
            const story = searchFile('components', answers.name+'.stories.ts');
            const css = searchFile('components', answers.name+'.scss');
            exec(`code ${result}`);
            if(story) {
                exec(`code ${story}`);
            }
            if(css) {
                exec(`code ${css}`);
            }
        } else {
            console.error('No component found with the name '+answers.name);
        }
    })

    // Define the "open" generator for opening all of the files in IDE for an existing component
    plop.setGenerator('open', {
        description: 'open component files in IDE',
        prompts: [{
            type: 'input',
            name: 'name',
            message: 'Component name (e.g. TextInput)'
        }],
        actions: [{
            type: 'open component',
            componentName: '{{ pascalCase name }}'
        }]
    });
};
