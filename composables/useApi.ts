﻿import {type UseFetchOptions, useRequestFetch} from "#app";
import * as devalue from "devalue";
import type {FetchError} from "ofetch";
import type {AvailableRouterMethod, NitroFetchOptions, NitroFetchRequest} from "nitropack";
import type {AsyncData, KeysOf, PickFrom} from "#app/composables/asyncData";
import type {DefaultAsyncDataErrorValue} from "#app/defaults";
import type {Ref} from "vue";

export function useApi<DataT>
(
    request: Ref<NitroFetchRequest> | NitroFetchRequest | (() => NitroFetchRequest),
    opts?: UseFetchOptions<string, DataT, KeysOf<DataT>, DataT, NitroFetchRequest, AvailableRouterMethod<NitroFetchRequest>>
) : AsyncData<PickFrom<DataT, KeysOf<DataT>> | DataT, FetchError | DefaultAsyncDataErrorValue> {
    return useFetch<string, FetchError, NitroFetchRequest, AvailableRouterMethod<NitroFetchRequest>, string, DataT>(request, {
        ...opts,
        transform(val) {
            return devalue.parse(val) as DataT;
        }
    });
}

export async function useFetchApi<T = unknown, R extends NitroFetchRequest = NitroFetchRequest, O extends NitroFetchOptions<R> = NitroFetchOptions<R>>(request: R, opts?: O) {
    const fetch = useRequestFetch();
    const result = await fetch<string>(request, opts);
    return devalue.parse(result) as T;
}

