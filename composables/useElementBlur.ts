import {onBeforeUnmount, onMounted, nextTick} from 'vue'
import type { Ref } from 'vue'

export default async function useElementBlur(component: Ref, callback: () => void) {
    if (!component) return
    const listener = (event: FocusEvent) => {
        setTimeout(async () => {
            await nextTick();
            if (component.value?.contains(document.activeElement)) {
                return
            }
            if (typeof callback === 'function') {
                callback()
            }
        })
    }
    onMounted(async () => {await nextTick(); component.value?.querySelectorAll('input, select').forEach((i: HTMLElement) => i.addEventListener('blur', listener))});
    onBeforeUnmount(() => component.value?.querySelectorAll('input, select').forEach((i: HTMLElement) => i.removeEventListener('blur', listener)))

    return { listener }
}
