<template>
    <NuxtLayout name="app">
        <section class="intake-layout">
            <section class="intake-layout-header">
                <div class="intake-layout-desc">
                    <h3 v-if="firstName" v-text="firstName + ','"></h3>
                    <p>
                        Your initial onboarding is <strong>{{ completion.total }}% complete</strong>.
                    </p>
                </div>
                <div class="intake-layout-kpis">
                    <ElementsTopKpi :value="totals.netIncome" label="Net Income" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.assets" label="Assets" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.liabilities" label="Liabilities" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.netWorth" label="Net Worth" class="intake-layout-kpi" />
                </div>
            </section>
            <section class="intake-layout-progress">
                <NavigationTabBar :tabs="tabBarSteps" :active-tab="activeTab" @clicked="handleTabClicked" />
            </section>
            <section class="intake-layout-content">
                <PartialsIntakeTitleBar
                    v-if="includeTitleBar && layoutTitle"
                    :title="layoutTitle"
                    @prev-clicked="handlePrevClick"
                    @next-clicked="handleNextClick"
                />

                <NuxtPage />
            </section>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
// import IntakeTitleBar from '~/PartialsIntakeTitleBar.vue';
//import type { IconSelectorProps } from '../components/elements/IconSelector.vue';

const overhaulStore = useOverhaulStore();

const route = useRoute();

await useAsyncData(async () => await overhaulStore.fetch());

// onMounted(() => {
//     const currentRoute = route.path.split('/').slice(-1)[0];
//     overhaulStore.init(currentRoute);
// })

const tabBarSteps = computed(() => overhaulStore.tabBarSteps);
const completion = computed(() => overhaulStore.completion);
const totals = computed(() => overhaulStore.totalValues);

const { user } = useOidcAuth();
const firstName = computed(() =>
    // For some reason, the firstName property is a string with the value 'undefined' when it is not set
    user.value?.userData?.firstName !== 'undefined' ? user.value?.userData?.firstName : undefined,
);

// Jan 16: Hardcoding this for demoing purposes
const includeTitleBar = computed(() => route.meta.includeTitleBar ?? true);
// const layoutTitle = computed(() => 'Please tell us about your <em>assets</em>');
const layoutTitle = computed(() => (route.meta.title ?? '') as string);

const activeTab = computed(() => {
    return route.path.split('/').slice(-1)[0];
});

const handleTabClicked = async (name: string | number) => await navigateTo('/client/intake/' + name);

const prevPage = computed(() => {
    const currentStepIndex = tabBarSteps.value.findIndex(step => step.name === activeTab.value);
    if (currentStepIndex <= 0) return;
    return tabBarSteps.value[currentStepIndex - 1]?.name;
});
async function handlePrevClick() {
    if (!prevPage.value) return;
    try {
        await overhaulStore.update();
    } catch (e) {
        console.log('Error: ', e);
    } finally {
        await navigateTo('/client/intake/' + prevPage.value);
    }
}

const nextPage = computed(() => {
    const currentStepIndex = tabBarSteps.value.findIndex(step => step.name === activeTab.value);
    if (currentStepIndex >= tabBarSteps.value.length - 1) return;
    return tabBarSteps.value[currentStepIndex + 1]?.name;
});

async function handleNextClick() {
    console.log('handleNextClick called');
    // Only mark as completed for main intake sections
    const mainSections = ['assets', 'liabilities', 'income', 'expenses', 'insurance'];
    console.log('Current activeTab:', activeTab.value);
    console.log('Is main section?', mainSections.includes(activeTab.value));

    if (mainSections.includes(activeTab.value)) {
        console.log('About to mark section as completed');
        console.log('Current completion before update:', completion.value);
        console.log('Marking section as completed:', activeTab.value);
        // Mark the current section as completed when clicking Continue
        // This ensures non-mandatory sections are marked complete even with no items
        await overhaulStore.markSectionAsCompleted(activeTab.value);
        console.log('Current completion after update:', completion.value);
    }
    console.log('Next page:', nextPage.value);
    if (!nextPage.value) return;
    try {
        await navigateTo('/client/intake/' + nextPage.value);
    } catch (e) {
        console.log('Error: ', e);
    }
}
</script>
