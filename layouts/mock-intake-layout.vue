<template>
    <div class="mock-layout-app">
        <div class="mock-layout-app-content">
            <div class="mock-sidebar-nav">
                <!-- Simplified mock sidebar -->
                <div class="mock-sidebar-logo">FP</div>
                <nav class="mock-sidebar-items">
                    <div class="mock-sidebar-item">📊</div>
                    <div class="mock-sidebar-item mock-sidebar-item-active">📝</div>
                    <div class="mock-sidebar-item">📈</div>
                    <div class="mock-sidebar-item">⚙️</div>
                </nav>
            </div>

            <section class="intake-layout">
                <section class="intake-layout-header">
                    <div class="intake-layout-desc">
                        <h3>{{ mockData.firstName }},</h3>
                        <p>
                            Your initial onboarding is <strong>{{ mockData.completion }}% complete</strong>.
                        </p>
                    </div>
                    <div class="intake-layout-kpis">
                        <ElementsTopKpi :value="mockData.totals.netIncome" label="Net Income" class="intake-layout-kpi" />
                        <ElementsTopKpi :value="mockData.totals.assets" label="Assets" class="intake-layout-kpi" />
                        <ElementsTopKpi :value="mockData.totals.liabilities" label="Liabilities" class="intake-layout-kpi" />
                        <ElementsTopKpi :value="mockData.totals.netWorth" label="Net Worth" class="intake-layout-kpi" />
                    </div>
                </section>
                <section class="intake-layout-progress">
                    <NavigationTabBar :tabs="mockTabBarSteps" :active-tab="activeTab" />
                </section>
                <section class="intake-layout-content">
                    <PartialsIntakeTitleBar
                        v-if="includeTitleBar && layoutTitle"
                        :title="layoutTitle"
                    />

                    <NuxtPage />
                </section>
            </section>
        </div>

        <div class="mock-layout-app-help-bar">
            <div class="mock-help-content">
                <div class="mock-help-nav">
                    <span>ℹ️</span>
                    <span>🔍</span>
                </div>
                <div class="mock-help-text">
                    <h4>Personal Information</h4>
                    <p>This is a mock demo page showing the personal information intake form. All fields are pre-filled with sample data and are read-only for demonstration purposes.</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

const route = useRoute();

// Mock data with realistic values
const mockData = ref({
    firstName: 'John',
    completion: 75,
    totals: {
        netIncome: 125000,
        assets: 450000,
        liabilities: 180000,
        netWorth: 270000,
    },
});

// Mock tab bar steps with realistic completion percentages
const mockTabBarSteps = ref([
    {
        label: 'Personal Info',
        name: 'personal',
        iconShape: 'info',
        completion: 100,
    },
    {
        label: 'Situation',
        name: 'situation',
        iconShape: 'grid-small',
        completion: 100,
    },
    {
        label: 'Assets',
        name: 'assets',
        iconShape: 'home',
        completion: 80,
    },
    {
        label: 'Liabilities',
        name: 'liabilities',
        iconShape: 'credit-card',
        completion: 60,
    },
    {
        label: 'Income',
        name: 'income',
        iconShape: 'trending-up',
        completion: 90,
    },
    {
        label: 'Expenses',
        name: 'expenses',
        iconShape: 'receipt',
        completion: 40,
    },
    {
        label: 'Insurance',
        name: 'insurance',
        iconShape: 'shield',
        completion: 20,
    },
    {
        label: 'Review',
        name: 'review',
        iconShape: 'check-circle',
        completion: 0,
    },
]);

const includeTitleBar = computed(() => route.meta.includeTitleBar ?? true);
const layoutTitle = computed(() => (route.meta.title ?? '') as string);

const activeTab = computed(() => {
    return route.path.split('/').slice(-1)[0];
});
</script>

<style scoped>
.mock-layout-app {
    display: flex;
    align-items: stretch;
    justify-content: center;
    padding-right: 260px;
    min-height: 100vh;
    background: var(--color-dark-grey);
}

.mock-layout-app-content {
    display: flex;
    flex-direction: row;
    gap: 40px;
    width: 100%;
    padding: 0 40px 0 0;
}

.mock-sidebar-nav {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    width: 80px;
    background: var(--color-sidebar);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
    gap: 20px;
}

.mock-sidebar-logo {
    width: 40px;
    height: 40px;
    background: var(--color-primary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.mock-sidebar-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.mock-sidebar-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 18px;
}

.mock-sidebar-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.mock-sidebar-item-active {
    background: rgba(255, 255, 255, 0.2);
}

.mock-layout-app-help-bar {
    width: 300px;
    background: var(--color-sidebar);
    position: fixed;
    right: 0;
    height: 100vh;
    padding: 40px 20px;
}

.mock-help-content {
    color: var(--color-white);
}

.mock-help-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    font-size: 18px;
}

.mock-help-text h4 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 10px;
    color: var(--color-white);
}

.mock-help-text p {
    font-size: 14px;
    line-height: 1.5;
    color: var(--color-grey);
}

/* Ensure the intake layout has proper spacing */
.intake-layout {
    margin-left: 80px;
    width: calc(100% - 80px);
}
</style>
