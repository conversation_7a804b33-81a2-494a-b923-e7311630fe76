<template>

    <section class="layout-app">
        <div class="layout-app-content">
            <NavigationSidebarNav mode="advisory" />
            <slot />
        </div>
        <div class="layout-app-help-bar">
            <NavigationSidebarHelp />
        </div>
    </section>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const isExpanded = ref(false);

function toggleSidebar() {
    console.log('Toggle Sidebar clicked');  // This should log now
    isExpanded.value = !isExpanded.value;
    console.log('Sidebar expanded:', isExpanded.value);  // Check the value change
}
</script>
