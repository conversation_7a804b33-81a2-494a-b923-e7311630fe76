<template>
    <NuxtLayout name="app">
        <section class="intake-layout">
            <section class="intake-layout-header">
                <div class="intake-layout-desc">
                    <h3 v-if="firstName" v-text="firstName + ','"></h3>
                    <p>
                        Your initial onboarding is <strong>{{ completionComp }}% complete</strong>.
                    </p>
                </div>
                <div class="intake-layout-kpis">
                    <ElementsTopKpi :value="totals.netIncome" label="Net Income" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.assets" label="Assets" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.liabilities" label="Liabilities" class="intake-layout-kpi" />
                    <ElementsTopKpi :value="totals.netWorth" label="Net Worth" class="intake-layout-kpi" />
                </div>
            </section>
            <section class="intake-layout-progress">
                <NavigationTabBar :tabs="stepsComp" :active-tab="activeTab" @clicked="handleTabClicked" />
            </section>
            <section class="intake-layout-content">
                <PartialsIntakeTitleBar
                    v-if="includeTitleBar && layoutTitle"
                    :title="layoutTitle"
                    @prev-clicked="handlePrevClick"
                    @next-clicked="handleNextClick"
                />

                <NuxtPage />
            </section>
        </section>
    </NuxtLayout>
</template>

<script setup lang="ts">
// import IntakeTitleBar from '~/PartialsIntakeTitleBar.vue';
//import type { IconSelectorProps } from '../components/elements/IconSelector.vue';

const intakeStore = useIntakeStore();
const { totals, progress, stepsComp, completionComp } = storeToRefs(intakeStore);

const route = useRoute();

await useAsyncData(async () => await intakeStore.fetch());

onMounted(() => {
    const currentRoute = route.path.split('/').slice(-1)[0];
    /*
    const routeName = route.name as string;
    const currentRoute = (routeName?.startsWith('intake-'))
        ? routeName.replace('intake-', '')
        : route.params?.step?.[0];*/
    intakeStore.init(currentRoute);
});

// This was replaced with a direct reference to stepsComp
const mappedTabs = computed(() => {
    return progress.value?.stepsComp?.map(step => {
        //if (step.name === 'personal') {
        //            return {...step, completion: personalProgress.value * 100}
        //      } else {
        return step;
        //    }
    });
});

const { user } = useOidcAuth();
const firstName = computed(() =>
    // For some reason, the firstName property is a string with the value 'undefined' when it is not set
    user.value?.userData?.firstName !== 'undefined' ? user.value?.userData?.firstName : undefined,
);

const includeTitleBar = computed(() => route.meta.includeTitleBar ?? true);
const layoutTitle = computed(() => (route.meta.title ?? '') as string);

const activeTab = computed(() => {
    return route.path.split('/').slice(-1)[0];
    /*
  const routeName = route.name as string;
    console.log(route.name);
    return (routeName?.startsWith('intake-'))
        ? routeName.replace('intake-', '')
        : route.params?.step?.[0];
   */
});

const handleTabClicked = async (name: string | number) => await navigateTo('/client/legacy/' + name);

const prevPage = computed(() => {
    const currentStepIndex = progress.value.steps.findIndex(step => step.name === activeTab.value);
    if (currentStepIndex <= 0) return;
    return progress.value.steps[currentStepIndex - 1]?.name;
});
async function handlePrevClick() {
    if (!prevPage.value) return;
    try {
        await intakeStore.update();
    } catch (e) {
        console.log('Error: ', e);
    } finally {
        await navigateTo('/client/legacy/' + prevPage.value);
    }
}

const nextPage = computed(() => {
    const currentStepIndex = progress.value.steps.findIndex(step => step.name === activeTab.value);
    if (currentStepIndex >= progress.value.steps.length - 1) return;
    return progress.value.steps[currentStepIndex + 1]?.name;
});
async function handleNextClick() {
    if (!nextPage.value) return;
    try {
        await intakeStore.update();
    } catch (e) {
        console.log('Error: ', e);
    } finally {
        await navigateTo('/client/legacy/' + nextPage.value);
    }
}
</script>
