import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath } from 'url';
import { resolve } from 'path';

export default defineConfig({
    plugins: [vue()],
    test: {
        environment: 'jsdom',
        include: ['tests/unit-tests/**/*.{test,spec}.{js,ts}'],
        globals: true,
    },
    resolve: {
        alias: {
            '@': resolve(__dirname, './'),
            '~': resolve(__dirname, './'),
            '#app': resolve(__dirname, './src/app'),
        },
    },
});
