﻿<template>
    <NuxtLayout name="app">
        <div class="page-container">
            <Modal :dynamic-placement="false" :with-overlay="true" :is-open="invitePopover" @close="handleInviteClose">
                <InviteUserModal @close="handleInviteClose" @send="handleInviteSend" />
            </Modal>
            <div class="content-top">
                <div class="content-top-title">
                    <h2>People</h2>
                    <div class="content-top-actions">
                        <Button :svg-icon="{ shape: 'plus' }" @click.prevent="handleInviteToggle">Invite User</Button>
                    </div>
                </div>

                <div class="content-top-filter-bar">
                    <div class="content-top-search">
                        <SvgIcon shape="search" class="content-top-search-icon" />
                        <input
                            type="text"
                            v-model="filter"
                            class="text-input content-top-search-input"
                            placeholder="Search"
                        />
                        <button
                            type="button"
                            v-if="hasFilter"
                            class="content-top-search-clear"
                            @keydown.escape="clearFilter"
                            @click.prevent="clearFilter"
                        >
                            clear
                        </button>
                    </div>
                    <div class="content-top-filters">
                        <button @click.prevent="handleSortToggle" class="content-top-filters-sort" ref="sort-button">
                            <div class="content-top-filters-sort-label">Sort By</div>
                            <div class="content-top-filters-sort-value" v-text="formattedSort"></div>
                        </button>
                        <Modal :is-open="sortPopover" @close="handleSortClose" class="user-management-filters-modal">
                            <div class="user-management-filters-modal-header">
                                <h3>Sorting</h3>
                            </div>

                            <CheckboxGroup
                                v-model:options="sortDirectionOptions"
                                v-model:selected="selectedSortDirection"
                                size="sm"
                                mode="radio"
                                variant="buttons"
                                class="content-top-filters-sort-directions"
                            />
                            <CheckboxGroup
                                v-model:options="sortOptions"
                                v-model:selected="selectedSort"
                                size="sm"
                                mode="radio"
                            />
                        </Modal>
                        <button
                            @click.prevent="handleFilterToggle"
                            class="content-top-filters-filter"
                            ref="filter-button"
                        >
                            <SvgIcon shape="filter" class="content-top-filters-icon" />
                            <div v-text="formattedFilter"></div>
                        </button>

                        <Modal
                            :is-open="filterPopover"
                            @close="handleFilterClose"
                            class="user-management-filters-modal"
                        >
                            <div class="user-management-filters-modal-header">
                                <h3>Filters</h3>
                            </div>
                            <CheckboxGroup
                                v-model:options="filterOptions"
                                v-model:selected="selectedFilter"
                                size="sm"
                                mode="radio"
                            />
                        </Modal>
                    </div>
                </div>
            </div>

            <div class="cards">
                <div class="cards-list">
                    <UserManagementItem
                        v-for="user in filteredUsers"
                        :key="user._id"
                        :user-item="user"
                        @requestAccess="handleRequestAccess"
                        @impersonate="handleImpersonate"
                        @stopImpersonate="handleStopImpersonate"
                    />
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>
<style scoped lang="scss">
@import '../../components/panels/advisor/UserManagement';
</style>
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import UserManagementItem from '~/components/panels/advisor/UserManagementItem.vue';
import InviteUserModal from '~/components/modals/InviteUserModal.vue';
import type { UserInvite } from '~/components/modals/InviteUserModal.vue';
import Button from '~/components/elements/Button.vue';
import CheckboxGroup, { type CheckboxGroupOption } from '~/components/forms/CheckboxGroup.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import Modal from '~/components/modals/Modal.vue';
import { useApi } from '~/composables/useApi';
import type { UserManagementUser } from '~/server/api/advisors/users.get';

const invitePopover = ref(false);

function handleInviteToggle() {
    if (invitePopover.value) {
        handleInviteClose();
    } else {
        handleInviteOpen();
    }
}

function handleInviteOpen() {
    invitePopover.value = true;
}

function handleInviteClose() {
    invitePopover.value = false;
}

async function handleInviteSend(invite: UserInvite) {
    try {
        console.log('Inviting:', invite);
        await $fetch('/api/advisors/inviteUser', {
            method: 'post',
            body: invite.primary,
        });
        await userListRefresh();
    } catch (e) {
        console.log('Error in handleRequestAccess()');
        console.log(`${e}`);
    }
    handleInviteClose();
}

const filter = ref('');
const hasFilter = computed(() => !!filter.value);

function clearFilter() {
    filter.value = '';
}

const sortPopover = ref(false);

function handleSortToggle() {
    if (sortPopover.value) {
        handleSortClose();
    } else {
        handleSortOpen();
    }
}

function handleSortOpen() {
    sortPopover.value = true;
}

function handleSortClose() {
    sortPopover.value = false;
}

const sortDirectionOptions = ref<CheckboxGroupOption[]>([
    {
        name: 'asc',
        label: 'Ascending',
    },
    {
        name: 'desc',
        label: 'Descending',
    },
]);

const sortOptions = ref<CheckboxGroupOption[]>([
    {
        name: 'date',
        label: 'Registered Date',
    },
    {
        name: 'firstName',
        label: 'First Name',
    },
    {
        name: 'lastName',
        label: 'Last Name',
    },
    {
        name: 'totalAssets',
        label: 'Total Assets',
    },
    {
        name: 'totalLiabilities',
        label: 'Total Liabilities',
    },
    {
        name: 'netWorth',
        label: 'Net Worth',
    },
    {
        name: 'accounts',
        label: 'Accounts',
    },
    {
        name: 'onboarded',
        label: 'Onboarded %',
    },
    {
        name: 'status',
        label: 'Status',
    },
]);
const selectedSort = ref('date');
const formattedSort = computed(() => {
    if (selectedSort.value) {
        return sortOptions.value.find(option => option.name === selectedSort.value)?.label ?? selectedSort.value;
    }
});
const selectedSortDirection = ref('asc');

watch(selectedSort, () => {
    handleSortClose();
});
watch(selectedSortDirection, () => {
    handleSortClose();
});

const filterPopover = ref(false);

function handleFilterToggle() {
    if (filterPopover.value) {
        handleFilterClose();
    } else {
        handleFilterOpen();
    }
}

function handleFilterOpen() {
    filterPopover.value = true;
}

function handleFilterClose() {
    filterPopover.value = false;
}

const filterOptions = ref<CheckboxGroupOption[]>([
    {
        label: 'All',
        name: 'filter',
    },
    {
        label: 'Accepted',
        name: 'accepted',
    },
    {
        label: 'Pending',
        name: 'pending',
    },
    {
        label: 'Declined',
        name: 'declined',
    },
    {
        label: 'Revoked',
        name: 'revoked',
    },
    {
        label: 'Onboarded',
        name: 'onboarded',
    },
    {
        label: 'Intake',
        name: 'intake',
    },
    {
        label: 'Clients',
        name: 'clients',
    },
    {
        label: 'Advisors',
        name: 'advisors',
    },
]);
const selectedFilter = ref('filter');
const formattedFilter = computed(() => {
    if (selectedFilter.value) {
        const filter =
            filterOptions.value.find(option => option.name === selectedFilter.value)?.label ?? selectedFilter.value;
        return filter === 'All' ? 'Filter' : filter;
    }
});
watch(selectedFilter, () => {
    handleFilterClose();
});

const filteredUsers = computed(() => {
    let users = userList.value;
    if (!users) return [];
    if (filter.value) {
        users = users.filter(
            user =>
                user.firstName.toLowerCase().includes(filter.value.toLowerCase()) ||
                user.lastName.toLowerCase().includes(filter.value.toLowerCase()),
        );
    }
    if (selectedFilter.value && selectedFilter.value !== 'filter') {
        switch (selectedFilter.value) {
            case 'accepted':
                users = users.filter(user => user.status === 'Joined');
                break;
            case 'pending':
                users = users.filter(user => user.status === 'Pending');
                break;
            case 'declined':
                users = users.filter(user => user.status === 'Declined');
                break;
            case 'revoked':
                users = users.filter(user => user.status === 'Revoked');
                break;
            case 'onboarded':
                users = users.filter(user => user.status === 'Joined' && user.onboarded === 100);
                break;
            case 'intake':
                users = users.filter(user => user.status === 'Joined' && (user.onboarded ?? 0) < 100);
                break;
            case 'clients':
                users = users.filter(user => user.role === 'Client');
                break;
            case 'advisors':
                users = users.filter(user => user.role === 'Advisor');
                break;
        }
    }

    users = users.sort((user1, user2) => {
        const direction = selectedSortDirection.value === 'asc' ? 1 : -1;
        switch (selectedSort.value) {
            case 'date':
                return (
                    direction *
                    ((user1.joinDate?.getTime() ?? Number.MAX_VALUE) - (user2.joinDate?.getTime() ?? Number.MAX_VALUE))
                );
            case 'firstName':
                return direction * user1.firstName.localeCompare(user2.firstName);
            case 'lastName':
                return direction * user1.lastName.localeCompare(user2.lastName);
            case 'totalAssets':
                return direction * ((user1.totalAssets ?? 0) - (user2.totalAssets ?? 0));
            case 'totalLiabilities':
                return direction * ((user1.totalLiabilities ?? 0) - (user2.totalLiabilities ?? 0));
            case 'netWorth':
                return direction * ((user1.netWorth ?? 0) - (user2.netWorth ?? 0));
            case 'accounts':
                return direction * ((user1.accounts ?? 0) - (user2.accounts ?? 0));
            case 'onboarded':
                return direction * ((user1.onboarded ?? 0) - (user2.onboarded ?? 0));
            case 'status':
                return direction * user1.status.localeCompare(user2.status);
        }
        return 0;
    });

    return users;
});

const { data: userList, refresh: userListRefresh } = await useApi<UserManagementUser[]>('/api/advisors/users');

async function handleImpersonate(user: UserManagementUser) {
    try {
        console.log('Impersonating user:', user);
        await $fetch('/api/advisors/impersonate', {
            method: 'post',
            body: { userId: user._id },
        });
        await reloadNuxtApp({ ttl: 1000, path: '/' });
    } catch (e) {
        console.log('Error in handleImpersonate()');
        console.log(`${e}`);
    }
}

async function handleStopImpersonate(user: UserManagementUser) {
    try {
        console.log('Stop impersonating user:', user);
        await $fetch('/api/advisors/impersonate', { method: 'delete' });
        await reloadNuxtApp({ ttl: 1000 });
    } catch (e) {
        console.log('Error in handleStopImpersonate()');
        console.log(`${e}`);
    }
}

async function handleRequestAccess(user: UserManagementUser) {
    try {
        console.log('Requesting access for user:', user);
        await $fetch('/api/advisors/requestAccess', {
            method: 'post',
            body: { userId: user._id },
        });
        await userListRefresh();
    } catch (e) {
        console.log('Error in handleRequestAccess()');
        console.log(`${e}`);
    }
}
</script>

<style lang="scss">
.modal.user-management-filters-modal {
    .user-management-filters-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        h3 {
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
        }
        .user-management-filters-modal-header-close {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-blue-grey);
            transition: color 0.2s ease-in-out;
            margin-right: -16px;
            position: relative;
            &:hover {
                color: var(--color-white);
            }
        }
    }
    .modal-content {
        h5 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        padding: 0;
        width: 400px;
    }
}
</style>
