<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div v-if="assetSituationsAdded">
        <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="asset in assets"
                :key="asset.name"
                :title="asset.kind"
                v-bind="asset"
                @add-item="() => handleSituationAddItem(asset)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(asset, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(asset.name, newMode, useAsDefault)"
                @remove-situation="() => handleRemoveSituation(asset)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddIncomeSource">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add asset</span>
            </button>
        </div>
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-if="primaryResidenceAsset">
            <ElementsIconSelector
                label="Primary Residence"
                :icon-shape="primaryResidenceAsset.iconShape ?? ''"
                :read-only="true"
                :read-only-selected="true"
                />
                <!-- @popover-save="(options) => handleIconSelectorSave(assetType.name, options)" -->
        </div>
        
<!--        <div class="intake-layout-grid-item" v-for="assetType in assetTypes" :key="assetType.name">-->
<!--            <ElementsIconSelector-->
<!--                v-bind="assetType"-->
<!--                v-model="assetType.selected"-->
<!--                @popover-save="(options) => handleIconSelectorSave(assetType.name, options)"-->
<!--                />-->
<!--        </div>-->
    </div>

    <AddObjectModal 
        kind="assets"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import { allAssetTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';
import type { IntakeSituationItem } from '~/stores/intake';
import AddObjectModal from '~/components/situations/AddObjectModal.vue';

const intakeStore = useIntakeStore();
const {
    data,
    assets,
    defaultMode,
    assetSituationsAdded
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const assetTypes = reactive(transformSituationTypesForCard(allAssetTypes));

const titleHtml = computed(() =>
    (assets.value?.length)
        ? 'Tell us about about your <em>assets</em>.'
        : 'What <em>types of assets</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/home')
}
async function handleNextClick() {
    if(assetSituationsAdded.value || nonResidenceAssets.value?.length || (!assetTypes?.filter(e => e.selected).length && !primaryResidenceAsset.value)) {
        await navigateTo('/client/legacy/liabilities')
    } else {
        populateAssetCards();
        assetSituationsAdded.value = true;
    }
}

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init(['assets', 'asset-401k', 'asset-auto', 'asset-personal-property', 'asset-investment-account', 'asset-real-estate', 'asset-equipment', 'asset-checking-bank-account', 'asset-saving-bank-account', 'asset-investment-account', 'asset-cd', 'asset-iras', 'asset-529', 'asset-utma']);
    // intakeStore.update();

    if (!assets.value?.length) {
        if (data.value.home?.type === 'own') {
          const residence = assetTypes?.find(e => e.name === 'primary-residence-asset');
            if (residence) {
                residence.selected = true;
            }
        }
    }
});

const nonResidenceAssets = computed(() => 
    assets.value?.filter(e => e.name !== 'primary-residence-asset')
)

const primaryResidenceAsset = computed<IntakeSituationItem|undefined>(() => {
    return assets.value.find(situation => situation.name === 'primary-residence-asset');
})

function populateAssetCards() {
    if (nonResidenceAssets.value?.length) {
        return;
    }    
    
    const selectedAssets = assetTypes?.filter(e => e.selected);
    const residenceAsset = selectedAssets.find(e => e.name === 'primary-residence-asset');
    
    // TODO: This will be deprecated once the Home tab is redone
    // if (residenceAsset) {
    //     console.log('happening');
    //     assets.value.push({
    //         cardMode: 'complete',
    //         entryMode: defaultMode.value,
    //         totalSteps: 1,
    //         completedSteps: 1,
    //         situationGroup: 'assets',
    //         kind: "Primary Residence",
    //         name: 'primary-residence-asset',
    //         unit: 'home',
    //         iconShape: 'house',
    //         simple: [
    //             { name: 'value', label: 'Value', type: 'currency', value: data.value.home.own.estimatedValue ?? undefined, required: true },
    //             { name: 'liabilities', label: 'Equity', type: 'currency', value: data.value.home.own.owed ?? undefined, required: true },
    //             { name: 'monthly-payment', label: 'Monthly Payment', type: 'currency', value: data.value.home.own.monthlyPayment ?? undefined, required: true },
    //         ],
    //         standard: {},
    //         template: [],
    //         footerRow: { label: 'Onboard Status', percentage: 0},
    //     });
    //     selectedAssets.splice(selectedAssets.findIndex(e => e.name === 'primary-residence'), 1);
    // }

    intakeStore.populateSituationCards('assets', selectedAssets);
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = assetTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(asset: IntakeSituationItem) {
    console.log(intakeStore.addSituationItem(asset))
}

function handleSituationRemoveItem(asset: IntakeSituationItem, itemId: number|string) {
    intakeStore.removeSituationItem(asset, itemId)
}

function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField('assets', situationName, fieldName, payload)
}

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('assets', situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode('assets', situationName, mode, useAsDefault)
}

function handleRemoveSituation(asset: IntakeSituationItem) {
    intakeStore.removeSituation('assets', asset)
}

function handleSituationItemModalClose() {
    intakeStore.recalculateTotals();
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleAddIncomeSource() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}

</script>
