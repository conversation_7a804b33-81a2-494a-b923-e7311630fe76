<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div v-if="income.length">
        <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="item in income"
                :key="item.name"
                :title="item.kind"
                v-bind="item"
                @add-item="() => handleSituationAddItem(item)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(item, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(item.name, newMode, useAsDefault)"
                @remove-situation="() => handleRemoveSituation(item)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddIncomeSource">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add income</span>
            </button>
        </div>
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="incomeType in incomeTypes" :key="incomeType.name">
            <ElementsIconSelector
                v-bind="incomeType"
                v-model="incomeType.selected"
                @popover-save="(options) => handleIconSelectorSave(incomeType.name, options)"
                />
        </div>
    </div>

    <AddObjectModal 
        kind="income"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import { allIncomeTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';
import type { IntakeSituationItem } from '~/stores/intake';
import AddObjectModal from '~/components/situations/AddObjectModal.vue';

const intakeStore = useIntakeStore();
const { income } = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const incomeTypes = reactive(transformSituationTypesForCard(allIncomeTypes));

const titleHtml = computed(() =>
    (income.value?.length)
        ? 'Tell us about your <em>income</em>.'
        : 'What <em>types of income</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/insurance')
}
async function handleNextClick() {
    if(income.value?.length || incomeTypes?.filter(e => e.selected).length === 0) {
        await navigateTo('/client/legacy/expenses')
    } else {
        populateIncomeCards();
    }
}

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init(['income', 'income', 'w2-employee', 'rental-income', 'alimony-income', 'royalties', 'retirement-incomes', 'child-support']);
});

function populateIncomeCards() {
    if (income.value?.length) {
        return;
    }    
    const selectedIncome = incomeTypes?.filter(e => e.selected);
    intakeStore.populateSituationCards('income', selectedIncome);
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = incomeTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(incomeItem: IntakeSituationItem) {
    intakeStore.addSituationItem(incomeItem)
}

function handleSituationRemoveItem(income: IntakeSituationItem, itemId: number|string) {
    intakeStore.removeSituationItem(income, itemId)
}

function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField('income', situationName, fieldName, payload)
}

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('income', situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode('income', situationName, mode, useAsDefault)
}

function handleRemoveSituation(income: IntakeSituationItem) {
    intakeStore.removeSituation('income', income)
}

function handleSituationItemModalClose() {
    intakeStore.recalculateTotals();
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleAddIncomeSource() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>
