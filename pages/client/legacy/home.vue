<template>
    <div class="intake-layout-content">
        <TabsRadioGroup
            :list="homeTypeOptions"
            :default="data.home?.type"
            default-slot-min-height="200px"
            @change="handleTabChange"
            v-if="data.home"
        >
            <template #default>
                Select the option that best describes your primary residence.
            </template>
            <template #own>
                <div v-if="primaryResidenceAssetSituation">
                    <SituationsSituationItem
                        title="Primary Residence"
                        v-bind="primaryResidenceAssetSituation"
                        :key="primaryResidenceAssetSituation.name"
                        :auto-open="autoOpenAssetModal"
                        :standard-can-add="false"
                        :can-delete="false"
                        :include-associated-items="false"
                        new-item-label="Primary Residence"
                        @standard-change="(...e) => handleSituationStandardChange('assets', ...e)"
                        @simple-change="(...e) => handleSituationSimpleChange('assets', ...e)"
                        @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange('assets', primaryResidenceAssetSituation?.name, newMode, useAsDefault)"
                    />
                </div>
            </template>

            <template #rent>
                <div v-if="housingRentSituation">
                    <SituationsSituationItem
                        title="Housing / Rent"
                        v-bind="housingRentSituation"
                        :key="housingRentSituation.name"
                        :auto-open="autoOpenExpenseModal"
                        :standard-can-add="false"
                        :can-delete="false"
                        :include-associated-items="false"
                        new-item-label="Primary Residence"
                        @standard-change="(...e) => handleSituationStandardChange('expenses', ...e)"
                        @simple-change="(...e) => handleSituationSimpleChange('expenses', ...e)"
                        @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange('expenses', housingRentSituation?.name, newMode, useAsDefault)"
                    />
                </div>
            </template>
        </TabsRadioGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { primaryResidenceAssetData, primaryResidenceExpenseData, transformSituationTypeForCard } from '~/components/situations/data/intake-data';
import TabsRadioGroup from '~/components/forms/TabsRadioGroup.vue';
import type {SituationEntryMode} from "~/components/situations/SituationItem.vue";

const intakeStore = useIntakeStore();
const {
    data,
    assets,
    expenses
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

const primaryResidenceType = reactive(transformSituationTypeForCard(primaryResidenceAssetData));
const housingRentType = reactive(transformSituationTypeForCard(primaryResidenceExpenseData));

definePageMeta({
    layout: 'intake',
    title: 'Tell us about your primary residence.'
})

onBeforeRouteLeave(() => {
    if (data.value.home?.type === 'rent' && primaryResidenceAssetSituation.value) {
        intakeStore.removeSituationByName('assets', primaryResidenceAssetSituation.value.name);
    } else if (data.value.home?.type === 'own' && housingRentSituation.value) {
        intakeStore.removeSituationByName('expenses', housingRentSituation.value.name);
    }
})


const homeTypeOptions = ref([
    {
        label: 'I own my home',
        description: 'You own your primary residence, either with or without a mortgage attached.',
        name: 'own',
    },
    {
        label: 'I rent a home',
        description: 'You either rent your primary residence or live in a home owned by someone else.',
        name: 'rent',
    },
])

onMounted(() => {
    helpStore.init(['expense-housing']);
});

const autoOpenAssetModal = ref(false);
// const autoOpenAssetModalComp = computed(() => autoOpenAssetModal.value && )
const autoOpenExpenseModal = ref(false);
// const autoOpenExpenseModalComp = computed(() => autoOpenExpenseModal.value && )

const primaryResidenceAssetSituation = computed(() => {
    return assets.value.find(situation => situation.name === 'primary-residence-asset');
})

const housingRentSituation = computed(() => {
    return expenses.value.find(situation => situation.name === 'primary-residence-expense');
})

function handleSituationSimpleChange(situationType: 'assets'|'expenses', situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField(situationType, situationName, fieldName, payload)
}

function handleSituationStandardChange(situationType: 'assets'|'expenses', situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField(situationType, situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationType: 'assets'|'expenses', situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode(situationType, situationName, mode, useAsDefault)
}

async function handleTabChange(tab: string) {
    if (tab === 'own') {
        // If the primary residence asset situation doesn't exist, create it
        if (!primaryResidenceAssetSituation.value) {
            // We only want the modal to auto-open if the situation doesn't already exist
            autoOpenAssetModal.value = true;
            
            await nextTick();
            intakeStore.populateSituationCard('assets', primaryResidenceType, {
                entryMode: 'standard',
            });
            await nextTick();

            autoOpenAssetModal.value = false;
        }
    } else {
        // If the housing rent situation doesn't exist, create it
        if (!housingRentSituation.value) {
          // We only want the modal to auto-open if the situation doesn't already exist
          autoOpenExpenseModal.value = true;

          await nextTick();
          intakeStore.populateSituationCard('expenses', housingRentType, {
            kind: 'Primary Residence',
            entryMode: 'standard',
            name: 'primary-residence-expense'
          });
        }
        if (housingRentSituation.value) {
            const descriptionField = Object.values(housingRentSituation.value.standard)?.[0]?.fields.find((field) => field.name === 'description');
            console.log(descriptionField, Object.values(housingRentSituation.value.standard), housingRentSituation.value?.standard[0]);
            if (descriptionField) {
                descriptionField.value = 'Primary Residence';
            }
            await nextTick();

            autoOpenExpenseModal.value = false;
        }
    }

    if(data.value.home) {
        data.value.home.type = tab;
    }
}

</script>
