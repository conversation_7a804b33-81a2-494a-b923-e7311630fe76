<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        :next-disabled="!canComplete"
        :next-arrow="false"
        next-text="Finish"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div class="intake-review-page">
        <div class="intake-review-kpis">
            <ElementsKpiGroup 
                label="Selections"
                :kpis="[
                    { label: 'Total', value: mappedSituationCounts.total },
                    { label: 'Standard Mode', value: mappedSituationCounts.standard },
                    { label: 'Simple Mode', value: mappedSituationCounts.simple },
                ]" 
            />
            <ElementsKpiGroup 
                label="Required Inputs"
                :kpis="[
                    { label: 'Total', value: mappedRequiredFields.total },
                    { label: 'Complete', value: mappedRequiredFields.complete },
                    { label: 'Incomplete', value: mappedRequiredFields.incomplete },
                ]" 
            />
        </div>

        <div class="intake-review-situations">
            <SituationsIntakeReviewGroup
                v-if="assets.length"
                svg-shape="home"
                label="Assets"
                instructions="Please review and complete the required fields for every asset you selected."
                :completion="progressObj.assets ?? 0"
                :situations="assets"

                @simple-change="(situationName, fieldName, payload) => handleSituationSimpleChange('assets', situationName, fieldName, payload)"
                @standard-change="(situationName, itemId, fieldName, payload) => handleSituationStandardChange('assets', situationName, itemId, fieldName, payload)"
                @entry-mode-change="(situationName, newMode, useAsDefault) => handleSituationEntryModeChange('assets', situationName, newMode, useAsDefault ?? false)"
                @add-item="(situationName: string|undefined) => handleSituationAddItem(assets, situationName)"
                @remove-item="(situationName: string|undefined, itemId: number|string) => handleSituationRemoveItem(assets, situationName, itemId)"
                @remove-situation="(situation: IntakeSituationItem) => handleRemoveSituation('assets', situation)"
            />
        </div>

        <div class="intake-review-situations">
            <SituationsIntakeReviewGroup
                v-if="liabilities.length"
                svg-shape="bank"
                label="Liabilities"
                instructions="Please review and complete the required fields for every liability you selected."
                :completion="progressObj.liabilities ?? 0"
                :situations="liabilities"

                @simple-change="(situationName, fieldName, payload) => handleSituationSimpleChange('liabilities', situationName, fieldName, payload)"
                @standard-change="(situationName, itemId, fieldName, payload) => handleSituationStandardChange('liabilities', situationName, itemId, fieldName, payload)"
                @entry-mode-change="(situationName, newMode, useAsDefault) => handleSituationEntryModeChange('liabilities', situationName, newMode, useAsDefault ?? false)"
                @add-item="(situationName: string|undefined) => handleSituationAddItem(liabilities, situationName)"
                @remove-item="(situationName: string|undefined, itemId: number|string) => handleSituationRemoveItem(liabilities, situationName, itemId)"
                @remove-situation="(situation: IntakeSituationItem) => handleRemoveSituation('liabilities', situation)"
            />
        </div>

        <div class="intake-review-situations">
            <SituationsIntakeReviewGroup
                v-if="insurance.length"
                svg-shape="secure-money"
                label="Insurance"
                instructions="Please review and complete the required fields for every insurance policy you provided."
                :completion="progressObj.insurance ?? 0"
                :situations="insurance"

                @simple-change="(situationName, fieldName, payload) => handleSituationSimpleChange('insurance', situationName, fieldName, payload)"
                @standard-change="(situationName, itemId, fieldName, payload) => handleSituationStandardChange('insurance', situationName, itemId, fieldName, payload)"
                @entry-mode-change="(situationName, newMode, useAsDefault) => handleSituationEntryModeChange('insurance', situationName, newMode, useAsDefault ?? false)"
                @add-item="(situationName: string|undefined) => handleSituationAddItem(insurance, situationName)"
                @remove-item="(situationName: string|undefined, itemId: number|string) => handleSituationRemoveItem(insurance, situationName, itemId)"
                @remove-situation="(situation: IntakeSituationItem) => handleRemoveSituation('insurance', situation)"
            />
        </div>

        <div class="intake-review-situations">
            <SituationsIntakeReviewGroup
                v-if="income.length"
                svg-shape="stocks"
                label="Income"
                instructions="Please review and complete the required fields for every source of income you provided."
                :completion="progressObj.income ?? 0"
                :situations="income"

                @simple-change="(situationName, fieldName, payload) => handleSituationSimpleChange('income', situationName, fieldName, payload)"
                @standard-change="(situationName, itemId, fieldName, payload) => handleSituationStandardChange('income', situationName, itemId, fieldName, payload)"
                @entry-mode-change="(situationName, newMode, useAsDefault) => handleSituationEntryModeChange('income', situationName, newMode, useAsDefault ?? false)"
                @add-item="(situationName: string|undefined) => handleSituationAddItem(income, situationName)"
                @remove-item="(situationName: string|undefined, itemId: number|string) => handleSituationRemoveItem(income, situationName, itemId)"
                @remove-situation="(situation: IntakeSituationItem) => handleRemoveSituation('income', situation)"
            />
        </div>

        <div class="intake-review-situations">
            <SituationsIntakeReviewGroup
                v-if="expenses.length"
                svg-shape="bill"
                label="Expenses"
                instructions="Please review and complete the required fields for every expense you selected."
                :completion="progressObj.expenses ?? 0"
                :situations="expenses"

                @simple-change="(situationName, fieldName, payload) => handleSituationSimpleChange('expenses', situationName, fieldName, payload)"
                @standard-change="(situationName, itemId, fieldName, payload) => handleSituationStandardChange('expenses', situationName, itemId, fieldName, payload)"
                @entry-mode-change="(situationName, newMode, useAsDefault) => handleSituationEntryModeChange('expenses', situationName, newMode, useAsDefault ?? false)"
                @add-item="(situationName: string|undefined) => handleSituationAddItem(expenses, situationName)"
                @remove-item="(situationName: string|undefined, itemId: number|string) => handleSituationRemoveItem(expenses, situationName, itemId)"
                @remove-situation="(situation: IntakeSituationItem) => handleRemoveSituation('expenses', situation)"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import type { IntakeSituationItem } from '~/stores/intake';

const intakeStore = useIntakeStore();
const {
    assets,
    liabilities,
    income,
    insurance,
    expenses,
    progress
    // assetTypes
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const titleHtml = computed(() =>
    'Review incomplete items'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/expenses')
}
async function handleNextClick() {
    if (canComplete.value) {
        await intakeStore.markIntakeAsCompleted();
        await navigateTo('/client/legacy/complete')
    }
}

const progressObj = computed(() => 
    progress.value.steps?.reduce((acc, curr) => {
        acc[curr.name] = curr.completion;
        return acc;
    }, {} as Record<string, number>)
);

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init([]);
});

function handleSituationAddItem(situationGroup: IntakeSituationItem[], situationName: string|undefined) {
    const situation = situationGroup.find(e => e.name === situationName);
    if (situation) {
        intakeStore.addSituationItem(situation)
        intakeStore.recalculateTotals();
    }
}

function handleSituationRemoveItem(situationGroup: IntakeSituationItem[], situationName: string|undefined, itemId: number|string) {
    const situation = situationGroup.find(e => e.name === situationName);
    if (situation) {
        intakeStore.removeSituationItem(situation, itemId)
        intakeStore.recalculateTotals();
    }
}

function handleSituationSimpleChange(situationKind: IntakeSituationKind, situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField(situationKind, situationName, fieldName, payload)
    intakeStore.recalculateTotals();
}

function handleSituationStandardChange(situationKind: IntakeSituationKind, situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField(situationKind, situationName, itemId, fieldName, payload)
    intakeStore.recalculateTotals();
}

function handleSituationEntryModeChange(situationKind: IntakeSituationKind, situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode(situationKind, situationName, mode, useAsDefault)
    intakeStore.recalculateTotals();
}

function handleRemoveSituation(situationKind: IntakeSituationKind, situation: IntakeSituationItem) {
    intakeStore.removeSituation(situationKind, situation)
}

const canComplete = computed(() => 
    mappedRequiredFields.value.incomplete === 0
);

// TODO: Move this to intake.ts store
const mappedRequiredFields = computed<{
    complete: number,
    incomplete: number,
    total: number,
}>(() => {
    const requiredFields: Array<boolean> = [];

    const situations = [assets.value, liabilities.value, income.value, expenses.value];
    
    situations.forEach(situation => {
        if (situation.length) {
            situation.forEach((situationItem) => {
                if (situationItem.entryMode === 'standard') {
                    Object.values(situationItem.standard).forEach((item) => {
                        requiredFields.push(...item.fields.filter(e => e.required).map(e => !!(e.value || e.value === 0)));
                    });
                } else {
                    requiredFields.push(...situationItem.simple.filter(e => e.required).map(e => (e.value || e.value === 0)));
                }
            })
        }
    });

    return {
        complete: requiredFields.filter(e => e).length,
        incomplete: requiredFields.filter(e => !e).length,
        total: requiredFields.length
    }
});

const mappedSituationCounts = computed(() => {
    const situations = [assets.value, liabilities.value, income.value, expenses.value];
    let totalCount = 0;
    let simpleCount = 0;
    let standardCount = 0;
    
    situations.forEach(situation => {
        if (situation.length) {
            const standard = situation.filter((situationItem) => (situationItem.entryMode === 'standard'))?.length ?? 0;

            totalCount += situation.length;
            standardCount += standard;
            simpleCount += situation.length - standard;
        }
    });

    return {
        total: totalCount,
        simple: simpleCount,
        standard: standardCount
    }
});
</script>
