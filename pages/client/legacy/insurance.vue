<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div v-if="insurance.length">
        <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="item in insurance"
                :key="item.name"
                :title="item.kind"
                v-bind="item"
                @add-item="() => handleSituationAddItem(item)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(item, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(item.name, newMode, useAsDefault)"
                @remove-situation="() => handleRemoveSituation(item)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddInsurance">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add insurance</span>
            </button>
        </div>
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="insuranceType in insuranceTypes" :key="insuranceType.name">
            <ElementsIconSelector
                v-bind="insuranceType"
                v-model="insuranceType.selected"
                @popover-save="(options) => handleIconSelectorSave(insuranceType.name, options)"
                />
        </div>
    </div>

    <AddObjectModal 
        kind="insurance"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import { allInsuranceTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';
import type { IntakeSituationItem } from '~/stores/intake';
import AddObjectModal from '~/components/situations/AddObjectModal.vue';

const intakeStore = useIntakeStore();
const { insurance } = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const insuranceTypes = reactive(transformSituationTypesForCard(allInsuranceTypes));

const titleHtml = computed(() =>
    (insurance.value?.length)
        ? 'Tell us about your <em>insurance</em>.'
        : 'What <em>types of insurance</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/liabilities')
}
async function handleNextClick() {
    if(insurance.value?.length || insuranceTypes?.filter(e => e.selected).length === 0) {
        await navigateTo('/client/legacy/income')
    } else {
        populateInsuranceCards();
    }
}

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init(['insurance', 'insurance', 'insurance-permanent-life-whole', 'insurance-permanent-life-vul', 'insurance-permanent-life-v2d', 'insurance-term-life-life', 'insurance-term-life-spousal', 'insurance-term-life-child', 'insurance-term-life-individual', 'insurance-term-life-supplemental', 'insurance-disability-business-overhead', 'insurance-disability-group-long', 'insurance-disability-group-short', 'insurance-disability-individual', 'insurance-long-term-care', 'insurance-accidental-death-and-dismemberment']);
});

function populateInsuranceCards() {
    if (insurance.value?.length) {
        return;
    }    
    const selectedInsurance = insuranceTypes?.filter(e => e.selected);
    intakeStore.populateSituationCards('insurance', selectedInsurance);
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = insuranceTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(insuranceItem: IntakeSituationItem) {
    intakeStore.addSituationItem(insuranceItem)
}

function handleSituationRemoveItem(insurance: IntakeSituationItem, itemId: number|string) {
    intakeStore.removeSituationItem(insurance, itemId)
}

function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField('insurance', situationName, fieldName, payload)
}

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('insurance', situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode('insurance', situationName, mode, useAsDefault)
}

function handleRemoveSituation(insurance: IntakeSituationItem) {
    intakeStore.removeSituation('insurance', insurance)
}

function handleSituationItemModalClose() {
    intakeStore.recalculateTotals();
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleAddInsurance() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>
