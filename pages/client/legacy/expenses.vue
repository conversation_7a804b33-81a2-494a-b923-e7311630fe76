<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div v-if="expenses.length">

        <div class="expense-panels">
            <ExpensePanel
                v-for="expense in mappedExpenses"
                :key="expense.name"
                :title="expense.kind"
                :svg-shape="expense.iconShape ?? ''"
                :monthly="expense.kpis.monthly"
                :annual="expense.kpis.annual"
                :percent-of-total="expense.kpis.percentOfTotal"
                v-bind="expense"
                @add-item="() => handleSituationAddItem(expense)"
                @delete="() => handleSituationRemove(expense)"
            >
                <template v-for="row in expense.mappedRows" :key="row.id">
                    <ExpenseInputRow
                        :id="`${row.id}`"
                        :owner-options="[{value: 'single', label: 'Single'},{value: 'joint', label: 'Joint'}]"
                        :owner="row.owner"
                        :description="row.description"
                        :description-readonly="row.descriptionReadonly"
                        :amount="row.amount"
                        :frequency="row.frequency"
                        @change="(...e) => handleRowChange(expense, row, ...e)"
                        @remove="(...e) => handleRowRemove(expense, ...e)"
                        />
                </template>

                <!-- <div class="mt-10">{{ expense.standard }}</div> -->
            </ExpensePanel>
        </div>

        <button class="intake-layout-grid-add-button" type="button" @click.prevent="handleShowAddObjectModal">
            <SvgIcon shape="plus" width="20" height="20" />
            <span>Add expenses</span>
        </button>

        <!-- <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="expense in expenses"
                :key="expense.name"
                :title="expense.kind"
                v-bind="expense"
                @add-item="() => handleSituationAddItem(expense)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(expense, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(expense.name, newMode, useAsDefault)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddIncomeSource">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add expense</span>
            </button>
        </div> -->
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="assetType in expenseTypes" :key="assetType.name">
            <ElementsIconSelector
                v-bind="assetType"
                v-model="assetType.selected"
                @popover-save="(options) => handleIconSelectorSave(assetType.name, options)"
                />
        </div>
    </div>

    <AddObjectModal 
        kind="expenses"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
// import { debounce } from 'lodash';
import type { SituationEntryMode } from '@/components/situations/SituationItem.vue';
import ExpensePanel from '@/components/panels/ExpensePanel.vue';
import ExpenseInputRow from '@/components/forms/ExpenseInputRow.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';
import { allExpenseTypes, transformSituationTypesForCard } from '@/components/situations/data/intake-data';
import { getNormalizeFrequencyMultiplier } from '@/utils';
import type { IntakeSituationItem, IntakeStandardItem } from '@/stores/intake';
import AddObjectModal from '@/components/situations/AddObjectModal.vue';

// const debounce = require('lodash/debounce')
import pkg from 'lodash';
const { debounce } = pkg;

const intakeStore = useIntakeStore();
const {
    data,
    expenses,
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const expenseTypes = reactive(transformSituationTypesForCard(allExpenseTypes));

const titleHtml = computed(() =>
    (expenses.value?.length)
        ? 'Tell us about your <em>expenses</em>.'
        : 'What <em>types of expenses</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/income')
}
async function handleNextClick() {
    if(expenses.value?.length || expenseTypes?.filter(e => e.selected).length === 0) {
        await navigateTo('/client/legacy/review')
    } else {
        populateExpenseCards();
    }
}

const totalAnnualExpenses = computed(() => expenses.value?.reduce((acc, expense) => 
    acc + Object.values(expense.standard).reduce((innerAcc, row) => {
            return getAnnualExpenseFromRow(row) + innerAcc;
        }, 0
    ), 0)
);

function getAnnualExpenseFromRow(row: IntakeStandardItem) {
    const amountField = row.fields.find(f => f.name === 'amount')?.value
    const multiplier = getNormalizeFrequencyMultiplier(amountField.frequency ?? 'Yearly', 'Yearly');
    return amountField.number * (multiplier ?? 1);

    // const frequencyMultiplier = getNormalizeFrequencyMultiplier(field.value.frequency, normalizeFrequency === 'monthly' ? 'Monthly' : 'Yearly');
    // value = (field.value.number ?? 0) * (frequencyMultiplier ?? 1);
}

function getAnnualExpenseFromGroup(group: IntakeSituationItem) {
    return Object.values(group.standard).reduce((acc, row) => {
        return getAnnualExpenseFromRow(row) + acc
    }, 0);
}

const mappedExpenses = computed(() => 
    expenses.value?.map((expense) => {
        const annualExpenses = getAnnualExpenseFromGroup(expense) ?? 0;
        const percentOfTotal = Math.round((annualExpenses / totalAnnualExpenses.value) * 100);
        return {
            ...expense,
            canAddItem: expense.name !== 'primary-residence-expense',
            kpis: {
                monthly: Math.floor(annualExpenses / 12) ?? 0,
                annual: annualExpenses ?? 0,
                percentOfTotal: percentOfTotal
            },
            mappedRows: Object.values(expense.standard).map((row) => {
                const amountField = row.fields.find(f => f.name === 'amount');
                return {
                    id: row.id,
                    owner: row.fields.find(f => f.name === 'owner')?.value,
                    description: row.fields.find(f => f.name === 'description')?.value,
                    descriptionReadonly: row.fields.find(f => f.name === 'description')?.readonly,
                    amount: amountField?.value.number ?? 0,
                    frequency: amountField?.value.frequency ?? 'Monthly'
                }
            })
        }
    })
)

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init(['expenses', 'expense-auto', 'expense-buisness', 'expense-charitable-donations', 'expense-childcare-tuition', 'expense-clothing-and-cosmetic', 'expense-dining', 'expense-healthcare', 'expense-housing', 'expense-insurance', 'expense-leisure-and-recreation', 'expense-loan-payments', 'expense-savings-brokerage-contributions', 'expense-subscriptions', 'expense-taxes']);
});

function populateExpenseCards() {
    if (expenses.value?.length) {
        return;
    }    
    const selectedExpenses = expenseTypes?.filter(e => e.selected);
    intakeStore.populateSituationCards('expenses', selectedExpenses, 'standard');
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = expenseTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(expense: IntakeSituationItem) {
    intakeStore.addSituationItem(expense)
}

// function handleSituationRemoveItem(expense: IntakeSituationItem, itemId: number|string) {
//     intakeStore.removeSituationItem(expense, itemId)
// }

function handleSituationRemove(expense: IntakeSituationItem) {
    intakeStore.removeSituation('expenses', expense)
}

const handleRowChange = debounce((expense: any, row: any, itemId: string, fieldName: string, newValue: string|number) => {
    let value;
    if (fieldName === 'amount') {
        value = {
            number: newValue,
            frequency: row.frequency
            // frequency: row.find(e => e.name === 'amount').value.frequency
        }
    } else if (fieldName === 'frequency') {
        fieldName = 'amount';
        value = {
            // number: row.find(e => e.name === ' amount').value.number,
            number: row.amount,
            frequency: newValue
        }
    } else {
        value = newValue;
    }

    intakeStore.updateFieldByItemId('expenses', itemId, fieldName, value);
}, 400);

function handleRowRemove(expense: any, itemId: string) {
    intakeStore.removeSituationItem(expense, itemId);
    console.log('expense', expense)
    console.log('handleRowRemove', itemId)
}

// function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
//     intakeStore.updateSimpleField('expenses', situationName, fieldName, payload)
// }

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('expenses', situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode('expenses', situationName, mode, useAsDefault)
}

function handleSituationItemModalClose() {
    intakeStore.recalculateTotals();
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleShowAddObjectModal() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>
