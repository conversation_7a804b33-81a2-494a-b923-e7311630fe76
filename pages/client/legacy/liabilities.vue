<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div v-if="liabilities.length">
        <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="liability in liabilities"
                :key="liability.name"
                :title="liability.kind"
                v-bind="liability"
                @add-item="() => handleSituationAddItem(liability)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(liability, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(liability.name, newMode, useAsDefault)"
                @remove-situation="() => handleRemoveSituation(liability)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddIncomeSource">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add liability</span>
            </button>
        </div>
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="liabilityType in liabilityTypes" :key="liabilityType.name">
            <ElementsIconSelector
                v-bind="liabilityType"
                v-model="liabilityType.selected"
                @popover-save="(options) => handleIconSelectorSave(liabilityType.name, options)"
                />
        </div>
    </div>

    <AddObjectModal 
        kind="liabilities"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import { allLiabilityTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';
import type { IntakeSituationItem } from '~/stores/intake';
import AddObjectModal from '~/components/situations/AddObjectModal.vue';

const intakeStore = useIntakeStore();
const { liabilities } = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    includeTitleBar: false
})

const liabilityTypes = reactive(transformSituationTypesForCard(allLiabilityTypes));

const titleHtml = computed(() =>
    (liabilities.value?.length)
        ? 'Tell us about your <em>liabilities</em>.'
        : 'What <em>types of liabilities</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/legacy/assets')
}
async function handleNextClick() {
    if(liabilities.value?.length || liabilityTypes?.filter(e => e.selected).length === 0) {
        await navigateTo('/client/legacy/insurance')
    } else {
        populateLiabilityCards();
    }
}

onMounted(() => {    
    console.log('liabilities mounted');
    intakeStore.recalculateTotals()
    helpStore.init(['liabilities', 'liability-mortgage', 'liability-auto-loans', 'liability-student-loans', 'liability-credit-card-debt', 'liability-equipment-loans', 'liability-unsecure-loans', 'liability-secure-loans', 'liability-pledge-loans', 'liability-401k-loans', 'liability-life-ins-loans', 'liability-2nd-mortgage', 'liability-home-equity', 'liability-margin-loans']);
});

function populateLiabilityCards() {
    if (liabilities.value?.length) {
        return;
    }    
    const selectedLiability = liabilityTypes?.filter(e => e.selected);
    intakeStore.populateSituationCards('liabilities', selectedLiability);
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = liabilityTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(liability: IntakeSituationItem) {
    intakeStore.addSituationItem(liability)
}

function handleSituationRemoveItem(liability: IntakeSituationItem, itemId: number|string) {
    intakeStore.removeSituationItem(liability, itemId)
}

function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField('liabilities', situationName, fieldName, payload)
}

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('liabilities', situationName, itemId, fieldName, payload)
}

function handleSituationEntryModeChange(situationName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode('liabilities', situationName, mode, useAsDefault)
}

function handleRemoveSituation(liability: IntakeSituationItem) {
    intakeStore.removeSituation('liabilities', liability)
}

function handleSituationItemModalClose() {
    intakeStore.recalculateTotals();
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleAddIncomeSource() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>
