﻿<template>
    <div class="intake-layout-grid">
        <div class="intake-layout-grid-item" v-for="selector in situations" :key="selector.id">
            <ElementsIconSelector
                v-bind="selector"
                v-model="selector.selected"
                />
        </div>
    </div>
</template>

<script setup lang="ts">

const intakeStore = useIntakeStore();
const {
    data,
    situations
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'intake',
    title: 'Select the options below that apply to your current life situation.'
})

onMounted(() => {
    if(data.value.maritalStatus === 'married') {
        const homeSituation = situations.value.find(e => e.value === 'married');
        if (homeSituation) homeSituation.selected = true;
    }
    intakeStore.recalculateTotals();
    helpStore.init([]);
})

</script>
