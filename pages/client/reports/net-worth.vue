<template>
<!-- pages\reports\NetWorth.vue -->
    <NuxtLayout name="app">
        <div class="net-worth">
            <div class="net-worth-header">
                <div class="net-worth-header-left">
                    <div class="net-worth-header-left-title">
                        <span class="net-worth-header-left-title-name">{{ pageName }}</span>
                    </div>
                </div>
                <div class="net-worth-header-right">
                    <NuxtLink class="net-worth-header-right-edit" to="/intake/review">
                        <SvgIcon shape="edit" width="20" height="20" />
                    </NuxtLink>
                    <NuxtLink class="net-worth-header-right-edit" @click.prevent="downloadData">
                        <SvgIcon shape="download" width="20" height="20" />
                    </NuxtLink>
                </div>
            </div>
            <div class="net-worth-nav">
                <NuxtLink class="net-worth-nav-item active" to="/client/reports/net-worth">Net Worth</NuxtLink>
                <NuxtLink class="net-worth-nav-item" to="/client/reports/cash-flow">Cash Flow</NuxtLink>
            </div>
            <div class="net-worth-container">

                <!-- Assets Section -->
                <div class="net-worth-content">
                    <div class="net-worth-content-container">
                        <div class="net-worth-content-left">Assets</div>
                        <div class="net-worth-content-right">
                            <div>VALUE</div>
                        </div>
                    </div>
                    <div v-for="(item, index) in assets" :key="'asset-' + index" class="net-worth-content-entry">
                        <div class="net-worth-content-entry-left">{{ item.label }}</div>
                        <div class="net-worth-content-entry-right">
                          <div>{{numberToUSD(item.value, false, true)}}</div>
                        </div>
                    </div>
                    <div class="net-worth-content-entry-total-right">
                        <div class="net-worth-content-entry-total-right-label">Total</div>
                        <div class="net-worth-content-entry-total-right-value">
                          {{numberToUSD(totalAssets, false, true)}}
                        </div>
                    </div>
                </div>

                <!-- Liabilities Section -->
                <div class="net-worth-content">
                    <div class="net-worth-content-container">
                        <div class="net-worth-content-left">Liabilities</div>
                        <div class="net-worth-content-right">
                            <div>BALANCE</div>
                        </div>
                    </div>
                    <div v-for="(item, index) in liabilities" :key="'liability-' + index" class="net-worth-content-entry">
                        <div class="net-worth-content-entry-left">{{ item.label }}</div>
                        <div class="net-worth-content-entry-right">
                          <div>{{numberToUSD(item.value, false, true)}}</div>
                        </div>
                    </div>
                    <div class="net-worth-content-entry-total-right">
                        <div class="net-worth-content-entry-total-right-label">Total</div>
                        <div class="net-worth-content-entry-total-right-value">
                          {{numberToUSD(totalLiabilities, false, true)}}
                        </div>
                    </div>
                </div>

                <!-- Net Worth Section -->
                <div class="net-worth-content">
                    <div class="net-worth-content-container">
                        <div class="net-worth-content-left">Net Worth</div>
                        <div class="net-worth-content-right">
                            <div>TOTAL</div>
                        </div>
                    </div>
                    <div class="net-worth-content-entry-total-right">
                        <div class="net-worth-content-entry-total-right-label">Total</div>
                        <div class="net-worth-content-entry-total-right-value">
                          {{numberToUSD(netWorth, false, true)}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import SvgIcon from '../../components/images/SvgIcon.vue';
import type {NetWorthData} from "~/types/reports";
import {numberToUSD} from "~/utils";

const { data } = await useLazyFetch<NetWorthData>('/api/reports/netWorth');

const props = defineProps<{ pageName?: string; }>();

const pageName = props.pageName || 'Net Worth';

const assets = computed(() => data.value?.assets);
const liabilities = computed(() => data.value?.liabilities);

const totalAssets = computed(() => {
    return data.value?.assets.reduce((sum, item) => sum + item.value, 0) ?? 0;
});

const totalLiabilities = computed(() => {
  return data.value?.liabilities.reduce((sum, item) => sum + item.value, 0) ?? 0;
});

const netWorth = computed(() => {
  return totalAssets.value - totalLiabilities.value;
});

const downloadData = () => {
    const data = {
        assets: assets.value,
        liabilities: liabilities.value,
        totalAssets: totalAssets.value,
        totalLiabilities: totalLiabilities.value,
        netWorth: netWorth.value,
    };
    const json = JSON.stringify(data, null, 2);
    const blob = new Blob([json], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'net-worth-data.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};
</script>

<style lang="scss">
.net-worth {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 40px;

    &-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-nav {
        display: flex;
        gap: 40px;
        padding-bottom: 20px;
        color: #97ABCC;
        padding-left: 15px;

        &-item {
            cursor: pointer;
            font-size: 14px;
            font-weight: 400;
            &.active, &:hover {
                color: #E9EDF1;
            }
        }
    }

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &-back {
            color: #57719C;
            cursor: pointer;

            &:hover {
                color: #E9EDF1;
            }
        }

        &-left {
            display: flex;
            align-items: center;
            

            &-title {
                display: flex;
                flex-direction: column;

                &-title {
                    color: #57719C;
                    font-size: 10px;
                    font-weight: 700;
                }

                &-name {
                    color: white;
                    font-size: 14px;
                    font-weight: 700;
                }
            }

            &-pfp {
                width: 40px;
                height: 40px;
                background-color: #dee2e6;
                border-radius: 50%;
                margin-right: 1rem;
                border: 3px solid #97ABCC;
                margin-left: 20px;
                background-size: cover;
                background-position: center;
            }
        }

        &-right {
            display: flex;
            gap: 10px;
            flex-direction: column;

            &-edit {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #97ABCC;
                background-color: rgba(86, 112, 156, 0.20);
                border-radius: 4px;
                width: 40px;
                height: 40px;
                font-size: 14px;
                font-weight: 400;

                &:hover {
                    background-color: rgba(86, 112, 156, 0.40);
                }
            }

            &-mode {
                background-color: #055EFA;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                align-self: center;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }

    &-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-container {
            display: flex;
            flex-direction: row;
            width: 100%;
        }

        &-entry {
            display: flex;
            flex-direction: row;
            width: 100%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);

            &-left, &-right {
                div {
                    box-sizing: border-box;
                    padding: 10px 20px;
                    border-left: 1px solid rgba(255, 255, 255, 0.05);
                    min-width: 66px;
                }
            }

            &-left {
                display: flex;
                color: #E9EDF1;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                word-wrap: break-word;
                width: 100%;
                align-items: center;
                padding-left: 20px;
            }

            &-right {
                font-size: 14px;
                color: #97ABCC;
                display: flex;
                flex-direction: row;

                div {
                    display: flex;
                    justify-content: flex-end;
                    min-width: 106px;
                    max-width: 106px;
                }
            }

            &-total-right {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                padding: 20px 20px 20px 0;
                align-items: center;
                border-top: 1px solid rgba(255, 255, 255, 0.05);

                &-label {
                    color: #97ABCC;
                }
                &-value {
                    display: flex;
                    justify-content: flex-end;
                    color: #E9EDF1;
                    min-width: 106px;
                    max-width: 106px;
                }
            }
        }

        &-left, &-right {
            div {
                box-sizing: border-box;
                padding: 10px 20px;
                border-left: 1px solid rgba(255, 255, 255, 0.05);
                min-width: 66px;
            }
        }

        &-left {
            display: flex;
            font-size: 14px;
            font-weight: 700;
            color: #E9EDF1;
            background-color: rgba(255, 255, 255, 0.05);
            width: 100%;
            align-items: center;
            box-sizing: border-box;
            padding-left: 20px;
        }

        &-right {
            font-size: 14px;
            color: #97ABCC;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            min-width: 106px;
            max-width: 106px;

            div {
                display: flex;
                justify-content: flex-end;
                min-width: 100%;
            }
        }
    }
}
</style>
