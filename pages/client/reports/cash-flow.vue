<template>
  <!-- components\reports\CashFlow.vue -->
  <NuxtLayout name="app">
    <div class="cash-flow">
      <div class="cash-flow-header">
        <div class="cash-flow-header-left">
          <div class="cash-flow-header-left-title">
            <span class="cash-flow-header-left-title-name">{{ pageName }}</span>
          </div>
        </div>
        <div class="cash-flow-header-right">
          <NuxtLink class="cash-flow-header-right-edit" to="/client/intake/review">
            <SvgIcon shape="edit" width="20" height="20"/>
          </NuxtLink>
          <button class="cash-flow-header-right-edit" @click="downloadData">
            <SvgIcon shape="download" width="20" height="20"/>
          </button>
        </div>
      </div>
      <div class="cash-flow-nav">
        <NuxtLink class="cash-flow-nav-item" to="/client/reports/net-worth">Net Worth</NuxtLink>
        <NuxtLink class="cash-flow-nav-item active" to="/client/reports/cash-flow">Cash Flow</NuxtLink>
      </div>
      <div class="cash-flow-container">

        <!-- Income Section -->
        <div class="cash-flow-content">
          <div class="cash-flow-content-container">
            <div class="cash-flow-content-left">Income</div>
            <div class="cash-flow-content-right">
              <div>ANNUAL</div>
              <div>MONTHLY</div>
            </div>
          </div>
          <div v-for="(item, index) in income" :key="'income-' + index" class="cash-flow-content-entry">
            <div class="cash-flow-content-entry-left">{{ item.label }}</div>
            <div class="cash-flow-content-entry-right">
              <div>{{ numberToUSD(item.annual, false, true) }}</div>
              <div>{{ numberToUSD(item.monthly, false, true) }}</div>
            </div>
          </div>
          <div class="cash-flow-content-entry-total-right">
            <div class="cash-flow-content-entry-total-right-label">Total</div>
            <div class="cash-flow-content-entry-total-right-value">{{
                numberToUSD(totalIncome.annual, false, true)
              }}
            </div>
            <div class="cash-flow-content-entry-total-right-value">{{
                numberToUSD(totalIncome.monthly, false, true)
              }}
            </div>
          </div>
        </div>

        <!-- Expenses Section -->
        <div class="cash-flow-content">
          <div class="cash-flow-content-container">
            <div class="cash-flow-content-left">Expenses</div>
            <div class="cash-flow-content-right">
              <div>ANNUAL</div>
              <div>MONTHLY</div>
            </div>
          </div>
          <div v-for="(item, index) in expenses" :key="'expense-' + index" class="cash-flow-content-entry">
            <div class="cash-flow-content-entry-left">{{ item.label }}</div>
            <div class="cash-flow-content-entry-right">
              <div>{{ numberToUSD(item.annual, false, true) }}</div>
              <div>{{ numberToUSD(item.monthly, false, true) }}</div>
            </div>
          </div>
          <div class="cash-flow-content-entry-total-right">
            <div class="cash-flow-content-entry-total-right-label">Total</div>
            <div class="cash-flow-content-entry-total-right-value">{{
                numberToUSD(totalExpenses.annual, false, true)
              }}
            </div>
            <div class="cash-flow-content-entry-total-right-value">{{
                numberToUSD(totalExpenses.monthly, false, true)
              }}
            </div>
          </div>
        </div>

        <div class="cash-flow-content">
          <div class="cash-flow-content-container">
            <div class="cash-flow-content-left">Excess or Shortage Before Savings</div>
            <div class="cash-flow-content-right">
              <div>ANNUAL</div>
              <div>MONTHLY</div>
            </div>
          </div>
          <div class="cash-flow-content-entry-total-right">
            <div class="cash-flow-content-entry-total-right-label">Total</div>
            <div class="cash-flow-content-entry-total-right-value">
              {{ numberToUSD(totalIncome.annual - totalExpenses.annual, false, true) }}
            </div>
            <div class="cash-flow-content-entry-total-right-value">
              {{ numberToUSD(totalIncome.monthly - totalExpenses.monthly, false, true) }}
            </div>
          </div>
        </div>

        <!-- Savings Section -->
        <div class="cash-flow-content">
          <div class="cash-flow-content-container">
            <div class="cash-flow-content-left">Savings</div>
            <div class="cash-flow-content-right">
              <div>ANNUAL</div>
              <div>MONTHLY</div>
            </div>
          </div>
          <div v-for="(item, index) in savings" :key="'saving-' + index" class="cash-flow-content-entry">
            <div class="cash-flow-content-entry-left">{{ item.label }}</div>
            <div class="cash-flow-content-entry-right">
              <div>{{ numberToUSD(item.annual, false, true) }}</div>
              <div>{{ numberToUSD(item.monthly, false, true) }}</div>
            </div>
          </div>
          <div class="cash-flow-content-entry-total">
            <div class="cash-flow-content-entry-total-right">
              <div class="cash-flow-content-entry-total-right-label">Total</div>
              <div class="cash-flow-content-entry-total-right-value">{{
                  numberToUSD(totalSavings.annual, false, true)
                }}
              </div>
              <div class="cash-flow-content-entry-total-right-value">{{
                  numberToUSD(totalSavings.monthly, false, true)
                }}
              </div>
            </div>
          </div>
        </div>


        <div class="cash-flow-content">
          <div class="cash-flow-content-container">
            <div class="cash-flow-content-left">Excess or Shortage After Savings</div>
            <div class="cash-flow-content-right">
              <div>ANNUAL</div>
              <div>MONTHLY</div>
            </div>
          </div>
          <div class="cash-flow-content-entry-total-right">
            <div class="cash-flow-content-entry-total-right-label">Total</div>
            <div class="cash-flow-content-entry-total-right-value">
              {{ numberToUSD(totalIncome.annual - totalExpenses.annual - totalSavings.annual, false, true) }}
            </div>
            <div class="cash-flow-content-entry-total-right-value">
              {{ numberToUSD(totalIncome.monthly - totalExpenses.monthly - totalSavings.monthly, false, true) }}
            </div>
          </div>
        </div>

      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import {computed} from 'vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import {numberToUSD} from "~/utils";
import type {CashFlowData} from "~/types/reports";

const props = defineProps<{
  pageName?: string;
}>();

const {data} = await useLazyFetch<CashFlowData>('/api/reports/cashFlow');
const income = computed(() => data.value?.income);
const expenses = computed(() => data.value?.expenses);
const savings = computed(() => data.value?.savings);

const pageName = props.pageName || 'Cash Flow';

const totalIncome = computed(() => {
  return {
    annual: income.value?.reduce((sum, item) => sum + item.annual, 0) ?? 0,
    monthly: income.value?.reduce((sum, item) => sum + item.monthly, 0) ?? 0
  };
});

const totalExpenses = computed(() => {
  return {
    annual: expenses.value?.reduce((sum, item) => sum + item.annual, 0) ?? 0,
    monthly: expenses.value?.reduce((sum, item) => sum + item.monthly, 0) ?? 0
  };
});

const totalSavings = computed(() => {
  return {
    annual: savings.value?.reduce((sum, item) => sum + item.annual, 0) ?? 0,
    monthly: savings.value?.reduce((sum, item) => sum + item.monthly, 0) ?? 0
  };
});

const downloadData = () => {
  const data = {
    income: income.value,
    expenses: expenses.value,
    savings: savings.value,
    totalIncome: totalIncome.value,
    totalExpenses: totalExpenses.value,
    totalSavings: totalSavings.value,
  };
  const jsonString = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(data, null, 2)
  )}`;
  const link = document.createElement('a');
  link.href = jsonString;
  link.download = 'cash-flow-data.json';

  link.click();
};
</script>

<style lang="scss">
.cash-flow {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40px;

  &-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &-nav {
    display: flex;
    gap: 40px;
    padding-bottom: 20px;
    color: #97ABCC;
    padding-left: 15px;

    &-item {
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      &.active, &:hover {
        color: #E9EDF1;
      }
    }
  }

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-back {
      color: #57719C;
      cursor: pointer;

      &:hover {
        color: #E9EDF1;
      }
    }

    &-left {
      display: flex;
      align-items: center;

      &-title {
        display: flex;
        flex-direction: column;

        &-title {
          color: #57719C;
          font-size: 10px;
          font-weight: 700;
        }

        &-name {
          color: white;
          font-size: 14px;
          font-weight: 700;
        }
      }

      &-pfp {
        width: 40px;
        height: 40px;
        background-color: #dee2e6;
        border-radius: 50%;
        margin-right: 1rem;
        border: 3px solid #97ABCC;
        margin-left: 20px;
        background-size: cover;
        background-position: center;
      }
    }

    &-right {
      display: flex;
      gap: 10px;
      flex-direction: column;

      &-edit {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: #97ABCC;
        background-color: rgba(86, 112, 156, 0.20);
        border-radius: 4px;
        width: 40px;
        height: 40px;
        font-size: 14px;
        font-weight: 400;

        &:hover {
          background-color: rgba(86, 112, 156, 0.40);
        }
      }

      &-mode {
        background-color: #055EFA;
        color: white;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: 14px;
        align-self: center;
        cursor: pointer;

        &:hover {
          background-color: #0056b3;
        }
      }
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-container {
      display: flex;
      flex-direction: row;
      width: 100%;
    }

    &-entry {
      display: flex;
      flex-direction: row;
      width: 100%;


      &-left, &-right {
        div {
          box-sizing: border-box;
          padding: 10px 20px;
          border-left: 1px solid rgba(255, 255, 255, 0.05);
          min-width: 66px;
        }
      }

      &-left {
        display: flex;
        color: #E9EDF1;
        font-size: 12px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        word-wrap: break-word;
        width: 100%;
        align-items: center;
        padding: 10px 20px;
      }

      &-right {
        font-size: 14px;
        color: #97ABCC;
        display: flex;
        flex-direction: row;
        min-width: 200px;

        div {
          min-width: 100px;
          display: flex;
          justify-content: flex-end;
        }
      }

      &-total-right {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        padding: 20px 20px 20px 0;
        border-top: 1px solid rgba(255, 255, 255, 0.05);

        &-label {
          color: #97ABCC;
        }

        &-value {
          min-width: 100px;
          display: flex;
          justify-content: flex-end;
          color: #E9EDF1;
        }
      }
    }

    &-left, &-right {
      div {
        box-sizing: border-box;
        padding: 10px 20px;
        border-left: 1px solid rgba(255, 255, 255, 0.05);
        min-width: 66px;
      }
    }

    &-left {
      display: flex;
      font-size: 14px;
      font-weight: 700;
      color: #E9EDF1;
      background-color: rgba(255, 255, 255, 0.05);
      width: 100%;
      align-items: center;
      box-sizing: border-box;
      padding: 10px 20px;
    }

    &-right {
      font-size: 14px;
      color: #97ABCC;
      display: flex;
      flex-direction: row;
      background-color: rgba(255, 255, 255, 0.05);
      min-width: 200px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);

      div {
        min-width: 100px;
      }
    }
  }
}
</style>
