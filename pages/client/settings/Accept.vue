<template>
  <div class="access-page" v-if="invite">
    <div class="header">
      <UserAvatar :user="invite" />
      <div class="invitation">
        <span class="invitation-message">
          Invitation from {{ invite.firstName }} {{ invite.lastName }} to advise
          on your portfolio
        </span>
        <p class="recommendation">
          {{ distanceToNow(invite.updatedAt ?? invite.createdAt) }}
        </p>
      </div>
    </div>

    <div class="contact-info">
      <p>
        <strong>{{ invite.firstName }} {{ invite.lastName }}</strong>
      </p>
      <p>
        <a :href="'mailto:' + invite.email">{{ invite.email }}</a>
      </p>
    </div>

    <div class="action-buttons">
      <button
        v-if="!isAccepted"
        class="accept"
        @click.prevent="$emit('accept', invite)"
      >
        Accept Invite
      </button>
      <button
        v-if="!isAccepted"
        class="decline"
        @click.prevent="$emit('decline', invite)"
      >
        Decline Invite
      </button>
      <button
        v-if="isAccepted"
        class="revoke"
        @click.prevent="$emit('revoke', invite)"
      >
        Revoke Access
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ProfileInvitation } from "~/server/api/profile/index.get";
import { computed } from "vue";
import UserAvatar from "~/components/images/UserAvatar.vue";
import { distanceToNow } from "~/utils";

defineEmits(["accept", "decline", "revoke"]);

// Define props
const props = defineProps<{ invite?: ProfileInvitation }>();

// Computed property to check if the invite is accepted
const isAccepted = computed(() => {
  return props.invite && props.invite.userType === "Advisor";
});
</script>

<style scoped>
.access-page {
  color: #e9edf1;
  font-family: "Oxygen", sans-serif;
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 20px;
  border-bottom: 2px solid #2a3344;
  padding-bottom: 10px;
}

.header-nav NuxtLink:hover {
  background-color: #374256; /* Change background color on hover */
}

.header-nav svg {
  fill: #97abcc; /* Set the icon color */
}

.invitation {
  flex: 1;
}

.invitation-message {
  color: #97abcc;
}

.invitation p {
  margin: 0;
  color: #57719c;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  word-wrap: break-word;
  font-family: "Oxygen", sans-serif;
}

.recommendation {
  font-size: 12px;
  color: #57719c;
  font-weight: 400;
  line-height: 18px;
  word-wrap: break-word;
  font-family: "Oxygen", sans-serif;
}

.revoke {
  background-color: #ff4d4d;
  color: white;
}

.mortgage-approval span {
  font-weight: bold;
  color: #e9edf1;
}

.contact-info {
  margin-top: 20px;
}

.contact-info p {
  margin: 0;
  color: #57719c;
}

.contact-info strong {
  color: #e9edf1;
}

.contact-info a {
  color: #97abcc;
  text-decoration: none;
}

.contact-info a:hover {
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  font-family: "Oxygen", sans-serif;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 700;
  cursor: pointer;
}

.accept {
  background-color: #007bff;
  color: white;
}

.decline {
  background-color: #3a4454;
  color: #97abcc;
}
</style>
