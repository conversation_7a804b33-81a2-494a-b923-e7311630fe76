<template>
    <NuxtLayout name="app">
        <div class="profile">
            <ProfileHeader />
            <div class="profile-info">
                <!-- General Info Sections -->
                <div class="profile-info-section" v-for="(section, index) in infoSections" :key="index">
                    <div class="profile-info-section-title">{{ section.title }}</div>
                    <div class="profile-info-section-value">
                        <div v-if="Array.isArray(section.details)">
                            <div
                                class="profile-info-section-value-splitter"
                                v-for="(detail, i) in editableDetails[index]"
                                :key="i"
                            >
                                <span v-if="detail.label" class="profile-info-section-value-label">
                                    {{ detail.label }}
                                </span>
                                <span v-if="!isEditing">{{ detail.value }}</span>
                                <TextInput
                                    v-else
                                    v-model="editableDetails[index][i].value"
                                    class="dependent-input"
                                    type="text"
                                    :placeholder="detail.label === 'Phone' ? 'Enter phone number' : ''"
                                />
                            </div>
                        </div>
                        <div v-else-if="section.details">
                            <span v-if="!isEditing">{{ editableDetails[index] }}</span>
                            <TextInput v-else v-model="editableDetails[index]" class="dependent-input" type="text" />
                        </div>
                        <div v-else>
                            <span class="profile-info-section-value-empty"> No {{ section.type }} </span>
                        </div>
                    </div>
                </div>

                <!-- Dependents Section -->
                <div class="profile-info-section">
                    <div class="profile-info-section-title">Dependents</div>
                    <div class="profile-info-section-value">
                        <ul v-if="!isEditing">
                            <li v-for="(dependent, i) in dependents" :key="i">
                                {{ dependent.name }} ({{ dependent.relationship }}) -
                                {{ dependent.dob }}
                            </li>
                        </ul>
                        <div v-else>
                            <div class="dependent-entry" v-for="(dependent, i) in dependents" :key="i">
                                <div class="dependent-inputs">
                                    <div class="dependent-entry">
                                        <TextInput
                                            v-model="dependents[i].name"
                                            class="dependent-input"
                                            placeholder="Name"
                                            type="text"
                                        />
                                        <TextInput
                                            v-model="dependents[i].relationship"
                                            class="dependent-input"
                                            placeholder="Relationship"
                                            type="text"
                                        />
                                        <TextInput
                                            class="dependent-input"
                                            placeholder="Birthday"
                                            type="date"
                                            :svg-icon="{ shape: 'calendar' }"
                                            v-model="newDependent.birthdate"
                                            :min="minBirthdate"
                                            :max="maxBirthdate"
                                        />
                                    </div>
                                    <button @click="removeDependent(i)" class="remove-button">Remove</button>
                                </div>
                            </div>
                            <div class="new-dependent-entry">
                                <div class="new-dependent-inputs">
                                    <div class="dependent-entry new-dependent">
                                        <TextInput
                                            v-model="newDependent.name"
                                            class="dependent-input"
                                            placeholder="Name"
                                            type="text"
                                        />
                                        <TextInput
                                            v-model="newDependent.relationship"
                                            class="dependent-input"
                                            placeholder="Relationship"
                                            type="text"
                                        />
                                        <TextInput
                                            class="dependent-input"
                                            placeholder="Birthday"
                                            type="date"
                                            :svg-icon="{ shape: 'calendar' }"
                                            v-model="newDependent.birthdate"
                                            :min="minBirthdate"
                                            :max="maxBirthdate"
                                        />
                                    </div>
                                    <button @click="addDependent" class="add-button">Add</button>
                                </div>
                            </div>
                        </div>
                        <div v-if="!dependents.length && !isEditing" class="profile-info-section-value-empty">
                            No Dependents
                        </div>
                    </div>
                </div>
            </div>
            <button @click="toggleEditMode" class="edit-button">
                {{ isEditing ? 'Save Changes' : 'Edit' }}
            </button>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import ProfileHeader from '~/components/panels/profile/ProfileHeader.vue';
import type { ProfileResponse } from '~/server/api/profile/index.get';
import TextInput from '../../components/forms/TextInput.vue';

const { data: profileData } = await useApi<ProfileResponse>('/api/profile');

// Generate profile information sections
const getInfo = function* (profile: typeof profileData.value): Generator<InfoSection> {
    if (!profile) return null;
    if (profile.name) yield { title: 'Name', type: 'contact', details: profile.name };
    if (profile.email || profile.phone) {
        yield {
            title: 'Contact Information',
            type: 'contact',
            details: [
                { label: 'Email', value: profile.email },
                { label: 'Phone', value: profile.phone },
            ].filter(detail => detail.value),
        };
    }

    if (profile.address) yield { title: 'Address', type: 'address', details: profile.address };
    if (profile.spouse) {
        yield {
            title: 'Spouse',
            type: 'spouse',
            details: profile.spouse,
        };
        if (profile.spouseEmail || profile.spousePhone) {
            yield {
                title: 'Spouse Contact Information',
                type: 'contact',
                details: [
                    { label: 'Email', value: profile.spouseEmail },
                    { label: 'Phone', value: profile.spousePhone },
                ].filter(detail => detail.value),
            };
        }
    }
};

const infoSections = computed<InfoSection[]>(() => [...getInfo(profileData.value)]);

// Create a reactive copy of details for editing
const editableDetails = ref(
    infoSections.value.map(section =>
        Array.isArray(section.details) ? section.details.map(detail => ({ ...detail })) : section.details,
    ),
);

// State to toggle edit mode
const isEditing = ref(false);

// State for dependents
const dependents = ref(profileData.value?.dependents || []);
const newDependent = ref({ name: '', relationship: '', birthdate: '' });

const toggleEditMode = () => {
    if (!isEditing.value) {
        infoSections.value.forEach((section, index) => {
            if (
                section.title === 'Contact Information' &&
                !editableDetails.value[index].some(detail => detail.label === 'Phone')
            ) {
                // Add an empty phone field if it doesn't exist
                editableDetails.value[index].push({ label: 'Phone', value: '' });
            }
        });
        saveProfile();
    }
    isEditing.value = !isEditing.value;
};

const addDependent = () => {
    if (
        newDependent.value.name.trim() &&
        newDependent.value.relationship.trim() &&
        newDependent.value.birthdate.trim()
    ) {
        dependents.value.push({ ...newDependent.value });
        newDependent.value = { name: '', relationship: '', birthdate: '' };
    }
};

const removeDependent = index => {
    dependents.value.splice(index, 1);
};

const saveProfile = () => {
    const updatedData = {
        ...profileData.value,
        dependents: dependents.value,
        contactInfo: editableDetails.value,
    };

    console.log('Updated Profile:', updatedData);
    // Simulate sending data to an API
    // await fetch("/api/profile", { method: "POST", body: JSON.stringify(updatedData) });
};
</script>

<style lang="scss">
.profile {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 40px;
    box-sizing: border-box;

    &-info {
        display: flex;
        flex-direction: column;
        width: 100%;

        &-section {
            display: flex;
            flex-direction: row;
            padding: 20px;
            margin-bottom: 1px;
            background-color: rgba(255, 255, 255, 0.05);
            width: 100%;

            &:nth-of-type(1) {
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            &:last-of-type {
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
            }

            &-title {
                justify-content: flex-start;
                color: #97abcc;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
                min-width: 50%;
            }

            &-value {
                display: flex;
                flex-direction: column;
                gap: 10px;
                justify-content: flex-start;
                color: #97abcc;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
                width: 100%;

                &-empty {
                    color: #57719c;
                }

                &-label {
                    color: #57719c;
                }

                &-input {
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 14px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 400;
                }
            }
        }
    }

    .dependent-entry {
        display: flex;
        gap: 10px;
        align-items: center;
        margin-bottom: 10px;
        width: 100%;

        .dependent-inputs {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .remove-button {
            padding: 5px 10px;
            background-color: #ff4d4d;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            white-space: nowrap;

            &:hover {
                background-color: #cc0000;
            }
        }
    }

    .new-dependent-entry {
        display: flex;
        flex-direction: column;
        width: 100%;

        .new-dependent-inputs {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        button {
            width: 100%;
            margin-top: 10px;
        }
    }

    .add-button {
        padding: 5px 10px;
        background-color: #007bff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        width: 100%;

        &:hover {
            background-color: #0056b3;
        }
    }

    .edit-button {
        margin-top: 20px;
        padding: 10px 20px;
        background-color: #007bff;
        color: #fff;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        width: 100%;

        &:hover {
            background-color: #0056b3;
        }
    }

    .profile-info-section-value-splitter {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .dependent-input {
        width: 100%;
    }
}
</style>
