<template>
    <!-- components\panels\settings\Security.vue -->
    <NuxtLayout name="app">
        <div class="security">
            <Modal :with-overlay="true" :is-open="showResetModal" @close="closeResetModal" class="security-modal">
                <template #default>
                    <div class="security-modal-content">
                        <div>We’ve received your request to reset your password.</div>
                        <div>
                            An email has been sent to the address on file with instructions on how to complete the
                            process.
                        </div>
                        <div style="padding-top: 20px">
                            If you have any questions or need additional help, please reach out to our support team.
                        </div>
                    </div>
                    <div class="security-modal-actions">
                        <Button @click="closeResetModal">OK</Button>
                    </div>
                </template>
            </Modal>
            <Modal :with-overlay="true" :is-open="showDeleteModal" @close="closeDeleteModal" class="security-modal">
                <template #header>
                    <div class="security-modal-header">
                        <span>Account Deletion Confirmation</span>
                        <button class="popover-modal-close" @click.prevent="closeDeleteModal">
                            <SvgIcon class="popover-modal-close-icon" shape="close" width="20" height="20" />
                        </button>
                    </div>
                </template>
                <template #default>
                    <div class="security-modal-content">
                        <div class="security-modal-warning">
                            Note: This action is irreversible. All your data will be permanently deleted, and your
                            account will no longer be accessible
                        </div>
                        <div>
                            To proceed with deleting your account, please confirm your email address by entering it
                            below. This step helps us ensure that this request is genuine and authorized by you.
                        </div>
                        <div>
                            If you have any questions or need additional help, please reach out to our support team.
                        </div>
                        <div class="security-modal-content-input">
                            <TextInput
                                size="sm"
                                label="Enter your email address to confirm deletion *"
                                v-model="confirmEmail"
                                :placeholder="user?.userData.email"
                            />
                        </div>
                    </div>
                    <div class="security-modal-actions">
                        <Button color="red" :disabled="deleteDisabled" @click.prevent="deleteAccount">Delete</Button>
                        <Button @click.prevent="closeDeleteModal">Cancel</Button>
                    </div>
                </template>
            </Modal>
            <ProfileHeader />
            <div class="security-info">
                <Button v-if="showReset" @click="resetPassword">Password reset</Button>
                <Button variant="muted" @click="showDeleteModal = true">Delete FinPro account</Button>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import ProfileHeader from '~/components/panels/profile/ProfileHeader.vue';
import Button from '~/components/elements/Button.vue';
import Modal from '~/components/modals/Modal.vue';
import TextInput from '~/components/forms/TextInput.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';

const { user, logout } = useOidcAuth();

const showResetModal = ref(false);
const showDeleteModal = ref(false);

const confirmEmail = ref();

const showReset = computed(() => {
    const sub: string = (user.value?.userInfo?.sub as string) ?? '';
    return sub.startsWith('auth0|');
});

const deleteDisabled = computed(() => {
    return confirmEmail.value !== user.value?.userData.email;
});

function closeResetModal() {
    showResetModal.value = false;
}
function closeDeleteModal() {
    showDeleteModal.value = false;
}

async function resetPassword() {
    console.log('Clicked reset password');
    await $fetch('/api/profile/resetPassword', {
        method: 'post',
        body: { userId: user.value?.userData?._id ?? '' },
    });
    closeResetModal();
}

async function deleteAccount() {
    console.log('Clicked delete account');
    await $fetch('/api/profile/deleteAccount', {
        method: 'post',
        body: { userId: user.value?.userData?._id ?? '', email: confirmEmail.value },
    });
    return navigateTo('/inactiveAccount');
}
</script>

<style lang="scss">
.security {
    display: flex;
    flex-direction: column;
    width: 100vh;
    padding: 40px;
    box-sizing: border-box;

    &-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;

        &-signout {
            display: flex;
            flex-direction: row;
            gap: 10px;
            align-items: center;
            cursor: pointer;
            color: #97abcc;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 24px;
        }
    }

    &-modal {
        display: flex;
        flex-direction: column;
        padding: 20px;
        gap: 20px;
        border-radius: 10px;

        &-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #ffffff;
            font-size: 18px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            padding: 20px 0;
        }

        &-content {
            &-input {
                padding: 20px 0;
                width: 50%;
            }
        }

        &-warning {
            color: var(--color-red);
            font-weight: 700;
            margin: 10px 0;
        }

        &-actions {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            padding-top: 20px;

            &-button {
                display: flex;
                justify-content: center;
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                cursor: pointer;
                min-width: 200px;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }

    &-info {
        display: flex;
        flex-direction: row;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 4px;
        padding: 20px;
        gap: 20px;

        .security-link {
            display: flex;
            justify-content: center;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            cursor: pointer;
            min-width: 200px;

            &:hover {
                background-color: #0056b3;
            }
        }

        .security-delete {
            display: flex;
            justify-content: center;
            background-color: #0056b3;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            cursor: pointer;
            min-width: 200px;

            &:hover {
                background-color: #ec1a57;
            }
        }
    }
}
</style>
