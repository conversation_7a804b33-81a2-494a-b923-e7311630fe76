<template>
    <NuxtLayout name="app">
        <div class="access">
            <Modal :dynamic-placement="false" :with-overlay="true" :is-open="acceptPopover" @close="handleAcceptClose">
                <Accept @accept="handleInvitationAccepted" @decline="handleInvitationDeclined" @revoke="handleInvitationRevoked" :invite="currentInvite"/>
            </Modal>

            <ProfileHeader/>

            <div class="access-info" v-if="pendingInvites && pendingInvites.length">
                <div class="access-info-section" v-for="invite in pendingInvites" :key="invite.ownerId">
                    <div class="access-info-section-header">
                        <div class="access-info-section-header-date">
                            {{ invite.createdAt?.toDateString() }}
                            <span>{{ distanceToNow(invite.createdAt) }} ago</span>
                        </div>
                        <div class="access-info-section-header-title">
                            {{ invite.firstName }} {{ invite.lastName }}
                            <div class="access-info-section-header-description">
                                Invitation from {{ invite.firstName }} {{ invite.lastName }}
                                to advise on your portfolio
                            </div>
                        </div>
                        <Button class="access-info-section-header-button" @click.prevent="handleAcceptOpen(invite)">Respond</Button>
                    </div>
                </div>
            </div>
            
            <template v-for="{ inviteClass, invites } in [
                { inviteClass: 'accepted', invites: acceptedAdvisorInvites},
                { inviteClass: 'declined', invites: declinedInvites},
                { inviteClass: 'revoked', invites: revokedInvites}]">
                <div class="access-info" v-if="invites && invites.length">
                    <div :class="['access-info-section', inviteClass]" v-for="invite in invites" :key="invite.ownerId" @click.prevent="handleAcceptOpen(invite)">
                        <div class="access-info-section-header normal">
                            <UserAvatar :user="invite"/>
                            <div class="access-info-section-header-title">
                                Professional Financial Services
                                <div class="access-info-section-header-description">
                                    {{ invite.firstName }} {{ invite.lastName }}
                                </div>
                            </div>
                            <div class="access-info-section-header-detail">
                                Invitation {{ getInvitationText(invite) }}
                                <span>{{ distanceToNow(invite.updatedAt ?? invite.createdAt) }} ago</span>
                            </div>
                            <SvgIcon shape="keyboard-arrow-right" width="24" height="24" color="#57719C"/>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import {ref} from "vue";
import type {ProfileInvitation, ProfileResponse} from "~/server/api/profile/index.get";
import ProfileHeader from "~/components/panels/profile/ProfileHeader.vue";
import Modal from "~/components/modals/Modal.vue";
import Accept from "./Accept.vue";
import Button from "~/components/elements/Button.vue";
import SvgIcon from "~/components/images/SvgIcon.vue";
import UserAvatar from "~/components/images/UserAvatar.vue";
import {distanceToNow} from '~/utils';

const {data: profileData, refresh: profileRefresh} = await useApi<ProfileResponse>("/api/profile");

const pendingInvites = computed(() => {
    return profileData.value?.invites.filter((i) => i.userType === "Invited");
});
const acceptedAdvisorInvites = computed(() => {
    return profileData.value?.invites.filter((i) => i.userType === "Advisor");
});
const acceptedUserInvites = computed(() => {
    return profileData.value?.invites.filter((i) => i.userType === "User");
});
const declinedInvites = computed(() => {
    return profileData.value?.invites.filter((i) => i.userType === "Declined");
});
const revokedInvites = computed(() => {
    return profileData.value?.invites.filter((i) => i.userType === "Revoked");
});

function getInvitationText(invite: ProfileInvitation) {
    switch (invite.userType) {
        case "Invited":
            return "Pending";
        case "Advisor":
        case "User":
            return "Accepted";
        case "Declined":
            return "Declined";
        case "Revoked":
            return "Revoked";
        default:
            return "";
    }
}

watch(profileData, (newData) => {
    console.log("Profile Data Updated:", newData);
    console.log("Revoked Invites:", revokedInvites.value);
});

export interface AccessProps {
    inviteDate?: string;
    inviteDuration?: string;
    inviteTitle?: string;
    inviteDescription?: string;
    respondButtonText?: string;
    bankLogoUrl?: string;
    bankName?: string;
    bankUser?: string;
    statementTitle?: string;
    statementDuration?: string;
}

withDefaults(defineProps<AccessProps>(), {
    inviteDate: "Feb 12, 2024",
    inviteDuration: "2 mo",
    inviteTitle: "Super Advisor Services",
    inviteDescription: "Invitation by James Gunn to advise on the MCU",
    respondButtonText: "RESPOND",
    bankLogoUrl: "",
    bankName: "Bank of America",
    bankUser: "Santa Clause",
    statementTitle: "Personal Financial Statement",
    statementDuration: "Since today",
});

const acceptPopover = ref(false);
const currentInvite = ref<ProfileInvitation>();

function handleAcceptOpen(invite: ProfileInvitation) {
    currentInvite.value = invite;
    acceptPopover.value = true;
}

function handleAcceptClose() {
    acceptPopover.value = false;
}

async function handleInvitationAccepted(invite: ProfileInvitation) {
    console.log("Accepted:", invite);
    await $fetch("/api/profile/access", {
        method: "post",
        body: {ownerId: invite.ownerId, action: "accept"},
    });
    await profileRefresh();
    handleAcceptClose();
}

async function handleInvitationDeclined(invite: ProfileInvitation) {
    console.log("Declined:", invite);
    await $fetch("/api/profile/access", {
        method: "post",
        body: {ownerId: invite.ownerId, action: "decline"},
    });
    await profileRefresh();
    handleAcceptClose();
}

async function handleInvitationRevoked(invite: ProfileInvitation) {
    console.log("Revoked:", invite);
    await $fetch("/api/profile/access", {
        method: "post",
        body: {ownerId: invite.ownerId, action: "revoke"},
    });
    await profileRefresh();
    handleAcceptClose();
}
</script>

<style lang="scss">
.access {
  display: flex;
  flex-direction: column;
  width: 100vh;
  padding: 40px;
  box-sizing: border-box;

  &-info {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    &-section {
      display: flex;
      background-color: rgba(255, 255, 255, 0.05);
      padding: 20px;
      margin-bottom: 1px;
      border-radius: 4px;
      align-content: stretch;

      &.accepted {
        background-color: rgba(0, 255, 0, 0.05);
        cursor: pointer;
      }

      &.declined {
        background-color: rgba(255, 0, 0, 0.05);
        cursor: pointer;
      }

      &-header {
        display: flex;
        flex-direction: row;
        gap: 20px;
        flex: 1;
        align-items: center;
        font-size: 14px;
        font-family: "Oxygen", sans-serif;
        font-weight: 700;

        &.normal {
          font-weight: normal;
        }

        &-date {
          display: flex;
          flex-direction: column;
          color: #97abcc;
          line-height: 20px;
          justify-content: center;
          min-width: 120px;

          span {
            color: #57719c;
            font-weight: 400;
          }
        }

        &-title {
          display: flex;
          flex-direction: column;
          color: #e9edf1;
          line-height: 20px;
          align-items: flex-start;
          word-wrap: break-word;
          flex: 1;
        }

        &-description {
          color: #97abcc;
          font-weight: 400;
          word-wrap: break-word;
        }

        &-button {
          align-self: center;
          background-color: #007bff;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 8px 16px;
          cursor: pointer;

          &:hover {
            background-color: #0056b3;
          }
        }

        &-detail {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-end;
          color: #97abcc;
          font-weight: 400;
          line-height: 20px;
          word-wrap: break-word;

          span {
            margin-left: 8px;
            color: #57719c;
          }
        }
      }
    }

    &-avatar {
      width: 40px;
      height: 40px;
      box-sizing: border-box;
      border: 1px solid var(--color-secondary-blue-grey, #57719c);
      border-radius: 50%;
      background: var(--color-secondary-blue-grey, #57719c);
      background-size: cover;
      background-position: center;
      color: var(--color-primary-white, #e9edf1);
      font-weight: 700;
      font-size: 14px;
      line-height: 30px;
      cursor: pointer;
      user-select: none;

      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 0;
      gap: 10px;
      flex: none;
      margin-right: 20px;

      &-image {
        border-radius: 50%;
      }
    }
  }
}
</style>
