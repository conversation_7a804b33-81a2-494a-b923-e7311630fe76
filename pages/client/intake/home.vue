<template>
    <div class="intake-layout-content">
        <TabsRadioGroup
            :list="homeTypeOptions"
            :default="(primaryResidenceExpenseItem) ? 'rent' : (primaryResidenceAssetItem) ? 'own' : null" 
            default-slot-min-height="200px"
            @change="handleTabChange"
        >
            <template #default>
                Select the option that best describes your primary residence.
            </template>
            <template #own>
                <OverhaulLineItem
                    v-if="primaryResidenceAssetLineItem"
                    @clicked="handleAssetModalOpen"
                    v-bind="primaryResidenceAssetLineItem"
                    :draggable="false"
                />
            </template>

            <template #rent>
                <OverhaulLineItem
                    v-if="primaryResidenceExpenseLineItem"
                    @clicked="handleExpenseModalOpen"
                    v-bind="primaryResidenceExpenseLineItem"
                    :draggable="false"
                />
            </template>
        </TabsRadioGroup>
    </div>

    <SubModal
        :is-open="assetModalIsOpen"
        :show-footer="true"
        title="Tell us about your <strong>Primary Residence</strong>"
        @close="handleAssetModalClose"
        @confirm="handleAssetModalConfirm"
    >
        <div class="overhaul-create-and-link-modal">
            <div v-if="primaryResidenceAssetItem"
                v-for="field in primaryResidenceAssetItem.fields"
                :key="field.name"
            >
                <!-- TODO: Temporarily hiding "owner" -->
                <SituationInputGroup
                    v-if="field && field.name != 'owner'"
                    v-bind="field"
                    :key="field.name"
                    @change="(payload) => handleAssetItemFieldChange(field, payload)"
                />
            </div>
        </div>
    </SubModal>

    <SubModal
        :is-open="expenseModalIsOpen"
        :show-footer="true"
        title="Tell us about your <strong>Primary Residence</strong>"
        @close="handleExpenseModalClose"
        @confirm="handleExpenseModalConfirm"
    >
        <div class="overhaul-create-and-link-modal">
            <div 
                v-if="primaryResidenceExpenseFields"
                v-for="field in primaryResidenceExpenseFields"
                :key="field?.name ?? 'amount'"
            >
                <!-- TODO: Temporarily hiding "owner" -->
                <SituationInputGroup
                    v-if="field && field.name != 'owner'"
                    v-bind="field"
                    :key="field.name"
                    @change="(payload) => handleExpenseItemFieldChange(field, payload)"
                />
            </div>
        </div>
    </SubModal>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { primaryResidenceAssetData, primaryResidenceExpenseData, transformSituationTypeForCard } from '~/components/situations/data/intake-data';
import TabsRadioGroup from '~/components/forms/TabsRadioGroup.vue';
import type {SituationEntryMode} from "~/components/situations/SituationItem.vue";
import OverhaulLineItem from '~/components/intake/OverhaulLineItem.vue';
import SubModal from '~/components/modals/SubModal.vue';
import SituationInputGroup, { type SituationInputGroupProps } from '~/components/situations/SituationInputGroup.vue';

const overhaulStore = useOverhaulStore();
// const {
//     data,
// } = storeToRefs(overhaulStore);

const helpStore = useHelpStore();

definePageMeta({
    layout: 'overhaul-layout',
    title: 'Tell us about your <strong>primary residence</strong>.'
})

const homeType = ref<'own'|'rent'>()

onMounted(() => {
    homeType.value = primaryResidenceExpenseItem.value ? 'rent' : 'own';
})

onBeforeRouteLeave(() => {
    if (homeType.value === 'rent' && primaryResidenceAssetItem.value) {
        overhaulStore.deleteItem('assets', primaryResidenceAssetItem.value.id);
    } else if (homeType.value === 'own' && primaryResidenceExpenseItem.value) {
        overhaulStore.deleteItem('expenses', primaryResidenceExpenseItem.value.id);
    }
})

const homeTypeOptions = ref([
    {
        label: 'I own my home',
        description: 'You own your primary residence, either with or without a mortgage attached.',
        name: 'own',
    },
    {
        label: 'I rent a home',
        description: 'You either rent your primary residence or live in a home owned by someone else.',
        name: 'rent',
    },
])

onMounted(() => {
    helpStore.init(['expense-housing']);
});

const primaryResidenceAssetItem = computed(() => 
    overhaulStore.assetItemsArr.find(item => item.kind === 'AssetHome')
)

const primaryResidenceAssetLineItem = computed(() => 
    overhaulStore.assetLineItems.find(item => item.kind === 'AssetHome')
)

const primaryResidenceExpenseItem = computed(() => 
    overhaulStore.expenseItemsArr.find(item => item.kind === 'ExpensePrimaryResidence')
)

const primaryResidenceExpenseLineItem = computed(() => 
    overhaulStore.expenseLineItems.find(item => item.kind === 'ExpensePrimaryResidence')
)

const primaryResidenceExpenseFields = computed(() => 
    [
        // primaryResidenceExpenseItem.value?.fields.find(e => e.name === 'description'),
        primaryResidenceExpenseItem.value?.fields.find(e => e.name === 'amount')
    ]
        // .sort((a, b) => {
        //     if (a.name === "description") return -1;
        //     if (b.name === "description") return 1;
        //     return 0; // no change
        // })
)

//   __  __           _       _     
//  |  \/  | ___   __| | __ _| |___ 
//  | |\/| |/ _ \ / _` |/ _` | / __|
//  | |  | | (_) | (_| | (_| | \__ \
//  |_|  |_|\___/ \__,_|\__,_|_|___/
//                                  
const assetModalIsOpen = ref(false);
function handleAssetModalClose() {
    assetModalIsOpen.value = false
}
function handleAssetModalOpen() {
    assetModalIsOpen.value = true
}
function handleAssetModalConfirm() {
    assetModalIsOpen.value = false
}

const expenseModalIsOpen = ref(false);
function handleExpenseModalClose() {
    expenseModalIsOpen.value = false
}
function handleExpenseModalOpen() {
    expenseModalIsOpen.value = true
}
function handleExpenseModalConfirm() {
    expenseModalIsOpen.value = false
}

function handleAssetItemFieldChange(field: SituationInputGroupProps, payload: any) {
    if (!primaryResidenceAssetItem.value) return;
    overhaulStore.updateItemField('assets', primaryResidenceAssetItem.value.id, field.name, payload)
}

function handleExpenseItemFieldChange(field: SituationInputGroupProps, payload: any) {
    if (!primaryResidenceExpenseItem.value) return;
    overhaulStore.updateItemField('expenses', primaryResidenceExpenseItem.value.id, field.name, payload)
}

async function handleTabChange(tab: 'own'|'rent') {
    homeType.value = tab;
    if (tab === 'own') {
        // If the primary residence asset situation doesn't exist, create it
        if (!primaryResidenceAssetItem.value) {
            overhaulStore.addItem('assets', 'AssetHome', 'Standard');
            await nextTick();
            handleAssetModalOpen();
        }
    } else {
        // If the housing rent situation doesn't exist, create it
        if (!primaryResidenceExpenseItem.value) {
            overhaulStore.addItem('expenses', 'ExpensePrimaryResidence', 'Standard');

            await nextTick();

            handleExpenseItemFieldChange({name: 'description'}, 'Rent: Primary Residence');

            handleExpenseModalOpen();
        }
    }

    // if(data.value.home) {
    //     data.value.home.type = tab;
    // }
}

</script>
