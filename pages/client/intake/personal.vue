<template>
    <div class="intake-layout-list">
        <!-- Name Block -->
        <InputGroupBlock title="Name" description="Your legal name.">
            <template v-if="data.name">
                <TextInput label="First" v-model="data.name.firstName" />
                <TextInput label="Middle" v-model="data.name.middleName" />
                <TextInput label="Last" v-model="data.name.lastName" />
            </template>

            <template v-else>
                <TextInput label="First" />
                <TextInput label="Middle" />
                <TextInput label="Last" />
            </template>
        </InputGroupBlock>

        <!-- Contact Information -->
        <InputGroupBlock title="Contact Information">
            <template v-if="data">
                <TextInput label="Email" v-model="data.email" />
                <TextInput
                    label="Phone Number"
                    v-model="data.phone"
                    @input="e => (data.phone = formatPhoneNumber(e.target.value))"
                />
            </template>
        </InputGroupBlock>

        <!-- Birthdate Block -->
        <InputGroupBlock title="Birthdate">
            <TextInput
                label="Birthdate"
                type="date"
                :svg-icon="{ shape: 'calendar' }"
                v-model="data.birthdate"
                :min="minBirthdate"
                :max="maxBirthdate"
            />
        </InputGroupBlock>

        <!-- Marital Status Block -->
        <InputGroupBlock title="Marital status" description="Your current marital status.">
            <CheckboxGroup
                variant="buttons"
                mode="radio"
                v-model:options="maritalStatusOptions"
                v-model:selected="maritalStatus"
            />
        </InputGroupBlock>

        <!-- Spouse Information (Conditional) -->
        <template v-if="maritalStatus === 'married' && data.spouse">
            <InputGroupBlock title="Spouse's Name" description="Your spouse's legal name.">
                <TextInput label="First" v-model="data.spouse.firstName" />
                <TextInput label="Middle" v-model="data.spouse.middleName" />
                <TextInput label="Last" v-model="data.spouse.lastName" />
            </InputGroupBlock>

            <!-- Contact Information -->
            <InputGroupBlock title="Spouse Contact Information">
                <template v-if="maritalStatus === 'married' && data.spouse">
                    <TextInput label="Email" v-model="data.spouse.email" />
                    <TextInput
                        label="Phone Number"
                        v-model="data.spouse.phone"
                        @input="e => (data.spouse.phone = formatPhoneNumber(e.target.value))"
                    />
                </template>
            </InputGroupBlock>

            <InputGroupBlock title="Spouse's Birthdate">
                <TextInput
                    label="Birthdate"
                    type="date"
                    :svg-icon="{ shape: 'calendar' }"
                    v-model="data.spouse.birthdate"
                    :min="minBirthdate"
                    :max="maxBirthdate"
                />
            </InputGroupBlock>

            <!-- Dependents Block -->
            <InputGroupBlock
                title="Dependents"
                description="Please provide your dependents' names and birthdays."
                :multiRow="true"
            >
                <template v-if="dependentsCount > 0">
                    <div v-for="(dependent, index) in data.dependents" :key="index" class="dependent-block">
                        <div class="dependent-row">
                            <TextInput class="dependent-input" placeholder="Name" v-model="dependent.name" />
                            <TextInput
                                class="dependent-input"
                                placeholder="Relationship"
                                v-model="dependent.relationship"
                            />
                            <TextInput
                                class="dependent-input"
                                placeholder="Birthday"
                                type="date"
                                :svg-icon="{ shape: 'calendar' }"
                                v-model="dependent.dob"
                                :min="minBirthdate"
                                :max="maxBirthdate"
                            />
                            <button class="remove-button" @click="removeDependent(index)">
                                <SvgIcon shape="trash" />
                            </button>
                        </div>
                    </div>
                </template>

                <!-- No Dependents Message -->
                <template v-else>
                    <div class="no-dependents">
                        <div class="no-dependents-container">
                            <p class="no-dependents-question">Would you like to add dependents now?</p>
                            <p class="no-dependents-description">
                                Dependents are stored for the purposes of tax deductions and will not have an account or
                                be required to log in.
                            </p>
                            <div class="no-dependents-actions">
                                <Button variant="primary" @click="addDependent">YES, I WANT TO ADD THEM NOW</Button>
                            </div>
                        </div>
                    </div>
                </template>
            </InputGroupBlock>

            <!-- Add Dependents Controls -->
            <div class="add-dependents" v-if="dependentsCount > 0">
                <span>{{ dependentsCount }} dependents</span>
                <div class="add-dependent-button">
                    <Button variant="muted" @click="addDependent">+ Dependent</Button>
                    <Button variant="primary" @click="saveDependent">SAVE</Button>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import InputGroupBlock from '~/components/forms/InputGroupBlock.vue';
import TextInput from '~/components/forms/TextInput.vue';
import CheckboxGroup from '~/components/forms/CheckboxGroup.vue';
import Button from '~/components/elements/Button.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';

const overhaulStore = useOverhaulStore();
const { data } = storeToRefs(overhaulStore);

const helpStore = useHelpStore();

onMounted(() => {
    helpStore.init([]);
});

definePageMeta({
    layout: 'overhaul-layout',
    title: 'Personal information',
});

const maritalStatus = ref(data.value.maritalStatus);
const maritalStatusOptions = ref([
    { label: 'Single', name: 'single' },
    { label: 'Married', name: 'married' },
    { label: 'Widow', name: 'widow' },
    { label: 'Divorced', name: 'divorced' },
    { label: 'Separated', name: 'separated' },
]);

const minBirthdate = ref('1900-01-01');
const maxBirthdate = ref(new Date().toISOString().split('T')[0]);

function addDependent() {
    data.value.dependents.push({
        name: '',
        relationship: '',
        dob: '',
    });
}

function saveDependent() {
    // TODO: Implement save logic
    // For now, just log to console
    console.log('Dependents saved:', data.value.dependents);
}

function removeDependent(index: number) {
    data.value.dependents.splice(index, 1);
}

watch(maritalStatus, newStatus => {
    data.value.maritalStatus = newStatus;
    if (newStatus === 'married') {
        if (!data.value.dependents) {
            data.value.dependents = [];
        }
    }
});

// Ensure the data is saved when the user navigates away
onBeforeRouteLeave(() => {
    overhaulStore.update();
});

// Computed property to track the number of dependents
const dependentsCount = computed(() => data.value.dependents?.length);

function formatPhoneNumber(value: string): string {
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)})${digits.slice(3)}`;
    return `(${digits.slice(0, 3)})${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
}
</script>

<style scoped>
.dependent-block {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.dependent-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.dependent-input {
    flex: 1;
    margin-right: 10px;
}

.remove-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #97abcc;
}

.add-dependents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 20px;
    position: relative;
    bottom: 5px;
}

.add-dependent-button {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.no-dependents {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    &-container {
        width: 485px;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-actions {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-question {
        color: #97abcc;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
    }

    &-description {
        color: #57719c;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 20px;
        word-wrap: break-word;
    }
}
</style>
