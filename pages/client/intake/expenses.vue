<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div class="intake-expenses-page" v-if="expenseItems.length">
        <div class="expense-panels">
            <ExpensePanel
                v-for="group in expenseGroups"
                :key="group?.name"
                v-bind="group"
                @add-item="() => handleSituationAddItem(group)"
                @delete="() => handleExpenseGroupRemove(group)"
            >
                <template v-for="item in group?.items" :key="item.id">
                    <ExpenseInputRow
                        :owner-options="[{value: 'single', label: 'Single'},{value: 'joint', label: 'Joint'}]"
                        v-bind="item"
                        @change="(...e) => handleRowChange(item, ...e)"
                        @remove="handleRowRemove"
                        />
                </template>
            </ExpensePanel>
        </div>

        <button class="intake-layout-grid-add-button" type="button" @click.prevent="handleShowAddObjectModal">
            <SvgIcon shape="plus" width="20" height="20" />
            <span>Add expenses</span>
        </button>

        <!-- <div class="situation-card-grid">
            <SituationsSituationItem
                v-for="expense in expenses"
                :key="expense.name"
                :title="expense.kind"
                v-bind="expense"
                @add-item="() => handleSituationAddItem(expense)"
                @remove-item="(itemId: number|string) => handleSituationRemoveItem(expense, itemId)"
                @standard-change="handleSituationStandardChange"
                @simple-change="handleSituationSimpleChange"
                @entry-mode-change="(newMode, useAsDefault) => handleSituationEntryModeChange(expense.name, newMode, useAsDefault)"
                @close="handleSituationItemModalClose"
            />

            <button class="situation-card-grid-add-button" type="button" @click.prevent="handleAddIncomeSource">
                <ImagesSvgIcon shape="plus" width="40" height="40" />
                <span>Add expense</span>
            </button>
        </div> -->
    </div>

    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="assetType in expenseTypes" :key="assetType.name">
            <ElementsIconSelector
                v-bind="assetType"
                v-model="assetType.selected"
                @popover-save="(options) => handleIconSelectorSave(assetType.name, options)"
                />
        </div>
    </div>

    <!-- <AddObjectModal 
        kind="expenses"
        entry-mode="standard"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    /> -->

    <OverhaulAddObjectModal
        category="expenses"
        entry-mode="Standard"
        :is-open="addItemsModalIsOpen"
        @close="handleCloseAddItemsModal"
    />
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import type { SituationEntryMode } from '@/components/situations/SituationItem.vue';
import ExpensePanel from '@/components/panels/ExpensePanel.vue';
import ExpenseInputRow from '@/components/forms/ExpenseInputRow.vue';
import SvgIcon from '@/components/images/SvgIcon.vue';
import { allExpenseTypes, allTypes, transformSituationTypesForCard } from '@/components/situations/data/intake-data';
import { getNormalizeFrequencyMultiplier } from '@/utils';
import type { IntakeSituationItem, IntakeStandardItem } from '@/stores/intake';
import AddObjectModal from '@/components/situations/AddObjectModal.vue';

import pkg from 'lodash';
import { sub } from 'date-fns';
import OverhaulAddObjectModal from '~/components/intake/OverhaulAddObjectModal.vue';
const { debounce } = pkg;

const overhaulStore = useOverhaulStore();
const {
    data,
    expenses,
} = storeToRefs(overhaulStore);

const helpStore = useHelpStore();
const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'overhaul-layout',
    includeTitleBar: false
})

const expenseTypes = reactive(transformSituationTypesForCard(allExpenseTypes));

const expenseLineItems = computed(() => {
    return overhaulStore.expenseLineItems;
});

const expenseItems = computed(() => 
    overhaulStore.expenseItemsArr
);

const titleHtml = computed(() =>
    (expenseItems.value?.length)
        ? 'Tell us about your <em>expenses</em>.'
        : 'What <em>types of expenses</em> do you have?'
)
async function handlePrevClick() {
    await navigateTo('/client/intake/income')
}
async function handleNextClick() {
    if(expenseItems.value?.length || expenseTypes?.filter(e => e.selected).length === 0) {
        await navigateTo('/client/intake/review')
    } else {
        populateExpenseCards();
    }
}

// The total expense amount for the entire portfolio
const totalAnnualExpenses = computed(() => expenseItems.value?.reduce(annualExpenseReducer, 0));

function getAnnualExpenseFromRow(row: IntakeStandardItem) {
    const amountField = row.fields.find(f => f.name === 'amount')?.value
    const multiplier = getNormalizeFrequencyMultiplier(amountField.frequency ?? 'Yearly', 'Yearly');
    return amountField.number * (multiplier ?? 1);

    // const frequencyMultiplier = getNormalizeFrequencyMultiplier(field.value.frequency, normalizeFrequency === 'monthly' ? 'Monthly' : 'Yearly');
    // value = (field.value.number ?? 0) * (frequencyMultiplier ?? 1);
}

function annualExpenseReducer(acc: number, item: TPopulatedItem) {
    const amountField = item.fields.find(f => f.name === 'amount')?.value;
    const multiplier = getNormalizeFrequencyMultiplier(amountField.frequency ?? 'Yearly', 'Yearly');
    return acc + ((amountField?.number ?? 0) * (multiplier ?? 1));
}

function getAnnualExpenseFromGroup(group: IntakeSituationItem) {
    return Object.values(group.standard).reduce((acc, row) => {
        return getAnnualExpenseFromRow(row) + acc
    }, 0);
}

const mappedExpenses = computed(() => [])

export interface ExpenseGroup {
    kind: string;
    title: string;
    svgShape: string;
    name: string;
    monthly: number;
    annual: number;
    percentOfTotal: number;
    items: TPopulatedItem[];
}

const expenseGroups = computed(() => {
    if (!expenseItems.value?.length) {
        return [];
    }

    const uniqueKinds = [...new Set(expenseItems.value.map(e => e.kind))];
    const mappedGroups = uniqueKinds.map(kind => {
        const kindData = allTypes.find(e => e.kind === kind);
        if (!kindData) {
            return;
        }

        const itemsOfKind = expenseItems.value.filter(e => e.kind === kind);
        const annualExpenseAmount = itemsOfKind.reduce(annualExpenseReducer, 0)
        const percentOfTotal = Math.round((annualExpenseAmount / totalAnnualExpenses.value) * 100);
        return {
            kind,
            title: kindData.label,
            svgShape: kindData.iconShape,
            name: kindData.name,
            monthly: Math.floor(annualExpenseAmount / 12) ?? 0,
            annual: annualExpenseAmount ?? 0,
            percentOfTotal: percentOfTotal,
            canAddItem: kindData.name !== 'primary-residence-expense',
            items: itemsOfKind.map(item => {
                const amountField = item.fields.find(f => f.name === 'amount');
                return {
                    id: item.id,
                    owner: item.fields.find(f => f.name === 'owner')?.value ?? 'single',
                    description: item.fields.find(f => f.name === 'description')?.value,
                    descriptionReadonly: item.fields.find(f => f.name === 'description')?.readonly,
                    amount: amountField?.value.number ?? 0,
                    frequency: amountField?.value.frequency ?? 'Monthly'
                }
            })
        } 
    });

    console.log ('mappedGroups', mappedGroups)

    return mappedGroups.filter(e => e);
})

onMounted(() => {
    // overhaulStore.recalculateTotals()
    helpStore.init(['expenses', 'expense-auto', 'expense-buisness', 'expense-charitable-donations', 'expense-childcare-tuition', 'expense-clothing-and-cosmetic', 'expense-dining', 'expense-healthcare', 'expense-housing', 'expense-insurance', 'expense-leisure-and-recreation', 'expense-loan-payments', 'expense-savings-brokerage-contributions', 'expense-subscriptions', 'expense-taxes']);
});

// function populateExpenseCards() {
//     if (expenses.value?.length) {
//         return;
//     }    
//     const selectedExpenses = expenseTypes?.filter(e => e.selected);
//     overhaulStore.populateSituationCards('expenses', selectedExpenses, 'standard');
// }

function populateExpenseCards() {
    if (expenseItems.value?.length) {
        return;
    }

    const selectedExpenses = expenseTypes?.filter(e => e.selected);

    selectedExpenses.forEach(expenseKindData => {
        const selectedSubOptions = expenseKindData.subOptions?.options.filter(e => e.value);
        if (selectedSubOptions?.length) {
            selectedSubOptions.forEach(subOption => {
                overhaulStore.addItem('expenses', expenseKindData.kind, 'Standard', subOption.name);
            });
        } else {
            overhaulStore.addItem('expenses', expenseKindData.kind, 'Standard');
        }
    });
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = expenseTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(expense: IntakeSituationItem) {
    console.log('handleSituationAddItem', expense);
    overhaulStore.addItem('expenses', expense.kind, 'Standard');
}

function handleExpenseGroupRemove(expenseGroup: Partial<{kind: string}>) {
    const kind = expenseGroup.kind;
    
    const itemsOfKind = expenseItems.value.filter(e => e.kind === kind);
    itemsOfKind.forEach(item => {
        overhaulStore.deleteItem('expenses', item.id);
    });
}

const handleRowChange = debounce((row: any, itemId: string, fieldName: string, newValue: string|number) => {
    let value;
    if (fieldName === 'amount') {
        value = {
            number: newValue,
            frequency: row.frequency
        }
    } else if (fieldName === 'frequency') {
        fieldName = 'amount';
        value = {
            number: row.amount,
            frequency: newValue
        }
    } else {
        value = newValue;
    }

    overhaulStore.updateItemField('expenses', itemId, fieldName, value);
}, 400);

function handleRowRemove(itemId: string) {
    overhaulStore.deleteItem('expenses', itemId);
}

//      _       _     _   ___ _                       __  __           _       _ 
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//                                                                               
const addItemsModalIsOpen = ref(false);
function handleShowAddObjectModal() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>

<style>
.intake-expenses-page {
    margin-top: -20px;
}
</style>