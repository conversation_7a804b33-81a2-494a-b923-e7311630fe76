<template>
    <PartialsIntakeTitleBar :title="titleHtml" @prev-clicked="handlePrevClick" @next-clicked="handleNextClick" />

    <template v-if="showLineItems">
        <div class="overhaul-listing-filter-row">
            <div class="overhaul-listing-filters">
                <ToggleTag
                    label="All"
                    :count="liabilityLineItems.length"
                    :is-selected="activeFilters.size === 0"
                    @click.prevent="removeAllFilters"
                />
                <ToggleTag
                    v-for="filter in filters"
                    v-bind="filter"
                    :key="filter.label"
                    :is-selected="activeFilters.has(filter.name)"
                    @click.prevent="toggleFilter(filter.name)"
                />
            </div>
        </div>
        <div class="overhaul-line-item-group">
            <div class="overhaul-line-item-group-header">
                <h4>
                    <!-- Qualified Liabilities -->
                </h4>
                <button class="overhaul-line-item-add-button" @click.prevent="handleAddItemModalOpen">
                    Add <SvgIcon shape="plus" :width="20" :height="20" />
                </button>
            </div>

            <OverhaulLineItem
                v-for="item in sortedLineItems"
                @clicked="() => handleLineItemClick(item)"
                :draggable="true"
                v-bind="item"
            />

            <OverhaulModal
                :is-open="modalIsOpen"
                :initial-item="clickedItem"
                category="liabilities"
                @close="handleModalClose"
            />
        </div>

        <OverhaulAddObjectModal category="liabilities" :is-open="addItemModalIsOpen" @close="handleAddItemModalClose" />

        <OverhaulInitialItemModal
            category="liabilities"
            :is-open="initialItemModalIsOpen"
            :selected-item-id="clickedItem"
            @close="handleInitialItemModalClose"
            @confirm="handleInitialItemModalConfirm"
        />
    </template>

    <!-- "Default" view of icon selectors to add items -->
    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="kindData in allItemKindData" :key="kindData.name">
            <ElementsIconSelector
                v-bind="kindData"
                v-model="kindData.selected"
                @popover-save="options => handleIconSelectorSave(kindData.name, options)"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import OverhaulLineItem from '~/components/intake/OverhaulLineItem.vue';
import OverhaulModal from '~/components/intake/OverhaulModal.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import ToggleTag from '~/components/elements/ToggleTag.vue';
import OverhaulAddObjectModal from '~/components/intake/OverhaulAddObjectModal.vue';
import OverhaulInitialItemModal from '~/components/intake/OverhaulInitialItemModal.vue';
import type { TOverhaulLineItem } from '~/stores/overhaul';
import { allLiabilityTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';

const overhaulStore = useOverhaulStore();

definePageMeta({
    name: 'liabilities',
    layout: 'overhaul-layout',
    title: 'Please tell us about your <em>liabilities</em>',
    includeTitleBar: false,
});

const liabilityLineItems = computed(() => {
    return overhaulStore.liabilityLineItems;
});

const liabilityItems = computed(() => overhaulStore.liabilityItemsArr);

const filteredLineItems = computed(() => {
    return activeFilters.size > 0
        ? liabilityLineItems.value.filter(item => activeFilters.has(item.name))
        : liabilityLineItems.value;
});

const titleHtml = computed(() =>
    liabilityLineItems.value?.length
        ? 'Tell us about about your <em>liabilities</em>.'
        : 'What <em>types of liabilities</em> do you have?',
);

const showLineItems = computed(() => liabilityLineItems.value?.length);

const allItemKindData = reactive(transformSituationTypesForCard(allLiabilityTypes));

async function handlePrevClick() {
    await navigateTo('/client/intake/assets');
}
async function handleNextClick() {
    const selectedLiabilities = allItemKindData?.filter(e => e.selected);
    if (!showLineItems.value && selectedLiabilities?.length) {
        createSelectedItems();
    } else {
        await overhaulStore.markSectionAsCompleted('liabilities');
        await navigateTo('/client/intake/insurance');
    }
}

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = allItemKindData.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach(option => {
            option.value = options.includes(option.name);
        });
    }
}

function createSelectedItems() {
    const selectedLiabilities = allItemKindData?.filter(e => e.selected);

    // For each selected kind, add a new item of that kind
    selectedLiabilities?.forEach(kindData => {
        const selectedSubOptions = kindData.subOptions?.options.filter(e => e.value);
        if (selectedSubOptions?.length) {
            selectedSubOptions.forEach(subOption => {
                overhaulStore.addItem('liabilities', kindData.kind, 'Standard', subOption.name);
            });
        } else {
            overhaulStore.addItem('liabilities', kindData.kind);
        }
    });

    // Reset all selected values
    allItemKindData.forEach(element => {
        element.selected = false;
        if (element.subOptions) {
            element.subOptions.options.forEach(option => {
                option.value = false;
            });
        }
    });
}

const modalIsOpen = ref(false);
function handleModalOpen() {
    console.log('open');
    modalIsOpen.value = true;
}
function handleModalClose() {
    console.log('close');
    modalIsOpen.value = false;
    clickedItem.value = null;
}

const clickedItem: Ref<string | null> = ref(null);
async function handleLineItemClick(item: TOverhaulLineItem) {
    // console.log(itemId);
    clickedItem.value = item.id;

    await nextTick();

    if (!item.entryMode) {
        handleInitialItemModalOpen();
    } else {
        handleModalOpen();
    }
}

// Filters functionality was just copied over from OverhaulModal for now
const filters = computed(() => {
    const ii = liabilityItems.value?.reduce((acc, item) => {
        if (!acc.has(item.name)) {
            acc.set(item.name, {
                label: item.kindLabel,
                name: item.name,
                count: 1,
            });
        } else {
            const obj = acc.get(item.name);
            obj.count++;
            acc.set(obj.name, obj);
        }
        return acc;
    }, new Map<string, any>());

    return Array.from(ii.values());
});

const activeFilters = reactive(new Set<string>());

function toggleFilter(filter: string) {
    if (activeFilters.has(filter)) {
        activeFilters.delete(filter);
    } else {
        activeFilters.add(filter);
    }
}
const filteredItems = computed(() => {
    if (activeFilters.size === 0) return items.value;
    return liabilityLineItems.value.filter(item => activeFilters.has(item.name));
});
function removeAllFilters() {
    activeFilters.clear();
}

const addItemModalIsOpen = ref(false);
function handleAddItemModalOpen() {
    addItemModalIsOpen.value = true;
}
function handleAddItemModalClose() {
    addItemModalIsOpen.value = false;
}

const initialItemModalIsOpen = ref(false);
function handleInitialItemModalOpen() {
    initialItemModalIsOpen.value = true;
}
function handleInitialItemModalClose() {
    initialItemModalIsOpen.value = false;
}
async function handleInitialItemModalConfirm(newItemId: string) {
    initialItemModalIsOpen.value = false;

    await nextTick();

    clickedItem.value = newItemId;

    await nextTick();

    handleModalOpen();
}

const statusOrder = { complete: 0, 'in-progress': 1, 'not-started': 2 };

const sortedLineItems = computed(() => {
    return [...filteredLineItems.value].sort((a, b) => {
        const statusA = statusOrder[a.status] ?? 99;
        const statusB = statusOrder[b.status] ?? 99;
        if (statusA !== statusB) return statusA - statusB;
        return (a.label || a.kindLabel || '').localeCompare(b.label || b.kindLabel || '');
    });
});
</script>

<style lang="scss">
.overhaul-line-item-group {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 1px;
    .overhaul-line-item-group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 9px;

        h4 {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: -0.02em;

            color: #57719c;
        }

        button {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            gap: 10px;

            color: #97abcc;
        }
    }
}

// Just copied from OverhaulModal.scss for now
.overhaul-listing-filter-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    margin-bottom: 20px;
}

.intake-layout-title + .overhaul-listing-filter-row {
    margin-top: -20px;
}

.overhaul-listing-filters {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    flex: 1 1 auto;
    gap: 4px;
}
</style>
