<template>
    <PartialsIntakeTitleBar :title="titleHtml" @prev-clicked="handlePrevClick" @next-clicked="handleNextClick" />

    <template v-if="showLineItems">
        <div class="overhaul-listing-filter-row">
            <div class="overhaul-listing-filters">
                <ToggleTag
                    label="All"
                    :count="insuranceLineItems.length"
                    :is-selected="activeFilters.size === 0"
                    @click.prevent="removeAllFilters"
                />
                <ToggleTag
                    v-for="filter in filters"
                    v-bind="filter"
                    :key="filter.label"
                    :is-selected="activeFilters.has(filter.name)"
                    @click.prevent="toggleFilter(filter.name)"
                />
            </div>
        </div>
        <div class="overhaul-line-item-group">
            <div class="overhaul-line-item-group-header">
                <h4>
                    <!-- Qualified Insurance -->
                </h4>
                <button class="overhaul-line-item-add-button" @click.prevent="handleAddItemModalOpen">
                    Add <SvgIcon shape="plus" :width="20" :height="20" />
                </button>
            </div>

            <OverhaulLineItem
                v-for="item in sortedLineItems"
                @clicked="() => handleLineItemClick(item)"
                :draggable="true"
                v-bind="item"
            />

            <OverhaulModal
                :is-open="modalIsOpen"
                :initial-item="clickedItem"
                category="insurance"
                @close="handleModalClose"
            />
        </div>

        <OverhaulAddObjectModal
            category="insurance"
            :is-open="addItemModalIsOpen"
            entry-mode="Standard"
            @close="handleAddItemModalClose"
        />

        <OverhaulInitialItemModal
            category="insurance"
            :is-open="initialItemModalIsOpen"
            :selected-item-id="clickedItem"
            @close="handleInitialItemModalClose"
            @confirm="handleInitialItemModalConfirm"
        />
    </template>

    <!-- "Default" view of icon selectors to add items -->
    <div class="intake-layout-grid" v-else>
        <div class="intake-layout-grid-item" v-for="insuranceType in insuranceTypes" :key="insuranceType.name">
            <ElementsIconSelector
                v-bind="insuranceType"
                v-model="insuranceType.selected"
                @popover-save="options => handleIconSelectorSave(insuranceType.name, options)"
            />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import OverhaulLineItem from '~/components/intake/OverhaulLineItem.vue';
import OverhaulModal from '~/components/intake/OverhaulModal.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import ToggleTag from '~/components/elements/ToggleTag.vue';
import OverhaulAddObjectModal from '~/components/intake/OverhaulAddObjectModal.vue';
import OverhaulInitialItemModal from '~/components/intake/OverhaulInitialItemModal.vue';
import type { TOverhaulLineItem } from '~/stores/overhaul';
import { allInsuranceTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';

const overhaulStore = useOverhaulStore();

definePageMeta({
    name: 'insurance',
    layout: 'overhaul-layout',
    includeTitleBar: false,
});

const insuranceLineItems = computed(() => {
    return overhaulStore.insuranceLineItems;
});

const insuranceItems = computed(() => overhaulStore.insuranceItemsArr);

const filteredLineItems = computed(() => {
    return activeFilters.size > 0
        ? insuranceLineItems.value.filter(item => activeFilters.has(item.name))
        : insuranceLineItems.value;
});

const statusOrder = { complete: 0, 'in-progress': 1, 'not-started': 2 };

const sortedLineItems = computed(() => {
    return [...filteredLineItems.value].sort((a, b) => {
        const statusA = statusOrder[a.status] ?? 99;
        const statusB = statusOrder[b.status] ?? 99;
        if (statusA !== statusB) return statusA - statusB;
        return (a.label || a.kindLabel || '').localeCompare(b.label || b.kindLabel || '');
    });
});

const modalIsOpen = ref(false);
function handleModalOpen() {
    modalIsOpen.value = true;
}
function handleModalClose() {
    modalIsOpen.value = false;
    clickedItem.value = null;
}

const clickedItem: Ref<string | null> = ref(null);
async function handleLineItemClick(item: TOverhaulLineItem) {
    clickedItem.value = item.id;

    await nextTick();

    if (!item.entryMode) {
        handleInitialItemModalOpen();
    } else {
        handleModalOpen();
    }
}

const showLineItems = computed(() => insuranceLineItems.value?.length);

const titleHtml = computed(() =>
    insuranceLineItems.value?.length
        ? 'Tell us about about your <em>insurance</em>.'
        : 'What <em>types of insurance</em> do you have?',
);
async function handlePrevClick() {
    await navigateTo('/client/intake/liabilities');
}
async function handleNextClick() {
    const selectedInsurances = insuranceTypes?.filter(e => e.selected);
    if (!showLineItems.value && selectedInsurances?.length) {
        createSelectedItems();
    } else {
        await overhaulStore.markSectionAsCompleted('insurance');
        await navigateTo('/client/intake/income');
    }
}

const insuranceTypes = reactive(transformSituationTypesForCard(allInsuranceTypes));
function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = insuranceTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach(option => {
            option.value = options.includes(option.name);
        });
    }
}

function createSelectedItems() {
    if (insuranceLineItems.value?.length) {
        return;
    }

    const selectedInsurances = insuranceTypes?.filter(e => e.selected);

    // For each selected kind, add a new item of that kind
    selectedInsurances?.forEach(kindData => {
        const selectedSubOptions = kindData.subOptions?.options.filter(e => e.value);
        if (selectedSubOptions?.length) {
            selectedSubOptions.forEach(subOption => {
                overhaulStore.addItem('insurance', kindData.kind, 'Standard', subOption.name);
            });
        } else {
            overhaulStore.addItem('insurance', kindData.kind);
        }
    });
}

// Filters functionality was just copied over from OverhaulModal for now
const filters = computed(() => {
    const ii = insuranceItems.value?.reduce((acc, item) => {
        if (!acc.has(item.name)) {
            acc.set(item.name, {
                label: item.kindLabel,
                name: item.name,
                count: 1,
            });
        } else {
            const obj = acc.get(item.name);
            obj.count++;
            acc.set(obj.name, obj);
        }
        return acc;
    }, new Map<string, any>());

    return Array.from(ii.values());
});

const activeFilters = reactive(new Set<string>());

function toggleFilter(filter: string) {
    if (activeFilters.has(filter)) {
        activeFilters.delete(filter);
    } else {
        activeFilters.add(filter);
    }
}
const filteredItems = computed(() => {
    if (activeFilters.size === 0) return items.value;
    return insuranceLineItems.value.filter(item => activeFilters.has(item.name));
});
function removeAllFilters() {
    activeFilters.clear();
}

const addItemModalIsOpen = ref(false);
function handleAddItemModalOpen() {
    addItemModalIsOpen.value = true;
}
function handleAddItemModalClose() {
    addItemModalIsOpen.value = false;
}

const initialItemModalIsOpen = ref(false);
function handleInitialItemModalOpen() {
    initialItemModalIsOpen.value = true;
}
function handleInitialItemModalClose() {
    initialItemModalIsOpen.value = false;
}
async function handleInitialItemModalConfirm(newItemId: string) {
    initialItemModalIsOpen.value = false;

    await nextTick();

    clickedItem.value = newItemId;

    await nextTick();

    handleModalOpen();
}
</script>

<style lang="scss">
.overhaul-line-item-group {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 1px;
    .overhaul-line-item-group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 9px;

        h4 {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: -0.02em;

            color: #57719c;
        }

        button {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border: none;
            background: none;
            cursor: pointer;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            gap: 10px;

            color: #97abcc;
        }
    }
}

// Just copied from OverhaulModal.scss for now
.overhaul-listing-filter-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    margin-bottom: 20px;
}

.intake-layout-title + .overhaul-listing-filter-row {
    margin-top: -20px;
}

.overhaul-listing-filters {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    flex: 1 1 auto;
    gap: 4px;
}
</style>
