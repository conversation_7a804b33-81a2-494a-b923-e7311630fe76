﻿<template>
    <div class="intake-layout-grid">
        <div class="intake-layout-grid-item" v-for="selector in allSituations" :key="selector.id">
            <ElementsIconSelector
                v-bind="selector"
                v-model="selector.selected"
                />
        </div>
    </div>
</template>

<script setup lang="ts">
import { allTypes, intakeSituations } from '~/components/situations/data/intake-data';
const overhaulStore = useOverhaulStore();

const {
    data
} = storeToRefs(overhaulStore);

const helpStore = useHelpStore();

definePageMeta({
    layout: 'overhaul-layout',
    title: 'Select the options below that apply to your current life situation.'
})

const allSituations = ref(cloneObj(intakeSituations));
const selectedItems = computed(() => allSituations.value.filter(e => e.selected).map(e => e.value));

watch(selectedItems, (newVal) => {
    data.value.situations = newVal;
})

onMounted(() => {
    // If their marital status on the initial screen was married, pre-select "married"
    if(data.value.maritalStatus === 'married') {
        const marriedSituation = allSituations.value.find(e => e.value === 'married');
        if (marriedSituation) marriedSituation.selected = true;
    }

    // If they added dependents on the initial screen, pre-select "kids"
    if(data.value.dependents?.length) {
        const kidsSituation = allSituations.value.find(e => e.value === 'kids');
        if (kidsSituation) kidsSituation.selected = true;
    }

    // Select all situations that have already been added to their portfolio
    data.value.situations?.forEach((sitch: string) => {
        const situation = allSituations.value.find(e => e.value === sitch);
        if (situation) situation.selected = true;
    })

    // overhaulStore.recalculateTotals();
    helpStore.init([]);
})

</script>
