<template>
    <PartialsIntakeTitleBar
        :title="titleHtml"
        :next-disabled="!canComplete"
        :next-arrow="false"
        next-text="Finish"
        @prev-clicked="handlePrevClick"
        @next-clicked="handleNextClick"
        />

    <div class="intake-review-page">
        <div class="intake-review-kpis">
            <ElementsKpiGroup 
                label="Selections"
                :kpis="[
                    { label: 'Total', value: mappedSituationCounts.total },
                    { label: 'Standard Mode', value: mappedSituationCounts.standard },
                    { label: 'Simple Mode', value: mappedSituationCounts.simple },
                ]" 
            />
            <ElementsKpiGroup 
                label="Required Inputs"
                :kpis="[
                    { label: 'Total', value: mappedRequiredFields.total },
                    { label: 'Complete', value: mappedRequiredFields.complete },
                    { label: 'Incomplete', value: mappedRequiredFields.incomplete },
                ]" 
            />
        </div>

        <div class="intake-review-situations">
            <OverhaulIntakeReviewGroup
                v-if="assets?.length"
                svg-shape="home"
                label="Assets"
                instructions="Please review and complete the required fields for every asset you selected."
                :completion="completionObj.assets ?? 0"
                :items="assets"
                category="assets"
            />
        </div>

        <div class="intake-review-situations">
            <OverhaulIntakeReviewGroup
                v-if="liabilities?.length"
                svg-shape="bank"
                label="Liabilities"
                instructions="Please review and complete the required fields for every liability you selected."
                :completion="completionObj.liabilities ?? 0"
                :items="liabilities"
                category="liabilities"
            />
        </div>

        <div class="intake-review-situations">
            <OverhaulIntakeReviewGroup
                v-if="insurance?.length"
                svg-shape="secure-money"
                label="Insurance"
                instructions="Please review and complete the required fields for every insurance policy you provided."
                :completion="completionObj.insurance ?? 0"
                :items="insurance"
                category="insurance"
            />
        </div>

        <div class="intake-review-situations">
            <OverhaulIntakeReviewGroup
                v-if="income?.length"
                svg-shape="stocks"
                label="Income"
                instructions="Please review and complete the required fields for every source of income you provided."
                :completion="completionObj.income ?? 0"
                :items="income"
                category="income"
            />
        </div>

        <div class="intake-review-situations">
            <OverhaulIntakeReviewGroup
                v-if="expenses?.length"
                svg-shape="bill"
                label="Expenses"
                instructions="Please review and complete the required fields for every expense you selected."
                :completion="completionObj.expenses ?? 0"
                :items="expenses"
                category="expenses"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import OverhaulIntakeReviewGroup from '~/components/intake/OverhaulIntakeReviewGroup.vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import type { IntakeSituationItem } from '~/stores/intake';

const overhaulStore = useOverhaulStore();
const intakeStore = useIntakeStore();
const {
    // assets,
    // liabilities,
    // income,
    // insurance,
    // expenses,
    // progress
    // assetTypes
} = storeToRefs(intakeStore);

const helpStore = useHelpStore();
//const { topics } = storeToRefs(helpStore);

definePageMeta({
    layout: 'overhaul-layout',
    includeTitleBar: false
})

const titleHtml = computed(() =>
    'Review incomplete items'
)
async function handlePrevClick() {
    await navigateTo('/client/intake/expenses')
}
async function handleNextClick() {
    if (canComplete.value) {
        await overhaulStore.markIntakeAsCompleted();
        await navigateTo('/client/intake/complete')
    }
}

// TODO: Currently filtering out items that have been added but haven't
// had an entryMode selected yet.
const allItems = computed(() => 
    overhaulStore.allItemsArr.filter(item => item.entryMode)
)
const assets = computed(() => 
    overhaulStore.assetItemsArr.filter(item => item.entryMode)
)
const liabilities = computed(() => 
    overhaulStore.liabilityItemsArr.filter(item => item.entryMode)
)
const income = computed(() => 
    overhaulStore.incomeItemsArr.filter(item => item.entryMode)
)
const insurance = computed(() => 
    overhaulStore.insuranceItemsArr.filter(item => item.entryMode)
)
const expenses = computed(() => 
    overhaulStore.expenseItemsArr.filter(item => item.entryMode)
)

const completionObj = computed(() => 
    overhaulStore.completion
);

onMounted(() => {
    intakeStore.recalculateTotals()
    helpStore.init([]);
});

function handleSituationAddItem(situationGroup: IntakeSituationItem[], itemName: string|undefined) {
    const situation = situationGroup.find(e => e.name === itemName);
    if (situation) {
        intakeStore.addSituationItem(situation)
        intakeStore.recalculateTotals();
    }
}

function handleSituationRemoveItem(situationGroup: IntakeSituationItem[], itemName: string|undefined, itemId: number|string) {
    const situation = situationGroup.find(e => e.name === itemName);
    if (situation) {
        intakeStore.removeSituationItem(situation, itemId)
        intakeStore.recalculateTotals();
    }
}

function handleSituationSimpleChange(situationKind: IntakeSituationKind, itemName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField(situationKind, itemName, fieldName, payload)
    intakeStore.recalculateTotals();
}

function handleSituationStandardChange(situationKind: IntakeSituationKind, itemName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField(situationKind, itemName, itemId, fieldName, payload)
    intakeStore.recalculateTotals();
}

function handleSituationEntryModeChange(situationKind: IntakeSituationKind, itemName: string|null|undefined, mode: SituationEntryMode, useAsDefault?: boolean) {
    intakeStore.updateSituationEntryMode(situationKind, itemName, mode, useAsDefault)
    intakeStore.recalculateTotals();
}

function handleRemoveSituation(situationKind: IntakeSituationKind, situation: IntakeSituationItem) {
    intakeStore.removeSituation(situationKind, situation)
}

const canComplete = computed(() => 
    mappedRequiredFields.value.incomplete === 0
);

// TODO: Move this to intake.ts store
const mappedRequiredFields = computed<{
    complete: number,
    incomplete: number,
    total: number,
}>(() => {
    const requiredFields: Array<boolean> = [];

    allItems.value?.forEach((situationItem) => {
        requiredFields.push(...situationItem.fields?.filter(e => e.required).map(e => !!(e.value || e.value === 0)));
    })

    return {
        complete: requiredFields?.filter(e => e).length,
        incomplete: requiredFields?.filter(e => !e).length,
        total: requiredFields.length
    }
});

const mappedSituationCounts = computed(() => {
    // const situations = [assets.value, liabilities.value, income.value, expenses.value];
    let totalCount = 0;
    let simpleCount = 0;
    let standardCount = 0;

    allItems.value?.forEach((item) => {
        totalCount++;
        if (item.entryMode === 'Standard') {
            standardCount++;
        } else {
            simpleCount++;
        }
    })

    return {
        total: totalCount,
        simple: simpleCount,
        standard: standardCount
    }
});
</script>
