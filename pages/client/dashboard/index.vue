<template>
    <NuxtLayout name="app">
        <div class="page-container">
            <div class="page-heading">
                <h2>Overview</h2>
            </div>

            <div class="kpi-groups">
                <NuxtLoadingIndicator />
                <div class="kpi-groups-row" v-for="rowItem in chartData">
                    <PartialsMaybeList :items="rowItem">
                        <template #item="item">
                            <ElementsKpiGroup v-bind="item.kpi">
                                <PartialsMaybeList :items="item.charts">
                                    <template #empty>
                                        <div class="kpi-group-placeholder">
                                            <ImagesSvgIcon shape="lock" />
                                            <p>
                                                Trend data is locked for Simple Mode. Switch to Standard Mode for
                                                detailed trends and charts.
                                            </p>
                                        </div>
                                    </template>
                                    <template #item="chart">
                                        <DashboardLineChart v-if="chart.chartType === 'line'" v-bind="chart" />
                                        <DashboardTreeChart v-if="chart.chartType === 'tree'" v-bind="chart" />
                                    </template>
                                </PartialsMaybeList>
                            </ElementsKpiGroup>
                        </template>
                    </PartialsMaybeList>
                </div>
                <div v-for="widget in customWidgets" :key="widget.id" class="dashboard-custom-widget-row">
                    <DashboardWidget :widget="widget" />
                </div>
            </div>
        </div>
    </NuxtLayout>
</template>

<script setup lang="ts">
import type { DashboardReportCategory, DashboardReportData, DashboardReportTimeSeries } from '~/types/dashboard';
import type { KpiGroupProps } from '~/components/elements/KpiGroup.vue';
import type { TreeChartProps } from '~/components/dashboard/TreeChart.vue';
import type { LineChartProps } from '~/components/dashboard/LineChart.vue';
import { useRequestFetch } from '#app';
import DashboardWidget from '~/components/dashboard/DashboardWidget.vue';

type DashboardLineChart = { chartType: 'line' } & LineChartProps;
type DashboardTreeChart = { chartType: 'tree' } & TreeChartProps;
type DashboardChart = DashboardLineChart | DashboardTreeChart;
type DashboardRowItem = {
    kpi: KpiGroupProps;
    charts: DashboardChart | DashboardChart[];
};

// Nov 1: Putting this in here just so the onboardingComplete property is synced correctly
const overhaulStore = useOverhaulStore();
await useAsyncData(async () => await overhaulStore.fetch());

const { data /*, error, status*/ } = await useLazyFetch<DashboardReportData>('/api/reports/dashboard');

const chartData = computed((): (DashboardRowItem | DashboardRowItem[])[] | null => {
    const d = data.value;
    console.log(d);
    if (!d) return null;
    const netWorthKpi: KpiGroupProps = {
        label: 'Net Worth',
        kpis: {
            value: d.netWorth.value,
            succinct: true,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/reports/net-worth',
    };
    const assetsKpi: KpiGroupProps = {
        label: 'Assets',
        kpis: {
            label: 'Total Value',
            succinct: true,
            value: d.assets.value,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/assets',
    };
    const liabilitiesKpi: KpiGroupProps = {
        label: 'Liabilities',
        kpis: {
            label: 'Total Debt',
            succinct: true,
            value: d.liabilities.value,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/liabilities',
    };
    const incomeKpi: KpiGroupProps = {
        label: 'Income',
        kpis: [
            {
                label: 'Annually',
                succinct: true,
                value: d.income.value.annually,
                formatting: 'currency',
            },
            {
                label: 'Monthly',
                succinct: true,
                value: d.income.value.monthly,
                formatting: 'currency',
            },
        ],
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/income',
    };
    const expenseKpi: KpiGroupProps = {
        label: 'Expenses',
        kpis: [
            {
                label: 'Annually',
                succinct: true,
                value: d.expenses.value.annually,
                formatting: 'currency',
            },
            {
                label: 'Monthly',
                succinct: true,
                value: d.expenses.value.monthly,
                formatting: 'currency',
            },
        ],
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/expenses',
    };
    return [
        {
            kpi: netWorthKpi,
            charts: getTimeSeries(d.netWorth.timeSeries),
        },
        {
            kpi: netWorthKpi,
            charts: getTreeCategorized(d.netWorth.categories, ['category', 'subcategory', 'kind']),
        },
        [
            {
                kpi: assetsKpi,
                charts: getTreeCategorized(d.assets.categories),
            },
            {
                kpi: liabilitiesKpi,
                charts: getTreeCategorized(d.liabilities.categories),
            },
        ],
        [
            {
                kpi: incomeKpi,
                charts: getTreeCategorized(d.income.categories),
            },
            {
                kpi: expenseKpi,
                charts: getTreeCategorized(d.expenses.categories),
            },
        ],
        [
            {
                kpi: incomeKpi,
                charts: getTimeSeries(d.income.timeSeries),
            },
            {
                kpi: expenseKpi,
                charts: getTimeSeries(d.expenses.timeSeries),
            },
        ],
    ];
});

function getTimeSeries(data: DashboardReportTimeSeries[]): DashboardRowItem['charts'] {
    const labels = data.map(d => d.date);
    const dataset = data.map(d => d.value);
    const min = dataset.reduce((acc, v) => (v < acc ? v : acc), 0);
    const max = 1.2 * dataset.reduce((acc, v) => (v > acc ? v : acc), 0);
    return {
        chartType: 'line',
        curved: true,
        showPoints: true,
        yLabelFormat: 'currency',
        xLabelFormat: 'date',
        min: min,
        max: max,
        data: {
            labels: labels,
            datasets: [
                {
                    data: dataset,
                },
            ],
        },
    };
}

function getTreeCategorized(
    data: DashboardReportCategory[],
    groups: (keyof DashboardReportCategory)[] = ['subcategory', 'kind'],
): DashboardRowItem['charts'] {
    //const min = data.reduce((acc,v) => v.value < acc ? v.value : acc, 0);
    const max = data.reduce((acc, v) => (v.value > acc ? v.value : acc), 0);
    return {
        chartType: 'tree',
        max: max,
        data: {
            datasets: [
                {
                    data: [],
                    key: 'value',
                    groups: groups,
                    tree: data,
                },
            ],
        },
    };
}

//    ____          _                   __        ___     _            _
//   / ___|   _ ___| |_ ___  _ __ ___   \ \      / (_) __| | __ _  ___| |_ ___
//  | |  | | | / __| __/ _ \| '_ ` _ \   \ \ /\ / /| |/ _` |/ _` |/ _ \ __/ __|
//  | |__| |_| \__ \ || (_) | | | | | |   \ V  V / | | (_| | (_| |  __/ |_\__ \
//   \____\__,_|___/\__\___/|_| |_| |_|    \_/\_/  |_|\__,_|\__, |\___|\__|___/
//                                                          |___/

const customWidgets = reactive<any[]>([]);

async function populateCustomWidgets(dashboards: any[]) {
    const dashboard = dashboards.find(d => d.name === 'Overview Dashboard');
    if (dashboard) {
        if (dashboard.widgets) {
            customWidgets.push(...dashboard.widgets);
        }
    }
}

onMounted(async () => {
    const fetch = useRequestFetch();
    await fetch('/api/dashboard').then(e => {
        populateCustomWidgets(e);
    });
});
</script>

<style lang="scss">
.kpi-groups {
    display: flex;
    align-items: flex-start;
    justify-content: stretch;
    flex-direction: column;
    gap: 10px;

    .kpi-groups-row {
        display: grid;
        width: 100%;
        gap: 10px;
        @for $i from 2 through 6 {
            &:has(> :last-child:nth-child(#{$i})) {
                grid-template-columns: repeat(#{$i}, 1fr);
            }
        }
    }
}

.page-container {
    display: flex;
    align-items: stretch;
    width: 100%;
    padding-top: 40px;
    justify-content: flex-start;
    flex-direction: column;
}
</style>
