<template>
    <NuxtLayout name="app">
        <div class="page-container">
            <div class="page-heading">
                <h2>Liabilities</h2>
                <div class="page-heading-actions">
                    <FormsTextInput
                        v-model="search"
                        placeholder="Search"
                        :svg-icon="{ shape: 'search' }"
                        size="sm"
                        style="max-width: 140px"
                    />
                    <ElementsFiltersButton
                        v-if="filtersData?.length"
                        :data="filtersData"
                        @change="handleFiltersChange"
                    />
                    <ElementsButton @click.prevent="handleShowAddObjectModal" :svg-icon="{ shape: 'plus' }">
                        Add Liability
                    </ElementsButton>
                </div>
            </div>

            <div class="spark-kpi-row">
                <SparkKpi v-for="kpi in pageKpis" :label="kpi.label" :value="kpi.value" />
            </div>

            <div class="situation-crud-items">
                <div class="dashboard-tag-filters" v-if="filterPills.length">
                    <ToggleTag
                        label="All"
                        :count="allItems?.length"
                        :is-selected="allPillIsSelected"
                        @click.prevent="handleAllFilterToggle"
                    />
                    <ToggleTag
                        :label="pill.label"
                        :count="pill.count"
                        v-for="pill in filterPills"
                        :is-selected="pill.checked"
                        @click.prevent="toggleKindFilter(pill.value)"
                    />
                    <div class="dashboard-tag-filters-actions">
                        <ElementsButton
                            v-if="!isDeleteMode"
                            class="centered-icon-btn"
                            variant="muted"
                            :svg-icon="{ shape: 'dot-menu' }"
                            @click="toggleDeleteMode"
                        />
                        <ElementsButton v-if="isDeleteMode" variant="muted" @click="toggleDeleteMode">
                            Cancel
                        </ElementsButton>
                    </div>
                </div>

                <SituationsSituationCrudItem
                    v-if="filteredItems.length"
                    v-for="item in filteredItems"
                    :item="item"
                    :status="item.isComplete ? 'complete' : 'incomplete'"
                    :show-delete-checkbox="isDeleteMode"
                    :selected-items="selectedItems"
                    @selection-change="handleSelectionChange"
                />
                <div v-else-if="search.trim()" class="situation-crud-placeholder">
                    No liabilities matched your search
                    <div v-if="activeFiltersCount">You've got some active filters, try clearing them</div>
                </div>
                <div class="situation-crud-placeholder" v-else>You have not added any liabilities yet</div>

                <div v-if="selectedItems.length > 0" class="dashboard-delete-selected-button">
                    <ElementsButton variant="muted" @click="handleDeleteItems()">
                        Delete <span class="dashboard-delete-count">{{ selectedItems.length }}</span>
                        {{ selectedItems.length === 1 ? 'item' : 'items' }}
                    </ElementsButton>
                </div>
            </div>
        </div>

        <SituationsAddObjectModal
            kind="liabilities"
            entry-mode="standard"
            :is-open="addItemsModalIsOpen"
            @close="handleCloseAddObjectModal"
        />
    </NuxtLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import SparkKpi from '~/components/elements/SparkKpi.vue';
import { useIntakeStore } from '~/stores/intake';
import type { FiltersModalFilterGroup } from '~/components/modals/FiltersModal.vue';
import { allLiabilityTypes } from '~/components/situations/data/intake-data';
import type { ActiveFilter } from '~/components/elements/FiltersButton.vue';
import type { SituationCrudItemProps } from '~/components/situations/SituationCrudItem.vue';
import { storeToRefs } from 'pinia';
import ToggleTag from '~/components/elements/ToggleTag.vue';

const overhaulStore = useOverhaulStore();
await useAsyncData(async () => await overhaulStore.fetch());

const pageKpis = computed(() => {
    let liabilitiesTotal = 0;
    let assetsTotal = 0;

    Object.values(filteredItems.value).forEach(item => {
        // Liabilities are summed as well
        const liabilityAmounts = item.fields
            ?.filter(field => field.affects === 'liabilities')
            .reduce(reduceSituationFieldsToTotal, 0);
        liabilitiesTotal += liabilityAmounts;

        // Assets are summed as well
        const assetAmounts = item.fields
            ?.filter(field => field.affects === 'assets')
            .reduce(reduceSituationFieldsToTotal, 0);
        assetsTotal += assetAmounts;
    });

    return [
        {
            label: 'Liabilities',
            value: liabilitiesTotal,
            formatting: 'currency',
        },
        {
            label: 'Equity',
            value: assetsTotal - liabilitiesTotal,
            formatting: 'currency',
        },
    ];
});

const activeFilters = ref<ActiveFilter[]>([]);

const defaultFiltersData = computed<FiltersModalFilterGroup[]>(() => [
    // {
    //     title: 'Date',
    //     name: 'date',
    //     type: 'range',
    //     mode: 'year',
    //     data: {
    //         min: 2008,
    //         max: 2024,
    //         bottom: 2008,
    //         top: 2024,
    //     }
    // },
    {
        title: 'Liability Value',
        name: 'liabilityValue',
        type: 'range',
        mode: 'currency',
        data: {
            min: 0,
            max: maxLiabilityValue.value,
            bottom: 0,
            top: maxLiabilityValue.value,
        },
    },
    {
        title: 'Type',
        name: 'kind',
        description: 'The type of liability',
        type: 'checkboxes',
        data: kinds.value,
    },
]);

// Can't initiate before mount because it references nested computed values
const filtersData = ref([]);

onMounted(() => {
    filtersData.value = JSON.parse(JSON.stringify(defaultFiltersData.value));
});

// Used to set the max value for the liability value filter
const maxLiabilityValue = computed(() => {
    const allValues = allItems.value?.map(item =>
        item.fields?.filter(field => field.affects === 'liabilities')?.reduce(reduceSituationFieldsToTotal, 0),
    );
    return Math.max(...allValues);
});

const search = ref('');

const allItems = computed(() => overhaulStore.liabilityItemsArr);

const allLiabilityKinds = new Map(allLiabilityTypes.map(type => [type.kind, type.label]));

// All of the standard and simple items, run through active search and filters
const filteredItems = computed(() => {
    const searchedItems = search.value?.trim() ? allItems.value.filter(searchCallback) : allItems.value;

    return activeFilters.value?.reduce(filterReducer, searchedItems);
});

const filterPills = computed(() => {
    const countsOfEachKind: { [kind: string]: number } = allItems.value?.reduce((acc, obj) => {
        acc[obj.kind] = (acc[obj.kind] || 0) + 1;
        return acc;
    }, {});

    const kindFilters = filtersData.value
        .find(e => e.name === 'kind')
        ?.data.map(filter => {
            return {
                ...filter,
                count: countsOfEachKind[filter.value] ?? 1,
            };
        });

    if (!kindFilters) return [];

    // By default, all items are checked, but in that case, we only want to show the "All" pill checked
    // So if *any* are unchecked, return as is; otherwise return all items as unchecked
    if (kindFilters.some(e => !e.checked)) {
        return kindFilters;
    } else {
        return kindFilters.map(e => {
            return {
                ...e,
                checked: false,
            };
        });
    }
});

const allPillIsSelected = computed(() => {
    return !filtersData.value.find(e => e.name === 'kind')?.data?.some(filter => !filter.checked);
});

function toggleKindFilter(kind: string) {
    const kindFilters = filtersData.value.find(e => e.name === 'kind')?.data;
    if (!kindFilters) return [];

    const filterOfKind = kindFilters.find(filter => filter.value === kind);
    const otherFilters = kindFilters.filter(filter => filter.value !== kind);

    // If all filters are checked, uncheck everything except the one being toggled
    if (kindFilters.every(filter => filter.checked)) {
        otherFilters.forEach(filter => (filter.checked = false));
        filterOfKind.checked = true;
    } else {
        filterOfKind.checked = !filterOfKind.checked;
    }

    handleFiltersChange(filtersData.value);
}

function handleAllFilterToggle() {
    const kindFilters = filtersData.value.find(e => e.name === 'kind')?.data;
    if (!kindFilters) return [];

    kindFilters.forEach(filter => (filter.checked = true));

    handleFiltersChange(filtersData.value);
}

const activeFiltersCount = computed(() => {
    return activeFilters.value.reduce((acc, filter) => {
        if (filter.type === 'checkboxes') {
            return acc + (filter.data.some(item => item.checked) && !filter.data.every(item => item.checked) ? 1 : 0);
        }
        if (filter.type === 'range') {
            return acc + (filter.data.bottom !== filter.data.min || filter.data.top !== filter.data.max ? 1 : 0);
        }
        return acc;
    }, 0);
});

// Prep for populating the filters modal
const kinds = computed(() =>
    [...new Set(allItems.value.map(item => item.kind))].map(kind => ({
        label: allLiabilityKinds.get(kind) || kind,
        value: kind,
        checked: true,
    })),
);

function handleFiltersChange(filters) {
    const newFilters = JSON.parse(JSON.stringify(filters));
    filtersData.value = newFilters;
    activeFilters.value = newFilters.map(filter => {
        let data;
        switch (filter.type) {
            case 'range':
                data = [filter.data.top, filter.data.bottom];
                break;
            case 'checkboxes':
                data = filter.data.filter(item => item.checked).map(item => item.value);
                break;
        }
        return {
            name: filter.name,
            type: filter.type,
            data,
        };
    });
}

function filterReducer(carryItems: TPopulatedItem[], filterObj: ActiveFilter) {
    switch (filterObj?.name) {
        case 'kind':
            return filterObj.data?.length ? carryItems?.filter(item => filterObj.data.includes(item.kind)) : carryItems;
        case 'liabilityValue':
            return filterObj.data?.length
                ? carryItems?.filter(item => {
                      const liabilityValue = item.fields
                          ?.filter(field => field.affects === 'liabilities')
                          ?.reduce(reduceSituationFieldsToTotal, 0);
                      console.log('liabilityValue', liabilityValue, filterObj.data);
                      return liabilityValue >= filterObj.data[1] && liabilityValue <= filterObj.data[0];
                  })
                : carryItems;
        default:
            return carryItems;
    }
}

function searchCallback(item: { kind: string; fields: { name: string; value: string }[] }) {
    // Search the "kind" (e.g. Real Estate, Bank Account, etc)
    if (item.kind.toLowerCase().includes(search.value.toLowerCase())) {
        return true;
    }

    // Get just the searchable fields (description, address)
    const searchableFields = item.fields?.filter(field => ['description', 'address'].includes(field.name));

    // See if any of the searchable fields contain the search term
    return searchableFields.some(field => field.value.toLowerCase().includes(search.value.toLowerCase()));
}

//      _       _     _ _               ___ _
//     / \   __| | __| (_)_ __   __ _  |_ _| |_ ___ _ __ ___  ___
//    / _ \ / _` |/ _` | | '_ \ / _` |  | || __/ _ \ '_ ` _ \/ __|
//   / ___ \ (_| | (_| | | | | | (_| |  | || ||  __/ | | | | \__ \
//  /_/   \_\__,_|\__,_|_|_| |_|\__, | |___|\__\___|_| |_| |_|___/
//                              |___/

const addItemsModalIsOpen = ref(false);
function handleShowAddObjectModal() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddObjectModal() {
    addItemsModalIsOpen.value = false;
}

const selectedItems = ref<string[]>([]);

function handleSelectionChange(id: string, selected: boolean) {
    if (selected) {
        selectedItems.value.push(id);
    } else {
        selectedItems.value = selectedItems.value.filter(itemId => itemId !== id);
    }
}

function handleDeleteItems() {
    selectedItems.value.forEach(id => {
        overhaulStore.deleteItem('liabilities', id);
    });
    selectedItems.value = [];
}

const isDeleteMode = ref(false);
function toggleDeleteMode() {
    isDeleteMode.value = !isDeleteMode.value;
    if (!isDeleteMode.value) {
        // Clear selections when exiting delete mode
        selectedItems.value = [];
    }
}
</script>

<style scoped>
.page-container {
    display: flex;
    align-items: stretch;
    width: 100%;
    padding-top: 40px;
    justify-content: flex-start;
    flex-direction: column;
}

.dashboard-tag-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    margin-bottom: 16px;

    &-actions {
        display: flex;
        gap: 8px;
        margin-left: auto;
    }
}

.centered-icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 5px !important;
}
</style>
