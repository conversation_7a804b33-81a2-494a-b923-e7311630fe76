<template>
    <NuxtLayout name="app">
        <div class="page-container">
            <div class="page-heading">
                <h2>Expenses</h2>
                <div class="page-heading-actions">
                    <FormsTextInput
                        v-model="search"
                        placeholder="Search"
                        :svg-icon="{ shape: 'search' }"
                        size="sm"
                        style="max-width: 140px"
                    />
                    <ElementsFiltersButton
                        v-if="filtersData?.length"
                        :data="filtersData"
                        @change="handleFiltersChange"
                    />
                    <ElementsButton @click.prevent="handleShowAddObjectModal" :svg-icon="{ shape: 'plus' }">
                        Add Expense
                    </ElementsButton>
                </div>
            </div>

            <div class="spark-kpi-row">
                <SparkKpi v-for="kpi in pageKpis" :label="kpi.label" :value="kpi.value" />
            </div>

            <div class="expenses-object-content">
                <div class="dashboard-tag-filters" v-if="filterPills.length > 1">
                    <ToggleTag
                        label="All"
                        :count="allItems?.length"
                        :is-selected="allPillIsSelected"
                        @click.prevent="handleAllFilterToggle"
                    />
                    <ToggleTag
                        :label="pill.label"
                        :count="pill.count"
                        v-for="pill in filterPills"
                        :is-selected="pill.checked"
                        @click.prevent="toggleKindFilter(pill.value)"
                    />
                </div>
                <div class="expense-panels">
                    <ExpensePanel
                        v-for="expense in mappedExpenses"
                        v-bind="expense"
                        :key="expense.name"
                        :title="expense.kind"
                        :svg-shape="expense.iconShape"
                        :monthly="expense.kpis.monthly"
                        :annual="expense.kpis.annual"
                        :percent-of-total="expense.kpis.percentOfTotal"
                        @add-item="() => handleSituationAddItem(expense)"
                        @delete="() => handleSituationRemove(expense)"
                    >
                        <template v-for="row in expense.mappedRows" :key="row.id">
                            <ExpenseInputRow
                                :id="String(row.id)"
                                :owner-options="[
                                    { value: 'single', label: 'Single' },
                                    { value: 'joint', label: 'Joint' },
                                ]"
                                :owner="row.owner"
                                :description="row.description"
                                :description-readonly="row.descriptionReadonly"
                                :amount="row.amount"
                                :frequency="row.frequency"
                                @change="(...e) => handleRowChange(expense, row, ...e)"
                                @remove="(...e) => handleRowRemove(expense, ...e)"
                            />
                        </template>

                        <!-- <div class="mt-10">{{ expense.standard }}</div> -->
                    </ExpensePanel>
                    <!-- <div v-else class="situation-crud-placeholder" style="margin-bottom:20px;">
                        You have not added any expenses yet
                    </div> -->
                </div>
            </div>
        </div>

        <SituationsAddObjectModal
            kind="expenses"
            entry-mode="standard"
            :is-open="addItemsModalIsOpen"
            @close="handleCloseAddItemsModal"
        />
    </NuxtLayout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
// import { debounce } from 'lodash';
import SparkKpi from '~/components/elements/SparkKpi.vue';
import { useIntakeStore } from '~/stores/intake';
import type { IntakeSituationItem, IntakeStandardItem } from '~/stores/intake';
import ExpensePanel from '~/components/panels/ExpensePanel.vue';
import ExpenseInputRow from '~/components/forms/ExpenseInputRow.vue';
import { getNormalizeFrequencyMultiplier } from '~/utils';
import { allExpenseTypes, transformSituationTypesForCard } from '~/components/situations/data/intake-data';
import type { ActiveFilter } from '~/components/elements/FiltersButton.vue';
import type { SituationCrudItemProps } from '~/components/situations/SituationCrudItem.vue';
import type { FiltersModalFilterGroup } from '~/components/modals/FiltersModal.vue';

// const debounce = require('lodash/debounce')
import pkg from 'lodash';
import ToggleTag from '~/components/elements/ToggleTag.vue';
const { debounce } = pkg;

const intakeStore = useIntakeStore();
const { expenses } = storeToRefs(intakeStore);
await useAsyncData(async () => await intakeStore.fetch());

// const items = ref(intakeStore.standardExpenses);

const pageKpis = computed(() => {
    // let annualExpenses = 0;

    const annualExpenses = filteredItems.value?.reduce((acc: number, expense: IntakeSituationItem) => {
        return acc + (getAnnualExpenseFromGroup(expense) ?? 0);
    }, 0);
    // Object.values(filteredItems.value).forEach((item) => {
    //     // Liabilities are summed as well
    //     annualExpenses += item.fields?.filter((field) => field.affects === 'expenses')
    //         .reduce(reduceSituationFieldsToTotal, 0);
    // });

    return [
        {
            label: 'Annual expenses',
            value: annualExpenses,
            formatting: 'currency',
        },
        {
            label: 'Monthly expenses',
            value: annualExpenses / 12,
            formatting: 'currency',
        },
    ];
});

const expenseTypes = reactive(transformSituationTypesForCard(allExpenseTypes));

const totalAnnualExpenses = computed(() =>
    expenses.value?.reduce(
        (acc, expense) =>
            acc +
            Object.values(expense.standard).reduce((innerAcc: number, row) => {
                return getAnnualExpenseFromRow(row) + innerAcc;
            }, 0),
        0,
    ),
);

function getAnnualExpenseFromRow(row: IntakeStandardItem) {
    // console.log('getAnnualExpenseFromRow', row);
    const amountField = row.fields.find(f => f.name === 'amount');
    if (!amountField) return 0;
    const multiplier = getNormalizeFrequencyMultiplier(amountField.value.frequency ?? 'Yearly', 'Yearly');
    // console.log(amountField, amountField.value.number, multiplier)
    return (amountField.value.number ?? 0) * (multiplier ?? 1);
}

function getAnnualExpenseFromGroup(group: IntakeSituationItem): number {
    // console.log('row', group, group.standard);
    return Object.values(group.standard).reduce((acc, row) => {
        // console.log('reduce row', row);
        const rowAnnualExpense = getAnnualExpenseFromRow(row);
        // console.log('rowAnnualExpense', rowAnnualExpense);
        return rowAnnualExpense + acc;
    }, 0) as number;
}

const mappedExpenses = computed(() =>
    // expenses.value?.map((expense) => {
    filteredItems.value?.map(expense => {
        const annualExpenses = getAnnualExpenseFromGroup(expense) ?? 0;
        const percentOfTotal = Math.round((annualExpenses / totalAnnualExpenses.value) * 100);
        return {
            ...expense,
            kpis: {
                monthly: Math.floor(annualExpenses / 12) ?? 0,
                annual: annualExpenses ?? 0,
                percentOfTotal: percentOfTotal,
            },
            mappedRows: Object.values(expense.standard).map(row => {
                const amountField = row.fields.find(f => f.name === 'amount');
                return {
                    id: row.id,
                    owner: row.fields.find(f => f.name === 'owner')?.value,
                    description: row.fields.find(f => f.name === 'description')?.value,
                    descriptionReadonly: row.fields.find(f => f.name === 'description')?.readonly,
                    amount: amountField?.value.number ?? 0,
                    frequency: amountField?.value.frequency ?? 'Monthly',
                };
            }),
        };
    }),
);

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = expenseTypes.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach(option => {
            option.value = options.includes(option.name);
        });
    }
}

function handleSituationAddItem(expense: IntakeSituationItem) {
    intakeStore.addSituationItem(expense);
    intakeStore.update();
}

function handleSituationRemove(expense: IntakeSituationItem) {
    intakeStore.removeSituation('expenses', expense);
}

const handleRowChange = debounce(
    (expense: any, row: any, itemId: string, fieldName: string, newValue: string | number) => {
        let value;
        if (fieldName === 'amount') {
            value = {
                number: newValue,
                frequency: row.frequency,
                // frequency: row.find(e => e.name === 'amount').value.frequency
            };
        } else if (fieldName === 'frequency') {
            fieldName = 'amount';
            value = {
                // number: row.find(e => e.name === ' amount').value.number,
                number: row.amount,
                frequency: newValue,
            };
        } else {
            value = newValue;
        }

        intakeStore.updateFieldByItemId('expenses', itemId, fieldName, value);
    },
    400,
);
// function handleRowChange(expense: any, itemId: string, fieldName: string, newValue: string|number) {

function handleRowRemove(expense: any, itemId: string) {
    console.log('handleRowRemove', itemId);
}

const activeFilters = ref<ActiveFilter[]>([]);

const defaultFiltersData = computed<FiltersModalFilterGroup[]>(() => [
    // {
    //     title: 'Date',
    //     name: 'date',
    //     type: 'range',
    //     mode: 'year',
    //     data: {
    //         min: 2008,
    //         max: 2024,
    //         bottom: 2008,
    //         top: 2024,
    //     }
    // },
    // {
    //     title: 'Expense Amount',
    //     name: 'expenseAmount',
    //     type: 'range',
    //     mode: 'currency',
    //     data: {
    //         min: 0,
    //         max: maxExpenseValue.value,
    //         bottom: 0,
    //         top: maxExpenseValue.value,
    //     }
    // },
    {
        title: 'Type',
        name: 'kind',
        description: 'The type of expense',
        type: 'checkboxes',
        data: kinds.value,
    },
]);

// Can't initiate before mount because it references nested computed values
const filtersData = ref([]);

onMounted(() => {
    intakeStore.recalculateTotals();
    filtersData.value = JSON.parse(JSON.stringify(defaultFiltersData.value));
});

// Used to set the max value for the expense value filter
// const maxExpenseValue = computed(() => {
//     return Math.max(
//         ...Object.values(items.value)
//             .map((item) =>
//                 item.fields?.filter((field) => field.affects === 'expenses')
//                 ?.reduce(reduceSituationFieldsToTotal, 0)
//             )
//         );
// });

const search = ref('');

// All standard AND simple items mapped to a single array
const allItems = computed<SituationCrudItemProps['item'][]>(() => {
    let allItems: SituationCrudItemProps['item'][] = [];
    expenses.value.forEach(situationGroup => {
        // Expenses are currently never simple, but keeping it here for future parity
        if (situationGroup.entryMode === 'simple') {
            allItems.push({
                id: situationGroup.id,
                kind: situationGroup.kind,
                entryMode: 'simple',
                itemsKind: situationGroup.itemsKind,
                isComplete: situationGroup.cardMode === 'complete',
                fields: situationGroup.simple,
            });
        } else {
            const standardItems = Object.values(situationGroup.standard).map(item => ({
                ...item,
                kind: situationGroup.kind,
                itemsKind: situationGroup.itemsKind,
                unit: situationGroup.unit,
            }));

            allItems = allItems.concat(standardItems);
        }
    });
    return allItems;
});

// All of the standard and simple items, run through active search and filters
// const filteredItems = computed(() => {
//     const searchedItems = (search.value?.trim())
//         ? allItems.value.filter(searchCallback)
//         : allItems.value;

//     return activeFilters.value?.reduce(filterReducer, searchedItems)
// });

const filteredItems = computed(() => {
    if (!allItems.value || allItems.value.length < 1) return [];

    const kindFilter = activeFilters.value?.find(filter => {
        return filter.name === 'kind';
    });

    const filtered = kindFilter?.data?.length
        ? expenses.value.filter(expense => {
              return kindFilter?.data?.includes(expense.itemsKind);
          })
        : expenses.value;

    return search.value?.trim()
        ? filtered.filter(group => Object.values(group.standard).filter(searchCallback).length > 0)
        : filtered;
});

const allExpenseKinds = new Map(allExpenseTypes.map(type => [type.kind, type.label]));

//   _____ _ _ _              ____  _ _ _
//  |  ___(_) | |_ ___ _ __  |  _ \(_) | |___
//  | |_  | | | __/ _ \ '__| | |_) | | | / __|
//  |  _| | | | ||  __/ |    |  __/| | | \__ \
//  |_|   |_|_|\__\___|_|    |_|   |_|_|_|___/

const filterPills = computed(() => {
    const countsOfEachKind: { [kind: string]: number } = allItems.value?.reduce((acc, obj) => {
        acc[obj.itemsKind] = (acc[obj.itemsKind] || 0) + 1;
        return acc;
    }, {});

    const kindFilters = filtersData.value
        .find(e => e.name === 'kind')
        ?.data.map(filter => {
            return {
                ...filter,
                count: countsOfEachKind[filter.value] ?? 1,
            };
        });
    if (!kindFilters) return [];

    allItems.value.filter(searchCallback);

    // By default, all items are checked, but in that case, we only want to show the "All" pill checked
    // So if *any* are unchecked, return as is; otherwise return all items as unchecked
    if (kindFilters.some(e => !e.checked)) {
        return kindFilters;
    } else {
        return kindFilters.map(e => {
            return {
                ...e,
                checked: false,
            };
        });
    }
});

const allPillIsSelected = computed(() => {
    return !filtersData.value.find(e => e.name === 'kind')?.data?.some(filter => !filter.checked);
});

function toggleKindFilter(kind: string) {
    const kindFilters = filtersData.value.find(e => e.name === 'kind')?.data;
    if (!kindFilters) return [];

    const filterOfKind = kindFilters.find(filter => filter.value === kind);
    const otherFilters = kindFilters.filter(filter => filter.value !== kind);

    // If all filters are checked, uncheck everything except the one being toggled
    if (kindFilters.every(filter => filter.checked)) {
        otherFilters.forEach(filter => (filter.checked = false));
        filterOfKind.checked = true;
    } else {
        filterOfKind.checked = !filterOfKind.checked;
    }

    handleFiltersChange(filtersData.value);

    // if (filterOfKind) {
    //     filterOfKind.checked = !filterOfKind.checked;
    // }
    // const checkedCount = kindFilters.filter(item => item.checked).length;
    // if (checkedCount === 0 || checkedCount === kindFilters.length) {
    //     kindFilters.forEach((filter) => filter.checked = false);
    // }
}

function handleAllFilterToggle() {
    const kindFilters = filtersData.value.find(e => e.name === 'kind')?.data;
    if (!kindFilters) return [];

    kindFilters.forEach(filter => (filter.checked = true));

    handleFiltersChange(filtersData.value);
}

// Prep for populating the filters modal
const kinds = computed(() =>
    [...new Set(allItems.value.map(item => item.itemsKind))].map(kind => ({
        label: allExpenseKinds.get(kind) || kind,
        value: kind,
        checked: true,
    })),
);
const owners = computed(() =>
    [...new Set(allItems.value.map(item => item.owner))].map(owner => ({
        label: owner,
        value: owner,
        checked: true,
    })),
);

function handleFiltersChange(filters) {
    // console.log('filters', JSON.stringify(filters), JSON.stringify(filtersData.value));
    const newFilters = JSON.parse(JSON.stringify(filters));
    filtersData.value = newFilters;
    activeFilters.value = newFilters.map(filter => {
        let data;
        switch (filter.type) {
            case 'range':
                data = [filter.data.top, filter.data.bottom];
                break;
            case 'checkboxes':
                data = filter.data.filter(item => item.checked).map(item => item.value);
                break;
        }
        return {
            name: filter.name,
            type: filter.type,
            data,
        };
    });
}

// Call back in
function expenseGroupFilterReducer(carryItems: IntakeStandardItem[], filterObj: ActiveFilter) {
    switch (filterObj?.name) {
        case 'kind':
            return filterObj.data?.length
                ? carryItems?.filter(item => filterObj.data.includes(item.itemsKind))
                : carryItems;
        default:
            return carryItems;
    }
}

function expenseItemFilterReducer(carryItems: IntakeStandardItem[], filterObj: ActiveFilter) {
    // console.log('filterReducer triggered', filterObj);
    // console.log('filterObj.data', filterObj);
    switch (filterObj?.name) {
        case 'kind':
            return filterObj.data?.length
                ? carryItems?.filter(item => filterObj.data.includes(item.itemsKind))
                : carryItems;
        default:
            return carryItems;
    }
}

function filterReducer(carryItems: IntakeStandardItem[], filterObj: ActiveFilter) {
    // console.log('filterReducer triggered', filterObj);
    switch (filterObj?.name) {
        case 'kind':
            return filterObj.data?.length
                ? carryItems?.filter(item => filterObj.data.includes(item.itemsKind))
                : carryItems;
        case 'assetValue':
            return filterObj.data?.length
                ? carryItems?.filter(item => {
                      const assetValue = item.fields
                          ?.filter(field => field.affects === 'assets')
                          ?.reduce(reduceSituationFieldsToTotal, 0);
                      // console.log('assetValue', assetValue, filterObj.data);
                      return assetValue >= filterObj.data[1] && assetValue <= filterObj.data[0];
                  })
                : carryItems;
        default:
            return carryItems;
    }
}

function searchCallback(item) {
    item.fields?.filter(field => ['description'].includes(field.name));
    return item.fields.some(
        field => typeof field.value === 'string' && field.value.toLowerCase().includes(search.value.toLowerCase()),
    );
}

//      _       _     _   ___ _                       __  __           _       _
//     / \   __| | __| | |_ _| |_ ___ _ __ ___  ___  |  \/  | ___   __| | __ _| |
//    / _ \ / _` |/ _` |  | || __/ _ \ '_ ` _ \/ __| | |\/| |/ _ \ / _` |/ _` | |
//   / ___ \ (_| | (_| |  | || ||  __/ | | | | \__ \ | |  | | (_) | (_| | (_| | |
//  /_/   \_\__,_|\__,_| |___|\__\___|_| |_| |_|___/ |_|  |_|\___/ \__,_|\__,_|_|
//
const addItemsModalIsOpen = ref(false);
function handleShowAddObjectModal() {
    addItemsModalIsOpen.value = true;
}
function handleCloseAddItemsModal() {
    addItemsModalIsOpen.value = false;
}
</script>
