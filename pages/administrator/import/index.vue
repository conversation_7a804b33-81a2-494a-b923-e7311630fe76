﻿<template>
  <NuxtLayout name="app">
    <div class="page-container">
      <div class="page-heading">
        <h2>Import files</h2>
      </div>

      <TransitionSlideDown :is-visible="!!savedFiles">
        <FormMessage type="info" :message="`${savedFiles} files saved.`" />
      </TransitionSlideDown>

      <TransitionSlideDown :is-visible="!!error">
        <FormMessage type="error" :message="error.message" />
      </TransitionSlideDown>

      <div v-if="status === 'pending'">
        Saving...
      </div>
      <div v-else>
        <div class="email-input">
          <InputGroupBlock title="Email address">
            <TextInput v-model="email" type="email" placeholder="Email address"/>
          </InputGroupBlock>
        </div>
        <div v-if="filesLoading">
          Adding files
        </div>
        <template v-else>
          <div class="file-input">
            <input type="file" ref="fileInput" multiple @input="addFiles"
                   accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                   style="display:none" />
            <Button @click.prevent="chooseFiles()">Choose Files</Button>  
          </div>
          <div class="file-list">
            <InputGroupBlock :title="file.name" v-for="(file, index) in files" :key="index">
              <TextInput label="Date" type="date"
                         :svg-icon="{shape: 'calendar'}"
                         :modelValue="new Date(file.lastModified).toISOString().split('T')[0]"
                         @update:modelValue="(newValue: string) => { file.lastModified = newValue; }"
              />
              <button class="delete-item" @click.prevent="removeFile(file)">
                <SvgIcon shape="trash" width="20" height="20" />
              </button>
            </InputGroupBlock>
          </div>
          <Button :disabled="!canImport" @click="handleSubmit()">Import</Button>
        </template>
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import {ref} from 'vue';
import Button from '~/components/elements/Button.vue';
import TextInput from "~/components/forms/TextInput.vue";
import InputGroupBlock from "~/components/forms/InputGroupBlock.vue";
import type {TypedInternalResponse} from "nitropack";
import TransitionSlideDown from "~/components/animation/TransitionSlideDown.vue";
import FormMessage from "~/components/forms/FormMessage.vue";
import SvgIcon from "~/components/images/SvgIcon.vue";

const {handleFileInput, files} = useFileStorage()

const email = ref('');
const data = ref<TypedInternalResponse<'/api/import'>>();
const status = ref<'success'|'idle'|'pending'|'error'>('idle');
const error = ref();
const filesLoading = ref(false);
const fileInput = ref<HTMLInputElement>();

const canImport = computed(() => {
  return email.value && files.value?.length > 0;
})

function chooseFiles() {
  fileInput.value?.click()
}

async function addFiles(payload: Event) {
  filesLoading.value = true;
  await handleFileInput(payload);
  filesLoading.value = false;
}
function removeFile(file: typeof files.value[number]) {
  const i = files.value.indexOf(file);
  files.value.splice(i, 1);
}

async function handleSubmit() {
  if (!email.value) return;
  status.value = 'pending';
  await $fetch('/api/import', {
    method: 'POST',
    body: {
      email: email.value,
      files: files.value
    }
  }).catch((err: Error) => {
    status.value = 'error';
    error.value = err;
  }).then(value => {
    if (value) {
      status.value = 'success';
      data.value = value;
    }
  });
}

const savedFiles = computed(() => {
  data.value
  return data.value?.snapshots.length ?? 0;
});

</script>

<style scoped>
.email-input,
.file-list,
.file-input {
  margin-bottom: 1em;
  max-width: 1200px;
}
.file-list {
  display: flex;
  flex-direction: column;
  gap:4px;
}
.delete-item {
  justify-self: flex-end;
  opacity: .5;
  transition: opacity .15s;
  &:hover {
    opacity: .8;
  }
}
</style>