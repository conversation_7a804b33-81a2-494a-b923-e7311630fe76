<template>
    <NuxtLayout name="app">
        <div class="page-container">
            <div class="page-heading">
                <h2>Dashboards / Overview</h2>

                <div class="page-heading-actions">
                    <Button variant="muted" to="/client/dashboard">Cancel</Button>
                    <Button @click.prevent="handleSaveDashboard">Save</Button>
                </div>
            </div>

            <div class="kpi-groups">
                <NuxtLoadingIndicator />
                <div class="kpi-groups-row" v-for="rowItem in chartData">
                    <PartialsMaybeList :items="rowItem">
                        <template #item="item">
                            <ElementsKpiGroup v-bind="item.kpi">
                                <PartialsMaybeList :items="item.charts">
                                    <template #empty>
                                        <div class="kpi-group-placeholder">
                                            <ImagesSvgIcon shape="lock" />
                                            <p>
                                                Trend data is locked for Simple Mode. Switch to Standard Mode for
                                                detailed trends and charts.
                                            </p>
                                        </div>
                                    </template>
                                    <template #item="chart">
                                        <DashboardLineChart v-if="chart.chartType === 'line'" v-bind="chart" />
                                        <DashboardTreeChart v-if="chart.chartType === 'tree'" v-bind="chart" />
                                    </template>
                                </PartialsMaybeList>
                            </ElementsKpiGroup>
                        </template>
                    </PartialsMaybeList>
                </div>
                <div v-for="widget in customWidgets">
                    {{ widget }}
                </div>
                <div class="kpi-groups-add-button" @click.prevent="handleShowAddWidgetModal">
                    <SvgIcon shape="plus" width="20" height="20" />
                    <span>Add Widget</span>
                </div>
            </div>
        </div>

        <AddWidgetModal :is-open="addWidgetModalIsOpen" @close="handleCloseAddWidgetModal" @confirm="handleAddWidget" />
    </NuxtLayout>
</template>

<script setup lang="ts">
import type { DashboardCategory, DashboardData, DashboardReportData, DashboardTimeSeries } from '~/types/dashboard';
import type { KpiGroupProps } from '~/components/elements/KpiGroup.vue';
import type { TreeChartProps } from '~/components/dashboard/TreeChart.vue';
import type { LineChartProps } from '~/components/dashboard/LineChart.vue';

import dummyData from './data.json';
import Button from '~/components/elements/Button.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import AddWidgetModal, { type AddWidgetModalReturn } from '~/components/dashboard/AddWidgetModal.vue';
import { useRequestFetch } from '#app';
import type { IntakeData } from '~/types/intake';

type DashboardLineChart = { chartType: 'line' } & LineChartProps;
type DashboardTreeChart = { chartType: 'tree' } & TreeChartProps;
type DashboardChart = DashboardLineChart | DashboardTreeChart;
type DashboardRowItem = {
    kpi: KpiGroupProps;
    charts: DashboardChart | DashboardChart[];
};

const { data /*, error, status*/ } = await useLazyFetch<DashboardReportData>('/api/reports/dashboard');

const dataViewer = computed(() => {
    console.log('data', data.value);
    return data.value;
});

const router = useRouter();

const addWidgetModalIsOpen = ref(false);

function handleShowAddWidgetModal() {
    addWidgetModalIsOpen.value = true;
}
function handleCloseAddWidgetModal() {
    addWidgetModalIsOpen.value = false;
}

const chartData = computed((): (DashboardRowItem | DashboardRowItem[])[] | null => {
    const d = dummyData;
    if (!d) return null;
    const netWorthKpi: KpiGroupProps = {
        label: 'Net Worth',
        kpis: {
            value: d.netWorth.value,
            succinct: true,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/reports/net-worth',
    };
    const assetsKpi: KpiGroupProps = {
        label: 'Assets',
        kpis: {
            label: 'Total Value',
            succinct: true,
            value: d.assets.value,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/assets',
    };
    const liabilitiesKpi: KpiGroupProps = {
        label: 'Liabilities',
        kpis: {
            label: 'Total Debt',
            succinct: true,
            value: d.liabilities.value,
            formatting: 'currency',
        },
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/liabilities',
    };
    const incomeKpi: KpiGroupProps = {
        label: 'Income',
        kpis: [
            {
                label: 'Annually',
                succinct: true,
                value: d.income.value.annually,
                formatting: 'currency',
            },
            {
                label: 'Monthly',
                succinct: true,
                value: d.income.value.monthly,
                formatting: 'currency',
            },
        ],
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/income',
    };
    const expenseKpi: KpiGroupProps = {
        label: 'Expenses',
        kpis: [
            {
                label: 'Annually',
                succinct: true,
                value: d.expenses.value.annually,
                formatting: 'currency',
            },
            {
                label: 'Monthly',
                succinct: true,
                value: d.expenses.value.monthly,
                formatting: 'currency',
            },
        ],
        disclaimer: d.disclaimer,
        clickDest: '/client/dashboard/expenses',
    };
    return [
        {
            kpi: netWorthKpi,
            charts: getTimeSeries(d.netWorth.timeSeries),
        },
        {
            kpi: netWorthKpi,
            charts: getTreeCategorized(d.netWorth.categories, ['category', 'subcategory', 'kind']),
        },
        [
            {
                kpi: assetsKpi,
                charts: getTreeCategorized(d.assets.categories),
            },
            {
                kpi: liabilitiesKpi,
                charts: getTreeCategorized(d.liabilities.categories),
            },
        ],
        [
            {
                kpi: incomeKpi,
                charts: getTreeCategorized(d.income.categories),
            },
            {
                kpi: expenseKpi,
                charts: getTreeCategorized(d.expenses.categories),
            },
        ],
        [
            {
                kpi: incomeKpi,
                charts: getTimeSeries(d.income.timeSeries),
            },
            {
                kpi: expenseKpi,
                charts: getTimeSeries(d.expenses.timeSeries),
            },
        ],
    ];
});

function getTimeSeries(data: DashboardTimeSeries[]): DashboardRowItem['charts'] {
    const labels = data.map(d => d.date);
    const dataset = data.map(d => d.value);
    const min = dataset.reduce((acc, v) => (v < acc ? v : acc), 0);
    const max = 1.2 * dataset.reduce((acc, v) => (v > acc ? v : acc), 0);
    return {
        chartType: 'line',
        curved: true,
        showPoints: true,
        yLabelFormat: 'currency',
        xLabelFormat: 'date',
        min: min,
        max: max,
        data: {
            labels: labels,
            datasets: [
                {
                    data: dataset,
                },
            ],
        },
    };
}

function getTreeCategorized(
    data: DashboardCategory[],
    groups: (keyof DashboardCategory)[] = ['subcategory', 'kind'],
): DashboardRowItem['charts'] {
    //const min = data.reduce((acc,v) => v.value < acc ? v.value : acc, 0);
    const max = data.reduce((acc, v) => (v.value > acc ? v.value : acc), 0);
    return {
        chartType: 'tree',
        max: max,
        data: {
            datasets: [
                {
                    data: [],
                    key: 'value',
                    groups: groups,
                    tree: data,
                },
            ],
        },
    };
}

//    ____          _                    ____            _     _                         _
//   / ___|   _ ___| |_ ___  _ __ ___   |  _ \  __ _ ___| |__ | |__   ___   __ _ _ __ __| |___
//  | |  | | | / __| __/ _ \| '_ ` _ \  | | | |/ _` / __| '_ \| '_ \ / _ \ / _` | '__/ _` / __|
//  | |__| |_| \__ \ || (_) | | | | | | | |_| | (_| \__ \ | | | |_) | (_) | (_| | | | (_| \__ \
//   \____\__,_|___/\__\___/|_| |_| |_| |____/ \__,_|___/_| |_|_.__/ \___/ \__,_|_|  \__,_|___/

const customWidgets = reactive<any[]>([]);

async function handleAddWidget(data: AddWidgetModalReturn) {
    customWidgets.push(data);
    handleCloseAddWidgetModal();
}

async function handleSaveDashboard() {
    const OVERVIEW_DASHBOARD_ID = 'a3b5c7d9e1f2g4h6i8j0k1l2m';
    const body = {
        name: 'Overview Dashboard',
        id: OVERVIEW_DASHBOARD_ID,
        widgets: customWidgets,
    };
    console.log('body', body);
    await fetch<IntakeData>('/api/dashboard', {
        method: 'POST',
        body,
    })
        .then((...e) => {
            console.log('thn', e);
        })
        .finally(() => {
            // router.push('/client/dashboard');
        });
}

onMounted(async () => {
    // const fetch = useRequestFetch();
    // const data = await fetch('/api/dashboard/681e32a3da5ea435694ff486')
    // await fetch<IntakeData>('/api/dashboard', {
    //     method: 'POST',
    //     body: {
    //         ...data,
    //         name: "Caleb Rox",
    //         widgets: [{
    //             foo: 'bar'
    //         }]
    //     },
    // }).then((...e) => {
    //     console.log('thn', e);
    // })
    // console.log('got em', data);
});
</script>
