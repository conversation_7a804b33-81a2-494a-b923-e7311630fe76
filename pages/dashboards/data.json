{"disclaimer": "As of May 7, 2025", "netWorth": {"value": 2157182, "timeSeries": [{"date": "2025-05-04T02:51:40.211Z", "value": 2157182}, {"date": "2025-01-12T20:14:19.916Z", "value": 2125640}, {"date": "2024-11-10T16:41:57.009Z", "value": 1101570}, {"date": "2024-10-15T15:12:06.540Z", "value": 1203790}, {"date": "2024-10-05T22:41:52.353Z", "value": 1258800}, {"date": "2024-09-10T18:06:33.731Z", "value": 1230000}, {"date": "2024-08-10T15:05:32.706Z", "value": 1240000}], "categories": [{"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Vehicle", "value": 12564}, {"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Equipment", "value": 230891}, {"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Real Estate", "value": 12038083}, {"category": "Liability", "subcategory": "Student Loans", "kind": "Student Loan", "value": 123123}, {"category": "Liability", "subcategory": "Other", "kind": "Mortgage", "value": 1233}]}, "assets": {"value": 3613554, "timeSeries": [{"date": "2025-04-02T02:51:40.211Z", "value": 12281538}, {"date": "2025-04-01T22:20:45.168Z", "value": 12281538}, {"date": "2025-03-27T21:07:09.679Z", "value": 12281538}, {"date": "2025-03-26T20:20:06.192Z", "value": 12281538}, {"date": "2025-03-25T23:06:39.424Z", "value": 2320962955}, {"date": "2025-03-24T16:30:43.086Z", "value": 212564}, {"date": "2025-03-19T17:15:43.839Z", "value": 24920}, {"date": "2025-03-18T19:11:13.145Z", "value": 12379}, {"date": "2025-03-17T20:14:19.916Z", "value": 12379}, {"date": "2025-03-14T16:41:57.009Z", "value": 12379}, {"date": "2025-03-13T15:12:06.540Z", "value": 12379}, {"date": "2025-03-12T22:41:52.353Z", "value": 120300}, {"date": "2025-03-11T18:06:33.731Z", "value": 120000}, {"date": "2025-03-10T15:05:32.706Z", "value": 120000}, {"date": "2025-03-07T22:09:18.280Z", "value": 0}, {"date": "2025-03-06T23:46:17.389Z", "value": 0}, {"date": "2025-03-05T23:46:48.558Z", "value": 123333}, {"date": "2025-03-04T22:12:14.429Z", "value": 0}], "categories": [{"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Vehicle", "value": 12564}, {"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Equipment", "value": 230891}, {"category": "<PERSON><PERSON>", "subcategory": "Personal", "kind": "Real Estate", "value": 12038083}]}, "liabilities": {"value": 1456372, "timeSeries": [{"date": "2025-04-02T02:51:40.211Z", "value": 124356}, {"date": "2025-04-01T22:20:45.168Z", "value": 124356}, {"date": "2025-03-27T21:07:09.679Z", "value": 124356}, {"date": "2025-03-26T20:20:06.192Z", "value": 124356}, {"date": "2025-03-25T23:06:39.424Z", "value": 124356}, {"date": "2025-03-24T16:30:43.086Z", "value": 0}, {"date": "2025-03-19T17:15:43.839Z", "value": 12456}, {"date": "2025-03-18T19:11:13.145Z", "value": 1222}, {"date": "2025-03-17T20:14:19.916Z", "value": 1222}, {"date": "2025-03-14T16:41:57.009Z", "value": 1222}, {"date": "2025-03-13T15:12:06.540Z", "value": 0}, {"date": "2025-03-12T22:41:52.353Z", "value": 0}, {"date": "2025-03-11T18:06:33.731Z", "value": 0}, {"date": "2025-03-10T15:05:32.706Z", "value": 0}, {"date": "2025-03-07T22:09:18.280Z", "value": 0}, {"date": "2025-03-06T23:46:17.389Z", "value": 0}, {"date": "2025-03-05T23:46:48.558Z", "value": 0}, {"date": "2025-03-04T22:12:14.429Z", "value": 0}], "categories": [{"category": "Liability", "subcategory": "Student Loans", "kind": "Student Loan", "value": 123123}, {"category": "Liability", "subcategory": "Other", "kind": "Mortgage", "value": 1233}]}, "income": {"value": {"annually": 257580, "monthly": 21465}, "timeSeries": [{"date": "2025-05-01T00:00:00.000Z", "value": 21465}, {"date": "2025-04-15T00:00:00.000Z", "value": 21465}, {"date": "2025-04-01T00:00:00.000Z", "value": 21465}, {"date": "2025-03-15T00:00:00.000Z", "value": 21465}, {"date": "2025-03-01T00:00:00.000Z", "value": 21465}, {"date": "2025-02-15T00:00:00.000Z", "value": 21465}, {"date": "2025-02-01T00:00:00.000Z", "value": 21465}, {"date": "2025-01-15T00:00:00.000Z", "value": 21465}, {"date": "2025-01-01T00:00:00.000Z", "value": 21465}, {"date": "2024-12-15T00:00:00.000Z", "value": 31465}, {"date": "2024-12-01T00:00:00.000Z", "value": 21465}, {"date": "2024-11-15T00:00:00.000Z", "value": 21465}], "categories": [{"category": "Income", "subcategory": "Earned", "kind": "Employee"}, {"category": "Income", "subcategory": "Earned", "kind": "Employee"}]}, "expenses": {"value": {"annually": 44471, "monthly": 3705.9166666666665}, "timeSeries": [{"date": "2025-04-02T02:51:40.211Z", "value": 44471}, {"date": "2025-04-01T22:20:45.168Z", "value": 43471}, {"date": "2025-03-25T23:06:39.424Z", "value": 57372}, {"date": "2025-03-24T16:30:43.086Z", "value": 53276}, {"date": "2025-03-19T17:15:43.839Z", "value": 34224}, {"date": "2025-03-18T19:11:13.145Z", "value": 60240}, {"date": "2025-03-17T20:14:19.916Z", "value": 57108}, {"date": "2025-03-14T16:41:57.009Z", "value": 55908}, {"date": "2025-03-06T23:46:17.389Z", "value": 50400}, {"date": "2025-03-05T23:46:48.558Z", "value": 48000}, {"date": "2025-03-04T22:12:14.429Z", "value": 48000}], "categories": [{"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 12000}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 10000}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 13910}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 3939}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 3122}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 500}, {"category": "Expense", "subcategory": "Taxes", "kind": "Taxes", "value": 1000}]}}