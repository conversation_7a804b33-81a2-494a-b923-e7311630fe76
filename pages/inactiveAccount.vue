﻿<script setup lang="ts">
const { loggedIn, user, refresh, login, logout, currentProvider, clear } = useOidcAuth();
</script>

<template>
    <NuxtLayout name="minimal">
        <div class="page-container">
            <div class="user-container">
                <section class="user-layout">
                    <section class="pt-10 flex flex-col">
                        <div class="user-layout-title">
                            <h3>Inactive Account</h3>
                        </div>
                        <span>
                            Your account has been successfully deleted, and you no longer have access to our services or
                            any data associated with it. If you believe this was done in error or wish to continue using
                            our services, please contact support for assistance or create a new account.
                        </span>
                        <span class="emphasis">
                            Note: Attempting to log in with deleted account credentials will not restore access.
                        </span>
                    </section>
                </section>
                <section class="pt-10 flex items-center">
                    <div class="user-layout-title-actions">
                        <ElementsButton
                            class="intake-layout-action"
                            v-if="loggedIn"
                            :svg-icon="{ shape: 'close' }"
                            @click="logout(currentProvider)"
                        >
                            Logout
                        </ElementsButton>
                    </div>
                </section>
            </div>
        </div>
    </NuxtLayout>
</template>

<style scoped>
.page-container {
    display: flex;
    align-items: center;
    width: 100dvw;
    height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 40px;
    justify-content: flex-start;
    flex-direction: column;
}

.user-container {
    display: flex;
    align-items: stretch;
    width: 80%;
    max-width: 800px;
    justify-content: flex-start;
    flex-direction: column;
}
.user-layout {
    color: var(--color-white);
    width: 100%;

    &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        h3 {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1.25em;
            letter-spacing: -0.5px;
        }
    }
    .emphasis {
        font-style: normal;
        margin: 1rem 0;
        color: var(--color-grey);
    }
}
</style>
