<template>
    <div class="intake-layout-list">
        <!-- Name Block -->
        <InputGroupBlock title="Name" description="Your legal name.">
            <TextInput label="First" v-model="formData.name.firstName" />
            <TextInput label="Middle" v-model="formData.name.middleName" />
            <TextInput label="Last" v-model="formData.name.lastName" />
        </InputGroupBlock>

        <!-- Contact Information -->
        <InputGroupBlock title="Contact Information">
            <TextInput label="Email" v-model="formData.email" />
            <TextInput
                label="Phone Number"
                v-model="formData.phone"
                @input="handlePhoneInput"
            />
        </InputGroupBlock>

        <!-- Birthdate Block -->
        <InputGroupBlock title="Birthdate">
            <TextInput
                label="Birthdate"
                type="date"
                :svg-icon="{ shape: 'calendar' }"
                v-model="formData.birthdate"
                :min="minBirthdate"
                :max="maxBirthdate"
            />
        </InputGroupBlock>

        <!-- Marital Status Block -->
        <InputGroupBlock title="Marital status" description="Your current marital status.">
            <CheckboxGroup
                variant="buttons"
                mode="radio"
                v-model:options="maritalStatusOptions"
                v-model:selected="formData.maritalStatus"
            />
        </InputGroupBlock>

        <!-- Spouse Information (Conditional) -->
        <template v-if="formData.maritalStatus === 'married' && formData.spouse">
            <InputGroupBlock title="Spouse's Name" description="Your spouse's legal name.">
                <TextInput label="First" v-model="formData.spouse.firstName" />
                <TextInput label="Middle" v-model="formData.spouse.middleName" />
                <TextInput label="Last" v-model="formData.spouse.lastName" />
            </InputGroupBlock>

            <!-- Spouse Contact Information -->
            <InputGroupBlock title="Spouse Contact Information">
                <TextInput label="Email" v-model="formData.spouse.email" />
                <TextInput
                    label="Phone Number"
                    v-model="formData.spouse.phone"
                    @input="handleSpousePhoneInput"
                />
            </InputGroupBlock>

            <InputGroupBlock title="Spouse's Birthdate">
                <TextInput
                    label="Birthdate"
                    type="date"
                    :svg-icon="{ shape: 'calendar' }"
                    v-model="formData.spouse.birthdate"
                    :min="minBirthdate"
                    :max="maxBirthdate"
                />
            </InputGroupBlock>

            <!-- Dependents Block -->
            <InputGroupBlock
                title="Dependents"
                description="Please provide your dependents' names and birthdays."
                :multiRow="true"
            >
                <template v-if="formData.dependents.length > 0">
                    <div v-for="(dependent, index) in formData.dependents" :key="index" class="dependent-block">
                        <div class="dependent-row">
                            <TextInput
                                class="dependent-input"
                                placeholder="Name"
                                v-model="dependent.name"
                            />
                            <TextInput
                                class="dependent-input"
                                placeholder="Relationship"
                                v-model="dependent.relationship"
                            />
                            <TextInput
                                class="dependent-input"
                                placeholder="Birthday"
                                type="date"
                                :svg-icon="{ shape: 'calendar' }"
                                v-model="dependent.dob"
                                :min="minBirthdate"
                                :max="maxBirthdate"
                            />
                            <button class="remove-button" @click="removeDependent(index)">
                                <SvgIcon shape="trash" />
                            </button>
                        </div>
                    </div>
                </template>

                <!-- No Dependents Message -->
                <template v-else>
                    <div class="no-dependents">
                        <div class="no-dependents-container">
                            <p class="no-dependents-question">Would you like to add dependents now?</p>
                            <p class="no-dependents-description">
                                Dependents are stored for the purposes of tax deductions and will not have an account or
                                be required to log in.
                            </p>
                            <div class="no-dependents-actions">
                                <Button variant="primary" @click="addDependent">YES, I WANT TO ADD THEM NOW</Button>
                            </div>
                        </div>
                    </div>
                </template>
            </InputGroupBlock>

            <!-- Add Dependents Controls -->
            <div class="add-dependents" v-if="formData.dependents.length > 0">
                <span>{{ formData.dependents.length }} dependents</span>
                <div class="add-dependent-button">
                    <Button variant="muted" @click="addDependent">+ Dependent</Button>
                    <Button variant="primary" @click="saveDependent">SAVE</Button>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import InputGroupBlock from '~/components/forms/InputGroupBlock.vue';
import TextInput from '~/components/forms/TextInput.vue';
import CheckboxGroup from '~/components/forms/CheckboxGroup.vue';
import Button from '~/components/elements/Button.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';

definePageMeta({
    layout: 'mock-intake-layout',
    title: 'Personal information',
});

// Reactive form data initialized with mock values
const formData = reactive({
    name: {
        firstName: 'John',
        middleName: 'Michael',
        lastName: 'Smith',
    },
    email: '<EMAIL>',
    phone: '(*************',
    birthdate: '1985-03-15',
    maritalStatus: 'married',
    spouse: {
        firstName: 'Sarah',
        middleName: 'Elizabeth',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '(*************',
        birthdate: '1987-07-22',
    },
    dependents: [
        {
            name: 'Emma Smith',
            relationship: 'Daughter',
            dob: '2015-09-10',
        },
        {
            name: 'Lucas Smith',
            relationship: 'Son',
            dob: '2018-12-03',
        },
    ],
});

const maritalStatusOptions = ref([
    { label: 'Single', name: 'single' },
    { label: 'Married', name: 'married' },
    { label: 'Widow', name: 'widow' },
    { label: 'Divorced', name: 'divorced' },
    { label: 'Separated', name: 'separated' },
]);

// Date constraints
const minBirthdate = ref('1900-01-01');
const maxBirthdate = ref(new Date().toISOString().split('T')[0]);

// Phone number formatting function
function formatPhoneNumber(value: string): string {
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
}

// Phone input handlers
function handlePhoneInput(event: Event) {
    const target = event.target as HTMLInputElement;
    formData.phone = formatPhoneNumber(target.value);
}

function handleSpousePhoneInput(event: Event) {
    const target = event.target as HTMLInputElement;
    formData.spouse.phone = formatPhoneNumber(target.value);
}

// Dependent management functions
function addDependent() {
    formData.dependents.push({
        name: '',
        relationship: '',
        dob: '',
    });
}

function removeDependent(index: number) {
    formData.dependents.splice(index, 1);
}

function saveDependent() {
    // Mock save functionality - just log to console for demo
    console.log('Dependents saved:', formData.dependents);
    alert('Dependents saved successfully! (This is a demo - no actual saving occurs)');
}

// Watch for marital status changes to manage spouse data
watch(() => formData.maritalStatus, (newStatus) => {
    if (newStatus === 'married') {
        // Ensure spouse object exists when married
        if (!formData.spouse) {
            formData.spouse = {
                firstName: '',
                middleName: '',
                lastName: '',
                email: '',
                phone: '',
                birthdate: '',
            };
        }
        // Ensure dependents array exists
        if (!formData.dependents) {
            formData.dependents = [];
        }
    }
});
</script>

<style scoped>
.dependent-block {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.dependent-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.dependent-input {
    flex: 1;
    margin-right: 10px;
}

.remove-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #97abcc;
}

.remove-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.add-dependents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 20px;
    position: relative;
    bottom: 5px;
}

.add-dependent-button {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.no-dependents {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    &-container {
        width: 485px;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-actions {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-question {
        color: #97abcc;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
    }

    &-description {
        color: #57719c;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 20px;
        word-wrap: break-word;
    }
}
</style>
