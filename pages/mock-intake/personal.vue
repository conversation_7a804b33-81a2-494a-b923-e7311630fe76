<template>
    <div class="intake-layout-list">
        <!-- Name Block -->
        <InputGroupBlock title="Name" description="Your legal name.">
            <TextInput label="First" :model-value="mockData.name.firstName" readonly />
            <TextInput label="Middle" :model-value="mockData.name.middleName" readonly />
            <TextInput label="Last" :model-value="mockData.name.lastName" readonly />
        </InputGroupBlock>

        <!-- Contact Information -->
        <InputGroupBlock title="Contact Information">
            <TextInput label="Email" :model-value="mockData.email" readonly />
            <TextInput label="Phone Number" :model-value="mockData.phone" readonly />
        </InputGroupBlock>

        <!-- Birthdate Block -->
        <InputGroupBlock title="Birthdate">
            <TextInput
                label="Birthdate"
                type="date"
                :svg-icon="{ shape: 'calendar' }"
                :model-value="mockData.birthdate"
                readonly
            />
        </InputGroupBlock>

        <!-- Marital Status Block -->
        <InputGroupBlock title="Marital status" description="Your current marital status.">
            <CheckboxGroup
                variant="buttons"
                mode="radio"
                v-model:options="maritalStatusOptions"
                v-model:selected="mockData.maritalStatus"
            />
        </InputGroupBlock>

        <!-- Spouse Information (Conditional) -->
        <template v-if="mockData.maritalStatus === 'married' && mockData.spouse">
            <InputGroupBlock title="Spouse's Name" description="Your spouse's legal name.">
                <TextInput label="First" :model-value="mockData.spouse.firstName" readonly />
                <TextInput label="Middle" :model-value="mockData.spouse.middleName" readonly />
                <TextInput label="Last" :model-value="mockData.spouse.lastName" readonly />
            </InputGroupBlock>

            <!-- Spouse Contact Information -->
            <InputGroupBlock title="Spouse Contact Information">
                <TextInput label="Email" :model-value="mockData.spouse.email" readonly />
                <TextInput label="Phone Number" :model-value="mockData.spouse.phone" readonly />
            </InputGroupBlock>

            <InputGroupBlock title="Spouse's Birthdate">
                <TextInput
                    label="Birthdate"
                    type="date"
                    :svg-icon="{ shape: 'calendar' }"
                    :model-value="mockData.spouse.birthdate"
                    readonly
                />
            </InputGroupBlock>

            <!-- Dependents Block -->
            <InputGroupBlock
                title="Dependents"
                description="Please provide your dependents' names and birthdays."
                :multiRow="true"
            >
                <template v-if="mockData.dependents.length > 0">
                    <div v-for="(dependent, index) in mockData.dependents" :key="index" class="dependent-block">
                        <div class="dependent-row">
                            <TextInput 
                                class="dependent-input" 
                                placeholder="Name" 
                                :model-value="dependent.name" 
                                readonly 
                            />
                            <TextInput
                                class="dependent-input"
                                placeholder="Relationship"
                                :model-value="dependent.relationship"
                                readonly
                            />
                            <TextInput
                                class="dependent-input"
                                placeholder="Birthday"
                                type="date"
                                :svg-icon="{ shape: 'calendar' }"
                                :model-value="dependent.dob"
                                readonly
                            />
                            <button class="remove-button" disabled>
                                <SvgIcon shape="trash" />
                            </button>
                        </div>
                    </div>
                </template>

                <!-- No Dependents Message -->
                <template v-else>
                    <div class="no-dependents">
                        <div class="no-dependents-container">
                            <p class="no-dependents-question">Would you like to add dependents now?</p>
                            <p class="no-dependents-description">
                                Dependents are stored for the purposes of tax deductions and will not have an account or
                                be required to log in.
                            </p>
                            <div class="no-dependents-actions">
                                <Button variant="primary" disabled>YES, I WANT TO ADD THEM NOW</Button>
                            </div>
                        </div>
                    </div>
                </template>
            </InputGroupBlock>

            <!-- Add Dependents Controls -->
            <div class="add-dependents" v-if="mockData.dependents.length > 0">
                <span>{{ mockData.dependents.length }} dependents</span>
                <div class="add-dependent-button">
                    <Button variant="muted" disabled>+ Dependent</Button>
                    <Button variant="primary" disabled>SAVE</Button>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import InputGroupBlock from '~/components/forms/InputGroupBlock.vue';
import TextInput from '~/components/forms/TextInput.vue';
import CheckboxGroup from '~/components/forms/CheckboxGroup.vue';
import Button from '~/components/elements/Button.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';

definePageMeta({
    layout: 'mock-intake-layout',
    title: 'Personal information',
});

// Mock data with realistic placeholder values
const mockData = ref({
    name: {
        firstName: 'John',
        middleName: 'Michael',
        lastName: 'Smith',
    },
    email: '<EMAIL>',
    phone: '(*************',
    birthdate: '1985-03-15',
    maritalStatus: 'married',
    spouse: {
        firstName: 'Sarah',
        middleName: 'Elizabeth',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '(*************',
        birthdate: '1987-07-22',
    },
    dependents: [
        {
            name: 'Emma Smith',
            relationship: 'Daughter',
            dob: '2015-09-10',
        },
        {
            name: 'Lucas Smith',
            relationship: 'Son',
            dob: '2018-12-03',
        },
    ],
});

const maritalStatusOptions = ref([
    { label: 'Single', name: 'single' },
    { label: 'Married', name: 'married' },
    { label: 'Widow', name: 'widow' },
    { label: 'Divorced', name: 'divorced' },
    { label: 'Separated', name: 'separated' },
]);
</script>

<style scoped>
.dependent-block {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
}

.dependent-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.dependent-input {
    flex: 1;
    margin-right: 10px;
}

.remove-button {
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #97abcc;
}

.remove-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.add-dependents {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 20px;
    position: relative;
    bottom: 5px;
}

.add-dependent-button {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.no-dependents {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    &-container {
        width: 485px;
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-actions {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-question {
        color: #97abcc;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
    }

    &-description {
        color: #57719c;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 20px;
        word-wrap: break-word;
    }
}
</style>
