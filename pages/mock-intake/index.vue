<template>
    <div class="mock-intake-index">
        <div class="mock-intake-welcome">
            <h1>Mock Intake Demo</h1>
            <p>This is a demonstration version of the client intake process with mock data and no backend dependencies.</p>
            
            <div class="mock-intake-links">
                <h2>Available Pages:</h2>
                <ul>
                    <li>
                        <NuxtLink to="/mock-intake/personal" class="mock-link">
                            📝 Personal Information
                        </NuxtLink>
                        <p>Interactive personal information intake form with editable fields and local state management</p>
                    </li>
                </ul>
            </div>
            
            <div class="mock-intake-info">
                <h3>About This Demo</h3>
                <ul>
                    <li>✅ Preserves exact UI layout and styling from the original</li>
                    <li>✅ Uses realistic mock data as default values</li>
                    <li>✅ All form fields are interactive and editable</li>
                    <li>✅ Local state management for real-time form interaction</li>
                    <li>✅ Phone number formatting and validation</li>
                    <li>✅ Dynamic dependent management (add/remove)</li>
                    <li>✅ Marital status conditional logic</li>
                    <li>✅ No backend dependencies required</li>
                    <li>✅ Perfect for interactive demos and user testing</li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
definePageMeta({
    layout: 'mock-intake-layout',
    title: 'Mock Intake Demo',
});
</script>

<style scoped>
.mock-intake-index {
    padding: 40px;
    color: var(--color-white);
    max-width: 800px;
}

.mock-intake-welcome h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--color-white);
}

.mock-intake-welcome p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: var(--color-grey);
    line-height: 1.6;
}

.mock-intake-links {
    margin-bottom: 40px;
}

.mock-intake-links h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--color-white);
}

.mock-intake-links ul {
    list-style: none;
    padding: 0;
}

.mock-intake-links li {
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(var(--color-white-rgb), 0.05);
    border-radius: 8px;
}

.mock-link {
    display: inline-block;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-primary);
    text-decoration: none;
    margin-bottom: 8px;
}

.mock-link:hover {
    text-decoration: underline;
}

.mock-intake-links li p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--color-blue-grey);
}

.mock-intake-info {
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 30px;
    border-radius: 8px;
}

.mock-intake-info h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--color-white);
}

.mock-intake-info ul {
    list-style: none;
    padding: 0;
}

.mock-intake-info li {
    margin-bottom: 10px;
    font-size: 0.95rem;
    color: var(--color-grey);
    line-height: 1.5;
}
</style>
