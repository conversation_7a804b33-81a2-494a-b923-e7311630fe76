﻿<script setup lang="ts">
const {loggedIn, user, refresh, login, logout, currentProvider, clear} = useOidcAuth()
</script>

<template>
  <NuxtLayout name="minimal">
    <div class="page-container">
    <div class="user-container">
      <section class="user-heading">
        <h2>{{ user?.userInfo?.name ?? 'Unknown' }}</h2>
        <p>Logged in: {{ loggedIn }}</p>
        <p>Current provider: {{ currentProvider }}</p>
      </section>
      <section class="user-layout">
        <section class="user-layout-content pt-10">
          <div class="user-layout-title" style="margin-bottom: 1rem">
            <h3>User</h3>
            <div class="user-layout-title-actions">
              <ElementsButton class="intake-layout-action"
                              v-if="!loggedIn"
                              :svg-icon="{shape: 'lock'}"
                              @click="login()">
                Login
              </ElementsButton>
              <ElementsButton class="intake-layout-action"
                              v-if="loggedIn"
                              :svg-icon="{shape: 'close'}"
                              @click="logout(currentProvider)">
                Logout
              </ElementsButton>
              <ElementsButton class="intake-layout-action"
                              v-if="loggedIn && user?.canRefresh"
                              :svg-icon="{shape: 'arrow-circle-right'}"
                              @click="refresh()">
                Refresh
              </ElementsButton>
              <ElementsButton class="intake-layout-action"
                              v-if="loggedIn"
                              :svg-icon="{shape: 'hide'}"
                              @click="clear()">
                Clear Session
              </ElementsButton>
            </div>
          </div>
          <div class="user-layout-list">
            <FormsInputGroupBlock v-for="(value, key, index) in user" :key="index" :title="key">{{ value }}</FormsInputGroupBlock>
          </div>
        </section>

        <section class="user-layout-content pt-10">
          <div class="user-layout-title" style="margin-bottom: 1rem">
            <h3>User Info</h3>
          </div>
          <div class="user-layout-list">
            <FormsInputGroupBlock v-for="(value, key, index) in user?.userInfo" :key="index" :title="key">{{ value }}</FormsInputGroupBlock>
          </div>
        </section>

        <section class="user-layout-content pt-10">
          <div class="user-layout-title" style="margin-bottom: 1rem">
            <h3>Claims</h3>
          </div>
          <div class="user-layout-list">
            <FormsInputGroupBlock v-for="(value, key, index) in user?.claims" :key="index" :title="key">{{ value }}</FormsInputGroupBlock>
          </div>
        </section>
      </section>
    </div>
    </div>
  </NuxtLayout>
</template>

<style scoped>
.page-container {
  display: flex;
  align-items: center;
  width: 100dvw;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  padding-top: 40px;
  justify-content: flex-start;
  flex-direction: column;
}

.user-container {
  display: flex;
  align-items: stretch;
  padding-top: 40px;
  width: 80%;
  justify-content: flex-start;
  flex-direction: column;
}
.user-heading {
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  h2 {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.66666667em;
    letter-spacing: -0.5px;
  }
}
.user-layout {
  color: var(--color-white);
  width: 100%;
  padding-top: 40px;
  .user-layout-content {
    width: 100%;
    .user-layout-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40px;
      h3 {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1.25em;
        letter-spacing: -0.5px;
        em {
          font-style: normal;
          color: var(--color-grey);

        }
      }
      .user-layout-title-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
      }
    }
  }
  .user-layout-list {
    display: flex;
    align-items: stretch;
    flex-direction: column;
    gap:4px;
  }

}
</style>