<template>
    <div class="icons-grid">
        <div v-for="icon in svgIconShapes" :key="icon[0]">
            <ImagesSvgIcon
                :shape="icon[0]"
            />
            <span class="name">{{ icon[0] }}</span>
        </div>
    </div>
</template>

<script setup>
import { svgIconShapes } from '@/components/images/SvgIconShapes.ts';
</script>

<style scoped lang="scss">
.icons-grid {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 1rem;
    color: #fff;
    > div {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 4px;
    }

    .name {
        font-size:12px;
        font-weight: bold;
        text-align: center;
    }
}
</style>
