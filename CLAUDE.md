# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Install dependencies
npm install

# Start development server (http://localhost:3000)
npm run dev

# Build for production
npm run build

# Build for test environment
npm run build:test

# Run tests
npm test                    # Automated tests
npm run test:unit          # Unit tests with Vitest
npm run test:ui            # Unit tests with Vitest UI
npm run test-storybook     # Storybook tests

# Code quality
npm run prettier           # Format code
# Note: ESLint and lint-staged are configured via husky pre-commit hooks

# Storybook
npm run storybook          # Start Storybook dev server
npm run build-storybook    # Build Storybook

# Database
npm run mongo              # Start MongoDB via Docker
npm run mongo-dev          # Start MongoDB and dev server concurrently

# Component generation
npm run new                # Generate new component with Plop
npm run open               # Open component selector with Plop

# Other utilities
npm run lighthouse         # Run Lighthouse performance tests
npm run start              # Start with Azure Static Web Apps CLI
npm run startNode          # Start with Node.js directly
```

## Architecture Overview

### Tech Stack

- **Framework**: Nuxt 3 (Vue.js 3 with TypeScript)
- **Styling**: Tailwind CSS + SCSS
- **State Management**: Pinia
- **Database**: MongoDB (with Mongoose schemas)
- **Authentication**: OIDC with Auth0
- **Testing**: Vitest (unit), Jest (automated), Storybook (component)
- **Documentation**: Storybook + Nuxt Content
- **File Processing**: Azure Document Intelligence for OCR
- **Storage**: Azure Blob Storage
- **Email**: Azure Communication Services

### Project Structure

#### Core Directories

- `components/` - Vue components organized by category (forms, modals, navigation, etc.)
- `pages/` - File-based routing with role-based access (client/, advisor/, administrator/)
- `server/` - Nitro server-side code including API routes and services
- `types/` - TypeScript type definitions for the domain model
- `stores/` - Pinia state management
- `composables/` - Vue composables for reusable logic

#### Key Architecture Patterns

**Component Organization**:

- Components are categorized by function (forms, modals, elements, etc.)
- Each component has corresponding `.stories.ts` files for Storybook
- SCSS files are co-located with components
- Unit tests follow the same directory structure in `tests/unit-tests/`

**Server Architecture**:

- API routes in `server/api/` organized by feature
- Database models in `server/models/` using Mongoose schemas
- Services in `server/services/` for external integrations (Azure Blob, Document Intelligence)
- Use cases in `server/use-cases/` for business logic
- DTOs in `server/dto/` for data transfer objects

**Type System**:

- Domain types organized by category (assets, liabilities, income, expenses, insurance)
- Base types provide common interfaces (BaseLineItem, Frequency enums)
- Union types for flexible line item handling

**Authentication & Authorization**:

- OIDC authentication with Auth0 integration
- Role-based middleware (requireClient, requireAdvisor, requireAdministrator)
- Route-level protection via `nuxt.config.ts` routeRules

### Key Business Domain

This is a financial planning application with three main user roles:

- **Clients**: Manage personal financial data and view reports
- **Advisors**: View and manage multiple client accounts
- **Administrators**: System-wide management and user administration

Core financial entities include assets, liabilities, income, expenses, and insurance products. The application supports both "Standard" and "Simple" modes for data entry complexity.

### Development Workflow

1. **Component Development**: Use Storybook for component development and testing
2. **Code Generation**: Use Plop templates (`npm run new`) for consistent component creation
3. **Testing**: Write unit tests for components and server-side logic
4. **Code Quality**: Pre-commit hooks run ESLint and Prettier automatically
5. **Database**: Local MongoDB via Docker for development

### Environment Configuration

Runtime configuration is handled through `nuxt.config.ts` with environment-specific settings for:

- Database connections (MongoDB)
- Authentication (Auth0)
- Azure services (Storage, Document Intelligence, Email)
- Application Insights telemetry

### OCR Integration

The application includes paystub OCR functionality using Azure Document Intelligence, with processing logic in `server/use-cases/paystub/` and API endpoints in `server/api/paystub/`.
