import type { StorybookConfig } from '@storybook/vue3-vite';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
import { resolve } from 'path';

import vue from '@vitejs/plugin-vue';

const config: StorybookConfig = {
    stories: [
        // "../components/**/*.mdx",
        '../components/**/*.stories.@(js|jsx|mjs|ts|tsx)',
    ],
    addons: [
        '@storybook/addon-a11y',
        '@storybook/addon-designs',
        '@storybook/addon-essentials',
        '@storybook/addon-interactions',
        '@storybook/addon-links',
        '@chromatic-com/storybook',
    ],
    framework: {
        name: '@storybook/vue3-vite',
        options: {
            docgen: 'vue-component-meta',
        },
    },
    docs: {
        autodocs: false,
    },
    async viteFinal(config) {
        // Merge custom configuration into the default config
        const { mergeConfig } = await import('vite');

        return mergeConfig(config, {
            plugins: [vue()],
            resolve: {
                alias: {
                    '~': resolve(__dirname, '../'),
                    '@': resolve(__dirname, '../'),
                    '#app': resolve(__dirname, '../.nuxt/app'),
                },
            },
            css: {
                postcss: {
                    plugins: [tailwindcss, autoprefixer],
                },
                preprocessorOptions: {
                    scss: {
                        quietDeps: true,
                    },
                },
            },
        });
    },
};

export default config;
