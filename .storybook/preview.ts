import { setup } from '@storybook/vue3';
import { action } from '@storybook/addon-actions';
import { createPinia } from 'pinia';

setup(app => {
    // Mock NuxtLink for Storybook
    app.component('NuxtLink', {
        props: {
            to: {
                type: String,
                required: true,
            },
        },
        methods: {
            log() {
                action('link target')(this.to);
            },
        },
        template: '<a @click="log"><slot /></a>',
    });
    app.use(createPinia());
});

// import '../assets/css/tailwind.css';
import '../assets/css/app.scss';
