﻿# Use root/example as user/password credentials
services:
  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: example

  mongo-express:
    image: mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: root
      ME_CONFIG_MONGODB_ADMINPASSWORD: example
      ME_CONFIG_MONGODB_URL: **********************************/
      ME_CONFIG_BASICAUTH: false


#name: finpro-cosmos
#services:
#  azure-cosmos-emulator:
#    container_name: cosmosdb
#    stdin_open: true
#    tty: true
#    ports:
#      - 8081:8081
#      - 10250-10255:10250-10255
#    environment:
#      - AZURE_COSMOS_EMULATOR_PARTITION_COUNT=2
#      #- AZURE_COSMOS_EMULATOR_ENABLE_DATA_PERSISTENCE=true
#    image: mcr.microsoft.com/cosmosdb/linux/azure-cosmos-emulator:latest
#    