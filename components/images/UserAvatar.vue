<template v-if="user">
    <div class="user-avatar" :class="{ 'user-avatar-active': active }" :style="avatarSize">
        <span v-if="showInitials">{{ getInitials(userData) }}</span>
        <img
            v-else-if="userData?.profileImage"
            :src="userData.profileImage"
            class="user-avatar-image"
            referrerpolicy="no-referrer"
            crossorigin="anonymous"
            :style="avatarSize"
            :alt="getInitials(userData)"
        />
    </div>
</template>

<script setup lang="ts">
import { getInitials } from '../../utils';
import type { UserSession } from '#nuxt-oidc/types';

const { user: currentUser } = useOidcAuth();

const userData = computed(() => {
    const user = props.user ?? currentUser.value?.userData;
    if (!user) {
        return;
    }
    return 'userData' in user ? user.userData : user;
});

const showInitials = computed(() => {
    if (props.mode === 'initials') return true;
    return !userData.value?.profileImage;
});

const avatarSize = computed(() => {
    return {
        '--user-avatar-size': `${props.size}px`,
        fontSize: `${props.size * 0.47}px`,
    };
});

export interface UserAvatarProps {
    active?: boolean;
    size?: number;
    user?: UserSession | { firstName: string; lastName: string; profileImage?: string };
    mode?: 'photo' | 'initials';
}

const props = withDefaults(defineProps<UserAvatarProps>(), {
    size: 40,
    mode: 'photo',
});
</script>

<style lang="scss">
.user-avatar {
    width: var(--user-avatar-size);
    height: var(--user-avatar-size);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 14px;
    cursor: pointer;
    user-select: none;

    transition:
        color 0.2s,
        background 0.2s,
        width 0.2s,
        height 0.2s,
        font-size 0.2s;
    color: #ffffff;
    background: var(--color-blue-grey) center/cover no-repeat;

    &-image {
        border-radius: 50%;
    }

    &:hover {
        &:not(.user-avatar-active) {
            background: var(--color-grey);
        }
    }

    &.user-avatar-active {
        background: var(--color-blue);
    }
}
</style>
