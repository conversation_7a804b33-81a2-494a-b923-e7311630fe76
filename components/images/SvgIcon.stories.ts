import SvgIcon from './SvgIcon.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { svgIconShapeKeys } from './SvgIconShapes';
// import { action } from '@storybook/addon-actions';

const meta: Meta<typeof SvgIcon> = {
    component: SvgIcon,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        shape: {
            control: { type: 'select' },
            options: svgIconShapeKeys, // imported from ./SvgIconShapes.ts
            description: 'The name / slug of the icon to render'
        },
        color: {
            description: 'A hex/rgb color value; Defaults to `currentColor` **NOTE**: prefer assigning colors via parent `color` CSS.'
        },
        width: {},
        height: {}
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="display:flex;align-items:center;justify-content:center;margin: 3em; line-height:0; color:#fff"><div style="flex:none;border:1px dashed rgba(255,255,255,.2)"><story/></div></div>' })]
};

export default meta;
type Story = StoryObj<typeof SvgIcon>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=871-19309&mode=dev',
        },
    },
    args: {
        shape: 'account'
    }
};

export const CustomSize: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=871-19309&mode=dev',
        },
    },
    args: {
        shape: 'budget',
        width: 48,
        height: 48
    }
};

export const CustomColor: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=871-19309&mode=dev',
        },
    },
    args: {
        shape: 'financial-profile',
        color: '#8a53ff'
    }
};

export const AllIcons: Story = {
    render: () => ({
        components: { SvgIcon },
        template: `
            <div style="display: flex; flex-wrap: wrap; gap: 1rem; color: #fff">
                <div v-for="shape in svgIconShapeKeys" :key="shape" style="flex: none; border: 1px dashed rgba(255,255,255,.2); padding: 1rem; text-align: center">
                    <SvgIcon :shape="shape" width="48" height="48" />
                    <div style="margin-top: 0.5rem; font-size: 0.75rem">{{ shape }}</div>
                </div>
            </div>
        `,
        data() {
            return {
                svgIconShapeKeys
            };
        }
    })
};
