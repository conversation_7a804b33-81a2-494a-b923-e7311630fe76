/**
 ** *****WHEN ADDING A NEW ICON*****
 * Replace all id tags and corresponding references (eg url(#a) to SVG_ID_A, SVG_ID_B, etc
 * Replace any non-mask colors with currentColor (fill layers are typically #ffffff)
 */

export const svgIconShapes = new Map([
    [
        'account',
        `<path fill-rule="evenodd" clip-rule="evenodd" d="M12 3.19998L3.79999 6.9L3.8 9.19999H5.49999V18.4H3.8V20.3H20.2V18.4H18.5V9.24999H20.2V6.9L12 3.19998ZM11.5 16.4546C10.7707 16.4057 10.104 16.1899 9.5 15.8073L9.8229 14.6482C10.3795 14.9859 10.9385 15.186 11.5 15.2487V12.8402C11.2544 12.758 11.0802 12.694 10.9775 12.6482C10.0153 12.2745 9.53425 11.5773 9.53425 10.5566C9.53425 9.89173 9.73646 9.36703 10.1409 8.98248C10.4904 8.65016 10.9434 8.44264 11.5 8.35993V7.5H11.7701H12.2495H12.5V8.33356C13.1789 8.38893 13.7787 8.58008 14.2994 8.90701L13.8982 9.97439C13.4058 9.7302 12.9397 9.58084 12.5 9.52632V11.7132L12.5382 11.7264C12.6034 11.748 12.6474 11.7624 12.6703 11.7695C13.3258 12.0067 13.7939 12.3014 14.0744 12.6536C14.3581 13.0058 14.5 13.4964 14.5 14.1253C14.5 14.8046 14.2994 15.3437 13.8982 15.7426C13.5463 16.0925 13.0803 16.3152 12.5 16.4107V17.5H12.2495H11.7701H11.5V16.4546ZM11.5 11.3506V9.59083C11.3518 9.64266 11.2315 9.7184 11.1389 9.81806C10.9954 9.9726 10.9172 10.1972 10.9041 10.4919C10.9041 10.9304 11.1014 11.2161 11.4961 11.3491L11.5 11.3506ZM12.5 13.186V15.1696C12.9364 15.0103 13.1546 14.6586 13.1546 14.1146C13.1546 13.6872 12.9364 13.3776 12.5 13.186Z" fill="currentColor"/>`,
    ],
    [
        'asterisk',
        `<mask id="SVG_ID_ASTERISK" width="32" height="32" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="m20.79 9.23-2-3.46L14 8.54V3h-4v5.54L5.21 5.77l-2 3.46L8 12l-4.79 2.77 2 3.46L10 15.46V21h4v-5.54l4.79 2.77 2-3.46L16 12z"/>
        </mask>
        <g mask="url(#SVG_ID_ASTERISK)">
            <rect width="32" height="32" fill="#fff"/>
        </g>`,
    ],
    [
        'arrow-back',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="m12 5-7 7 7 7 1.228-1.228-4.871-4.912H19v-1.72H8.357l4.871-4.912L12 5Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'arrow-circle-right',
        `<mask id="SVG_ID_A" width="18" height="17" x="3" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#055EFA" d="M12.001 4.284a8.215 8.215 0 1 1 0 16.43 8.215 8.215 0 1 1 0-16.43Zm0 15.537c4.022 0 7.322-3.3 7.322-7.322s-3.3-7.322-7.322-7.322S4.68 8.477 4.68 12.5s3.3 7.322 7.322 7.322Z"/>
                <path fill="#055EFA" d="M8.117 12.5c0-.267.216-.482.482-.482h7.287a.481.481 0 0 1 0 .962H8.599a.481.481 0 0 1-.482-.48Z"/>
                <path fill="#055EFA" d="M11.988 7.5a.42.42 0 0 1 .301.127l4.218 4.361a.736.736 0 0 1 0 1.024l-4.218 4.36a.418.418 0 1 1-.601-.581l4.15-4.291-4.15-4.291a.418.418 0 0 1 .3-.709Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
            </g>`,
    ],
    [
        'arrow-forward',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="m12 5 7 7-7 7-1.228-1.228 4.871-4.912H5v-1.72h10.643l-4.871-4.912L12 5Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'assets',
        `<mask id="SVG_ID_A" width="20" height="20" x="2" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="M17.5 2.5h-11l3 5h5l3-5Zm4.5 5-3-4-2.5 4H22Zm-20 0 3-4 2.5 4H2Zm0 2 9 12-3-12H2Zm20 0-9 12 3-12h6Zm-8 0-2 8-2-8h4Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'back-arrow',
        `<mask id="SVG_ID_B" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20z"/>
        </mask>
        <g mask="url(#SVG_ID_B)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'budget',
        `<mask id="SVG_ID_A" width="20" height="18" x="2" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" fill-rule="evenodd" d="M3 3a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h18a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H3Zm17 2H4v8h16V5Z" clip-rule="evenodd"/>
                <path fill="currentColor" d="M2 16h20v2H2v-2Zm0 3h20v2H2v-2ZM15 9a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM8 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm10 0a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'camera',
        `<circle cx="12" cy="12" r="3.2" fill="currentColor"/>
        <path d="M9 2 7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5" fill="currentColor"/>`,
    ],
    [
        'calculator',
        `<mask id="SVG_ID_A" width="16" height="20" x="4" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" fill-rule="evenodd" d="M20 3a1 1 0 0 0-1-1H5a1 1 0 0 0-1 1v18a1 1 0 0 0 1 1h14a1 1 0 0 0 1-1V3Zm-2 1H6v4h12V4ZM7 10h2v2H7v-2Zm6 0h-2v2h2v-2Zm2 0h2v2h-2v-2Zm-6 4H7v2h2v-2Zm2 0h2v2h-2v-2Zm6 0h-2v2h2v-2ZM7 18h2v2H7v-2Zm10 0h-6v2h6v-2Z" clip-rule="evenodd"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'calendar',
        `<mask id="SVG_ID_A" width="16" height="18" x="4" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="M17.82 5.32c.47 0 .86.17 1.172.508.339.313.508.703.508 1.172v11.68c0 .442-.17.833-.508 1.172-.312.312-.703.468-1.172.468H6.18c-.47 0-.873-.156-1.211-.468A1.676 1.676 0 0 1 4.5 18.68V7c0-.469.156-.86.469-1.172a1.653 1.653 0 0 1 1.21-.508H7V3.68h1.68v1.64h6.64V3.68H17v1.64h.82Zm0 13.36v-8.36H6.18v8.36h11.64Zm-8.32-5V12H7.82v1.68H9.5Zm3.32 0V12h-1.64v1.68h1.64Zm3.36 0V12H14.5v1.68h1.68ZM9.5 17v-1.68H7.82V17H9.5Zm3.32 0v-1.68h-1.64V17h1.64Zm3.36 0v-1.68H14.5V17h1.68Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
            </g>`,
    ],
    [
        'chat',
        `<mask id="SVG_ID_A" width="16" height="16" x="4" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="M17.284 6.69a7.5 7.5 0 0 0-12.21 8.25.793.793 0 0 1 .068.48l-.66 3.172a.75.75 0 0 0 .454.847.75.75 0 0 0 .296.053h.15l3.21-.645a.946.946 0 0 1 .48.067 7.5 7.5 0 0 0 8.25-12.21l-.038-.015Zm-8.302 6.052a.75.75 0 1 1 0-********* 0 0 1 0 1.5Zm3 0a.75.75 0 1 1 0-1.501.75.75 0 0 1 0 1.5Zm3 0a.75.75 0 1 1 0-1.501.75.75 0 0 1 0 1.5Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'checkbox',
        `<g clip-path="url(#SVG_ID_A)">
            <path d="M19 3H5C3.89 3 3 3.9 3 5V19C3 20.1 3.89 21 5 21H19C20.11 21 21 20.1 21 19V5C21 3.9 20.11 3 19 3ZM10 17L5 12L6.41 10.59L10 14.17L17.59 6.58L19 8L10 17Z" fill="currentColor"/>
        </g>
        <defs>
            <clipPath id="SVG_ID_A">
                <rect width="24" height="24" fill="white"/>
            </clipPath>
        </defs>`,
    ],
    [
        'checkmark',
        `<mask id="SVG_ID_A" width="19" height="14" x="3" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="m8.836 16.046-4.354-4.354L3 13.164 8.836 19 21.364 6.472 19.892 5 8.836 16.046Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'close',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'dashboard',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="4" width="16" height="16">
            <path d="M12.875 4H20V9.33333H12.875V4ZM12.875 20V11.125H20V20H12.875ZM4 20V14.6667H11.125V20H4ZM4 12.875V4H11.125V12.875H4Z" fill="white"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'debt',
        `<mask id="SVG_ID_A" width="21" height="17" x="2" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="M12.005 9.832v1.983a10.489 10.489 0 0 1-.33-.1c-.477-.138-.715-.433-.715-.886.016-.305.11-.537.283-.697.174-.16.428-.26.762-.3Zm1.671 4.74c0 .66-.364 1.047-1.092 1.158v-2.216c.008.003.035.013.082.027.052.011.083.02.095.028.61.193.915.527.915 1.003Z"/>
                <path fill="currentColor" fill-rule="evenodd" d="M5.964 6h12.1a2 2 0 0 1 1.962 1.615l1.963 10A2 2 0 0 1 20.027 20H4a2 2 0 0 1-1.963-2.385L4 7.615A2 2 0 0 1 5.964 6Zm6.03 11 .011 1.07h.579v-1.092c.842-.07 1.506-.312 1.99-.724s.726-.97.726-1.671c0-.65-.171-1.157-.514-1.52-.338-.365-.903-.67-1.694-.914l-.16-.045-.206-.061a3.11 3.11 0 0 1-.142-.045V9.81c.618.023 1.281.184 1.99.485l.484-1.103c-.705-.379-1.53-.581-2.474-.607v-.847h-.579v.858c-.823.052-1.478.277-1.966.674s-.732.94-.732 1.627c0 1.054.58 1.774 1.742 ************.484.16.956.29v2.418c-.787-.022-1.57-.236-2.35-.64l-.39 1.197c.815.442 1.725.668 2.728.68Z" clip-rule="evenodd"/>
                <path fill="currentColor" d="M13 6a1 1 0 1 0-2 0H9a3 3 0 1 1 6 0h-2Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'dollar',
        `<mask id="SVG_ID_A" width="8" height="15" x="8" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="m11.632 19.52-.016-1.502c-1.33-.016-2.536-.334-3.616-.955l.517-1.683c1.033.568 2.071.868 3.115.9v-3.397a16.47 16.47 0 0 1-1.268-.407c-1.54-.543-2.31-1.556-2.31-3.038 0-.965.324-1.727.971-2.285.648-.559 1.516-.874 2.607-.948V5h.767v1.19c1.253.036 2.346.32 3.28.853l-.642 1.55c-.94-.423-1.819-.65-2.638-.681v3.076c.***************.188.063l.274.086.211.063c1.05.344 1.798.772 2.247 1.283.454.512.681 1.224.681 2.137 0 .987-.32 1.77-.963 2.349-.642.579-1.521.918-2.638 1.017v1.534h-.767Zm0-8.79V7.943c-.444.058-.78.199-1.01.423-.23.224-.355.55-.375.978 0 .637.315 1.052.947 1.245.073.026.219.073.438.14Zm.767 5.503c.966-.157 1.448-.7 1.448-1.628 0-.668-.404-1.138-1.213-1.41a.5.5 0 0 0-.125-.038.997.997 0 0 1-.11-.04v3.116Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'dollar-sign',
        `<mask id="SVG_ID_DOLLAR" width="20" height="20" x="2" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4"></path>
        </mask>
        <g mask="url(#SVG_ID_DOLLAR)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'dot-menu',
        `<mask id="SVG_ID_A" width="12" height="4" x="6" y="10" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="M10.947 10.456c.305-.304.655-.456 1.053-.456.398 0 .748.152 1.053.456.304.304.456.655.456 1.053 0 .397-.152.748-.456 1.052-.305.305-.655.457-1.053.457-.398 0-.748-.152-1.053-.457-.304-.304-.456-.655-.456-1.052 0-.398.152-.749.456-1.053Zm4.492 0c.304-.304.655-.456 1.052-.456.398 0 .749.152 1.053.456.304.304.456.655.456 1.053 0 .397-.152.748-.456 1.052-.304.305-.655.457-1.053.457-.397 0-.748-.152-1.052-.457-.305-.304-.457-.655-.457-1.052 0-.398.152-.749.457-1.053Zm-8.983 0C6.76 10.152 7.111 10 7.51 10c.397 0 .748.152 1.052.456.305.304.457.655.457 1.053 0 .397-.152.748-.457 1.052-.304.305-.655.457-1.052.457-.398 0-.749-.152-1.053-.457C6.152 12.257 6 11.906 6 11.51c0-.398.152-.749.456-1.053Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    ['download', `<path fill="currentColor" d="M5 20h14v-2H5zM19 9h-4V3H9v6H5l7 7z"></path>`],
    [
        'drag',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="7" y="4" width="11" height="16">
            <path d="M13.5965 16.5848C14.0019 16.1793 14.4698 15.9766 15 15.9766C15.5302 15.9766 15.9981 16.1793 16.4035 16.5848C16.809 16.9903 17.0117 17.4581 17.0117 17.9883C17.0117 18.5185 16.809 18.9864 16.4035 19.3918C15.9981 19.7973 15.5302 20 15 20C14.4698 20 14.0019 19.7973 13.5965 19.3918C13.191 18.9864 12.9883 18.5185 12.9883 17.9883C12.9883 17.4581 13.191 16.9903 13.5965 16.5848ZM13.5965 10.5965C14.0019 10.191 14.4698 9.9883 15 9.9883C15.5302 9.9883 15.9981 10.191 16.4035 10.5965C16.809 11.0019 17.0117 11.4698 17.0117 12C17.0117 12.5302 16.809 12.9981 16.4035 13.4035C15.9981 13.809 15.5302 14.0117 15 14.0117C14.4698 14.0117 14.0019 13.809 13.5965 13.4035C13.191 12.9981 12.9883 12.5302 12.9883 12C12.9883 11.4698 13.191 11.0019 13.5965 10.5965ZM16.4035 7.4152C15.9981 7.82066 15.5302 8.02339 15 8.02339C14.4698 8.02339 14.0019 7.82066 13.5965 7.4152C13.191 7.00975 12.9883 6.54191 12.9883 6.0117C12.9883 5.48148 13.191 5.01365 13.5965 4.60819C14.0019 4.20273 14.4698 4 15 4C15.5302 4 15.9981 4.20273 16.4035 4.60819C16.809 5.01365 17.0117 5.48148 17.0117 6.0117C17.0117 6.54191 16.809 7.00975 16.4035 7.4152ZM7.60819 4.60819C8.01365 4.20273 8.48148 4 9.0117 4C9.54191 4 10.0097 4.20273 10.4152 4.60819C10.8207 5.01365 11.0234 5.48148 11.0234 6.0117C11.0234 6.54191 10.8207 7.00975 10.4152 7.4152C10.0097 7.82066 9.54191 8.02339 9.0117 8.02339C8.48148 8.02339 8.01365 7.82066 7.60819 7.4152C7.20273 7.00975 7 6.54191 7 6.0117C7 5.48148 7.20273 5.01365 7.60819 4.60819ZM7.60819 10.5965C8.01365 10.191 8.48148 9.9883 9.0117 9.9883C9.54191 9.9883 10.0097 10.191 10.4152 10.5965C10.8207 11.0019 11.0234 11.4698 11.0234 12C11.0234 12.5302 10.8207 12.9981 10.4152 13.4035C10.0097 13.809 9.54191 14.0117 9.0117 14.0117C8.48148 14.0117 8.01365 13.809 7.60819 13.4035C7.20273 12.9981 7 12.5302 7 12C7 11.4698 7.20273 11.0019 7.60819 10.5965ZM10.4152 16.5848C10.8207 16.9903 11.0234 17.4581 11.0234 17.9883C11.0234 18.5185 10.8207 18.9864 10.4152 19.3918C10.0097 19.7973 9.54191 20 9.0117 20C8.48148 20 8.01365 19.7973 7.60819 19.3918C7.20273 18.9864 7 18.5185 7 17.9883C7 17.4581 7.20273 16.9903 7.60819 16.5848C8.01365 16.1793 8.48148 15.9766 9.0117 15.9766C9.54191 15.9766 10.0097 16.1793 10.4152 16.5848Z" fill="#aaa"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'edit',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#fff" d="m18.54 8.273-1.372 1.372-2.813-2.813 1.372-1.371a.716.716 0 0 1 .527-.211c.21 0 .387.07.527.21l1.759 1.76c.14.14.211.316.211.527 0 .211-.07.387-.21.527ZM5.25 15.938l8.297-8.297 2.812 2.812-8.297 8.297H5.25v-2.813Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
            </g>`,
    ],
    [
        'eye',
        `<mask id="SVG_ID_A" width="22" height="16" x="1" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="currentColor" d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5ZM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5Zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3Z"/>
            </mask>
            <g mask="url(#SVG_ID_A)">
                <path fill="currentColor" d="M0 0h24v24H0z"/>
            </g>`,
    ],
    [
        'filter',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="5" width="16" height="15">
            <path d="M4 6.33333H14.6667V7.66667H4V6.33333Z" fill="#E9EDF1"/>
            <path d="M4 17H14.6667V18.3333H4V17Z" fill="#E9EDF1"/>
            <path d="M16 6.33333H20V7.66667H16V6.33333Z" fill="#E9EDF1"/>
            <path d="M4 11.6667H8V13H4V11.6667Z" fill="#E9EDF1"/>
            <path d="M16 17H20V18.3333H16V17Z" fill="#E9EDF1"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3333 7.66667C15.7015 7.66667 16 7.36819 16 7C16 6.63181 15.7015 6.33333 15.3333 6.33333C14.9651 6.33333 14.6667 6.63181 14.6667 7C14.6667 7.36819 14.9651 7.66667 15.3333 7.66667ZM15.3333 9C16.4379 9 17.3333 8.10457 17.3333 7C17.3333 5.89543 16.4379 5 15.3333 5C14.2288 5 13.3333 5.89543 13.3333 7C13.3333 8.10457 14.2288 9 15.3333 9Z" fill="#E9EDF1"/>
            <path d="M9.33333 11.6667H20V13H9.33333V11.6667Z" fill="#E9EDF1"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3333 18.3333C15.7015 18.3333 16 18.0349 16 17.6667C16 17.2985 15.7015 17 15.3333 17C14.9651 17 14.6667 17.2985 14.6667 17.6667C14.6667 18.0349 14.9651 18.3333 15.3333 18.3333ZM15.3333 19.6667C16.4379 19.6667 17.3333 18.7712 17.3333 17.6667C17.3333 16.5621 16.4379 15.6667 15.3333 15.6667C14.2288 15.6667 13.3333 16.5621 13.3333 17.6667C13.3333 18.7712 14.2288 19.6667 15.3333 19.6667Z" fill="#E9EDF1"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.66667 13C9.03486 13 9.33333 12.7015 9.33333 12.3333C9.33333 11.9651 9.03486 11.6667 8.66667 11.6667C8.29848 11.6667 8 11.9651 8 12.3333C8 12.7015 8.29848 13 8.66667 13ZM8.66667 14.3333C9.77124 14.3333 10.6667 13.4379 10.6667 12.3333C10.6667 11.2288 9.77124 10.3333 8.66667 10.3333C7.5621 10.3333 6.66667 11.2288 6.66667 12.3333C6.66667 13.4379 7.5621 14.3333 8.66667 14.3333Z" fill="#E9EDF1"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'financial-profile',
        `<mask id="SVG_ID_A" width="18" height="18" x="3" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M15.667 3.063H8.333a5.28 5.28 0 0 0-5.27 5.27v7.334a5.28 5.28 0 0 0 5.27 5.27h7.334a5.28 5.28 0 0 0 5.27-5.27V8.333a5.28 5.28 0 0 0-5.27-5.27Zm3.896 12.604a3.905 3.905 0 0 1-3.896 3.896H8.333a3.905 3.905 0 0 1-3.896-3.896V8.333a3.905 3.905 0 0 1 3.896-3.896h7.334a3.905 3.905 0 0 1 3.896 3.896v7.334Z"/>
            <path fill="currentColor" d="M12 10.395a.697.697 0 0 0-.687.688v4.583a.688.688 0 1 0 1.375 0v-4.583a.697.697 0 0 0-.688-.688Zm3.667-2.75a.697.697 0 0 0-.688.688v7.333a.688.688 0 0 0 1.375 0V8.333a.696.696 0 0 0-.687-.688ZM8.334 12.23a.697.697 0 0 0-.688.687v2.75a.688.688 0 1 0 1.375 0v-2.75a.697.697 0 0 0-.687-.687Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'flag',
        `<g clip-path="url(#SVG_ID_A)">
            <mask id="SVG_ID_B" width="15" height="17" x="5" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
                <path fill="#000" d="M14.4 6 14 4H5v17h2v-7h5.6l.4 2h7V6h-5.6Z"/>
            </mask>
            <g mask="url(#SVG_ID_B)">
                <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
            </g>
        </g>
        <defs>
            <clipPath id="SVG_ID_A">
                <path fill="#fff" d="M0 0h24v24H0z"/>
            </clipPath>
        </defs>`,
    ],
    [
        'hide',
        `<path fill-rule="evenodd" clip-rule="evenodd" d="M9.30843 4.80842L11.5225 7.02252C11.6797 7.00761 11.839 6.99999 12 6.99999C14.76 6.99999 17 9.23999 17 12C17 12.161 16.9924 12.3203 16.9775 12.4775L20.4436 15.9436C21.5395 14.8226 22.415 13.4845 23 12C21.27 7.60999 17 4.49999 12 4.49999C11.0739 4.49999 10.1729 4.60667 9.30843 4.80842ZM21.5009 19.9991L4.00093 2.49905L2.5 3.99999L5.1739 6.67388C3.31268 7.99572 1.84986 9.84341 1 12C2.73 16.39 7 19.5 12 19.5C13.7584 19.5 15.4265 19.1153 16.9257 18.4256L20 21.5L21.5009 19.9991ZM7 12C7 11.0035 7.29196 10.0749 7.79496 9.29494L9.26437 10.7644C9.09449 11.1409 9 11.5592 9 12C9 13.66 10.34 15 12 15C12.4408 15 12.8591 14.9055 13.2356 14.7356L14.705 16.205C13.9251 16.708 12.9964 17 12 17C9.24 17 7 14.76 7 12Z" fill="currentColor"/>`,
    ],
    [
        'grid',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M17.432 17.432v-3.099h-3.099v3.1h3.1Zm-4.666-4.666H19V19h-6.234v-6.234Zm4.666-3.1V6.569h-3.099v3.099h3.1ZM12.766 5H19v6.234h-6.234V5Zm-3.1 12.432v-3.099H6.569v3.1h3.099ZM5 12.766h6.234V19H5v-6.234Zm4.667-3.1V6.569h-3.1v3.099h3.1ZM5 5h6.234v6.234H5V5Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'grid-small',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M15.48 19v-3.52H19V19h-3.52Zm0-5.24v-3.52H19v3.52h-3.52Zm-5.24-5.24V5h3.52v3.52h-3.52ZM15.48 5H19v3.52h-3.52V5Zm-5.24 8.76v-3.52h3.52v3.52h-3.52Zm-5.24 0v-3.52h3.52v3.52H5ZM5 19v-3.52h3.52V19H5Zm5.24 0v-3.52h3.52V19h-3.52ZM5 8.52V5h3.52v3.52H5Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'inbox',
        `<mask id="SVG_ID_A" width="18" height="18" x="3" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="m20.058 11.395-3.062-6.123A2.75 2.75 0 0 0 14.53 3.75H9.47a2.75 2.75 0 0 0-2.466 1.522l-3.061 6.123a1.833 1.833 0 0 0-.193.825v5.28a2.75 2.75 0 0 0 2.75 2.75h11a2.75 2.75 0 0 0 2.75-2.75v-5.28a1.833 1.833 0 0 0-.192-.825ZM8.645 6.088a.917.917 0 0 1 .825-.505h5.06a.917.917 0 0 1 .825.505l2.493 4.995h-2.181a.917.917 0 0 0-.917.917v2.75h-5.5V12a.917.917 0 0 0-.917-.917H6.152l2.493-4.995ZM17.5 18.417h-11a.917.917 0 0 1-.917-.917v-4.583h1.834v2.75a.917.917 0 0 0 .916.916h7.334a.917.917 0 0 0 .916-.916v-2.75h1.834V17.5a.917.917 0 0 1-.917.917Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'info',
        `<mask id="SVG_ID_A" width="16" height="16" x="4" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M12 4.083a7.917 7.917 0 1 0 0 15.834 7.917 7.917 0 0 0 0-15.834Zm0 14.25a6.334 6.334 0 1 1 0-12.667 6.334 6.334 0 0 1 0 12.667Z"/>
            <path fill="currentColor" d="M12 9.626a.792.792 0 1 0 0-1.584.792.792 0 0 0 0 1.584Zm0 .791a.792.792 0 0 0-.792.792v3.958a.792.792 0 0 0 1.583 0V11.21a.792.792 0 0 0-.791-.792Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'keyboard-arrow-down',
        `<mask id="SVG_ID_A" width="12" height="8" x="6" y="8" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M7.41 8.59 12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'keyboard-arrow-left',
        `<mask id="SVG_ID_A" width="8" height="13" x="8" y="6" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="m15.705 7.705-4.58 4.59 4.58 4.59-1.41 1.41-6-6 6-6 1.41 1.41Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'keyboard-arrow-right',
        `<mask id="SVG_ID_A" width="8" height="13" x="8" y="6" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="m8.295 16.885 4.58-4.59-4.58-4.59 1.41-1.41 6 6-6 6-1.41-1.41Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'keyboard-arrow-up',
        `<mask id="SVG_ID_A" width="12" height="8" x="6" y="8" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M16.59 15.41 12 10.83l-4.59 4.58L6 14l6-6 6 6-1.41 1.41Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'list',
        `<mask id="SVG_ID_A" width="18" height="18" x="3" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#97ABCC" d="M17.295 7.236a.53.53 0 1 1 0 1.058H9.036a.53.53 0 1 1 0-1.058h8.259ZM6.812 7.235a.53.53 0 0 1 0 1.06h-.106a.53.53 0 1 1 0-1.06h.106Zm10.483 4.236a.53.53 0 1 1 0 1.059H9.036a.53.53 0 1 1 0-1.059h8.259Zm-10.483 0a.53.53 0 1 1 0 1.059h-.106a.53.53 0 1 1 0-1.059h.106Zm10.483 4.236a.53.53 0 1 1 0 1.058H9.036a.53.53 0 1 1 0-1.058h8.259Zm-10.483 0a.53.53 0 1 1 0 1.058h-.106a.53.53 0 1 1 0-1.058h.106Z"/>
            <path fill="#97ABCC" fill-rule="evenodd" d="M17 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3ZM7 3a4 4 0 0 0-4 4v10a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H7Z" clip-rule="evenodd"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h25.044v25.044H0z"/>
        </g>`,
    ],
    [
        'lock',
        `<mask id="SVG_ID_A" width="16" height="21" x="4" y="1" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M17.5 8.333h-.916V6.5A4.585 4.585 0 0 0 12 1.917 4.585 4.585 0 0 0 7.417 6.5v1.833H6.5a1.839 1.839 0 0 0-1.833 1.834v9.166c0 1.009.825 1.834 1.833 1.834h11a1.839 1.839 0 0 0 1.834-1.834v-9.166A1.839 1.839 0 0 0 17.5 8.333Zm-5.5 8.25a1.839 1.839 0 0 1-1.833-1.833c0-1.008.825-1.833 1.833-1.833 1.009 0 1.834.825 1.834 1.833A1.839 1.839 0 0 1 12 16.583Zm2.842-8.25H9.159V6.5A2.844 2.844 0 0 1 12 3.658 2.844 2.844 0 0 1 14.842 6.5v1.833Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'logout',
        `<path d="m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z" fill="currentColor"/>`,
    ],
    [
        'long-arrow',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" transform="rotate(180 12 12)" d="m12 5-7 7 7 7 1.228-1.228-4.871-4.912H19v-1.72H8.357l4.871-4.912L12 5Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'minus',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="5" y="10" width="14" height="3">
            <path d="M11.2081 12.5L11.2081 12.4729H5V10.8892H11.2398L12.9502 10.8891L19 10.8892V12.4729H12.9502L12.9502 12.5H11.2081Z" fill="#fff"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],

    [
        'payments',
        `<mask id="SVG_ID_A" width="24" height="24" x="0" y="0" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path d="M19 14V6c0-1.1-.9-2-2-2H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2m-9-1c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3m13-6v11c0 1.1-.9 2-2 2H4v-2h17V7z" fill="currentColor"></path>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'percent',
        `<mask id="SVG_ID_A" width="20" height="20" x="2" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path d="M7.5 11C9.43 11 11 9.43 11 7.5S9.43 4 7.5 4 4 5.57 4 7.5 5.57 11 7.5 11m0-5C8.33 6 9 6.67 9 7.5S8.33 9 7.5 9 6 8.33 6 7.5 6.67 6 7.5 6M4.0025 18.5832 18.59 3.9955l1.4142 1.4143L5.4167 19.9974zM16.5 13c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5m0 5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5" fill="currentColor"></path>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'person',
        `<mask id="SVG_ID_A" width="14" height="14" x="5" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M7.374 14.743c1.61-.655 3.152-.983 4.626-.983 1.474 0 3.002.328 4.585.983 1.61.627 2.415 1.46 2.415 2.497V19H5v-1.76c0-1.037.791-1.87 2.374-2.497Zm7.082-3.766C13.774 11.659 12.956 12 12 12c-.955 0-1.774-.341-2.456-1.023-.682-.683-1.024-1.501-1.024-2.457 0-.955.342-1.773 1.024-2.456C10.226 5.354 11.044 5 12 5c.955 0 1.774.355 2.456 1.064.682.683 1.024 1.501 1.024 2.456 0 .956-.342 1.774-1.024 2.457Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'plus',
        `<mask id="SVG_ID_A" width="14" height="16" x="5" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M11.208 19.489v-7.016H5v-1.584h6.24V4h1.71v6.89H19v1.583h-6.05v7.016h-1.742Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'priority',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="10" y="5" width="4" height="14">
            <path d="M10 5H13.1354V14.3333H10V5ZM10.4375 18.5625C10.1458 18.2465 10 17.8698 10 17.4323C10 16.9948 10.1458 16.6302 10.4375 16.3385C10.7535 16.0469 11.1302 15.901 11.5677 15.901C12.0052 15.901 12.3698 16.0469 12.6615 16.3385C12.9774 16.6302 13.1354 16.9948 13.1354 17.4323C13.1354 17.8698 12.9774 18.2465 12.6615 18.5625C12.3698 18.8542 12.0052 19 11.5677 19C11.1302 19 10.7535 18.8542 10.4375 18.5625Z" fill="currentColor"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="#E9EDF1"/>
        </g>`,
    ],
    [
        'reports',
        `<mask id="SVG_ID_A" width="16" height="16" x="3" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M15 3H5c-1.1 0-1.99.9-1.99 2L3 19c0 1.1.89 2 1.99 2H19c1.1 0 2-.9 2-2V9zM8 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-4c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-4c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m6 1V4.5l5.5 5.5z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'reply',
        `<mask id="SVG_ID_A" width="18" height="16" x="3" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M9.984 8.484c3.25.47 5.75 1.72 7.5 3.75 1.782 2 2.953 4.422 3.516 7.266-2.406-3.406-6.078-5.11-11.016-5.11v4.079L3 11.484 9.984 4.5v3.984Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'search',
        `<mask id="SVG_ID_A" width="16" height="16" x="4" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="m19.555 17.434-3.514-3.515a6.439 6.439 0 0 0 .982-3.397 6.5 6.5 0 1 0-6.5 6.5 6.44 6.44 0 0 0 3.396-.98l3.515 3.514a1.5 1.5 0 0 0 2.121-2.122Zm-9.032-2.412a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'settings',
        `<mask id="SVG_ID_A" width="16" height="16" x="4" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M9.962 14.99a2.81 2.81 0 0 0 2.038.827 2.81 2.81 0 0 0 2.038-.826c.563-.551.845-1.215.845-1.991s-.281-1.44-.845-1.99A2.81 2.81 0 0 0 12 10.182a2.81 2.81 0 0 0-2.038.826c-.564.551-.845 1.215-.845 1.991s.281 1.44.845 1.99Zm8.15-1.201 1.731 1.314c.18.126.205.3.077.526l-1.653 2.78c-.103.175-.27.225-.5.15l-2.038-.789c-.538.375-1 .639-1.384.789l-.307 2.103c-.052.225-.18.338-.385.338h-3.306c-.205 0-.333-.113-.385-.338l-.307-2.103a6.33 6.33 0 0 1-1.384-.789l-2.038.789c-.23.075-.397.025-.5-.15l-1.653-2.78c-.128-.225-.102-.4.077-.526l1.73-1.314A5.93 5.93 0 0 1 5.85 13c0-.35.012-.614.038-.789l-1.73-1.314c-.18-.126-.205-.3-.077-.526l1.653-2.78c.103-.175.27-.225.5-.15l2.038.79c.538-.377 1-.64 1.384-.79l.307-2.103c.052-.225.18-.338.385-.338h3.306c.205 0 .333.113.385.338l.307 2.103c.487.2.949.464 1.384.79l2.038-.79c.23-.075.397-.025.5.15l1.653 2.78c.*************-.077.526l-1.73 1.314c.026.175.038.438.038.789 0 .35-.012.614-.038.789Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'show',
        `<g clip-path="url(#SVG_ID_A)">
            <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5C17 19.5 21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z" fill="currentColor"/>
        </g>
        <defs>
            <clipPath id="SVG_ID_A">
                <rect width="24" height="24" fill="white"/>
            </clipPath>
        </defs>`,
    ],
    [
        'taxes',
        `<mask id="SVG_ID_A" width="18" height="19" x="3" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" fill-rule="evenodd" d="M10.274 6.957a3 3 0 1 1 .723-2.087l.794 1.39 1.356-2.19A3.001 3.001 0 0 1 19 5a3 3 0 0 1-5.5 1.659V7L9 14H7l3.809-6.152-.535-.891ZM8 6a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm9-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z" clip-rule="evenodd"/>
            <path fill="currentColor" d="M14.7 15.5c0 1.519-1.209 2.75-2.7 2.75s-2.7-1.231-2.7-2.75 1.209-2.75 2.7-2.75 2.7 1.231 2.7 2.75Zm-6.3 0a.908.908 0 0 1-.9.917c-.497 0-.9-.41-.9-.917 0-.506.403-.917.9-.917s.9.41.9.917Zm8.1.917c.497 0 .9-.41.9-.917a.908.908 0 0 0-.9-.917c-.497 0-.9.41-.9.917 0 .506.403.917.9.917Z"/>
            <path fill="currentColor" d="M4 10h5.5l-1 2h-3l-1 1v5.5l1 1h13l1-1V13l-1-1H12l1.5-2H20a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'thumbnail',
        `<mask id="SVG_ID_A" width="18" height="18" x="3" y="3" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#97ABCC" d="M10.47 6.176a1 1 0 0 1 1 1v3.295a1 1 0 0 1-1 1H7.178a1 1 0 0 1-1-1V7.176a1 1 0 0 1 1-1h3.294Zm.001 6.354a1 1 0 0 1 1 1v3.293a1 1 0 0 1-1 1H7.177a1 1 0 0 1-1-1V13.53a1 1 0 0 1 1-1h3.294Zm6.353-6.353a1 1 0 0 1 1 1v3.294a1 1 0 0 1-1 1H13.53a1 1 0 0 1-1-1V7.176a1 1 0 0 1 1-1h3.294Zm0 6.353a1 1 0 0 1 1 1v3.293a1 1 0 0 1-1 1H13.53a1 1 0 0 1-1-1V13.53a1 1 0 0 1 1-1h3.294Z"/>
            <path fill="#97ABCC" fill-rule="evenodd" d="M17 4H7a3 3 0 0 0-3 3v10a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3ZM7 3a4 4 0 0 0-4 4v10a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V7a4 4 0 0 0-4-4H7Z" clip-rule="evenodd"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'thumbs-down',
        `<mask id="SVG_ID_A" width="16" height="14" x="4" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M19.5 12.705v-1.41c0-.175-.032-.35-.096-.524L17.33 5.852C17.117 5.284 16.702 5 16.085 5H9.957c-.361 0-.68.142-.957.426a1.369 1.369 0 0 0-.415.984v6.983c0 .372.138.7.415.984L13.468 19l.734-.754a.995.995 0 0 0 .287-.721v-.23l-.67-3.213h4.309c.361 0 .68-.142.957-.426.277-.263.415-.58.415-.951ZM4.5 5v8.393h2.745V5H4.5Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'thumbs-up',
        `<mask id="SVG_ID_A" width="16" height="14" x="4" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="currentColor" d="M19.5 11.295v1.41c0 .175-.032.35-.096.524l-2.074 4.919c-.213.568-.628.852-1.245.852H9.957c-.361 0-.68-.142-.957-.426a1.37 1.37 0 0 1-.415-.984v-6.983c0-.372.138-.7.415-.984L13.468 5l.734.754a.995.995 0 0 1 .287.721v.23l-.67 3.213h4.309c.361 0 .68.142.957.426.277.263.415.58.415.951ZM4.5 19v-8.393h2.745V19H4.5Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],

    [
        'timeline',
        `<mask id="SVG_ID_A" width="20" height="17" x="2" y="2" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#D9D9D9" d="M2 10h20v3H2v-3Zm12-6c0 1.105-2 4-2 4s-2-2.895-2-4a2 2 0 1 1 4 0ZM6 17a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm16 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm-8 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h24v24H0z"/>
        </g>`,
    ],
    [
        'trash',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="4" width="13" height="16">
            <path d="M18.4167 4.875V6.66667H6V4.875H9.08333L10 4H14.4167L15.3333 4.875H18.4167ZM6.875 18.2083V7.54167H17.5417V18.2083C17.5417 18.6806 17.3611 19.0972 17 19.4583C16.6389 19.8194 16.2222 20 15.75 20H8.66667C8.19444 20 7.77778 19.8194 7.41667 19.4583C7.05556 19.0972 6.875 18.6806 6.875 18.2083Z" fill="white"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'unlink',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="5" width="16" height="15">
            <path d="M4 6.01408L5.01408 5L18.4225 18.4085L17.4085 19.4225L14.1784 16.23H12.7887V14.8404L10.9859 13H8.80751V11.4225H9.37089L7.71831 9.76995C7.09233 9.84507 6.56651 10.1205 6.14085 10.5962C5.71518 11.0469 5.50235 11.5853 5.50235 12.2113C5.50235 12.8873 5.74022 13.4757 6.21596 13.9765C6.71674 14.4523 7.30516 14.6901 7.98122 14.6901H11.2113V16.23H7.98122C6.8795 16.23 5.94053 15.8419 5.16432 15.0657C4.38811 14.2645 4 13.313 4 12.2113C4 11.4601 4.23787 10.7214 4.71362 9.99531C5.18936 9.26917 5.77778 8.76839 6.47887 8.49296L4 6.01408ZM15.1925 11.4225V13H15.0423L13.4648 11.4225H15.1925ZM16.0188 8.19249C17.1205 8.19249 18.0595 8.59311 18.8357 9.39437C19.6119 10.1706 20 11.1095 20 12.2113C20 13.8388 19.2739 15.0282 17.8216 15.7793L16.6573 14.615C17.1831 14.4898 17.6213 14.2019 17.9718 13.7512C18.3224 13.3005 18.4977 12.7872 18.4977 12.2113C18.4977 11.5352 18.2473 10.9593 17.7465 10.4836C17.2707 9.98279 16.6948 9.73239 16.0188 9.73239H12.7887V8.19249H16.0188Z" fill="white"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'upload',
        `<mask id="SVG_ID_A" width="12" height="15" x="6" y="5" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path fill="#fff" d="M6 18.223h12V20H6v-1.777Zm3.423-1.777v-5.289H6L12 5l6 6.157h-3.423v5.29H9.423Z"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <path fill="currentColor" d="M0 0h26.132v26.132H0z"/>
        </g>`,
    ],
    [
        'people',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="6" width="18" height="12">
            <path d="M15.2553 12.5106C15.9702 12.5106 16.7489 12.6128 17.5915 12.817C18.434 13.0213 19.2128 13.3532 19.9277 13.8128C20.6426 14.2723 21 14.7957 21 15.383V17.4128H16.0979V15.383C16.0979 14.2596 15.5617 13.3149 14.4894 12.5489C14.6681 12.5234 14.9234 12.5106 15.2553 12.5106ZM6.40851 12.817C7.25106 12.6128 8.02979 12.5106 8.74468 12.5106C9.45957 12.5106 10.2383 12.6128 11.0809 12.817C11.9234 13.0213 12.6894 13.3532 13.3787 13.8128C14.0936 14.2723 14.4511 14.7957 14.4511 15.383V17.4128H3V15.383C3 14.7957 3.35745 14.2723 4.07234 13.8128C4.78723 13.3532 5.56596 13.0213 6.40851 12.817ZM10.4298 10.1745C9.97021 10.6596 9.40851 10.9021 8.74468 10.9021C8.08085 10.9021 7.50638 10.6596 7.02128 10.1745C6.53617 9.68936 6.29362 9.11489 6.29362 8.45106C6.29362 7.78723 6.53617 7.21277 7.02128 6.72766C7.50638 6.24255 8.08085 6 8.74468 6C9.40851 6 9.97021 6.24255 10.4298 6.72766C10.9149 7.21277 11.1574 7.78723 11.1574 8.45106C11.1574 9.11489 10.9149 9.68936 10.4298 10.1745ZM16.9787 10.1745C16.4936 10.6596 15.9191 10.9021 15.2553 10.9021C14.5915 10.9021 14.017 10.6596 13.5319 10.1745C13.0468 9.68936 12.8043 9.11489 12.8043 8.45106C12.8043 7.78723 13.0468 7.21277 13.5319 6.72766C14.017 6.24255 14.5915 6 15.2553 6C15.9191 6 16.4936 6.24255 16.9787 6.72766C17.4638 7.21277 17.7064 7.78723 17.7064 8.45106C17.7064 9.11489 17.4638 9.68936 16.9787 10.1745Z" fill="white"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'pending',
        `<mask id="SVG_ID_A" width="16" height="16" x="4" y="4" maskUnits="userSpaceOnUse" style="mask-type:alpha">
            <path d="M12 4C13.1017 4 14.1283 4.21283 15.0798 4.6385C16.0563 5.03912 16.9077 5.61502 17.6338 6.3662C18.385 7.09233 18.9609 7.94366 19.3615 8.92019C19.7872 9.87167 20 10.8983 20 12C20 13.1017 19.7872 14.1408 19.3615 15.1174C18.9609 16.0689 18.385 16.9202 17.6338 17.6714C16.9077 18.3975 16.0563 18.9734 15.0798 19.3991C14.1283 19.7997 13.1017 20 12 20C10.8983 20 9.85915 19.7997 8.88263 19.3991C7.93114 18.9734 7.07981 18.3975 6.32864 17.6714C5.6025 16.9202 5.0266 16.0689 4.60094 15.1174C4.20031 14.1408 4 13.1017 4 12C4 10.8983 4.20031 9.87167 4.60094 8.92019C5.0266 7.94366 5.6025 7.09233 6.32864 6.3662C7.07981 5.61502 7.93114 5.03912 8.88263 4.6385C9.85915 4.21283 10.8983 4 12 4ZM7.98122 13.2019C8.33177 13.2019 8.61972 13.0892 8.84507 12.8638C9.07042 12.6135 9.1831 12.3255 9.1831 12C9.1831 11.6745 9.07042 11.3991 8.84507 11.1737C8.61972 10.9233 8.33177 10.7981 7.98122 10.7981C7.65571 10.7981 7.36776 10.9233 7.11737 11.1737C6.89202 11.3991 6.77934 11.6745 6.77934 12C6.77934 12.3255 6.89202 12.6135 7.11737 12.8638C7.36776 13.0892 7.65571 13.2019 7.98122 13.2019ZM12 13.2019C12.3255 13.2019 12.6009 13.0892 12.8263 12.8638C13.0767 12.6135 13.2019 12.3255 13.2019 12C13.2019 11.6745 13.0767 11.3991 12.8263 11.1737C12.6009 10.9233 12.3255 10.7981 12 10.7981C11.6745 10.7981 11.3865 10.9233 11.1361 11.1737C10.9108 11.3991 10.7981 11.6745 10.7981 12C10.7981 12.3255 10.9108 12.6135 11.1361 12.8638C11.3865 13.0892 11.6745 13.2019 12 13.2019ZM16.0188 13.2019C16.3443 13.2019 16.6197 13.0892 16.8451 12.8638C17.0955 12.6135 17.2207 12.3255 17.2207 12C17.2207 11.6745 17.0955 11.3991 16.8451 11.1737C16.6197 10.9233 16.3443 10.7981 16.0188 10.7981C15.6682 10.7981 15.3803 10.9233 15.1549 11.1737C14.9296 11.3991 14.8169 11.6745 14.8169 12C14.8169 12.3255 14.9296 12.6135 15.1549 12.8638C15.3803 13.0892 15.6682 13.2019 16.0188 13.2019Z" fill="currentColor"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
    [
        'filter',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="4" y="5" width="16" height="15">
             <path d="M4 6.33333H14.6667V7.66667H4V6.33333Z" fill="currentColor"/>
             <path d="M4 17H14.6667V18.3333H4V17Z" fill="currentColor"/>
             <path d="M16 6.33333H20V7.66667H16V6.33333Z" fill="currentColor"/>
             <path d="M4 11.6667H8V13H4V11.6667Z" fill="currentColor"/>
             <path d="M16 17H20V18.3333H16V17Z" fill="currentColor"/>
             <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3333 7.66667C15.7015 7.66667 16 7.36819 16 7C16 6.63181 15.7015 6.33333 15.3333 6.33333C14.9651 6.33333 14.6667 6.63181 14.6667 7C14.6667 7.36819 14.9651 7.66667 15.3333 7.66667ZM15.3333 9C16.4379 9 17.3333 8.10457 17.3333 7C17.3333 5.89543 16.4379 5 15.3333 5C14.2288 5 13.3333 5.89543 13.3333 7C13.3333 8.10457 14.2288 9 15.3333 9Z" fill="currentColor"/>
             <path d="M9.33333 11.6667H20V13H9.33333V11.6667Z" fill="currentColor"/>
             <path fill-rule="evenodd" clip-rule="evenodd" d="M15.3333 18.3333C15.7015 18.3333 16 18.0349 16 17.6667C16 17.2985 15.7015 17 15.3333 17C14.9651 17 14.6667 17.2985 14.6667 17.6667C14.6667 18.0349 14.9651 18.3333 15.3333 18.3333ZM15.3333 19.6667C16.4379 19.6667 17.3333 18.7712 17.3333 17.6667C17.3333 16.5621 16.4379 15.6667 15.3333 15.6667C14.2288 15.6667 13.3333 16.5621 13.3333 17.6667C13.3333 18.7712 14.2288 19.6667 15.3333 19.6667Z" fill="currentColor"/>
             <path fill-rule="evenodd" clip-rule="evenodd" d="M8.66667 13C9.03486 13 9.33333 12.7015 9.33333 12.3333C9.33333 11.9651 9.03486 11.6667 8.66667 11.6667C8.29848 11.6667 8 11.9651 8 12.3333C8 12.7015 8.29848 13 8.66667 13ZM8.66667 14.3333C9.77124 14.3333 10.6667 13.4379 10.6667 12.3333C10.6667 11.2288 9.77124 10.3333 8.66667 10.3333C7.5621 10.3333 6.66667 11.2288 6.66667 12.3333C6.66667 13.4379 7.5621 14.3333 8.66667 14.3333Z" fill="currentColor"/>
         </mask>
         <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
         </g>`,
    ],
    [
        'form',
        `<mask id="SVG_ID_A" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="5" width="19" height="14">
            <path d="M3 14.0201V9.97987H6.99329V14.0201H3ZM3 19V15.0067H6.99329V19H3ZM3 8.99329V5H6.99329V8.99329H3ZM8.02685 14.0201V9.97987H21.0403V14.0201H8.02685ZM8.02685 19V15.0067H21.0403V19H8.02685ZM8.02685 5H21.0403V8.99329H8.02685V5Z" fill="white"/>
        </mask>
        <g mask="url(#SVG_ID_A)">
            <rect width="24" height="24" fill="currentColor"/>
        </g>`,
    ],
]);

export const svgIconShapeKeys = Array.from(svgIconShapes.keys());

export type TSvgIconShapeKey = (typeof svgIconShapeKeys)[number];
