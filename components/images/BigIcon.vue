<template>
    <svg xmlns="http://www.w3.org/2000/svg" 
        :width="width"
        :height="height"
        fill="none"
        viewBox="0 0 52 52"
        v-html="svgContent"
        :id="elementId"
        :style="styleObj"
        class="big-icon"
    ></svg>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from "vue";

// All of the actual icon data is stored in SvgIconShapes.ts
import { bigIconShapes, bigIconShapeKeys } from './BigIconShapes';
import type { TBigIconShapeKey } from './BigIconShapes';

/** An larger, more complex SVG icon using of the predefined shapes */
defineOptions();

const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'svg-icon-' + componentId);

export interface Props {
    shape: TBigIconShapeKey;
    color?: string | null;
    width?: number;
    height?: number;
}

const props = withDefaults(defineProps<Props>(), {
    shape: 'bungalow',
    color: null,
    width: 52,
    height: 52
})

const styleObj = computed(() =>
    (props.color) ? { color: props.color } : {}
)

// Ensure each instance of this SVG on the page has unique ID tags
const svgContent = computed(() =>
    bigIconShapes.get(props.shape)
        ?.replaceAll('SVG_ID_', `SVG_${componentId}_`)
);
</script>
