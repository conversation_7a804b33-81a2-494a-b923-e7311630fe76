<template>
    <svg
        xmlns="http://www.w3.org/2000/svg"
        :width="compWidth"
        :height="compHeight"
        fill="none"
        :viewBox="computedViewBox"
        v-html="svgContent"
        :style="styleObj"
        :id="elementId"
        class="svg-icon"
    ></svg>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from "vue";
import { svgIconShapes, svgIconShapeKeys } from './SvgIconShapes';
import type { TSvgIconShapeKey } from './SvgIconShapes';

/** An SVG icon using of the predefined shapes */
defineOptions();

const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'svg-icon-' + componentId);

export interface Props {
    shape: TSvgIconShapeKey;
    color?: string | null;
    width?: number | string | null;
    height?: number | string | null;
    viewBox?: string; // New prop for custom viewBox
}

const props = withDefaults(defineProps<Props>(), {
    shape: 'info',
    color: null,
    width: 24,
    height: 24,
    viewBox: '0 0 24 24',
});

const styleObj = computed(() => (props.color ? { color: props.color } : {}));

const compWidth = computed(() => typeof props.width === 'number' ? props.width : parseInt(props.width));
const compHeight = computed(() => typeof props.height === 'number' ? props.height : parseInt(props.height));

// Computed value for dynamic viewBox
const computedViewBox = computed(() => props.viewBox || '0 0 24 24');

// Ensure each instance of this SVG on the page has unique ID tags
const svgContent = computed(() =>
    svgIconShapes.get(props.shape)?.replaceAll('SVG_ID_', `SVG_${componentId}_`)
);
</script>
