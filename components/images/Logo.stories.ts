import Logo from './Logo.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
// import { action } from '@storybook/addon-actions';

const meta: Meta<typeof Logo> = {
    component: Logo,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em; color: var(--color-white);"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof Logo>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        // 
    }
};

export const Brandmark: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        variant: 'brandmark'
    }
};

export const CustomColor: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        color: 'var(--color-blue)'
    }
};
