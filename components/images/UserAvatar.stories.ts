import UserAvatar from './UserAvatar.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof UserAvatar> = {
    component: UserAvatar,
    tags: ['autodocs'],
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof UserAvatar>;

export const Default: Story = {
    args: {
        user: {
            _id: '1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
        }
    }
};
