<template>
    <!-- 
    
        !! DEPRECATED
        Not used post-intake overhaul; replaced with OverhaulIntakeReviewGroup

    -->
    <div class="intake-review-group">
        <div class="intake-review-group-icon">
            <BigIcon
                :shape="svgShape"
                :width="50"
                :height="50"
            />
        </div>

        <div class="intake-review-group-text">
            <h4 v-text="label"></h4>

            <div class="intake-review-group-progress">
                <p><span v-text="completionPercentage"></span> Complete</p>

                <div class="intake-review-group-progress-bar" :style="{'--progress-bar-width': completionPercentage}">

                </div>
            </div>

            <p class="intake-review-group-instructions" v-text="instructions"></p>
        </div>

        <div class="intake-review-group-items">
            <p class="intake-review-group-count"> <em v-text="completedSituationsCount ?? 0"></em> / <em v-text="situationsCount"></em> Selections complete</p>

            <div class="intake-review-group-situations">
                <SituationItem
                    v-for="situation in transformedSituations"
                    v-bind="situation"

                    @simple-change="(...e) => emit('simple-change', ...e)"
                    @standard-change="(...e) => emit('standard-change', ...e)"
                    @entry-mode-change="(...e) => emit('entry-mode-change', situation.name, ...e)"
                    @remove-item="(...e) => emit('remove-item', situation.name, ...e)"
                    @add-item="emit('add-item', situation.name)"
                    @remove-situation="emit('remove-situation', situation)"
                />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SituationItem from './SituationItem.vue';
import type { SituationItemProps } from './SituationItem.vue';
import SituationLineItem from './SituationLineItem.vue';
import type { SituationLineItemProps } from './SituationLineItem.vue';
import type { SituationEntryMode, SituationItemEmits } from './SituationItem.vue';
import BigIcon from '../images/BigIcon.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'intake-review-group-' + componentId);

export interface TempSituation {
    title: string;
    itemsCount?: number | null;
    entryMode: 'simple' | 'standard';
    isCompleted: boolean;
}

export interface IntakeReviewGroupProps {
    svgShape: TBigIconShapeKey;
    label: string;
    instructions: string;
    completion: number;
    situations?: IntakeSituationItem[];
    // tempSituations?: TempSituation[]
}

const props = defineProps<IntakeReviewGroupProps>()

export interface IntakeReviewGroupEmits {
    (e: 'simple-change',
        situationName: string|null|undefined,
        fieldName: string,
        payload: any
    ): void,
    (e: 'standard-change', 
        situationName: string|null|undefined,
        itemId: string|number,
        fieldName: string,
        payload: any
    ): void,
    (e: 'entry-mode-change', 
        situationName: string,
        newMode: SituationEntryMode,
        useAsDefault: boolean
    ): void,
    (e: 'remove-item',  situationName: string|undefined, itemId: number|string): void,
    (e: 'add-item', situationName: string|undefined): void,
    (e: 'remove-situation', situation: IntakeSituationItem): void,
}
const emit = defineEmits<IntakeReviewGroupEmits>()

const transformedSituations = computed(() => props.situations?.map(situation => {
    return {
        ...situation,
        title: situation.kind,
        displayMode: 'line'
    }
}));

const completionPercentage = computed(() => `${props.completion}%`)

const situationsCount = computed(() => props.situations?.length ?? 0);
const completedSituations = computed(() => props.situations?.filter(e => 
    e.entryMode === 'standard'
        ? Object.values(e.standard)?.filter(field => field.isComplete === true).length ?? 0
        : e.simple.some(s => s.required && !s.value && s.value !== 0) ? 0 : 1
));

const completedSituationsCount = computed(() => 
    completedSituations.value?.length ?? 0
    // props.situations?.filter(e => {
    //     if (e.entryMode === 'simple') {
    //         return e.simple.some(s => s.required && !s.value)
    //     } else {
    //         (e.completedSteps ?? 0) >= (e.totalSteps ?? 1)
    //     }
    // }).length
);
</script>

<style lang="scss">
@import "./IntakeReviewGroup";
</style>
