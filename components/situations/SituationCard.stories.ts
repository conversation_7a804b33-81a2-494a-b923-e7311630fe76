import SituationCard from './SituationCard.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { bigIconShapeKeys } from '../images/BigIconShapes';

const meta: Meta<typeof SituationCard> = {
    component: SituationCard,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        iconShape: {
            control: { type: 'select' },
            options: bigIconShapeKeys, // imported from ./SvgIconShapes.ts
            description: 'The name / slug of the icon to render'
        },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { SituationCard },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <SituationCard v-bind="args" :foo="foo">
    //             ${args.default}
    //         </SituationCard>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em; min-width: 300px"><div class="width: 100%; display:flex; align-items: stretch; justify-content: stretch;"><story/></div></div>' })]
};

export default meta;
type Story = StoryObj<typeof SituationCard>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        totalSteps: 4,
        completedSteps: 2,
        type: 'Employee',
        title: '2 sources',
        iconShape: 'home',
        iconBadge: 'simple',
        rows: [
            { label: 'Income', percentage: .08, value: 250000 },
            { label: 'Assets' },
            { label: 'Liabilities' },
        ],
        footerRow: { label: 'Net Worth', percentage: .07, value: 250000 },
    }
};

export const Pending: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        mode: 'pending',
        totalSteps: 4,
        completedSteps: 1,
        type: 'Employee',
        iconShape: 'cleaning',
        footerRow: { label: 'Onboard Status', percentage: .07},
    }
};
