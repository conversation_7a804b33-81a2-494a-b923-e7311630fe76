import SituationCrudItem from './SituationCrudItem.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SituationCrudItem> = {
    component: SituationCrudItem,
    tags: ['autodocs'],

    decorators: [() => ({ template: '<div style="margin: 3em"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SituationCrudItem>;

export const Complete: Story = {
    args: {
        status: 'complete',
        label: ['Commercial Real Estate', '4232 University Dr'],
        kpis: [
            {
                label: 'Total value',
                value: 1649876131,
                formatting: 'currency',
                fade: false
            },
            {
                label: 'Last updated',
                value: '4 mo',
                fade: true
            }
        ]
    }
};

export const Incomplete: Story = {
    args: {
        status: 'incomplete',
        label: 'Income Tax',
        kpis: [
            {
                label: 'Total value',
                value: 216541,
                formatting: 'currency',
                fade: false
            },
            {
                label: 'Another value',
                value: 654987,
                formatting: 'currency',
                fade: false
            },
            {
                label: 'Last updated',
                value: '3 mo',
                fade: true
            }
        ]
    }
};
