<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="add-object-modal">
        <template #header>
            <div class="add-object-modal-header">
                <div class="add-object-modal-header-title">
                    <h4 v-if="title" v-html="title"></h4>
                </div>

                <div class="add-object-modal-header-actions">
                    <Button tabindex="-1" @click="handleModalClose" variant="muted"> Cancel </Button>

                    <Button tabindex="-1" v-text="addButtonText" @click.prevent="handleConfirmSelection" />
                </div>
            </div>
        </template>
        <div class="add-object-modal-grid">
            <div class="add-object-modal-grid-item" v-for="elementType in selectableElements" :key="elementType.kind">
                <ElementsIconSelector
                    v-bind="elementType"
                    v-model="elementType.selected"
                    @popover-save="options => handleIconSelectorSave(elementType.name, options)"
                />
            </div>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, onMounted, reactive, watch } from 'vue';
import type { Ref } from 'vue';
import Button from '../elements/Button.vue';
import Modal from '../modals/Modal.vue';
import ModalHeaderRow from '../modals/ModalHeaderRow.vue';
import { capitalize } from '../../utils';
import {
    allAssetTypes,
    allExpenseTypes,
    allIncomeTypes,
    allLiabilityTypes,
    transformSituationTypesForCard,
} from '../../components/situations/data/intake-data';

import type { TOverhaulCategory } from '../../stores/overhaul';
import { useOverhaulStore } from '../../stores/overhaul';

/** The modal for adding a situation after the intitial onboarding screen */
defineOptions();

export interface AddObjectModalProps {
    kind: TOverhaulCategory;
    isOpen: boolean;
    entryMode?: 'simple' | 'standard' | null;
}
const props = withDefaults(defineProps<AddObjectModalProps>(), {
    isOpen: false,
});

const emit = defineEmits<{
    close: [];
    open: [];
    confirm: [];
}>();

const overhaulStore = useOverhaulStore();

const situationTypesOfKind = computed(() => {
    switch (props.kind) {
        case 'assets':
            return transformSituationTypesForCard(allAssetTypes);
        case 'liabilities':
            return transformSituationTypesForCard(allLiabilityTypes);
        case 'income':
            return transformSituationTypesForCard(allIncomeTypes);
        case 'expenses':
            return transformSituationTypesForCard(allExpenseTypes);
        default:
            return [];
    }
});

const allTypesOfKind = reactive(situationTypesOfKind.value);
const selectableElements = ref(situationTypesOfKind.value);

onMounted(() => {
    initSync();
});

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = allTypesOfKind.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach(option => {
            option.value = options.includes(option.name);
        });
    }
}

function handleConfirmSelection() {
    const selectedTypes = selectableElements.value?.filter(e => e.selected);
    selectedTypes.forEach(itemType => {
        overhaulStore.addItem(props.kind, itemType.kind, 'Standard');
    });

    overhaulStore.update();

    handleModalClose();
}

function initSync() {
    selectableElements.value.forEach(element => {
        element.selected = false;
        element.subOptions?.options?.forEach(option => {
            option.value = false;
        });
    });
}

// Modal state logic
const modalIsOpen = ref(false);
function handleModalClose() {
    emit('close');
}
function handleModalOpen() {
    emit('open');
}

const title = computed(() => 'Add ' + props.kind);

const addButtonText = computed(() => 'Confirm & Add');

watch(
    () => props.isOpen,
    newValue => {
        if (newValue) {
            initSync();
        }
    },
);
</script>

<style lang="scss">
@import './AddObjectModal';
</style>
