@import "/assets/css/mixins";

.intake-review-group {
    border-radius: 4px;
    background: var(--color-grey-section);
    color: var(--color-white);
    display: flex;
    padding: 20px;
    align-items: flex-start;
    gap: 20px;
    .intake-review-group-icon {
        color: var(--color-blue-grey);
        width: 100px;
        height: 100px;
        background: rgba(var(--color-blue-grey-rgb), .2);
        border-radius: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex: none;
    }

    .intake-review-group-text {
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
        flex: auto;

        h4 {
            color: var(--color-white);
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.45em;
        }

        .intake-review-group-progress {
            display:flex;
            align-items: stretch;
            justify-content: flex-start;
            flex-direction: column;
            gap: 10px;

            color: var(--color-grey);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.25em;
        }

        .intake-review-group-progress-bar {
            height: 2px;
            width: 100%;
            border-radius: 1px;
            background: rgba(var(--color-white-rgb), .05);
            position: relative;

            &:before {
                content: '';
                display: block;
                position: absolute;
                top:0;
                left:0;
                width: var(--progress-bar-width);
                height: 100%;
                background: linear-gradient(270deg, #055EFA 0%, rgba(5, 94, 250, 0.40) 100%);
            }
        }

        .intake-review-group-instructions {
            color: var(--color-blue-grey);
            font-size: 14px;
            font-weight: 400;
            line-height: 1.25em;
        }
    }

    .intake-review-group-items {
        width: 48.97959184%;
        flex: none;
    }

    .intake-review-group-count {
        color: var(--color-grey);
        font-size: 12px;
        font-weight: 400;
        line-height: 1.45em;
        margin-bottom: 10px;
        padding-left: 10px;
        em {
            color: var(--color-white);
            font-style: normal;
            margin: 0 4px;
            &:first-child, & + em {
                margin-left: 0;
            }
        }
    }

    .intake-review-group-situations {
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 2px;
    }
}
