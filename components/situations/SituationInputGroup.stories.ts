import SituationInputGroup from './SituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SituationInputGroup> = {
    component: SituationInputGroup,
    tags: ['autodocs'],

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    render: (args) => ({
        components: { SituationInputGroup },
        setup() {
            return { args};
        },
        template: `
            <SituationInputGroup v-bind="args" style="width: 400px">
                ${args.default}
            </SituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SituationInputGroup>;

export const Money: Story = {
    args: {
        type: 'currency',
        label: 'Total value of your assets',
        description: 'The aggregate value that represents the collective amount of the individual components',
    }
};

export const Select: Story = {
    args: {
        type: 'select',
        label: 'What is your favorite color?',
        description: 'The hue that, upon initial glance, fills your heart with the greatest level of joy?',
        options: [
            {
                label: 'Red',
                value: 'red'
            },
            {
                label: 'Green',
                value: 'green'
            },
            {
                label: 'Blue',
                value: 'blue'
            }
        ],
    }
};
