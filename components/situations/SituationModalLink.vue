<template>
    <button class="situation-modal-link" @click.prevent="emit('click')" :tabindex="tabindex">
        <SvgIcon v-if="arrowPosition === 'left'" class="situation-modal-link-arrow" shape="arrow-back" width="20" height="20" />
        <span><slot /></span>
        <SvgIcon v-if="arrowPosition === 'right'" class="situation-modal-link-arrow" shape="arrow-forward" width="20" height="20" />
    </button>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Ref } from 'vue';

/** A subtle link with an optional arrow that animates slightly on hover */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'situation-modal-link-' + componentId);

export interface SituationModalLinkProps {
    arrow?: true|false|'left'|'right'|null;
    tabindex?: HTMLInputElement['tabIndex'];
}
const props = withDefaults(defineProps<SituationModalLinkProps>(), {
    tabindex: -1,
});

const arrowPosition = computed(() => {
    if (!props.arrow) return null;
    return (props.arrow != 'left') ? 'right' : 'left';
})

const emit = defineEmits<{
    click: []
}>()
</script>

<style lang="scss">
.situation-modal-link {
    color: var(--color-blue-grey);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.45em;
    gap: 10px;

    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;

    margin-top: 10px;

    transition: color .2s;

    .svg-icon {
        transition: transform .2s;
    }
    
    &:hover {
        color: var(--color-grey);
        .situation-modal-link-arrow {
            &:first-child {
                transform: translateX(-3px);
            }
            &:last-child:not(:first-child) {
                transform: translateX(3px);
            }
        }
    }
}
</style>

