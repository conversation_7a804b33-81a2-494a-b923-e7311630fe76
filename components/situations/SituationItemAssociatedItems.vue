<template>
    <div class="situation-item-associated-items">
        <div class="situation-modal-tabs-column">
            <div class="situation-item-relatd-items-empty" v-if="!visibleItems?.length">
                <p>
                    You haven't added any {{ pluralize(kind) }} yet.
                </p>
            </div>
            <div class="situation-modal-tabs">
                <button class="situation-modal-tab"
                    v-for="item in visibleItems"
                    :key="item.id"
                    :class="{'active': selectedItemId === item.id}"
                    @click="handleTabClick(item.id)"
                >
                    <span v-text="getItemTitleHtml(item)"></span>
                    <SvgIcon shape="keyboard-arrow-right" width="24" height="24" />
                </button>
            </div>

            <Button v-if="canAdd" variant="muted" svg-icon="plus" v-text="'Add ' + itemUnit" @click.prevent="addItem"></Button>

        </div>

        <!-- <div class="situation-modal-input-groups"
            
        >
            <SituationInputGroup
                v-bind="itemTypeInputGroupProps"
                @change="confirmNewItemKind"
            />
        </div> -->
        
        <div class="situation-modal-input-groups"
            v-for="item in visibleItems"
            v-if="visibleItems?.length"
            :key="item.id"
            :class="{'hidden': item.id !== selectedItemId}"
        >
            <SituationInputGroup
                v-for="field in item.fields"
                v-bind="field"
                @change="(payload) => (item.isTemp) ? handleTempFieldChange(item, field.name, payload) : handleFieldChange(item.id, field.name, payload)"
            />

            <Button
                v-if="item.isTemp && !item.fields.some(f => !f.value)"
                class="mt-2.5"
                size="sm"
                @click.prevent="handleConfirmTempItem(item)"
            >
                Confirm
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, onMounted } from 'vue';
import Button from '../elements/Button.vue';
import Modal from '../modals/Modal.vue';
import ModalHeaderRow from '../modals/ModalHeaderRow.vue';
import SituationCard from './SituationCard.vue';
import SituationLineItem from './SituationLineItem.vue';
import type { SituationLineItemProps } from './SituationLineItem.vue';
import SituationInputGroup from './SituationInputGroup.vue';
import type { SituationInputGroupProps } from './SituationInputGroup.vue';
import type { SituationCardProps, SituationCardRow } from './SituationCard.vue';
import SvgIcon from '../images/SvgIcon.vue';
import { capitalize, pluralize, singularize } from '../../utils';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import { useIntakeStore } from '../../stores/intake';
import type { IntakeStandardProperty, IntakeStandardItem, SituationRelationship } from '../../stores/intake';
import { allAssetTypes, allExpenseTypes, allIncomeTypes, allLiabilityTypes, transformSituationTypeForCard } from '../../components/situations/data/intake-data';
import type { Ref } from 'vue';

/** The view for managing a situations associated situations (e.g. an asset's associated liabilities) */
defineOptions();

export interface SituationItemAssociatedItemsProps {
    item: IntakeStandardItem;
    
    // The kind of the item that we're fetching relationships of
    itemKind: IntakeSituationKind;
    
    // The kind of relationships that we're fetching
    kind: IntakeSituationKind;

    name?: string;

    unit?: string;
    title?: string;

    canAdd?: boolean;
}
const props = withDefaults(defineProps<SituationItemAssociatedItemsProps>(), {
    canAdd: true
});

export interface SituationItemAssociatedItemsEmits {
    (e: 'close'): void,
    (e: 'open'): void
}
const emit = defineEmits<SituationItemAssociatedItemsEmits>()

const intakeStore = useIntakeStore();

type SituationItemAssociatedItemsItem = IntakeStandardItem & {isTemp?: boolean};
const tempItems = ref<SituationItemAssociatedItemsItem[]>([]);

const standardAssets = computed(() => intakeStore.standardAssets);
const standardLiabilities = computed(() => intakeStore.standardLiabilities);
const standardExpenses = computed(() => intakeStore.standardExpenses);
const standardIncome = computed(() => intakeStore.standardIncome);
const standardItems = computed(() => intakeStore.standardItems);

const visibleItems = computed<SituationItemAssociatedItemsItem[]>(() => {
    let allKindItems: IntakeStandardProperty;
    let associatedItems: Array<string|number>|undefined;
    let ownedItems: IntakeStandardProperty[] = [];

    let associatedProperty = `associated${capitalize(props.itemKind)}`;

    console.log(associatedProperty);
    switch (props.kind) {
        case 'assets':
            allKindItems = standardAssets.value;
            associatedItems = props.item.associatedAssets;
            ownedItems = Object.values(standardAssets.value).filter(a => a[associatedProperty]?.includes(props.item.id));
            break;
        case 'liabilities':
            allKindItems = standardLiabilities.value;
            associatedItems = props.item.associatedLiabilities;
            ownedItems = Object.values(standardLiabilities.value).filter(a => a[associatedProperty]?.includes(props.item.id));
            break;
        case 'expenses':
            allKindItems = standardExpenses.value;
            associatedItems = props.item.associatedExpenses;
            ownedItems = Object.values(standardExpenses.value).filter(a => a[associatedProperty]?.includes(props.item.id));
            break;
        case 'income':
            allKindItems = standardIncome.value;
            associatedItems = props.item.associatedIncome;
            ownedItems = Object.values(standardIncome.value).filter(a => a[associatedProperty]?.includes(props.item.id));
            break;
        default:
            allKindItems = {};
    }

    let items = associatedItems?.map((id: string) => allKindItems[id] ?? standardItems.value?.[id]) ?? [];
    items = items.concat(ownedItems, tempItems.value);
    return items;
});

// Formatted strings of the "type" of unit (e.g. "commerical real estate")
const itemUnitAdjective = computed(() => {
    let arr = props.title.toLowerCase().split(' / ');
    return (arr.length > 1)
        ? arr[1] + ' ' + arr[0]
        : arr[0]
});

// The "sub-type" if applicable (e.g. "residential" in "residential real estate")
const itemSubType = computed(() => {
    let arr = props.title.toLowerCase().split(' / ');
    return (arr.length > 1)
        ? arr[1]
        : undefined
})

const itemUnit = computed(() => props.unit ?? 'item')

const itemConversationalUnit = computed(() => props.title?.toLowerCase());
const itemConversationalUnitPlural = computed(() => pluralize(props.title?.toLowerCase()));

// The selected item in the standard view
const selectedItemId: Ref<string|null> = ref(null);
const selectedItem = computed(() => {
    return visibleItems.value?.find(i => i.id === selectedItemId.value);
});

// The values of the form fields on standard view
const standardValues: Ref<{
    [k: string]: {
        [k: string]: any
    }
}> = ref({});

// The items to show in the "Simple mode active" info block when in simple mode
const simpleVisibleStandardItems = computed(() => {
    return Object.values(props.standard)
        .map((item) => item.fields.find(f => f.name === 'description')?.value)
        .filter((item) => item)
})

// Switching between items by selecting one on the left
function handleTabClick(id: string) {
    console.log('handleTabClick', id);
    selectedItemId.value = id;
}

function formatItemTitle(item: { id: number }) {
    const itemValues = standardValues.value[item.id];
    return itemValues?.['description'] ? itemValues?.['description'] : `New ${capitalize(props.unit ?? 'item')}`;
}

async function focusFirstItem() {
    await nextTick();
    const firstKey = Object.keys(props.standard)[0];
    const firstItem = props.standard[firstKey];
    if (firstItem) {
        selectedItemId.value = firstItem.id;
    }
}

const assetTypeOptions = computed(() => 
    allAssetTypes.map((type) => ({
        label: type.label,
        value: type.kind
    }))
)

const liabilityTypeOptions = computed(() => 
    allLiabilityTypes.map((type) => ({
        label: type.label,
        value: type.kind
    }))
)

const expenseTypeOptions = computed(() => 
    allExpenseTypes.map((type) => ({
        label: type.label,
        value: type.kind
    }))
)

const incomeTypeOptions = computed(() => 
    allIncomeTypes.map((type) => ({
        label: type.label,
        value: type.kind
    }))
)

const currentKindTypes = computed(() => {
    switch (props.kind) {
        case 'assets':
            return allAssetTypes;
        case 'liabilities':
            return allLiabilityTypes;
        case 'expenses':
            return allExpenseTypes;
        case 'income':
            return allIncomeTypes;
        default:
            return [];
    }
})

const currentKindTypeOptions = computed(() => 
    currentKindTypes.value.map((type) => ({
        label: type.label,
        value: type.kind
    }))
)

const itemTypeInputGroupProps = computed(() => {
    return {
        name: 'vehicleType',
        label: `${capitalize(singularize(props.kind))} type`,
        description: `What type of ${singularize(props.kind)} are you adding?`,
        required: true,
        type: 'select',
        placeholder: 'Select one',
        options: assetTypeOptions.value,
        confirmButtonText: "Add"
    };
})

function addItem() {
    const tempId = String(Date.now()); 
    tempItems.value.push({
        id: tempId,
        isTemp: true,
        fields: [
            {
                label: 'Type',
                name: 'type',
                required: true,
                value: null,
                autoFocus: true,

                type: 'select',
                
                options: currentKindTypeOptions.value
            }
        ]
    });

    handleTabClick(tempId)
}

function handleConfirmTempItem(item: SituationItemAssociatedItemsItem) {
    const type = item.fields.find(f => f.name === 'type')?.value;
    
    const relationship: SituationRelationship = {
        kind: props.itemKind,
        id: props.item.id
    }
    const newId = intakeStore.addSituationAndItem(props.kind, type, relationship);

    console.log(newId);

    // Remove the temporary item
    tempItems.value = tempItems.value.filter((e) => e.id !== item.id);

    // Focus the New Item
    if (newId) {
        selectedItemId.value = newId;
    }
}

function handleFieldChange(itemId: string, fieldName: string, payload: any) {
    console.log('field change', selectedItem.value);
    intakeStore.updateFieldByItemId(props.kind, itemId, fieldName, payload);
}

function handleTempFieldChange(item: SituationItemAssociatedItemsItem, fieldName: string, payload: any) {
    console.log('handleTempFieldChange', item, fieldName, payload)
    const field = item.fields.find(f => f.name === fieldName);
    if (field) field.value = payload;
    if (fieldName === 'type') {
        const selectedKind = currentKindTypes.value.find(o => o.kind === payload)
        item.fields = item.fields.filter(f => f.name !== 'subType');
        if (selectedKind?.subTypes?.length) {
            item.fields.push({
                label: 'Sub-type',
                name: 'subType',
                required: true,
                value: null,
                autoFocus: true,
                type: 'select',
                options: selectedKind.subTypes.map((type) => ({
                    label: type.label,
                    value: type.name
                }))
            })
        } else {
            item.fields = item.fields.filter(f => f.name !== 'subType');
        }
    }
}

function getItemTitleHtml(item: any) {
    const desc = item.fields.find(f => f.name === 'description')?.value;
    return (desc) ? desc : `New ${capitalize(props.unit ?? 'item')}`;
}

function replaceFieldPlaceholders(str?: string) {
    return str?.replace('{{ type }}', itemUnitAdjective.value)
        .replace('{{ subType }}', itemSubType.value?.toLowerCase() ?? '')
        .replace('{{ unit }}', itemUnit.value)
        .replace('{{ units }}', pluralize(itemUnit.value));
}




function handleSituationAddItem(asset: IntakeSituationItem) {
    intakeStore.addSituationItem(asset)
}

function handleSituationRemoveItem(asset: IntakeSituationItem, itemId: number|string) {
    intakeStore.removeSituationItem(asset, itemId)
}

function handleSituationSimpleChange(situationName: string|null|undefined, fieldName: string, payload: any) {
    intakeStore.updateSimpleField('assets', situationName, fieldName, payload)
}

function handleSituationStandardChange(situationName: string|null|undefined, itemId: string|number, fieldName: string, payload: any) {
    intakeStore.updateStandardField('assets', situationName, itemId, fieldName, payload)
}


</script>

<style lang="scss">
.situation-item-associated-items {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    grid-column: span 2 / span 2;
}
.situation-item-relatd-items-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-blue-grey);
    padding: 20px;
    min-height: 100px;
    border: 1px solid var(--color-blue-grey);
    border-radius: 8px;
    text-align: center;
}
</style>
