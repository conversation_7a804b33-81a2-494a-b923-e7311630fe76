<template>
    <div class="situation-item">
        <SituationLineItem
            v-if="displayMode === 'line'"
            v-bind="situationLineItemProps"
            @click="handleModalOpen"
        />
        
        <SituationCard
            v-else
            v-bind="situationCardProps"
            @click="handleModalOpen"
        />

        <Modal :is-open="modalIsOpen" @close="handleModalClose" class="situation-modal">
            <template #header>
                <ModalHeaderRow
                    v-bind="modalHeaderRowArgs"
                    :active-mode="simpleMode ? 'simple' : 'standard'"
                    :mode-type-label="title"
                    :show-mode-switcher="showModeSwitcher"
                    :can-delete="canDelete"
                    @close="handleModalClose"
                    @delete="handleRemoveSituation"
                    @back="handleModalBack"
                    @entry-mode-change="handleEntryModeChange"
                />
            </template>

            <!-- Simple -->
            <div class="situation-modal-content" v-if="simpleMode">
                <div class="situation-modal-input-groups" ref="simpleModeInputs">
                    <SituationInputGroup
                        v-for="(field, index) in transformSimpleFields(simple)"
                        v-bind="field"
                        :auto-focus="index === 0"
                        :footer-disclaimer="index + 1 + ' / ' + simple.length + ' Complete'"
                        @change="(payload) => handleSimpleFieldChange(field.name, payload)"
                    />
                </div>
                <div class="situation-modal-simple-info-column">
                    <div class="situation-modal-mode-info" :class="{'info-expanded': modeInfoExpanded}">
                        <div class="situation-modal-mode-info-heading">
                            <span class="situation-modal-mode-info-icon">
                                <SvgIcon shape="priority" width="14" height="14" />
                            </span>
                            <h5>Simple mode active for <strong v-text="title"></strong></h5>
                            <button tabindex="-1" class="situation-modal-mode-info-toggle" @click="modeInfoExpanded = !modeInfoExpanded">
                                <SvgIcon shape="keyboard-arrow-up" width="20" height="20" />
                            </button>
                        </div>
                        <div class="situation-modal-mode-info-content">
                            <p class="fade">Simple Mode is ideal for giving quick, high-level estimates of your financial status.</p>
                            <template v-if="simpleVisibleStandardItems.length">
                                <p>Switch back to <strong>Standard mode</strong> to unlock the following commercial real estate properties.</p>
                                <div class="situation-modal-mode-info-items">
                                    <div class="situation-modal-mode-info-item" v-for="item in simpleVisibleStandardItems">
                                        {{ item }}
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <p>In order to enter more detailed information about this category, please switch to <strong>Standard mode</strong>.</p>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Standard -->
            <div class="situation-modal-content" v-else>
                <SituationItemAssociatedItems
                    v-if="activeSubSituation"
                    :item="activeSubSituation[0]"
                    :item-kind="props.situationGroup"
                    :kind="activeSubSituation[1]"
                />
                
                <template v-else>
                    <div class="situation-modal-tabs-column">
                        <div class="situation-modal-tabs">
                            <!-- Left column navigation of added standard items -->
                            <button class="situation-modal-tab"
                                v-for="item in standard"
                                :key="item.id"
                                :class="{'active': standardSelectedItem === item.id}"
                                @click="handleStandardItemTabClick(item.id)"
                            >
                                <span v-text="getItemTitleHtml(item)"></span> <!-- New Vehicle -->
                                <SvgIcon shape="keyboard-arrow-right" width="24" height="24" />
                            </button>
                        </div>

                        <Button v-if="standardCanAdd" variant="muted" svg-icon="plus" v-text="'+ Add ' + itemUnit" @click.prevent="addItem"></Button>
                    </div>
                    
                    <div class="situation-modal-input-groups"
                        v-for="item in standard"
                        :key="item.id"
                        :class="{'hidden': item.id !== standardSelectedItem}"
                        :data-id="item.id"
                    >
                        <SituationInputGroup
                            v-for="field in item.fields.filter((f) => !f.hiddenInModal)"
                            v-bind="field"
                            :key="item.id+field.name"
                            @change="(payload) => handleStandardFieldChange(item.id, field.name, field, payload)"
                        />
                        <button class="situation-modal-associated-link"
                            v-if="includeAssociatedItems"
                            v-for="associatedType in associatedTypes"
                            @click.prevent="showAssociatedItems(item, associatedType)"
                        >
                            <div class="situation-modal-associated-link-label">
                                <span v-text="capitalize(associatedType)"></span>
                                <span v-if="getAssociatedCountByType(item, associatedType)" class="associated-link-count" v-text="getAssociatedCountByType(item, associatedType)"></span>
                            </div>
                            <div class="situation-modal-associated-link-description" v-text="getAssociatedTypeMeta(associatedType)?.description"></div>
                            <div class="situation-modal-associated-link-arrow">
                                <SvgIcon shape="keyboard-arrow-right" width="24" height="24" />
                            </div>
                        </button>
                        <button class="situation-modal-delete-item" v-if="standardAsArray.length > 1" @click.prevent="handleRemoveItem(item.id)">
                            <SvgIcon shape="trash" width="20" height="20" />
                            Delete this item
                        </button>
                    </div>
                </template>
            </div>
        </Modal>
    </div>
</template>

<script setup lang="ts">
import { computed, nextTick, ref, onMounted } from 'vue';
import Button from '../elements/Button.vue';
import Modal from '../modals/Modal.vue';
import ModalHeaderRow from '../modals/ModalHeaderRow.vue';
import SituationCard from './SituationCard.vue';
import SituationLineItem from './SituationLineItem.vue';
import type { SituationLineItemProps } from './SituationLineItem.vue';
import SituationInputGroup from './SituationInputGroup.vue';
import SituationItemAssociatedItems from './SituationItemAssociatedItems.vue';
import type { SituationInputGroupProps } from './SituationInputGroup.vue';
import type { SituationCardProps, SituationCardRow } from './SituationCard.vue';
import SvgIcon from '../images/SvgIcon.vue';
import { capitalize, pluralize } from '../../utils';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import { useIntakeStore } from '../../stores/intake';
import type { IntakeStandardProperty } from '../../stores/intake';
import type { Ref } from 'vue';
import { storeToRefs } from 'pinia';

/** A monolithic component to handle displaying and editing a specific situation item */
defineOptions();

export type SituationEntryMode = 'simple' | 'standard';

export interface SituationItemProps {
    cardMode?: SituationCardProps['mode'];
    footerRow?: SituationCardRow;
    iconShape?: TBigIconShapeKey;
    entryMode?: SituationEntryMode;
    name?: string;
    unit?: string;
    displayMode?: 'line' | 'card' | null;
    situationGroup?: IntakeSituationKind;

    title: string;
    simple: SituationInputGroupProps[];
    standard: IntakeStandardProperty;
    template: SituationInputGroupProps[];

    // associatedAssets?: IntakeStandardItem['associatedAssets'];
    // associatedExpenses?: IntakeStandardItem['associatedExpenses'];
    // associatedIncome?: IntakeStandardItem['associatedIncome'];
    // associatedLiabilities?: IntakeStandardItem['associatedLiabilities'];

    newItemLabel?: string;
    includeAssociatedItems?: boolean;
    autoOpen?: boolean;
    standardCanAdd?: boolean
    canDelete?: boolean;
}
const props = withDefaults(defineProps<SituationItemProps>(), {
    cardMode: 'pending',
    displayMode: 'card',
    iconShape: 'money',
    footerRow: { label: 'Onboard Status', percentage: 0},
    entryMode: 'simple',
    standardCanAdd: true,
    includeAssociatedItems: true,
    canDelete: true
})

export interface SituationItemEmits {
    (e: 'simple-change',
        situationName: string|null|undefined,
        fieldName: string,
        payload: any
    ): void,
    (e: 'standard-change', 
        situationName: string|null|undefined,
        itemId: string|number,
        fieldName: string,
        payload: any
    ): void,
    (e: 'entry-mode-change', 
        newMode: SituationEntryMode,
        useAsDefault: boolean
    ): void,
    (e: 'remove-item', itemId: number|string): void,
    (e: 'add-item'): void,
    (e: 'remove-situation'): void,
    (e: 'close'): void,
    (e: 'open'): void
}
const emit = defineEmits<SituationItemEmits>()

onMounted(async () => {
    if (props.autoOpen) {
        await nextTick();
        handleModalOpen();
    }
});

const intakeStore = useIntakeStore();
const {
    standardAssets,
    standardLiabilities,
    standardExpenses,
    standardIncome
} = storeToRefs(intakeStore);

// Whether the modal is in simple mode or standard mode
const simpleMode: Ref<boolean> = ref(props.entryMode === 'simple' ? true : false);

// Whether the Simple/Standard mode info block is expanded
const modeInfoExpanded = ref(true);

const simpleModeInputs = ref<HTMLElement|null>(null);

const showModeSwitcher = computed(() => !['insurance', 'expenses'].includes(props.situationGroup))

// Formatted strings of the "type" of unit (e.g. "commerical real estate")
const itemUnitAdjective = computed(() => {
    let arr = props.title.toLowerCase().split(' / ');
    return (arr.length > 1)
        ? arr[1] + ' ' + arr[0]
        : arr[0]
});

// The "sub-type" if applicable (e.g. "residential" in "residential real estate")
const itemSubType = computed(() => {
    let arr = props.title.toLowerCase().split(' / ');
    return (arr.length > 1)
        ? arr[1]
        : undefined
})

const itemUnit = computed(() => props.unit ?? 'item')

const itemConversationalUnit = computed(() => props.title.toLowerCase());
const itemConversationalUnitPlural = computed(() => pluralize(props.title.toLowerCase()));

// The selected item in the standard view
const standardSelectedItem: Ref<number|string> = ref(0);

// The values of the form fields on standard view
const standardValues: Ref<{
    [k: string]: {
        [k: string]: any
    }
}> = ref({});

// The items to show in the "Simple mode active" info block when in simple mode
const simpleVisibleStandardItems = computed(() => {
    return Object.values(props.standard)
        .map((item) => item.fields.find(f => f.name === 'description')?.value)
        .filter((item) => item)
})

// Switching between items by selecting one on the left
function handleStandardItemTabClick(id: number|string) {
    standardSelectedItem.value = id;
}

function formatItemTitle(item: { id: number }) {
    const itemValues = standardValues.value[item.id];
    return itemValues?.['description'] ? itemValues?.['description'] : `New ${capitalize(props.unit)}`;
}

const allAssociatedItems = computed(() => {
    return Object.values(props.standard).reduce((acc, item) => {
        const obj = intakeStore.getItemAssociatedItems(item, props.situationGroup)

        return {
            assets: [...acc.assets, ...obj.assets],
            liabilities: [...acc.liabilities, ...obj.liabilities],
            income: [...acc.income, ...obj.income],
            expenses: [...acc.expenses, ...obj.expenses]
        }
    }, {assets: [], liabilities: [], income: [], expenses: []})
})

// Modal state logic
const modalIsOpen = ref(false);
function handleModalClose() {
    modalIsOpen.value = false;
    activeSubSituation.value = null;
    emit('close');
}
function handleModalOpen() {
    initModalState();
    emit('open');
    modalIsOpen.value = true;
    focusFirstItem();
}
function initModalState() {
    // if (items.length === 0) {
    //     addItem();
    // }
    simpleMode.value = (props.entryMode === 'simple') ? true : false;
}
function handleModalBack() {
    activeSubSituation.value = null;
}

function handleEntryModeChange(newMode: SituationEntryMode, useAsDefault: boolean) {
    simpleMode.value = newMode === 'simple';
    emit('entry-mode-change', newMode, useAsDefault);
    
    focusFirstItem();
}

async function focusFirstItem() {
    await nextTick();
    if (!simpleMode.value) {
        const firstKey = Object.keys(props.standard)[0];
        const firstItem = props.standard[firstKey];
        if (firstItem) {
            standardSelectedItem.value = firstItem.id;
        }
    } else {
        await nextTick();
        setTimeout(() => {
            simpleModeInputs.value?.querySelector('.collapsible-input-group-overlay')?.click();
        }, 50)
    }
}

// Add a new item to the standard view
function addItem() {
    emit('add-item');
}

function handleRemoveItem(itemId: number|string) {
    emit('remove-item', itemId);
}

function handleRemoveSituation() {
    handleModalClose();
    setTimeout(() => emit('remove-situation'), 100);
}

const modalHeaderRowArgs = computed(() => {
    if (activeSubSituation.value) {
        const title = `<span class="fade">${getItemDescription(activeSubSituation.value[0])} / </span> ${capitalize(activeSubSituation.value[1])}`;
        return {
            title: title,
            showBackButton: true
        }
    } else {
        return (simpleMode.value)
            ? {
                title: props.title
            }
            : {
                title: props.title,
                subTitle: "Standard Entry"
            }
    }
})

function handleSimpleFieldChange(fieldName: string, payload: any) {
    emit('simple-change', props.name, fieldName, payload);
}

function handleStandardFieldChange(itemId: number|string, name: string, field: any, payload: any) {
    field.value = payload;
    emit('standard-change', props.name, itemId, name, payload);
}

function getItemTitleHtml(item: any) {
    if (props.newItemLabel) return props.newItemLabel;

    const desc = item.fields.find(f => f.name === 'description')?.value;
    return (desc) ? desc : `New ${capitalize(props.unit ?? 'item')}`;
}

//   ____        _          ____  _ _               _   _                 
//  / ___| _   _| |__      / ___|(_) |_ _   _  __ _| |_(_) ___  _ __  ___ 
//  \___ \| | | | '_ \ ____\___ \| | __| | | |/ _` | __| |/ _ \| '_ \/ __|
//   ___) | |_| | |_) |_____|__) | | |_| |_| | (_| | |_| | (_) | | | \__ \
//  |____/ \__,_|_.__/     |____/|_|\__|\__,_|\__,_|\__|_|\___/|_| |_|___/

const activeSubSituation = ref<[IntakeStandardItem,IntakeSituationKind]|null>(null);

const associatedTypes = computed(() => 
    ['assets','liabilities','income'].filter((i) => i != props.situationGroup)
)

// TODO: Wire this up; currently hard-coded
// const associatedTypes: IntakeSituationKind[] = ['assets', 'liabilities'];

const associatedTypesMeta = {
    assets: {
        title: 'Assets',
        description: 'Any assets associated with this {{ unit }}'
    },
    income: {
        title: 'Income',
        description: 'Any income generated by this {{ unit }}'
    },
    expenses: {
        title: 'Expenses',
        description: 'Any expenses associated with this {{ unit }}'
    },
    liabilities: {
        title: 'Liabilities',
        description: 'Any debt associated with this {{ unit }}'
    },
}
function getAssociatedTypeMeta(type: IntakeSituationKind) {
    const obj = associatedTypesMeta[type];
    return {
        ...obj,
        description: replaceFieldPlaceholders(obj.description)
    }
}

function getAssociatedCountByType(item: IntakeStandardItem, type: IntakeSituationKind) {    
    const obj = getItemAssociatedItemsCount(item);
    return obj[type];
}

// Any items that have this item in their associated*Type* array
function getItemOwnedItems(item: IntakeStandardItem) {
    const associatedAttribute = `associated${capitalize(props.situationGroup)}`;
    
    return {
        assets: Object.values(standardAssets.value).filter(a => a[associatedAttribute]?.includes(item.id)),
        liabilities: Object.values(standardLiabilities.value).filter(a => a[associatedAttribute]?.includes(item.id)),
        expenses: Object.values(standardExpenses.value).filter(a => a[associatedAttribute]?.includes(item.id)),
        income: Object.values(standardIncome.value).filter(a => a[associatedAttribute]?.includes(item.id))
    }
}

function getItemAssociatedItemsCount(item: IntakeStandardItem) {
    const owned = getItemOwnedItems(item);
    return {
        assets: (item.associatedAssets?.length ?? 0) + (owned.assets?.length ?? 0),
        expenses: (item.associatedExpenses?.length ?? 0) + (owned.expenses?.length ?? 0),
        liabilities: (item.associatedLiabilities?.length ?? 0) + (owned.liabilities?.length ?? 0),
        income: (item.associatedIncome?.length ?? 0) + (owned.income?.length ?? 0),
    }
}

function showAssociatedItems(item: IntakeStandardItem, type: IntakeSituationKind) {
    activeSubSituation.value = [item, type];
}

function getItemDescription(item: IntakeStandardItem) {
    return item.fields.find(f => f.name === 'description')?.value ?? 'New Item';
}

//   _     _              _ _                       _ _           _             
//  | |   (_)_ __   ___  (_) |_ ___ _ __ ___     __| (_)___ _ __ | | __ _ _   _ 
//  | |   | | '_ \ / _ \ | | __/ _ \ '_ ` _ \   / _` | / __| '_ \| |/ _` | | | |
//  | |___| | | | |  __/ | | ||  __/ | | | | | | (_| | \__ \ |_) | | (_| | |_| |
//  |_____|_|_| |_|\___| |_|\__\___|_| |_| |_|  \__,_|_|___/ .__/|_|\__,_|\__, |
//                                                         |_|            |___/ 
const situationLineItemProps = computed<SituationLineItemProps>(() => {
    return {
        title: (simpleMode.value && props.cardMode != 'pending') ? props.title : lineItemTitle.value,
        entryMode: simpleMode.value ? 'simple' : 'standard',
        itemsCount: standardAsArray.value.length ?? 1,
        isCompleted: completeStepsCount.value >= totalStepsCount.value,
    }
})

//    ____              _       _ _           _             
//   / ___|__ _ _ __ __| |   __| (_)___ _ __ | | __ _ _   _ 
//  | |   / _` | '__/ _` |  / _` | / __| '_ \| |/ _` | | | |
//  | |__| (_| | | | (_| | | (_| | \__ \ |_) | | (_| | |_| |
//   \____\__,_|_|  \__,_|  \__,_|_|___/ .__/|_|\__,_|\__, |
//                                     |_|            |___/ 
const situationCardProps = computed(() => {
    return {
        // mode: isComplete.value ? 'complete' : 'pending',
        mode: props.cardMode,
        totalSteps: totalStepsCount.value,
        // TODO: This needs to be dynamic for non-simple entry
        completedSteps: completeStepsCount.value ?? 0,
        kind: (simpleMode.value && props.cardMode != 'pending') ? undefined : props.title,
        title: (simpleMode.value && props.cardMode != 'pending') ? props.title : cardTitle.value,
        iconShape: props.iconShape,
        // footerRow: props.footerRow,
        rows: cardRows.value,
        footerRow: footerRow.value,
        iconBadge: simpleMode.value ? 'Simple' : undefined,
    }
})

const isComplete = computed(() => {
    return props.cardMode === 'complete';
    // return props.completedSteps ?? 0 >= props.totalSteps ?? 1; 
})

const standardAsArray = computed(() => Object.values(props.standard));

const completeStepsCount = computed(() => 
    props.entryMode === 'standard'
        ? standardAsArray.value?.filter(field => field.isComplete === true).length ?? 0
        : props.simple.some(s => s.required && (!s.value && s.value !== 0)) ? 0 : 1
)

const totalStepsCount = computed(() => 
    props.entryMode === 'standard'
        ? standardAsArray.value.length ?? 1
        : 1
)

function totalReducer(acc: number, field: SituationInputGroupProps) {
    let val = 0;
    if (['number-frequency','currency-frequency'].includes(field.type) && field.value.frequency) {
        val = field.value.number ?? 0;
        if (val || val === 0) {
            switch(field.value.frequency) {
                case 'Daily':
                    val *= 365;
                    break;
                case 'Weekly':
                    val *= 52;
                    break;
                case 'Biweekly':
                    val *= 26;
                    break;
                case 'Semimonthly':
                    val *= 24;
                    break;
                case 'Monthly':
                    val *= 12;
                    break;
                case 'Quarterly':
                    val *= 4;
                    break;
                case 'Yearly':
                    break;
            }
        }
    } else {
        val = field.value ?? 0;
    }
    return acc + (val * (field.affectSign === 'negative' ? -1 : 1));
}

const incomeTotal = computed(() => {
    if (simpleMode.value) {
        const income = props.simple.filter(e => e.affects === 'income').reduce(totalReducer, 0)
        return income - props.simple.filter(e => e.affects === 'expenses').reduce(totalReducer, 0)
    } else {
        let total = 0;
        for (let itemKey in props.standard) {
            const assetItem = props.standard[itemKey];
            total += assetItem.fields.filter((field) => field.affects === 'income').reduce(totalReducer, 0)
            total -= assetItem.fields.filter((field) => field.affects === 'expenses').reduce(totalReducer, 0)
        }
        return total;
    }
});

const liabilitiesTotal = computed(() => {
    if (simpleMode.value) {
        return props.simple.filter(e => e.affects === 'liabilities').reduce(totalReducer, 0)
    } else {
        let total = 0;
        for (let itemKey in props.standard) {
            const liabilityItem = props.standard[itemKey];
            total += liabilityItem.fields.filter((field) => field.affects === 'liabilities').reduce(totalReducer, 0)
        }
        return total;
    }
})

const assetsTotal = computed(() => {
    if (simpleMode.value) {
        return props.simple.filter(e => e.affects === 'assets').reduce(totalReducer, 0)
    } else {
        let total = 0;
        for (let itemKey in props.standard) {
            const assetItem = props.standard[itemKey];
            total += assetItem.fields.filter((field) => field.affects === 'assets').reduce(totalReducer, 0)
        }
        return total;
    }
})

const netWorth = computed(() => 
    assetsTotal.value - liabilitiesTotal.value
)

const cardTitle = computed(() => {   
    if (simpleMode.value) return 'Simple ' + props.title;
    
    if (standardAsArray.value.length > 1) return standardAsArray.value.length + ' ' + itemConversationalUnitPlural.value;

    return standardAsArray.value?.[0]?.fields?.find(e => e.name === 'description')?.value ?? props.title;
})

const lineItemTitle = computed(() => {   
    if (simpleMode.value) return props.title;
    
    if (standardAsArray.value.length > 1) return standardAsArray.value.length + ' ' + itemConversationalUnitPlural.value;

    return standardAsArray.value?.[0]?.fields?.find(e => e.name === 'description')?.value ?? props.title;
})

const incomeTotalValue = computed(() => {
    let income = incomeTotal.value ? incomeTotal.value : null;

    const associatedIncome = allAssociatedItems.value?.income;
    const relatedIncomeTotal = associatedIncome?.reduce((total, item) => {
        total = total + item.fields.filter((field) => field.affects === 'income').reduce(totalReducer, 0)
        return total - item.fields.filter((field) => field.affects === 'expenses').reduce(totalReducer, 0)
    }, 0);

    const associatedExpenses = allAssociatedItems.value?.expenses;
    const relatedExpensesTotal = associatedExpenses?.reduce((total, item) => {
        total = total + item.fields.filter((field) => field.affects === 'income').reduce(totalReducer, 0)
        return total - item.fields.filter((field) => field.affects === 'expenses').reduce(totalReducer, 0)
    }, 0);

    if (income !== null || relatedIncomeTotal > 0 || relatedExpensesTotal > 0) {
        return income + relatedIncomeTotal + relatedExpensesTotal;
    } else {
        return undefined;
    }
})

const liabilitiesTotalValue = computed(() => {
    let liabilityAmount = liabilitiesTotal.value ? liabilitiesTotal.value : null;

    const associatedLiabilities = allAssociatedItems.value?.liabilities;
    const relatedLiabilitiesTotal = associatedLiabilities?.reduce((total, item) => {
        return total + item.fields.filter((field) => field.affects === 'liabilities').reduce(totalReducer, 0)
    }, 0);

    if (liabilityAmount !== null || relatedLiabilitiesTotal > 0 ) {
        return liabilityAmount + relatedLiabilitiesTotal;
    } else {
        return undefined;
    }
})

const assetsTotalValue = computed(() => {
    let assetAmount = assetsTotal.value ? assetsTotal.value : undefined;

    const associatedAssets = allAssociatedItems.value?.assets;
    const relatedAssetsTotal = associatedAssets?.reduce((total, item) => {
        return total + item.fields.filter((field) => field.affects === 'assets').reduce(totalReducer, 0)
    }, 0);

    if (assetAmount !== null || relatedAssetsTotal > 0 ) {
        return assetAmount + relatedAssetsTotal;
    } else {
        return undefined;
    }
})

// The rows of data that should populate the card
const cardRows = computed<SituationCardRow[]>(() => [
    {
        label: 'Income',
        value: incomeTotalValue.value,
        suffix: '/yr'
        // value: 987654321
    },
    {
        label: 'Assets',
        value: assetsTotalValue.value,
        // percentage: 0
    },
    {
        label: 'Liabilities',
        value: liabilitiesTotalValue.value,
        // percentage: 0
    }
])

// The data for the footer row of the card
const footerRow = computed<SituationCardRow>(() => (isComplete.value)
    ? {
        label: 'Net Worth',
        value: Math.abs(netWorth.value ?? 0),
        // value: netWorth.value ?? 0,
        // percentage: 0
        changeType: ((netWorth.value ?? 0) >= 0) ? 'positive' : 'negative'
    }
    : {
        label: 'Onboarding progress',
        value: 0,
        // percentage: 0
        // changeType: 'positive'
    }
)

function transformSimpleFields(fields: SituationItemProps['simple']) {
    return fields.map((field) => {
        return {
            ...field,
            label: replaceFieldPlaceholders(field.label),
            description: replaceFieldPlaceholders(field.description)
        }
    })
}

function replaceFieldPlaceholders(str?: string) {
    return str?.replace('{{ type }}', itemUnitAdjective.value)
        .replace('{{ subType }}', itemSubType.value?.toLowerCase() ?? '')
        .replace('{{ unit }}', itemUnit.value)
        .replace('{{ units }}', pluralize(itemUnit.value));
}
</script>

<style lang="scss">
.situation-item {
    display: grid;
}
</style>
