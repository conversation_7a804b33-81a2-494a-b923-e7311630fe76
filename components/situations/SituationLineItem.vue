<template>
    <div class="situation-line-item">
        <h5><span v-html="titleComp"></span> <span v-if="entryMode !== 'simple' && itemsCount && itemsCount > 1" v-text="itemsCount"></span></h5>
        <span class="situation-line-item-type" v-text="entryMode"></span>
        <span class="situation-line-item-status">
            <svg v-if="isCompleted" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask :id="svgId" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="2" y="4" width="16" height="12">
                    <path d="M7.36329 13.3712L3.7354 9.74336L2.5 10.9701L7.36329 15.8333L17.8033 5.39337L16.5766 4.16667L7.36329 13.3712Z" fill="#57719C"/>
                </mask>
                <g :mask="'url(#'+svgId+')'">
                    <rect width="20" height="20" fill="#97ABCC"/>
                </g>
            </svg>

            <svg v-else width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask :id="svgId" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="4" width="14" height="12">
                    <path d="M10.7075 11.5411V9.06378H9.49762V11.5411H10.7075ZM10.7075 13.9897V12.751H9.49762V13.9897H10.7075ZM3.33301 15.8333L10.1026 4.16666L16.8721 15.8333H3.33301Z" fill="white"/>
                </mask>
                <g :mask="'url(#'+svgId+')'">
                    <rect width="20" height="20" fill="#FFBA53"/>
                </g>
            </svg>

        </span>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance} from 'vue';

/** A line item display of a sitatuion; built for the intake form Review screen */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const svgId = computed(() => 'sli-' + componentId);

export interface SituationLineItemProps {
    title: string;
    itemsCount?: number | null;
    entryMode: 'simple' | 'standard';
    isCompleted: boolean;
}
const props = defineProps<SituationLineItemProps>();

const titleComp = computed(() => props.title ? props.title : '<em class="fade">No Description</em>');
</script>

<style lang="scss">
.situation-line-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    padding: 10px;
    gap: 20px;
    border-radius: 4px;
    background: rgba(var(--color-white-rgb), 0.05);
    cursor: pointer;

    color: var(--color-white);
    font-size: 12px;
    font-weight: 400;
    line-height: 1.45px;

    h5 {
        span {
            color: var(--color-grey);
            margin-left: 10px;
        }
    }

    .situation-line-item-type {
        color: var(--color-blue-grey);
        font-size: 10px;
        font-weight: 700;
        line-height: 1em;
        letter-spacing: 1px;
        text-transform: uppercase;
        flex: none;
        margin-left: auto;
        text-align: right;
    }

    .situation-line-item-status {
        width: 20px;
        flex: none;
    }
}
</style>

