<template>
    <div class="situation-card" @click.prevent="handleClick" :class="topLevelClass">
        <div class="situation-card-progress-bar">
            <div
                v-if="steps?.length"
                v-for="step in steps"
                class="situation-card-progress-bar-step"
                :class="{ 'completed': step }"
            ></div>
        </div>

        <div class="situation-card-pending-content" v-if="mode === 'pending'">
            <div class="situation-card-icon">
                <BigIcon v-if="iconShape" :shape="iconShape" />
            </div>

            <div class="situation-card-pending-cta">
                <h6>Please complete</h6>
                <h4 v-text="kind"></h4>
            </div>
        </div>
        
        <template v-else>
            <div class="situation-card-title">
                <div class="situation-card-title-text">
                    <h5 v-if="kind" v-text="kind"></h5>
                    <h4 v-if="title" v-text="title"></h4>
                </div>
                <div class="situation-card-icon">
                    <BigIcon v-if="iconShape" :shape="iconShape" />
                    <span class="situation-card-badge" v-if="iconBadge">
                        <span class="situation-card-badge-content" v-text="iconBadge"></span>
                    </span>
                </div>
            </div>

            <div class="situation-card-list">
                <dl v-for="row in rows">
                    <dt v-text="row.label"></dt>
                    <dd>
                        <span class="fade" v-if="row.percentage" v-text="Math.round(row.percentage * 100) + '%'"></span>
                        <template v-if="row.value || row.value === 0">
                            <span v-text="numberToUSD(row.value, false)"></span>
                            <span class="situation-card-list-row-suffix" v-if="row.suffix" v-text="row.suffix"></span>
                        </template>
                        <span class="fade" v-else>&ndash;</span>
                    </dd>
                </dl>
            </div>
        </template>

        <div class="situation-card-footer" v-if="footerRow">
            <dl>
                <dt v-text="footerRow.label"></dt>
                <dd>
                    <span class="situation-card-footer-percentage" v-if="footerRow.percentage">
                      {Math.round(footerRow.percentage * 100)}<em>%</em>
                    </span>
                    <span v-if="footerRow.changeType && footerRow.value" :class="{'situation-card-footer-change-negative' : footerRow.changeType !== 'positive'}" class="situation-card-footer-change-type"></span>
                    <span v-if="!footerRow.percentage" v-text="footerRowValue"></span>
                </dd>
            </dl>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import BigIcon from '../images/BigIcon.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import { numberToUSD } from '../../utils';

/** A card to group income or liabilities for a specific situation type (eg employ) */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'situation-card-' + componentId);

export interface SituationCardRow {
    label: string;
    value?: number|string|null;
    suffix?: string;
    percentage?: number;
    changeType?: 'positive'|'negative';
}

export interface SituationCardProps {
    totalSteps: number;
    completedSteps: number;

    mode?: 'pending'|'complete';

    kind?: string|null;
    title?: string|null;

    rows?: SituationCardRow[];
    footerRow?: SituationCardRow;
    
    iconShape?: TBigIconShapeKey;
    iconBadge?: string|null;

    hoverable?: boolean;
}
const props = withDefaults(defineProps<SituationCardProps>(), {
    hoverable: true,
    mode: 'complete'
});

const steps = computed(() => {
    const completedSteps = props.completedSteps ?? 0;
    const totalSteps = Math.max(props.totalSteps ?? 0, completedSteps);
    return (totalSteps && totalSteps > 0)
        ? new Array(totalSteps).fill(0).map((_, i) => (i < completedSteps) ? 1 : 0)
        : [];
})

const topLevelClass = computed(() => [
    props.hoverable ? 'situation-card-hoverable' : '',
    'situation-card-m-' + props.mode
]);

const footerRowValue = computed(() => (props.footerRow?.value) ? numberToUSD(props.footerRow.value, false) : '-');

const emit = defineEmits<{
    click: []
}>()

function handleClick() {
    emit('click');
}
</script>

<style lang="scss">
@import "./SituationCard";
</style>
