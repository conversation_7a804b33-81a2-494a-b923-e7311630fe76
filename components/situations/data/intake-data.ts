// noinspection JSUnusedGlobalSymbols

import type { TSvgIconShapeKey } from '../../images/SvgIconShapes';
import type { SituationEntryMode } from '../SituationItem.vue';
import type { SituationInputGroupProps } from '../SituationInputGroup.vue';
import type { TOverhaulItemCategory, TOverhaulSituation } from '../../../stores/overhaul';

export const intakeSituations: TOverhaulSituation[] = [
    {
        id: 2,
        iconShape: 'men-and-women',
        label: 'Married',
        value: 'married',
        selected: false,
    },
    {
        id: 3,
        iconShape: 'document-things',
        label: 'Kids',
        value: 'kids',
        selected: false,
    },
    {
        id: 4,
        iconShape: 'military',
        label: 'Military',
        value: 'military',
        selected: false,
    },
    {
        id: 5,
        iconShape: 'education',
        label: 'Student',
        value: 'student',
        selected: false,
    },
    {
        id: 6,
        iconShape: 'money-3',
        label: 'Investments',
        value: 'investments',
        selected: false,
    },
    {
        id: 7,
        iconShape: 'business',
        label: 'Business Owner',
        value: 'business-owner',
        selected: false,
    },
    {
        id: 8,
        iconShape: 'vacation-house',
        label: 'Retired',
        value: 'retired',
        selected: false,
    },
    {
        id: 9,
        iconShape: 'bank',
        label: 'Government',
        value: 'government',
        selected: false,
    },
];

export interface SituationType {
    iconShape: TSvgIconShapeKey;
    label: string;
    name: string;
    kind: string;
    category: TOverhaulItemCategory; // Asset, Liability
    unit?: string;
    fields: {
        simple: SituationInputGroupProps[];
        standard: SituationInputGroupProps[];
    };

    mode?: SituationEntryMode;
    subTypeName?: string;
    subTypeHeading?: string;
    subTypes?: {
        label: string;
        name: string;
        description?: string;
        iconShape?: TSvgIconShapeKey;
    }[];
    values?: SituationTypeValues;

    deductions?: string[];

    assetKinds?: string[];
    expenseKinds?: string[];
    incomeKinds?: string[];
    liabilityKinds?: string[];
}

export interface SituationTypeValues {
    simple?: { [key: string]: any };
    standard?: { [key: string]: any };
}

export function transformSituationTypesForCard(
    situationTypes: SituationType[],
): ReturnType<typeof transformSituationTypeForCard>[] {
    return situationTypes.map(transformSituationTypeForCard);
}

// Deprecated: previous "card" structure
export function transformSituationTypeForCard(situationType: SituationType): SituationType & {
    subOptions?: {
        heading: string;
        options: { name: string; description?: string; iconShape?: TSvgIconShapeKey; label: string; value: boolean }[];
    };
    selected: boolean;
} {
    if (situationType.subTypes?.length) {
        return {
            ...situationType,
            selected: false,
            subOptions: {
                heading: situationType.subTypeHeading ?? `What type(s) of <em>${situationType.label}</em> do you have?`,
                options: situationType.subTypes.map(subType => ({
                    ...subType,
                    value: false,
                })),
            },
        };
    } else {
        return {
            ...situationType,
            selected: false,
        };
    }
}

export const primaryResidenceAssetData: SituationType = {
    label: 'Primary Residence',
    name: 'primary-residence-asset',
    unit: 'residence',
    iconShape: 'house',
    category: 'Asset',
    kind: 'AssetHome',
    expenseKinds: ['ExpenseHousing'],
    liabilityKinds: ['LiabilityMortgage'],
    fields: {
        simple: [
            {
                name: 'address',
                label: 'Address',
                required: true,
                type: 'address',
            },
            {
                name: 'equityValue',
                label: 'Property value',
                description: 'The estimated or appraised value of the property.',
                required: true,
                affects: 'assets',
                type: 'currency',
            },
            {
                name: 'liabilities',
                label: 'What is your mortgage balance?',
                description: 'How much do you currently owe on your home? If you own outright, leave at $0.',
                required: true,
                affects: 'liabilities',
                type: 'currency',
            },
            {
                name: 'monthly',
                label: 'What is your total monthly payment?',
                description: 'Your gross monthly payment -- including principal, interest, and any escrow.',
                required: true,
                affects: 'income',
                suffix: '/month',
                affectSign: 'negative',
                type: 'currency',
            },
        ],
        standard: [
            {
                name: 'address',
                label: 'Address',
                description: 'The mailing address of your primary residence.',
                required: true,
                type: 'address',
            },
            {
                name: 'equityValue',
                label: 'Estimated Value',
                description: 'The estimated or appraised value of the property.',
                required: true,
                affects: 'assets',
                type: 'currency',
            },
            {
                name: 'owner',
                label: 'Owner',
                description: 'The individual or asset that owns this asset.',
                required: false,
                affects: 'assets',
                type: 'address',
            },
            {
                name: 'acquiredDate',
                label: 'Date Acquired',
                description: 'The date you acquired this property.',
                required: false,
                type: 'date',
            },
        ],
    },
};

export const primaryResidenceExpenseData: SituationType = {
    label: 'Rent: Primary Residence',
    name: 'primary-residence-expense',
    unit: 'residence',
    iconShape: 'house',
    category: 'Expense',
    // Feb 3: Renamed from ExpenseHousing to not conflict with other "housing" expenses
    kind: 'ExpensePrimaryResidence',
    fields: {
        simple: [],
        standard: [
            {
                name: 'amount',
                label: 'Rent payment',
                description: 'How much you pay towards rent?',
                required: true,
                type: 'currency-frequency',
                affects: 'expenses',
                value: {
                    number: null,
                    frequency: 'Monthly',
                },
            },
            {
                name: 'description',
                label: 'Description',
                hiddenInModal: true,
                readonly: true,
                required: false,
                type: 'text',
                value: 'Primary Residence',
            },
            {
                name: 'owner',
                label: 'Owner',
                hiddenInModal: true,
                type: 'text',
                value: null,
            },
        ],
    },
};

export const allAssetTypes: SituationType[] = [
    {
        label: 'Vehicle',
        name: 'asset-vehicle',
        unit: 'vehicle',
        iconShape: 'car',
        category: 'Asset',
        kind: 'AssetVehicle',
        expenseKinds: ['ExpenseAuto'],
        liabilityKinds: ['LiabilityAutoloan'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your vehicles',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your vehicles',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the vehicle (e.g. 2021 Nissan Rogue)',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'vehicleType',
                    label: 'Vehicle type',
                    description: 'What type of vehicle is this loan for?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Car',
                            value: 'Car',
                        },
                        {
                            label: 'Motorcycle',
                            value: 'Motorcycle',
                        },
                        {
                            label: 'Boat',
                            value: 'Boat',
                        },
                        {
                            label: 'Other',
                            value: 'Other',
                        },
                    ],
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this vehicle?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this vehicle',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Personal Property',
        name: 'asset-personal-property',
        unit: 'property',
        iconShape: 'furniture',
        category: 'Asset',
        kind: 'AssetPersonalProperty',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your personal property',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your personal property',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the property',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this property?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this property',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Investment Account',
        name: 'asset-investment-account',
        unit: 'account',
        iconShape: 'money',
        category: 'Asset',
        kind: 'AssetInvestmentAccount',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your investment accounts',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your investment accounts',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'equityValue',
                    label: 'Principal balance',
                    description: 'What is the current balance of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Real Estate',
        name: 'asset-real-estate',
        unit: 'property',
        iconShape: 'bungalow',
        category: 'Asset',
        kind: 'AssetRealEstate',
        expenseKinds: ['ExpenseHousing'],
        liabilityKinds: ['LiabilityMortgage'],
        // subTypeName: 'realEstateType',
        // subTypes: [
        //     {
        //         label: 'Personal',
        //         description: "House, apartment, condo, land, etc.",
        //         name: 'Personal',
        //     },
        //     {
        //         label: 'Business',
        //         description: "Office building, retail spaces, warehouses, industrial, etc.",
        //         name: 'Business',
        //     },
        //     {
        //         label: 'Rental',
        //         description: "Property that you rent out to others.",
        //         name: 'Rental',
        //     },
        //     {
        //         label: 'Other',
        //         description: "Any other property that doesn't match one of the other options.",
        //         name: 'Other',
        //     },
        // ],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description:
                        'The estimated total value of all of your real estate (not including your primary residence)',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description:
                        'The estimated total liabilities on all of your real estate (not including your primary residence)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the real estate property',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'address',
                    label: 'Address',
                    description: 'The address of this real estate property',
                    required: true,
                    type: 'address',
                },
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this property?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this property',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Equipment',
        name: 'asset-equipment',
        unit: 'equipment',
        iconShape: 'lawn-mower',
        category: 'Asset',
        kind: 'AssetEquipment',
        expenseKinds: ['ExpenseLoanPayment'],
        liabilityKinds: ['LiabilityEquipmentLoan'],
        // subTypeName: 'equipmentType',
        // subTypes: [
        //     {
        //         label: 'Personal',
        //         description: "Equipment used for personal activities or hobbies.",
        //         name: 'personal',
        //     },
        //     {
        //         label: 'Business',
        //         description: "Equipment used for business activities.",
        //         name: 'business',
        //     },
        //     {
        //         label: 'Rental',
        //         description: "Equipment primarily used as rental property.",
        //         name: 'rental',
        //     },
        //     {
        //         label: 'Other',
        //         description: "Equipment that doesn't match the other available types.",
        //         name: 'Other',
        //     },
        // ],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your equipment',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your equipment',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the equipment',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this equipment?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this equipment',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Bank Account',
        name: 'asset-bank-account',
        unit: 'account',
        iconShape: 'bank',
        category: 'Asset',
        kind: 'AssetBankAccount',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your bank accounts (checking, savings, etc)',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your bank accounts',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Principal balance',
                    description: 'What is the current balance of the account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'purpose',
                    label: 'Purpose',
                    description: 'What is the purpose of this account?',
                    type: 'text',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    type: 'percentage',
                },
                {
                    name: 'maturityDate',
                    label: 'Date acquired',
                    description: 'The date this account reaches maturity (if applicable).',
                    type: 'date',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'CD & Money Market',
        name: 'asset-cd-money-market',
        unit: 'account',
        iconShape: 'liabilities',
        category: 'Asset',
        kind: 'AssetCDMoneyMarket',
        expenseKinds: ['Expense TYPE'],
        liabilityKinds: ['Liability TYPE'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your CD and Money Market accounts.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your CD and Money Market accounts.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes',
                //     description: "What do you pay annually in taxes for this account?.",
                //     required: true,
                //     affects: 'liabilities',
                //     type: 'currency'
                // },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'IRA',
        name: 'asset-ira',
        unit: 'account',
        iconShape: 'money-temple',
        category: 'Asset',
        kind: 'AssetIRA',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        subTypeName: 'accountType',
        subTypes: [
            {
                label: 'Traditional IRA',
                name: 'Traditional IRA',
            },
            {
                label: 'Roth IRA',
                name: 'Roth IRA',
            },
            {
                label: 'SEP IRA',
                name: 'SEP IRA',
            },
            {
                label: 'SIMPLE IRA',
                name: 'SIMPLE IRA',
            },
        ],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your IRA accounts.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your IRA accounts.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: '401(k)',
        name: 'asset-401k',
        unit: 'account',
        iconShape: 'money-2',
        category: 'Asset',
        kind: 'Asset401k',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityRetirement401k'],
        subTypeName: 'accountType',
        subTypes: [
            {
                label: 'Traditional 401(k)',
                name: 'Traditional',
            },
            {
                label: 'Roth 401(k)',
                name: 'Roth',
            },
            {
                label: 'Solo 401(k)',
                name: 'Solo',
            },
        ],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total value of 401(k) accounts',
                    description: 'The estimated total value of all of your 401(k) accounts.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your 401(k) accounts.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: '529 Plan / CESA',
        name: 'asset-529-cesa',
        unit: 'account',
        iconShape: 'stocks',
        category: 'Asset',
        kind: 'Asset529CESA',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        subTypeName: 'accountType',
        subTypes: [
            {
                label: '529 Plan',
                name: '529 Plan',
            },
            {
                label: 'CESA',
                name: 'CESA',
            },
        ],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total value of 529 Plans / CESA accounts',
                    description: 'The estimated total value of all of your 529 Plans / CESA accounts.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your 529 Plans / CESA accounts.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'UTMA',
        name: 'asset-utma',
        unit: 'account',
        iconShape: 'person-dollar-bubble',
        category: 'Asset',
        kind: 'AssetUTMA',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your UTMA accounts.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your UTMA accounts.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this account?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this account.',
                    type: 'date',
                },
            ],
        },
    },
    {
        label: 'Other',
        name: 'asset-other',
        unit: 'asset',
        iconShape: 'cash',
        category: 'Asset',
        kind: 'AssetOther',
        expenseKinds: ['ExpenseOther'],
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [
                {
                    name: 'equityValue',
                    label: 'Total value',
                    description: 'The estimated total value of all of your other assets.',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'liabilities',
                    label: 'Total liabilities',
                    description: 'The estimated total liabilities on all of your other assets.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the asset.',
                    required: true,
                    type: 'text',
                },
                //
                //   {
                //        TODO: OWNER
                //   },
                //
                {
                    name: 'equityValue',
                    label: 'Equity / Value',
                    description: 'What is the estimated value of this asset?',
                    required: true,
                    affects: 'assets',
                    type: 'currency',
                },
                {
                    name: 'acquiredDate',
                    label: 'Date acquired',
                    description: 'The date you acquired this asset.',
                    type: 'date',
                },
            ],
        },
    },
];

// Sort all type arrays alphabetically by label
allAssetTypes.sort((a, b) => a.label.localeCompare(b.label));
sortSubTypes(allAssetTypes);

export const allIncomeTypes: SituationType[] = [
    {
        iconShape: 'business',
        label: 'Employee',
        name: 'income-employee',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeEmployee',
        assetKinds: ['Asset401k'],
        deductions: [],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Employer Name',
                    description: 'Legal name as it appears on your tax forms.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'jobTitle',
                    label: 'Job Title',
                    description: 'What is your title or role at this job?',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'startDate',
                    label: 'Start date',
                    description: 'When did you start working there?',
                    required: true,
                    type: 'date',
                },
            ],
        },
    },
    {
        iconShape: 'bungalow',
        label: 'Rental',
        name: 'income-rental',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeRental',
        liabilityKinds: ['LiabilityMortgage'],
        expenseKinds: ['ExpenseHousing'],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // }
            ],
        },
    },
    {
        iconShape: 'men-and-women',
        label: 'Alimony',
        name: 'income-alimony',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeAlimony',
        expenseKinds: [],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
    {
        iconShape: 'liabilities',
        label: 'Dividend Interest',
        name: 'income-dividend-interest',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeDividendInterest',
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amountDividend',
                    label: 'Dividend income amount',
                    description: 'How much income does this source generate from dividends?',
                    required: true,
                    affects: 'income',
                    type: 'currency',
                },
                {
                    name: 'amountInterest',
                    label: 'Interest income amount',
                    description: 'How much interest income does this source generate?',
                    required: true,
                    affects: 'income',
                    type: 'currency',
                },
                {
                    name: 'frequency',
                    label: 'Income frequency',
                    description: 'How often do you receive this income?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Daily',
                            value: 'Daily',
                        },
                        {
                            label: 'Weekly',
                            value: 'Weekly',
                        },
                        {
                            label: 'Biweekly',
                            value: 'Biweekly',
                        },
                        {
                            label: 'Semimonthly',
                            value: 'Semimonthly',
                        },
                        {
                            label: 'Monthly',
                            value: 'Monthly',
                        },
                        {
                            label: 'Quarterly',
                            value: 'Quarterly',
                        },
                        {
                            label: 'Yearly',
                            value: 'Yearly',
                        },
                        {
                            label: 'Other',
                            value: 'Other',
                        },
                    ],
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
    {
        iconShape: 'money-1',
        label: 'Royalty',
        name: 'income-royalty',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeRoyalty',
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
    {
        iconShape: 'secure-money',
        label: 'Retirement',
        name: 'income-retirement',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeRetirement',
        assetKinds: ['Asset401k'],
        subTypeName: 'retirementType',
        subTypeHeading: 'What type(s) of <em>Retirement Income</em> do you have?',
        subTypes: [
            {
                label: 'RMD',
                name: 'RMD',
            },
            {
                label: 'Pension',
                name: 'Pension',
            },
            {
                label: 'Social Security',
                name: 'Social Security',
            },
            {
                label: 'Retirement',
                name: 'Retirement',
            },
        ],
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
    {
        iconShape: 'document-things',
        label: 'Child Support',
        name: 'income-child-support',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeChildSupport',
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "expenses",
                //     label: 'Taxes',
                //     description: "What are your annual taxes for this income?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
    {
        iconShape: 'person-dollar-bubble',
        label: 'Other',
        name: 'income-other',
        unit: 'income',
        category: 'Income',
        kind: 'IncomeOther',
        fields: {
            simple: [
                {
                    name: 'value',
                    label: 'Total average {{ type }} income <strong>annually</strong>',
                    description:
                        'The estimated gross income from all of your {{ type }} sources, averaged out annually',
                    affects: 'income',
                    required: true,
                    type: 'currency',
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the income.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'amount',
                    label: 'Gross Income Amount',
                    description: 'How much income does this source generate?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'income',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                // TODO: Removing expense-specific fields from income
                // {
                //     name: "taxes",
                //     label: 'Annual taxes on this income',
                //     description: "How much do you pay in taxes?",
                //     required: true,
                //     affects: 'expenses',
                //     type: 'currency'
                // },
            ],
        },
    },
];

// Sort all type arrays alphabetically by label
allIncomeTypes.sort((a, b) => a.label.localeCompare(b.label));
sortSubTypes(allIncomeTypes);

export const allLiabilityTypes: SituationType[] = [
    {
        iconShape: 'house',
        label: 'Mortgage',
        name: 'liability-mortgage',
        unit: 'mortgage',
        category: 'Liability',
        kind: 'LiabilityMortgage',
        assetKinds: ['AssetRealEstate'],
        expenseKinds: ['ExpenseLoanPayment', 'ExpenseHousing', 'ExpenseInsurance'],
        subTypeName: 'mortgageType',
        subTypeHeading: 'What type(s) of <em>Mortgage Liabilities</em> do you have?',
        subTypes: [
            {
                label: 'Mortgage',
                name: 'mortgage',
            },
            {
                label: 'Home Equity Line of Credit',
                name: 'Home Equity',
            },
        ],
        fields: {
            simple: [
                {
                    name: 'balance',
                    label: 'Principal balance',
                    description: 'The estimated current total balance of your {{ type }} liabilities.',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    // entangled: 'ExpenseHousing:Mortgage',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Mortgage payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the mortgage when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'car',
        label: 'Auto Loan',
        name: 'liability-auto-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilityAutoLoan',
        assetKinds: ['AssetVehicle'],
        expenseKinds: ['ExpenseAuto', 'ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'vehicleType',
                    label: 'Vehicle type',
                    description: 'What type of vehicle is this loan for?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Car',
                            value: 'Car',
                        },
                        {
                            label: 'Motorcycle',
                            value: 'Motorcycle',
                        },
                        {
                            label: 'Boat',
                            value: 'Boat',
                        },
                        {
                            label: 'Other',
                            value: 'Other',
                        },
                    ],
                },
                {
                    name: 'loanType',
                    label: 'Loan type',
                    description: 'What type of loan is this?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Personal',
                            value: 'Personal',
                        },
                        {
                            label: 'Business',
                            value: 'Business',
                        },
                        {
                            label: 'Rental',
                            value: 'Rental',
                        },
                        {
                            label: 'Other',
                            value: 'Other',
                        },
                    ],
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Auto loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'education',
        label: 'Student Loan',
        name: 'liability-student-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilityStudentLoan',
        expenseKinds: ['ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'loanType',
                    label: 'Loan type',
                    description: 'What type of loan is this?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Public',
                            value: 'Public',
                        },
                        {
                            label: 'Private',
                            value: 'Private',
                        },
                    ],
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Student Loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'credit-card',
        label: 'Credit Card',
        name: 'liability-credit-card',
        unit: 'card',
        category: 'Liability',
        kind: 'LiabilityCreditCard',
        assetKinds: [],
        expenseKinds: ['ExpenseOther'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the card.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Credit card payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'lawn-mower',
        label: 'Equipment Loan',
        name: 'liability-equipment-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilityEquipmentLoan',
        assetKinds: ['AssetEquipment'],
        expenseKinds: ['ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'loanType',
                    label: 'Loan type',
                    description: 'What type of loan is this?',
                    required: true,
                    type: 'select',
                    placeholder: 'Select one',
                    options: [
                        {
                            label: 'Personal',
                            value: 'Personal',
                        },
                        {
                            label: 'Business',
                            value: 'Business',
                        },
                        {
                            label: 'Rental',
                            value: 'Rental',
                        },
                        {
                            label: 'Other',
                            value: 'Other',
                        },
                    ],
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Equipment loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'bag',
        label: 'Unsecure Loan',
        name: 'liability-unsecure-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilityUnsecureLoan',
        expenseKinds: ['ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Unsecure loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'secure-money',
        label: 'Secure Loan',
        name: 'liability-secure-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilitySecureLoan',
        assetKinds: ['AssetOther'],
        expenseKinds: ['ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Secure loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'money-temple',
        label: 'Pledge Loan',
        name: 'liability-pledge-loan',
        unit: 'loan',
        category: 'Liability',
        kind: 'LiabilityPledgeLoan',
        assetKinds: ['AssetOther'],
        expenseKinds: ['ExpenseLoanPayment'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the loan.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseLoanPayment',
                    entangledDescription: 'Pledge loan payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'person-dollar',
        label: 'Retirement 401(k)',
        name: 'liability-retirement-401k',
        unit: 'account',
        category: 'Liability',
        kind: 'LiabilityRetirement401k',
        assetKinds: ['AssetOther'],
        expenseKinds: ['ExpenseOther'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the account.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseOther',
                    entangledDescription: '401(k) contributions',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'person-dollar-bubble',
        label: 'Life Insurance',
        name: 'liability-life-insurance',
        unit: 'policy',
        category: 'Liability',
        kind: 'LiabilityLifeInsurance',
        assetKinds: ['AssetOther'],
        expenseKinds: ['ExpenseOther'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the policy.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseOther',
                    entangledDescription: 'Life insurance premiums',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },

    {
        iconShape: 'bank',
        label: 'Other',
        name: 'liability-Other',
        unit: 'liability',
        category: 'Liability',
        kind: 'LiabilityOther',
        assetKinds: ['AssetOther'],
        expenseKinds: ['ExpenseOther'],
        fields: {
            simple: [
                {
                    name: 'original',
                    label: 'Original amount of {{ type }} liabilities',
                    description: 'The estimated total balance of all {{ type }} liabilities when they were opened',
                    required: true,
                    type: 'currency',
                },
                {
                    name: 'balance',
                    label: 'Balance of {{ type }} liabilities',
                    description: 'The estimated current total balance of all of your {{ type }} liabilities',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'minimumPayment',
                    label: 'Minimum / current payment',
                    description: 'The estimated minimum or current payments for your {{ type }} liabilities.',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        frequency: 'Monthly',
                    },
                },
            ],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'Give a brief description or title for the liability.',
                    required: true,
                    type: 'text',
                },
                {
                    name: 'balance',
                    label: 'Account balance',
                    description: 'What is the current balance (e.g. how much do you owe?)',
                    required: true,
                    affects: 'liabilities',
                    type: 'currency',
                },
                {
                    name: 'interestRate',
                    label: 'Interest rate',
                    description: 'What is the interest rate for this account?',
                    required: true,
                    type: 'percentage',
                },
                {
                    name: 'minimumPayment',
                    frequencyName: 'paymentFrequency',
                    label: 'Current payment',
                    description: 'How much do you pay towards this?',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    entangled: 'ExpenseOther',
                    entangledDescription: '"Other" liability payment',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'originalBalance',
                    label: 'Original balance',
                    description: 'What was the balance of the loan when it originated?',
                    required: false,
                    type: 'currency',
                },
                {
                    name: 'originationDate',
                    label: 'Origination date',
                    description: 'The date this liability originated',
                    required: false,
                    type: 'date',
                },
                {
                    name: 'termLength',
                    label: 'Term length',
                    description: 'What is the term length (in months)?',
                    required: false,
                    type: 'number',
                    suffix: ' months',
                },
                {
                    name: 'paymentDate',
                    label: 'Payment date',
                    description: 'When are your payments typically due?',
                    required: false,
                    type: 'date',
                },
            ],
        },
    },
];

// Sort all type arrays alphabetically by label
allLiabilityTypes.sort((a, b) => a.label.localeCompare(b.label));
sortSubTypes(allLiabilityTypes);

export const allExpenseTypes: SituationType[] = [
    {
        iconShape: 'house',
        label: 'Housing',
        name: 'expense-housing',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseHousing',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Housing Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Rent',
                name: 'Rent',
            },
            {
                label: 'Real Estate Taxes',
                name: 'Real Estate Taxes',
            },
            {
                label: 'Maintenance',
                name: 'Maintenance',
            },
            {
                label: 'Other',
                name: 'Other',
            },
            {
                label: 'Mortgage',
                name: 'Mortgage',
            },
            {
                label: 'Electric',
                name: 'Electric',
            },
            {
                label: 'Gas',
                name: 'Gas',
            },
            {
                label: 'Water',
                name: 'Water',
            },
            {
                label: 'Maid',
                name: 'Maid',
            },
            {
                label: 'Lawn',
                name: 'Lawn',
            },
            {
                label: 'HOA Dues',
                name: 'HOA Dues',
            },
            {
                label: 'Security System',
                name: 'Security System',
            },
            {
                label: 'Pool Maintenance',
                name: 'Pool Maintenance',
            },
            {
                label: 'Home Improvements/Repairs',
                name: 'Home Improvements/Repairs',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'secure-money',
        label: 'Insurance',
        name: 'expense-insurance',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseInsurance',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Insurance Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Home Ins Premium',
                name: 'Home',
            },
            {
                label: "Renters' Insurance",
                name: 'Renters',
            },
            {
                label: 'Auto Insurance',
                name: 'Auto',
            },
            {
                label: 'Healthcare Insurance',
                name: 'Healthcare',
            },
            {
                label: 'Life Insurance',
                name: 'Life',
            },
            {
                label: 'Umbrella Insurance',
                name: 'Umbrella',
            },
            {
                label: 'Other (Disability, LTC, etc.)',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'vacation-house',
        label: 'Rental Real Estate',
        name: 'expense-rental-real-estate',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseRentalRealEstate',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Rental Real Estate Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Rental Real Estate Taxes',
                name: 'Taxes',
            },
            {
                label: 'Rental Real Estate Ins',
                name: 'Insurance',
            },
            {
                label: 'Other ',
                name: 'Other ',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'document-things',
        label: 'Childcare / Tuition',
        name: 'expense-childcare-tuition',
        unit: 'tuition',
        category: 'Expense',
        kind: 'ExpenseChildcareTuition',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Childcare / Tuition Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Alimony',
                name: 'Alimony',
            },
            {
                label: 'Child Support',
                name: 'Child Support',
            },
            {
                label: 'Dependent Care',
                name: 'Dependent Care',
            },
            {
                label: 'Education Costs (Tuition, Tutoring, etc.)',
                name: 'Education',
            },
            {
                label: 'Other (Extracurriculars, Supplies, Allowances, etc.)',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'credit-card',
        label: 'Groceries',
        name: 'expense-groceries',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseGroceries',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'bill',
        label: 'Dining Expenses',
        name: 'expense-dining',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseDining',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'money-temple',
        label: 'Savings / Brokerage Contributions',
        name: 'expense-savings-brokerage',
        unit: 'account',
        category: 'Expense',
        kind: 'ExpenseSavingsBrokerage',
        assetKinds: ['AssetOther'],
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Savings / Brokerage Expenses</em> do you have?',
        subTypes: [
            {
                label: '401(k) / 403(b)',
                name: '401(k) / 403(b)',
            },
            {
                label: 'Roth 401(k) / 403(b)',
                name: 'Roth 401(k) / 403(b)',
            },
            {
                label: 'Ret.Plan Savings - After Tax 401(k)',
                name: 'Ret Plan Savings',
            },
            {
                label: 'IRA Savings (Traditional)',
                name: 'IRA Savings (Traditional)',
            },
            {
                label: 'Roth IRA Savings (After Tax)',
                name: 'Roth IRA Savings (After Tax)',
            },
            {
                label: 'Other Retirement Savings',
                name: 'Other Retirement Savings',
            },
            {
                label: 'Education 1 - CESA',
                name: 'Education - CESA',
            },
            {
                label: 'Education 1 - UTMA',
                name: 'UTMA',
            },
            {
                label: 'Cash Savings',
                name: 'Cash Savings',
            },
            {
                label: 'Non-Qualified Savings',
                name: 'Non-Qualified Savings',
            },
            {
                label: 'Bank Charges',
                name: 'Bank Charges',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'money',
        label: 'Subscriptions',
        name: 'expense-subscriptions',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseSubscription',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'transfer',
        label: 'Loan Payment',
        name: 'expense-loan-payment',
        unit: 'payment',
        category: 'Expense',
        kind: 'ExpenseLoanPayment',
        liabilityKinds: ['LiabilityOther'],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'calculator-pencil',
        label: 'Healthcare',
        name: 'expense-healthcare',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseHealthcare',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Healthcare Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Doctor Visits',
                name: 'Doctor Visits',
            },
            {
                label: 'Vitamins',
                name: 'Vitamins',
            },
            {
                label: 'Prescription Drugs',
                name: 'Prescription Drugs',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'car',
        label: 'Auto Expenses',
        name: 'expense-auto',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseAuto',
        assetKinds: ['AssetVehicle'],
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Auto Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Auto Maintenance',
                name: 'Maintenance',
            },
            {
                label: 'Oil Changes',
                name: 'Oil Changes',
            },
            {
                label: 'Fuel',
                name: 'Fuel',
            },
            {
                label: 'Tolltag',
                name: 'Tolltag',
            },
            {
                label: 'Vehicle Tax',
                name: 'Taxes',
            },
            {
                label: 'Inspections',
                name: 'Inspections',
            },
            {
                label: 'Registration',
                name: 'Registration',
            },
            {
                label: 'Other',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'stocks',
        label: 'Charitable Donations',
        name: 'expense-donation',
        unit: 'donation',
        category: 'Expense',
        kind: 'ExpenseDonation',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'dry-cleaning',
        label: 'Clothing & Cosmetics',
        name: 'expense-clothing-cosmetic',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseClothingCosmetic',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Clothing & Cosmetics Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Clothing',
                name: 'Clothing',
            },
            {
                label: 'Dry Cleaning',
                name: 'Dry Cleaning',
            },
            {
                label: 'Jewelry',
                name: 'Jewelry',
            },
            {
                label: 'Hair Care',
                name: 'Hair Care',
            },
            {
                label: 'Nails',
                name: 'Nails',
            },
            {
                label: 'Make-Up',
                name: 'Make-Up',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'pool',
        label: 'Leisure & Recreation',
        name: 'expense-leisure-recreation',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseLeisureRecreation',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Leisure & Recreation Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Streaming Subscriptions',
                name: 'Streaming Subscriptions',
            },
            {
                label: 'Newspaper',
                name: 'Newspaper',
            },
            {
                label: 'Gym',
                name: 'Gym',
            },
            {
                label: 'Entertainment',
                name: 'Entertainment',
            },
            {
                label: 'Memberships',
                name: 'Memberships',
            },
            {
                label: 'Travel',
                name: 'Travel',
            },
            {
                label: 'Other (Hobbies, etc.)',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'bag',
        label: 'Taxes',
        name: 'expense-taxes',
        unit: 'tax',
        category: 'Expense',
        kind: 'ExpenseTaxes',
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Tax Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Federal Tax Withholding/Est. Payments',
                name: 'Federal Tax Withholding/Est. Pmts',
            },
            {
                label: 'Commission Tax Withholding',
                name: 'Commission Tax Withholding',
            },
            {
                label: 'Bonus Withholding',
                name: 'Bonus Withholding',
            },
            {
                label: 'State/ Local Inc. Tax Withholding',
                name: 'State/ Local Inc. Tax Withholding',
            },
            {
                label: 'State/ Local Inc. Commission Tax',
                name: 'State/ Local Inc. Commission Tax',
            },
            {
                label: 'State/ Local Inc. Bonus Tax',
                name: 'State/ Local Inc. Bonus Tax',
            },
            {
                label: 'Social Security',
                name: 'Social Security',
            },
            {
                label: 'Social Security - Commission',
                name: 'Social Security - Commission',
            },
            {
                label: 'Social Security - Bonus',
                name: 'Social Security - Bonus',
            },
            {
                label: 'Medicare',
                name: 'Medicare',
            },
            {
                label: 'Medicare - Commission',
                name: 'Medicare - Commission',
            },
            {
                label: 'Medicare - Bonus',
                name: 'Medicare - Bonus',
            },
            {
                label: 'Estimated Tax Payments',
                name: 'Estimated Tax Payments',
            },
            {
                label: 'SS/Pension Tax Withholdings',
                name: 'SS/Pension Tax Withholdings',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'business',
        label: 'Business',
        name: 'expense-business',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseBusiness',
        assetKinds: ['AssetOther'],
        liabilityKinds: ['LiabilityOther'],
        subTypeName: 'expenseType',
        subTypeHeading: 'What type(s) of <em>Business Expenses</em> do you have?',
        subTypes: [
            {
                label: 'Business Overhead',
                name: 'Business Overhead',
            },
            {
                label: 'Business Taxes',
                name: 'Business Taxes',
            },
            {
                label: 'Employee Benefit Expenses',
                name: 'Employee Benefit Expenses',
            },
            {
                label: 'Other',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'money-3',
        label: 'Employee Benefit',
        name: 'expense-other',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseEmployeeBenefit',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
    {
        iconShape: 'money-3',
        label: 'Other',
        name: 'expense-other',
        unit: 'expense',
        category: 'Expense',
        kind: 'ExpenseOther',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'amount',
                    label: 'Expense amount',
                    required: true,
                    type: 'currency-frequency',
                    affects: 'expenses',
                    value: {
                        number: null,
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'owner',
                    label: 'Owner',
                    required: true,
                    type: 'text',
                    value: 'Monthly',
                },
            ],
        },
    },
];

// Sort all type arrays alphabetically by label
allExpenseTypes.sort((a, b) => a.label.localeCompare(b.label));
sortSubTypes(allExpenseTypes);

export const allInsuranceTypes: SituationType[] = [
    {
        iconShape: 'secure-money',
        label: 'PERM - Life',
        name: 'insurance-perm-life',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsurancePermLife',
        assetKinds: [],
        subTypeName: 'insuranceType',
        subTypeHeading: 'What type(s) of <em>PERM - Life Insurance</em> do you have?',
        subTypes: [
            {
                label: 'Whole',
                name: 'Whole',
            },
            {
                label: 'VUL',
                name: 'VUL',
            },
            {
                label: 'V2D',
                name: 'V2D',
            },
            {
                label: 'Other',
                name: 'Other',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'companyName',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'deathBenefit',
                    label: 'Death Benefit',
                    required: true,
                    type: 'currency',
                    value: null,
                },
                {
                    name: 'cashValue',
                    label: 'Cash Value',
                    required: false,
                    type: 'currency',
                    value: null,
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },

    {
        iconShape: 'person-dollar',
        label: 'TERM - Life',
        name: 'insurance-term-life',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsuranceTermLife',
        subTypeName: 'lifeInsuranceType',
        subTypeHeading: 'What type(s) of <em>TERM - Life Insurance</em> do you have?',
        subTypes: [
            {
                label: 'Group - Life',
                name: 'Group - Life',
            },
            {
                label: 'Group - Supplemental',
                name: 'Group - Supplemental',
            },
            {
                label: 'Group - Spousal',
                name: 'Group - Spousal',
            },
            {
                label: 'Group - Child',
                name: 'Group - Child',
            },
            {
                label: 'Individual',
                name: 'Individual',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'companyName',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'deathBenefit',
                    label: 'Death Benefit',
                    required: true,
                    type: 'currency',
                    value: null,
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },

    {
        iconShape: 'disability',
        label: 'Disability',
        name: 'insurance-disability',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsuranceDisability',
        subTypeName: 'disabilityInsuranceType',
        subTypeHeading: 'What type(s) of <em>Disability Insurance</em> do you have?',
        subTypes: [
            {
                label: 'Group - Short',
                name: 'Group - Short',
            },
            {
                label: 'Group - Long',
                name: 'Group - Long',
            },
            {
                label: 'Individual',
                name: 'Individual',
            },
            {
                label: 'Business Overhead',
                name: 'Business Overhead',
            },
        ],
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'company',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'waitingPeriod',
                    label: 'Waiting Period',
                    required: true,
                    type: 'number',
                    suffix: ' days',
                    value: null,
                },
                {
                    name: 'benefit',
                    label: 'Benefit',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },

    {
        iconShape: 'hospital',
        label: 'LTC',
        name: 'insurance-ltc',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsuranceLTC',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'companyName',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'waitingPeriod',
                    label: 'Waiting Period',
                    required: true,
                    type: 'number',
                    suffix: ' days',
                    value: null,
                },
                {
                    name: 'benefit',
                    label: 'Benefit',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },

    {
        iconShape: 'person-dollar-bubble',
        label: 'AD&D',
        name: 'insurance-add',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsuranceADD',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'companyName',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'deathBenefit',
                    label: 'Death Benefit',
                    required: true,
                    type: 'currency',
                    value: null,
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },

    {
        iconShape: 'money',
        label: 'Other',
        name: 'insurance-other',
        unit: 'policy',
        category: 'Insurance',
        kind: 'InsuranceOther',
        fields: {
            simple: [],
            standard: [
                {
                    name: 'description',
                    label: 'Description',
                    description: 'A descrition or friendly name for this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'companyName',
                    label: 'Insurance Company',
                    description: 'The company associated with this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'owner',
                    label: 'Policy Owner',
                    description: 'The person or entitity who owns this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'insured',
                    label: 'Insured',
                    description: 'The name of the person or entitity which is insured by this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'premium',
                    label: 'Premium',
                    description: 'The premium or costs for this insurance policy',
                    required: true,
                    type: 'currency-frequency',
                    value: {
                        frequency: 'Monthly',
                    },
                },
                {
                    name: 'beneficiary',
                    label: 'Beneficiary',
                    description: 'The name of the person or entitity which is the beneficiary of this policy',
                    required: true,
                    type: 'text',
                    value: null,
                },
                {
                    name: 'deathBenefit',
                    label: 'Death Benefit',
                    required: true,
                    type: 'currency',
                    value: null,
                },
                {
                    name: 'anniversary',
                    label: 'Contract Anniversary',
                    required: false,
                    type: 'date',
                    value: null,
                },
                {
                    name: 'policyNumber',
                    label: 'Policy #',
                    required: false,
                    type: 'text',
                    value: null,
                },
            ],
        },
    },
];

// Sort all type arrays alphabetically by label
allInsuranceTypes.sort((a, b) => a.label.localeCompare(b.label));
sortSubTypes(allInsuranceTypes);

export const allTypes: SituationType[] = [
    ...allAssetTypes,
    ...[primaryResidenceAssetData],
    ...[primaryResidenceExpenseData],
    ...allLiabilityTypes,
    ...allIncomeTypes,
    ...allExpenseTypes,
    ...allInsuranceTypes,
];

function sortSubTypes(arr: { subTypes?: { label: string }[] }[]) {
    arr.forEach(type => {
        if (type.subTypes) {
            type.subTypes.sort((a, b) => a.label.localeCompare(b.label));
        }
    });
}
