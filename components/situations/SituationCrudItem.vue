<template>
    <div class="situation-crud-item" @click.prevent="handleItemClick">
        <div class="situation-crud-item-bar">
            <FormsCheckboxInput
                v-if="showDeleteCheckbox"
                size="xs"
                @click.stop
                v-model="isSelected"
                @update:modelValue="handleSelectionChange"
            />
            <div class="situation-crud-item-label">
                <div class="situation-crud-item-status">
                    <SvgIcon shape="checkmark" width="14" height="14" v-if="itemIsComplete" />
                    <SvgIcon shape="close" width="14" height="14" v-else />
                </div>
                <div class="situation-crud-item-text">
                    <span v-for="labelItem in labelComp" v-html="labelItem"></span>
                </div>
            </div>

            <div class="situation-crud-item-stats">
                <dl
                    v-for="kpi in lineItem.kpis"
                    v-if="lineItem.kpis ?? false"
                    :key="kpi.label"
                    :class="{ fade: kpi.fade }"
                >
                    <dd>
                        <span
                            v-if="kpi.label === 'Net Worth' && kpi.direction !== 'neg'"
                            class="overhaul-line-item-kpi-sign"
                            >+</span
                        >
                        <span
                            v-else-if="kpi.direction === 'neg'"
                            class="overhaul-line-item-kpi-sign overhaul-line-item-kpi-sign-negative"
                            >-</span
                        >
                        <span class="overhaul-line-item-kpi-prefix" v-if="kpi.formatCurrency">$</span>
                        <span class="overhaul-line-item-kpi-value" v-text="kpi.formattedValue ?? kpi.value"></span>
                        <span class="overhaul-line-item-kpi-suffix" v-if="kpi.suffix" v-text="kpi.suffix"></span>
                    </dd>
                    <dt v-text="kpi.label"></dt>
                </dl>
            </div>
        </div>
    </div>

    <LineItemModal
        @close="handleModalClose"
        :kpis="modalKpis"
        :typeLabel="item.kind"
        :nameLabel="item.entryMode === 'simple' ? 'Simple' : itemDescription"
    >
        <SituationInputGroup
            v-for="field in item.fields"
            v-bind="field"
            @change="payload => handleFieldChange(item, field, payload)"
        />
    </LineItemModal>

    <PostOnboardingLineItemModal :is-open="modalIsOpen" :item="item" @close="handleModalClose" />
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import { abbreviateNumber, transformPopulatedItemToLineItem } from '~/utils';
import LineItemModal from '../modals/LineItemModal.vue';
import SituationInputGroup from '../situations/SituationInputGroup.vue';
import FormsCheckboxInput from '../forms/CheckboxInput.vue';
import PostOnboardingLineItemModal from '../modals/PostOnboardingLineItemModal.vue';

import pkg from 'lodash';
import type { TOverhaulLineItem } from '~/stores/overhaul';
const { debounce } = pkg;

/** A line item for a situation item to display on the object overview pages (e.g. /client/dashboard/assets) */
defineOptions();
export interface SituationCrudItemProps {
    item: TPopulatedItem;
    showDeleteCheckbox?: boolean;
    selectedItems?: string[];
}
const props = withDefaults(defineProps<SituationCrudItemProps>(), {
    showDeleteCheckbox: false,
    selectedItems: () => [],
});
const emit = defineEmits(['selection-change']);

const isSelected = ref(false);

watch(
    () => props.selectedItems,
    newVal => {
        if (!newVal?.includes(props.item.id)) {
            isSelected.value = false;
        }
    },
);

function handleSelectionChange(value: boolean) {
    isSelected.value = value;
    emit('selection-change', props.item.id, value);
}

const lineItem = computed(() => transformPopulatedItemToLineItem(props.item));
const itemDescription = computed(
    () => lineItem.value.description ?? '<em>New ' + (props.item.unit ?? 'item') + '</em>',
);

const labelComp = computed(() => {
    return [lineItem.value.kindLabel, itemDescription.value];
});

const itemIsComplete = computed(() => (props.item.progress?.percentage ?? 0) >= 1);

function formatValue(value: string | number): string {
    if (typeof value === 'number') {
        // return value.toLocaleString('en-US', {style: 'currency', currency: 'USD'});
        return (value < 0 ? '-' : '') + abbreviateNumber(Math.abs(value), true);
    }
    return value;
}

// TODO: Rework this for OverhaulStore
const modalKpis = computed(() => [
    {
        label: 'value',
        value: 1337,
        // value: totals.value.assets ?? 0,
    },
    {
        label: 'Liability',
        value: 1337,
        // value: totals.value.liabilities ?? 0,
    },
    {
        label: 'Equity',
        value: 1337,
        // value: totals.value.equity,
    },
]);

const modalIsOpen = ref(false);
function handleItemClick() {
    modalIsOpen.value = true;
}
function handleModalClose() {
    modalIsOpen.value = false;
}
</script>

<style lang="scss">
@import './SituationCrudItem';
</style>
