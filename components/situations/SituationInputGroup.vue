<template>
    <div class="situation-input-group">
        <component
            v-if="dynamicComponent"
            :is="dynamicComponent"
            v-bind="$props"
            @change="handleChange"
        />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, defineAsyncComponent, ref } from 'vue';
import type { Component, Ref } from 'vue';
import CollapsibleInputGroup from '../forms/CollapsibleInputGroup.vue';
import type { NumberSituationInputGroupProps } from './input-groups/NumberSituationInputGroup.vue';
import type { SelectSituationInputGroupProps } from './input-groups/SelectSituationInputGroup.vue';
import type { CheckSituationInputGroupProps } from './input-groups/CheckSituationInputGroup.vue';
import type { TextSituationInputGroupProps } from './input-groups/TextSituationInputGroup.vue';
import type { NumberFrequencySituationInputGroupProps } from './input-groups/NumberFrequencySituationInputGroup.vue';
import type { PercentageSituationInputGroupProps } from './input-groups/PercentageSituationInputGroup.vue';
import type { TextInputProps } from '../forms/TextInput.vue';
import type {LineItem, LineItemKey} from "~/types/base";

/** A controlled input group for a single field or group of fields to be used on situation card modals */
defineOptions();

export type SituationItemInputType = 'number' | 'currency' | 'select' 
    | 'text' | 'textarea' | 'single-select' | 'multi-select'
    | 'currency-duration'
    | 'percentage' | 'date' | 'address' | 'toggle' | 'nested';

export type NetWorthComponentType = 'assets' | 'liabilities' | 'income' | 'expenses';

export interface BaseSituationInputGroupProps {
  label: string;
  name: string;
  frequencyName?: 'frequency' | 'paymentFrequency';
  description?: string;
  required?: boolean;
  value?: any;
  defaultValue?: any;
  autoFocus?: boolean;
  readonly?: boolean;
  hiddenInModal?: boolean;

  suffix?: TextInputProps['suffix'];
  svgIcon?: TextInputProps['svgIcon'];
  min?: TextInputProps['min'];
  max?: TextInputProps['max'];

  affects?: NetWorthComponentType;
  affectSign?: 'positive' | 'negative';

  footerButtons?: typeof CollapsibleInputGroup['footerButtons'];
  footerDisclaimer?: typeof CollapsibleInputGroup['footerDisclaimer'];

  confirmButtonText?: typeof CollapsibleInputGroup['confirmButtonText'];

  type: SituationItemInputType;
  
  // If the given field should be "entangled" with another field or type (e.g. a monthly payment for a loan)
  entangled?: string;
  
  // When an entangled field is created, what should the "description" be (assuming it's an expense) 
  entangledDescription?: string;
}

export type SituationInputGroupChildProps = 
    ({ type: NumberSituationInputGroupProps['type'] } & Omit<NumberSituationInputGroupProps, 'type'>)
    | ({ type: CheckSituationInputGroupProps['type'] } & Omit<CheckSituationInputGroupProps, 'type'>)
    | ({ type: TextSituationInputGroupProps['type'] } & Omit<TextSituationInputGroupProps, 'type'>)
    | ({ type: SelectSituationInputGroupProps['type'] } & Omit<SelectSituationInputGroupProps, 'type'>)
    | ({ type: PercentageSituationInputGroupProps['type'] } & Omit<PercentageSituationInputGroupProps, 'type'>)
    | ({ type: NumberFrequencySituationInputGroupProps['type'] } & Omit<NumberFrequencySituationInputGroupProps, 'type'>)

// An aggregate of all the possible props for the different types of input groups
// export type SituationInputGroupChildProps = 
//     CheckSituationInputGroupProps |
//     NumberSituationInputGroupProps |
//     TextSituationInputGroupProps |
//     SelectSituationInputGroupProps;

export type SituationInputGroupProps = BaseSituationInputGroupProps & SituationInputGroupChildProps;

const props = defineProps<SituationInputGroupProps>();

const emit = defineEmits<{
    change: [any],
}>()

const components: {[k: string]: Component} = {
    'number' : defineAsyncComponent(() => import('./input-groups/NumberSituationInputGroup.vue')),
    'currency' : defineAsyncComponent(() => import('./input-groups/NumberSituationInputGroup.vue')),
    'select' : defineAsyncComponent(() => import('./input-groups/SelectSituationInputGroup.vue')),
    'single-select' : defineAsyncComponent(() => import('./input-groups/CheckSituationInputGroup.vue')),
    'multi-select' : defineAsyncComponent(() => import('./input-groups/CheckSituationInputGroup.vue')),
    'text' : defineAsyncComponent(() => import('./input-groups/TextSituationInputGroup.vue')),
    'textarea' : defineAsyncComponent(() => import('./input-groups/TextSituationInputGroup.vue')),

    // TODO: These need to be implemented with custom components
    'percentage' : defineAsyncComponent(() => import('./input-groups/PercentageSituationInputGroup.vue')),
    'date' : defineAsyncComponent(() => import('./input-groups/TextSituationInputGroup.vue')),

    'address' : defineAsyncComponent(() => import('./input-groups/TextSituationInputGroup.vue')),
    'number-frequency': defineAsyncComponent(() => import('./input-groups/NumberFrequencySituationInputGroup.vue')),
    'currency-frequency': defineAsyncComponent(() => import('./input-groups/NumberFrequencySituationInputGroup.vue')),

    // 'toggle' : defineAsyncComponent(() => import('./input-groups/ SituationInputGroup.vue')),
    // 'nested' : defineAsyncComponent(() => import('./input-groups/ SituationInputGroup.vue')),
}

const dynamicComponent = computed(() => {
    return (components.hasOwnProperty(props.type))
        ? components[props.type]
        : null;
});

function handleChange(value: any) {
    emit('change', value);
}
</script>
