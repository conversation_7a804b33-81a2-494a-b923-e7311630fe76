import IntakeReviewGroup from './IntakeReviewGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { allAssetTypes, transformSituationTypeForCard } from '../../components/situations/data/intake-data';
import { ref } from 'vue';

const meta: Meta<typeof IntakeReviewGroup> = {
    component: IntakeReviewGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    render: (args) => ({
        components: { IntakeReviewGroup },
        setup() {
            const cardData = transformSituationTypeForCard(
                allAssetTypes[Math.floor(Math.random() * allAssetTypes.length)]
            );

            return {
                args: {
                    ...args,
                    situations: [
                        {
                            cardMode: 'pending',
                            totalSteps: 1,
                            completedSteps: 0,
                            kind: cardData.label,
                            name: cardData.name,
                            unit: cardData.unit ?? undefined,
                            iconShape: cardData.iconShape,
                            title: cardData.label,
                            entryMode: 'simple',
                            simple: cardData.fields.simple,
                            standard: {1: {id: 1, isComplete: false, fields: cardData.fields.standard}},
                            template: cardData.fields.standard.map(e => ({...e})),
                            footerRow: { label: 'Onboard Status', percentage: 0},
                            displayMode: 'line',
                        }
                    ]
                }
            };
        },
        template: `<IntakeReviewGroup v-bind="args" />`,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof IntakeReviewGroup>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        svgShape: 'home',
        label: 'Income',
        completion: 42,
        instructions: 'Please review and complete the required fields for every income item you provided.',
        tempSituations: [
            {
                title: "Employer Income",
                entryMode: "simple",
                itemsCount: 1,
                isCompleted: true
            },
            {
                title: "Rental Income",
                entryMode: "standard",
                itemsCount: 2,
                isCompleted: false
            }
        ],
    }
};
