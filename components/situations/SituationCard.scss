@import "/assets/css/mixins";

.situation-card {
    border-radius: 4px;
    background: rgba(var(--color-blue-grey-rgb), 0.20);
    color: var(--color-white);
    display: flex;
    align-items: stretch;
    justify-content: space-between;
    flex-direction: column;

    &.situation-card-hoverable {
        cursor: pointer;
        transition: background .2s;
        &:hover {
            background: rgba(var(--color-blue-grey-rgb), 0.25);
        }
    }

    .situation-card-progress-bar {
        padding: 4px;
        height: 10px;
        display: grid;
        gap: 4px;
        flex: none;
        @include grid-columns(2, 10);
        .situation-card-progress-bar-step {
            height: 2px;
            background: rgba(var(--color-white-rgb), .2);
            &.completed {
                background: var(--color-blue);
            }
        }
    }

    .situation-card-title {
        min-height: 90px;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 20px;
        gap: 10px;
        .situation-card-title-text {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            gap: 4px;
            h5 {
                color: var(--color-grey);
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.45em;
            }
            h4 {
                color: var(--color-white);
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                line-height: 1.45em;
            }
        }
    }

    .situation-card-icon {
        flex: none;
        width: 50px;
        height: 50px;
        border-radius: 25px;
        background: var(--color-blue-grey);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        svg, img {
            width: 30px;
            height: 30px;
        }
    }

    .situation-card-badge {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;

        display: flex;
        align-items: center;
        justify-content: center;
        .situation-card-badge-content {
            background: var(--color-blue);
            color: var(--color-white);
            font-size: 10px;
            font-weight: 700;
            line-height: 1em;
            letter-spacing: 1px;
            text-transform: uppercase;
            padding: 2px 4px;
            border-radius: 2px;
            text-align: center;
            white-space: nowrap;
        }
    }

    .situation-card-pending-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        padding: 25px 20px;
        gap: 10px;
        .situation-card-icon {
            background: var(--color-blue);
        }
        .situation-card-pending-cta {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;

            h6 {
                color: var(--color-blue-grey);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.45em;
                letter-spacing: 1.2px;
                text-transform: uppercase;
            }
            h5 {
                color: var(--primary-white);
                font-size: 18px;
                font-weight: 700;
                line-height: 1.5em;
            }
            h4 {
                color: var(--color-grey);
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.45em;
            }
        }
    }

    .situation-card-list, .situation-card-footer {
        dl {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 20px;
            min-height: 28px;
        }
        dt {
            flex: none;
            color: var(--color-blue-grey);
            font-size: 10px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.125em;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        dd {
            color: var(--color-grey);
            display: flex;
            align-items: center;
            justify-content: flex-end;
            text-align: right;
            font-size: 14px;
            font-weight: 700;
            line-height: 1.45em;
            gap: 10px;
            .fade {
                color: var(--color-grey);
                opacity: .5;
            }
            .situation-card-list-row-suffix {
                color: var(--color-grey);
                opacity: .5;
                margin-left: -10px;
            }
            .situation-card-footer-percentage {
                font-weight: 400;
                opacity: .5;
                em {
                    font-style: normal;
                }
            }
            .situation-card-footer-change-type {
                width: 15px;
                height: 14px;
                background-image: url("data:image/svg+xml,%3Csvg width='15' height='14' viewBox='0 0 15 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_7513_13062' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='3' y='2' width='9' height='10'%3E%3Cpath d='M6.87142 11.3686V7.27599H3.25V6.35216H6.88989V2.3335H7.88763V6.35216H11.4167V7.27599H7.88763V11.3686H6.87142Z' fill='white'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_7513_13062)'%3E%3Crect x='0.332031' width='14' height='14' fill='%2302BEBF'/%3E%3C/g%3E%3C/svg%3E%0A");
                background-size: contain;
                &.situation-card-footer-change-negative {
                    background-image: url("data:image/svg+xml,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cmask id='mask0_4492_17835' style='mask-type:alpha' maskUnits='userSpaceOnUse' x='5' y='10' width='14' height='3'%3E%3Cpath d='M11.2081 12.5L11.2081 12.4729H5V10.8892H11.2398L12.9502 10.8891L19 10.8892V12.4729H12.9502L12.9502 12.5H11.2081Z' fill='white'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_4492_17835)'%3E%3Crect width='24' height='24' fill='%23CB007A'/%3E%3C/g%3E%3C/svg%3E%0A");
                }
                & + span {
                    margin-left: -8px;
                }
            }
        }
    }

    .situation-card-footer {
        dl {
            background: rgba(var(--color-white-rgb), .1);
            padding: 10px 20px;
            min-height: 40px;
            border-bottom-right-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        dt {
            color: var(--color-white);
        }
    }

    &.situation-card-m-pending {
        .situation-card-footer {
            dl {
                background: rgba(var(--color-white-rgb), .05);
            }
            dt {
                color: rgba(var(--color-white-rgb), .5);
            }
            dd {
                .situation-card-footer-percentage {
                    opacity: 1;
                    color: var(--color-white);
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 700;
                    line-height: 1.45em;
                    letter-spacing: 1px;
                    em {
                        opacity: .5;
                        font-style: normal;
                        margin-left: 1px;
                    }
                }
            }
        }
    }
}
