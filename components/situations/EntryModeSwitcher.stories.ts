import EntryModeSwitcher from './EntryModeSwitcher.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof EntryModeSwitcher> = {
    component: EntryModeSwitcher,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof EntryModeSwitcher>;

export const Default: Story = {
    // 
};
