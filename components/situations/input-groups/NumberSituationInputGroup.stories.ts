import NumberSituationInputGroup from './NumberSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof NumberSituationInputGroup> = {
    component: NumberSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
            // description: '',
        }
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { NumberSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <NumberSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </NumberSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof NumberSituationInputGroup>;

export const Number: Story = {
    args: {
        label: 'Your favorite number',
        description: 'The number that causes your heart to flutter every time you see it',
        type: 'number',
    }
};

export const Money: Story = {
    args: {
        label: 'Total value',
        description: 'The aggregate value that represents the collective amount of the individual components',
        type: 'currency',
    }
};
