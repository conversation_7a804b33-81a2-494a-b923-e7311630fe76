<template>
    <div class="text-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
            @cancel="handleCancel"
        >
            <TextInput
                v-model="model"
                :type="type"
                :inputmode="inputmode ?? 'text'"
                :svg-icon="svgIcon"
                :suffix="suffix"
                :number-formatting="numberFormatting"
                :readonly="readonly"
                size="sm"
            />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import TextInput from '../../forms/TextInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';

/** SituationInputGroup field for the 'text' type */
defineOptions();

export interface TextSituationInputGroupFields {
    type?: typeof TextInput['type'];
    inputmode?: typeof TextInput['inputmode'];
    svgIcon?: typeof TextInput['svgIcon'];
    suffix?: typeof TextInput['suffix'];
    numberFormatting?: typeof TextInput['numberFormatting'];
}
export type TextSituationInputGroupProps = BaseSituationInputGroupProps & TextSituationInputGroupFields;

const props = defineProps<TextSituationInputGroupProps>()

const emit = defineEmits<{
    change: [any]
}>()

const model = ref(props.value ?? null);

const formattedValue = computed(() => {
    if (!model.value) return undefined;
    let str;
    if(props.numberFormatting) {
        str = (model.value) ? numberToUSD(model.value, false, props.type === 'currency') : undefined
    } else {
        str = model.value;
    }
    return str += props.suffix ?? '';
})

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (model.value) ? 'good' : 'required';
})

function handleDone() {
    emit('change', model.value);
}

function handleBlur() {
    emit('change', model.value);
}

function handleCancel() {
    model.value = props.value ?? null;
}

watch(() => props.value, (newValue) => {
    model.value = newValue;
})

</script>

