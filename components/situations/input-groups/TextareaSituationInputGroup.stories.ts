import TextareaSituationInputGroup from './TextareaSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof TextareaSituationInputGroup> = {
    component: TextareaSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
        }
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { TextareaSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <TextareaSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </TextareaSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TextareaSituationInputGroup>;

export const Textarea: Story = {
    args: {
        label: 'Brief description',
        description: 'Briefly describe the thing that you are currently thinking about'
    }
};
