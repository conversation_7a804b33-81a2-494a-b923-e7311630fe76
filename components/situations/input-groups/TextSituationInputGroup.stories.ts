import TextSituationInputGroup from './TextSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof TextSituationInputGroup> = {
    component: TextSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
            // description: '',
        },

        type: {
            control: { type: 'select' },
            options: ['number', 'currency'],
            // description: '',
        },
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { TextSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <TextSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </TextSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TextSituationInputGroup>;

export const Text: Story = {
    args: {
        label: 'Brief description',
        description: 'Briefly describe the thing that you are currently thinking about'
    }
};

export const Suffix = {
    args: {
        label: 'Describe your daily routine',
        description: 'What does a typical day in your life look like, briefly?',
        suffix: ' every day'
    },
};

export const Password = {
    args: {
        type: 'password',
        placeholder: "Password"
    },
};

export const Icon = {
    args: {
        svgIcon: {
            shape: 'dollar',
        }
    },
};
