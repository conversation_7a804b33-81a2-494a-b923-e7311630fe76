import SelectSituationInputGroup from './SelectSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SelectSituationInputGroup> = {
    component: SelectSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
            // description: '',
        }
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { SelectSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <SelectSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </SelectSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SelectSituationInputGroup>;

export const Default: Story = {
    args: {
        label: 'Which option?',
        description: 'The particular selection that aligns with your preferences and satisfies your desires',
        type: 'select',
        options: [
            { value: 'one', label: 'One' },
            { value: 'two', label: 'Two' },
            { value: 'three', label: 'Three' },
        ],
        placeholder: 'Select an option',
    }
};
