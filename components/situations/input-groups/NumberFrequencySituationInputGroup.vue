<template>
    <div class="number-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :auto-focus="autoFocus"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
        >
            <div class="number-situation-input-group-inputs">
                <TextInput
                    class="number-situation-input-group-text"
                    v-model="numberModel"
                    :svg-icon="(type === 'currency-frequency') ? {shape: 'dollar'} : svgIcon"
                    :suffix="suffix"
                    :min="min"
                    :max="max"
                    :number-formatting="numberFormatting"
                    :readonly="readonly"
                    size="sm"
                />
                
                <SelectInput
                    v-model="frequencyModel"
                    class="number-situation-input-group-select"
                    placeholder="Frequency"
                    :options="frequencyOptions"
                    size="sm"
                />
            </div>
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import SelectInput from '../../forms/SelectInput.vue';
import TextInput from '../../forms/TextInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';
import type { Ref } from 'vue';

/** SituationInputGroup field for 'number' or 'currency' types */
defineOptions();

export interface NumberFrequencySituationInputGroupFields {
	// value: {
    //     number: number;
    //     frequency: string;
    // },
    type: 'number-frequency' | 'currency-frequency';
	min?: number;
	max?: number;
	precision?: number;
	formatPrecision?: number;
    numberFormatting?: typeof TextInput['numberFormatting'];
}
export type NumberFrequencySituationInputGroupProps = BaseSituationInputGroupProps & NumberFrequencySituationInputGroupFields;

const props = withDefaults(defineProps<NumberFrequencySituationInputGroupProps>(), {
    numberFormatting: true,
    footerButtons: true
});

const emit = defineEmits<{
    change: [{number: number, frequency: string}]
}>()

const frequencyOptions = [
    {
        label: 'Daily',
        value: 'Daily'
    },
    {
        label: 'Weekly',
        value: 'Weekly'
    },
    {
        label: 'Biweekly',
        value: 'Biweekly'
    },
    {
        label: 'Semimonthly',
        value: 'Semimonthly'
    },
    {
        label: 'Monthly',
        value: 'Monthly'
    },
    {
        label: 'Quarterly',
        value: 'Quarterly'
    },
    {
        label: 'Yearly',
        value: 'Yearly'
    }
]

const numberModel = ref(props.value.number ?? null);
const frequencyModel = ref(props.value.frequency ?? null);

const numberModelIsFilled = computed(() => (numberModel.value || numberModel.value === 0))

const formattedNumber = computed(() => 
    (numberModelIsFilled.value) ? numberToUSD(numberModel.value, false, props.type === 'currency-frequency') : undefined
)

const formattedValue = computed(() => 
    // (formattedNumber.value && frequencyModel.value) ? formattedNumber.value + ' ' + frequencyModel.value.toLowerCase() : undefined
    (formattedNumber.value && frequencyModel.value) ? formattedNumber.value + formattedFrequency.value : undefined
)

const formattedFrequency = computed(() => {
    if (!frequencyModel.value) return undefined; 
    switch(frequencyModel.value) {
        case 'Daily': 
            return '/day';
        case 'Weekly': 
            return '/week';
        case 'Biweekly': 
            return '/two weeks';
        case 'Semimonthly': 
            return ' semimonthly';
        case 'Monthly': 
            return '/month';
        case 'Quarterly': 
            return '/quarter';
        case 'Yearly': 
            return '/year';
        default:
            return undefined;
    }
})

const isPulsing = computed(() => 
    props.required && !formattedNumber.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (numberModel.value) ? 'good' : 'required';
})

const numberFormatting = computed(() => {
    return true;
})

function handleDone() {
    emit('change', {
        number: numberModel.value,
        frequency: frequencyModel.value
    });
}

function handleBlur() {
    emit('change', {
        number: numberModel.value,
        frequency: frequencyModel.value
    });
}

function handleCancel() {
    numberModel.value = props.value.number ?? null;
    frequencyModel.value = props.value.frequency ?? null;
}

watch(() => props.value, (newValue) => {
    numberModel.value = newValue.number;
    frequencyModel.value = newValue.frequency;
})

</script>

<style lang="scss">
.number-situation-input-group-inputs {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: space-between;
}
.number-situation-input-group-text {
    flex: auto;
}
.number-situation-input-group-select {
    flex: none;
    min-width: 120px;
    width: 120px;
    height: 40px;

    --select-input-height: 40px;
}
</style>
