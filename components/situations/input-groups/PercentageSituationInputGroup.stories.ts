import PercentageSituationInputGroup from './PercentageSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof PercentageSituationInputGroup> = {
    component: PercentageSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
            // description: '',
        }
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { PercentageSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <PercentageSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </PercentageSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof PercentageSituationInputGroup>;

export const Default: Story = {
    args: {
        label: 'How likely do you think this is?',
        description: 'The number that causes your heart to flutter every time you see it',
        type: 'percentage',
    }
};
