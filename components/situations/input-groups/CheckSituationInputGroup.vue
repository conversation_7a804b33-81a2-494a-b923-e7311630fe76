<template>
    <div class="number-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :collapse-on-blur="true"
            :show-description-on-edit="expandMode !== 'absolute'"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            :expand-mode="expandMode"
            @done="handleDone"
            @blur="handleBlur"
            @cancel="handleCancel"
        >
            <CheckboxGroup
                :mode="(type === 'single-select') ? 'radio' : 'checkbox'"
                size="sm"
                :variant="variant"
                :filterable="filterable"
                v-model:options="modelOptions"
                v-model:selected="modelSelected"
                />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import CheckboxGroup from '../../forms/CheckboxGroup.vue';
import type { CheckboxGroupOption } from '../../forms/CheckboxGroup.vue';
import TextInput from '../../forms/TextInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';
import type { Ref } from 'vue';

/** SituationInputGroup field for 'multi-select' or 'single-select' types */
defineOptions();

export interface CheckSituationInputGroupFields {
	type: 'single-select' | 'multi-select';
	options: CheckboxGroupOption[];

    variant?: typeof CheckboxGroup['variant'];
    expandMode?: typeof CollapsibleInputGroup['expandMode'];
    filterable?: boolean;
}
export type CheckSituationInputGroupProps = BaseSituationInputGroupProps & CheckSituationInputGroupFields;

const props = withDefaults(defineProps<CheckSituationInputGroupProps>(), {
    expandMode: 'relative',
    filterable: false,
    variant: 'buttons',
});

const emit = defineEmits<{
    change: [id: string | string[]]
}>()

const modelSelected: Ref<string[] | string> = ref(props.value ?? []);
const modelOptions: Ref<CheckboxGroupOption[]> = ref(props.options ?? []);

const formattedValue = computed(() => {
    if (modelSelected.value) {
        if (typeof modelSelected.value === 'string') {
            return modelOptions.value.find((option) => option.name === modelSelected.value)?.label ?? modelSelected.value;
        } else {
            return modelSelected.value.map((i) => modelOptions.value.find((option) => option.name === i)?.label ?? i);
        }
    }    
})

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (modelSelected.value) ? 'good' : 'required';
})

const numberFormatting = computed(() => {
    return true;
})

function handleDone() {
    emit('change', modelSelected.value);
}

function handleBlur() {
    emit('change', modelSelected.value);
}

function handleCancel() {
    modelSelected.value =  props.value ?? [];
    modelOptions.value =  props.options ?? [];
}

watch(() => props.value, (newValue) => {
    modelSelected.value = newValue;
})

</script>

