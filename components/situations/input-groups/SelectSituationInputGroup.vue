<template>
    <div class="select-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
            @abort="handleCancel"
        >
            <SelectInput
                v-model="model"
                :options="options ?? []"
                :placeholder="placeholder"
                :readonly="readonly"
                size="sm"
            />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import type { Ref } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import SelectInput from '../../forms/SelectInput.vue';
import type { SelectInputOption } from '../../forms/SelectInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';

/** SituationInputGroup field for 'select' types */
defineOptions();

export interface SelectSituationInputGroupFields {
	type: 'select';
    
    // options should not be optional, but it's causing issues with the SituationInputGroup props for other types
	options?: SelectInputOption[];
    placeholder?: string;
}
export type SelectSituationInputGroupProps = BaseSituationInputGroupProps & SelectSituationInputGroupFields;

const props = defineProps<SelectSituationInputGroupProps>()

const emit = defineEmits<{
    change: [any]
}>()

const model: Ref<string|null> = ref(props.value ?? null);

const formattedValue = computed(() => {
    if (!model.value) return undefined;
    const option = props.options?.find((option) => option.value === model.value);
    return option?.label ?? model.value;
})

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (model.value) ? 'good' : 'required';
})

function handleDone() {
    emit('change', model.value);
}

function handleBlur() {
    emit('change', model.value);
}

function handleCancel() {
    model.value =  props.value ?? null;
}

watch(() => props.value, (newValue) => {
    model.value = newValue;
})

onMounted(() => {
    model.value = props.value ? props.value : null;
})
</script>

