<template>
    <div class="number-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :auto-focus="autoFocus"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
            @cancel="handleCancel"
        >
            <TextInput
                v-model="model"
                :svg-icon="svgIcon"
                suffix="%"
                :min="min"
                :max="max"
                :number-formatting="numberFormatting"
                :readonly="readonly"
                size="sm"
            />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import TextInput from '../../forms/TextInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';
import type { Ref } from 'vue';

/** SituationInputGroup field for 'number' or 'currency' types */
defineOptions();

export interface PercentageSituationInputGroupFields {
	type: 'percentage';
	min?: number;
	max?: number;
	precision?: number;
	formatPrecision?: number;
    numberFormatting?: typeof TextInput['numberFormatting'];
}
export type PercentageSituationInputGroupProps = BaseSituationInputGroupProps & PercentageSituationInputGroupFields;

const props = withDefaults(defineProps<PercentageSituationInputGroupProps>(), {
    numberFormatting: { precision: 2 },
    footerButtons: true
});

const emit = defineEmits<{
    change: [any]
}>()

const model = ref(props.value ?? null);

const formattedValue = computed(() => 
    (model.value) ? (model.value) + '%' : undefined
)

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (model.value) ? 'good' : 'required';
})

function handleDone() {
    emit('change', model.value);
}

function handleBlur() {
    emit('change', model.value);
}

function handleCancel() {
    model.value = props.value ?? null;
}

watch(() => props.value, (newValue) => {
    model.value = newValue;
})

</script>

