<template>
    <div class="text-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValue"
            :is-pulsing="isPulsing"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
            @cancel="handleCancel"
        >
            <TextareaInput
                v-model="model"
                :readonly="readonly"
                size="sm"
            />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import TextareaInput from '../../forms/TextareaInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';

/** SituationInputGroup field for the 'textarea' type */
defineOptions();

export type TextareaSituationInputGroupProps = BaseSituationInputGroupProps;

const props = defineProps<TextareaSituationInputGroupProps>()

const emit = defineEmits<{
    change: [any]
}>()

const model = ref(props.value ?? null);

const formattedValue = computed(() =>
    model.value ? model.value : ''
)

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (model.value) ? 'good' : 'required';
})

function handleDone() {
    emit('change', model.value);
}

function handleBlur() {
    emit('change', model.value);
}

function handleCancel() {
    model.value = props.value ?? null;
}

watch(props.value, (newValue) => {
    model.value = newValue;
})

</script>

