<template>
    <div class="number-situation-input-group">
        <CollapsibleInputGroup
            :label="label"
            :description="description"
            :formatted-value="formattedValueWithSuffix"
            :is-pulsing="isPulsing"
            :auto-focus="autoFocus"
            :collapse-on-blur="true"
            :show-description-on-edit="true"
            :validation-state="validationState"
            :footer-buttons="footerButtons"
            :footer-disclaimer="footerDisclaimer"
            :confirm-button-text="confirmButtonText"
            @done="handleDone"
            @blur="handleBlur"
            @cancel="handleCancel"
        >
            <TextInput
                v-model="model"
                :svg-icon="(type === 'currency') ? {shape: 'dollar'} : svgIcon"
                :suffix="suffix"
                :min="min"
                :max="max"
                :number-formatting="numberFormatting"
                :readonly="readonly"
                size="sm"
            />
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import CollapsibleInputGroup from '../../forms/CollapsibleInputGroup.vue';
import TextInput from '../../forms/TextInput.vue';
import type { BaseSituationInputGroupProps } from '../SituationInputGroup.vue';
import { numberToUSD } from '../../../utils';
import type { Ref } from 'vue';

/** SituationInputGroup field for 'number' or 'currency' types */
defineOptions();

export interface NumberSituationInputGroupFields {
	type: 'number' | 'currency';
	min?: number;
	max?: number;
	precision?: number;
	formatPrecision?: number;
    numberFormatting?: typeof TextInput['numberFormatting'];
}
export type NumberSituationInputGroupProps = BaseSituationInputGroupProps & NumberSituationInputGroupFields;

const props = withDefaults(defineProps<NumberSituationInputGroupProps>(), {
    numberFormatting: true,
    footerButtons: true
});

const emit = defineEmits<{
    change: [any]
}>()

const model = ref(props.value ?? null);

const modelIsFilled = computed(() => (model.value || model.value === 0))

const formattedValue = computed(() => 
    (modelIsFilled.value) ? numberToUSD(model.value, false, props.type === 'currency') : undefined
)

const formattedValueWithSuffix = computed(() => 
    (formattedValue.value && props.suffix) ? formattedValue.value + props.suffix : formattedValue.value
)

const isPulsing = computed(() => 
    props.required && !formattedValue.value
)

const validationState = computed<'good'|'required'|undefined>(() => {
    if (!props.required) return undefined;
    return (model.value) ? 'good' : 'required';
})

const numberFormatting = computed(() => {
    return true;
})

function handleDone() {
    emit('change', model.value);
}

function handleBlur() {
    emit('change', model.value);
}

function handleCancel() {
    model.value = props.value ?? null;
}

watch(() => props.value, (newValue) => {
    model.value = newValue;
})

</script>

