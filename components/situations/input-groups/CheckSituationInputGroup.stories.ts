import CheckSituationInputGroup from './CheckSituationInputGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof CheckSituationInputGroup> = {
    component: CheckSituationInputGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        required: {
            control: { type: 'boolean' },
            // description: '',
        }
    },

    //👇 Emitted events and default args
    args: {
        onChange: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { CheckSituationInputGroup },
        setup() {
            return { args };
        },
        template: `
            <CheckSituationInputGroup v-bind="args" style="width: 300px">
                ${args.default}
            </CheckSituationInputGroup>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof CheckSituationInputGroup>;

export const Radios: Story = {
    args: {
        label: 'What type?',
        description: 'Which of the following types best describes this particular article?',
        type: 'single-select',
        options: [
            {"name": "circle", "label": "Circle"},
            {"name": "square", "label": "Square"},
            {"name": "triangle", "label": "Triangle"},
            {"name": "rectangle", "label": "Rectangle"},
            {"name": "pentagon", "label": "Pentagon"},
            {"name": "hexagon", "label": "Hexagon"}
        ]        
    }
};

export const Checkboxes: Story = {
    args: {
        label: 'Which ones?',
        description: 'Inform us of the options that you would have selected were this question posed to you',
        type: 'multi-select',
        options: [
            { "label": "Apples", "name": "apples", "value": false },
            { "label": "Oranges", "name": "oranges", "value": false },
            { "label": "Grapes", "name": "grapes", "value": false },
        ]
    }
};

export const CheckboxList: Story = {
    args: {
        label: 'Which ones?',
        description: 'Inform us of the options that you would have selected were this question posed to you',
        type: 'multi-select',
        variant: 'list',
        expandMode: 'absolute',
        filterable: true,
        options: [
            { "label": "Apples", "name": "apples", "value": false },
            { "label": "Oranges", "name": "oranges", "value": false },
            { "label": "Grapes", "name": "grapes", "value": false },
            { "label": "Kiwi", "name": "kiwi", "value": false },
            { "label": "Strawberries", "name": "strawberries", "value": false },
            { "label": "Pineapple", "name": "pineapple", "value": false },
            { "label": "Watermelon", "name": "watermelon", "value": false },
            { "label": "Mangoes", "name": "mangoes", "value": false },
            { "label": "Peaches", "name": "peaches", "value": false },
            { "label": "Plums", "name": "plums", "value": false },
            { "label": "Blueberries", "name": "blueberries", "value": false },
            { "label": "Cherries", "name": "cherries", "value": false },
            { "label": "Pears", "name": "pears", "value": false },
            { "label": "Raspberries", "name": "raspberries", "value": false },
            { "label": "Blackberries", "name": "blackberries", "value": false }
        ]
    }
};
