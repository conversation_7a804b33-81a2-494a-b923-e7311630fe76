import SituationModalLink from './SituationModalLink.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SituationModalLink> = {
    component: SituationModalLink,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        arrow: {
            control: { type: 'select' },
            options: ['left', 'right', false],
            description: '',
        },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    render: (args) => ({
        components: { SituationModalLink },
        setup() {
            return { args };
        },
        template: `
            <SituationModalLink v-bind="args">
                ${args.default}
            </SituationModalLink>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SituationModalLink>;

export const Default: Story = {
    args: {
        default: 'Click me',
    }
};

export const LeftArrow: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6369-34291&m=dev',
        },
    },
    args: {
        default: 'Click me',
        arrow: 'left',
    }
};

export const RightArrow: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6342-32727&m=dev',
        },
    },
    args: {
        default: 'Click me',
        arrow: 'right',
    }
};
