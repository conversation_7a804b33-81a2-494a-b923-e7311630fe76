import SituationItem from './SituationItem.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
// import { Money, Select } from './SituationInputGroup.stories';
import { allAssetTypes, transformSituationTypeForCard } from '../../components/situations/data/intake-data';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SituationItem> = {
    component: SituationItem,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // 
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    render: (args) => ({
        components: { SituationItem },
        setup() {
            const cardData = transformSituationTypeForCard(
                allAssetTypes[Math.floor(Math.random() * allAssetTypes.length)]
            );

            return { 
                args: {
                    cardMode: 'pending',
                    totalSteps: 1,
                    completedSteps: 0,
                    kind: cardData.label,
                    name: cardData.name,
                    unit: cardData.unit ?? undefined,
                    iconShape: cardData.iconShape,
                    title: cardData.label,
                    simple: cardData.fields.simple,
                    standard: {1: {id: 1, isComplete: false, fields: cardData.fields.standard}},
                    template: cardData.fields.standard.map(e => ({...e})),
                    footerRow: { label: 'Onboard Status', percentage: 0},
                    displayMode: args.displayMode ?? 'card'
                }
            };
        },
        template: `<SituationItem v-bind="args" />`,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};


// :title="asset.kind"
// v-bind="asset"

export default meta;
type Story = StoryObj<typeof SituationItem>;

/*
Temporarily disabled because Storybook is throwing import errors in stores/intake.ts
export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {

        cardMode: 'pending',
        iconShape: 'car',
        entryMode: 'simple',

        title: 'Vehicles',
        fields: {
            simple: [
                {
                    type: 'currency',
                    name: "name",
                    label: 'Total value of vehicles',
                    description: 'The estimated total value of all of your vehicles',
                    required: true
                },
                {
                    type: 'currency',
                    name: "liabilities",
                    label: 'Total liabilities',
                    description: 'The estimated total amount of liabilities on all of your vehicles',
                    required: true
                }
            ],
            standard: [
                {
                    type: 'text',
                    name: "description",
                    label: 'Description',
                    description: "Brief description of the vehicle (e.g. 2021 Nissan Rogue)",
                    required: true,
                    value: null
                },
                {
                    type: 'currency',
                    name: "equity",
                    label: 'Equity / Value',
                    description: "How much of the vehicle do you own?",
                    required: true,
                    value: null
                },
                {
                    type: 'currency',
                    name: "owe",
                    label: 'How much do you owe?',
                    description: "If the vehicle is paid off, leave this blank, or enter $0",
                    required: true,
                    value: null
                },
            ]
        }
    }
};

export const Card: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        displayMode: 'card'
    }
};


export const LineItem: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        displayMode: 'line'
    }
};
*/
