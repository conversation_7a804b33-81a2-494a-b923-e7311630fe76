<template>
    <div class="entry-mode-switcher">
        <button tabindex="-1" class="entry-mode-switcher-button" @click.prevent="handlePopoverToggle">
            <span class="entry-mode-switcher-mode">MODE</span>
            <span class="entry-mode-switcher-active" v-text="capitalize(active ?? '')"></span>
            <span class="entry-mode-switcher-caret">
                <svg width="10" height="7" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M6.00167 8.00167L6.00334 8.00333L7.41755 6.58912L7.41588 6.58746L12.0033 2L10.5891 0.58579L6.00167 5.17324L1.41421 0.585785L0 2L4.58746 6.58746L4.58579 6.58913L6 8.00334L6.00167 8.00167Z"
                        fill="currentColor" />
                </svg>
            </span>
        </button>

        <PopoverModal :dynamic-placement="false" :with-overlay="false" :is-open="popoverIsOpen" @close="handlePopoverClose">
            <PopoverModalContent>
                <template #header>
                    <h5>
                        <template v-if="label">
                            <em>Set mode for</em> <span v-text="label"></span>
                        </template>
                        <template v-else>
                            <em>Set entry mode</em>
                        </template>
                    </h5>
                    <button class="popover-modal-close" @click.prevent="handlePopoverClose">
                        <SvgIcon shape="close" />
                    </button>
                </template>

                <CheckboxGroup
                    v-model:options="optionsRef"
                    v-model:selected="selectedRef"
                    size="sm"
                    mode="radio"
                    />
                
                <template #footer>
                    <CheckboxInput 
                        v-model="useAsDefault"
                        size="sm"
                        label="Use as default"
                    />
                    <Button size="sm" class="entry-mode-switcher-popover-button" @click.prevent="handlePopoverSubmit">Set Mode</Button>
                </template>
            </PopoverModalContent>
        </PopoverModal>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import { capitalize, pluralize, toLowerCase } from '../../utils';
import Button from '../elements/Button.vue';
import PopoverModal from '../modals/PopoverModal.vue';
import PopoverModalContent from '../modals/PopoverModalContent.vue';
import CheckboxGroup from '../forms/CheckboxGroup.vue';
import CheckboxInput from '../forms/CheckboxInput.vue';
import type { CheckboxGroupOption } from '../forms/CheckboxGroup.vue';
import type { SituationEntryMode } from './SituationItem.vue';
import SvgIcon from '../images/SvgIcon.vue';

/** A display of the current entry mode (eg simple or standard) and an option dropdown */
defineOptions();

export interface EntryModeSwitcherProps {
    active: SituationEntryMode | null;
    label?: string | null;
    standardDescription?: string;
    simpleDescription?: string;
}
const props = defineProps<EntryModeSwitcherProps>();

const useAsDefault = ref(false);

const popoverIsOpen = ref(false);
function handlePopoverOpen() {
    popoverIsOpen.value = true;
}
function handlePopoverClose() {
    popoverIsOpen.value = false;
}
function handlePopoverToggle() {
    if (popoverIsOpen.value) {
        handlePopoverClose();
    } else {
        handlePopoverOpen();
    }
}
function handlePopoverSubmit() {
    emit('change', selectedRef.value, useAsDefault.value);
    handlePopoverClose();
}

const standardDesc = computed(() => (props.standardDescription) 
    ? props.standardDescription
    : "Enter detailed information about each "+ pluralize(toLowerCase(props.label ?? 'item'))
)

const simpleDesc = computed(() => (props.simpleDescription) 
    ? props.simpleDescription
    : "Enter estimated totals or best guess for all " + pluralize(toLowerCase(props.label ?? 'item'))
)

const optionsRef = ref<CheckboxGroupOption[]>([{
    name: 'simple',
    label: 'Simple',
    description: simpleDesc,
    value: props.active === 'simple'
}, {
    name: 'standard',
    label: 'Standard',
    description: standardDesc,
    value: props.active === 'standard'
}]);
const selectedRef: Ref<SituationEntryMode> = ref(props.active ?? 'simple');


const emit = defineEmits<{
    change: [mode: SituationEntryMode, useAsDefault: boolean]
}>()
</script>

<style lang="scss">
.entry-mode-switcher {
    .entry-mode-switcher-button {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        background: rgba(var(--color-blue-grey-rgb), .2);
        padding: 5px 20px;
        min-height: 44px;
        gap: 10px;

        transition: background .2s ease-in-out;
        &:hover {
            background: rgba(var(--color-blue-grey-rgb), .3);
        }

        .entry-mode-switcher-mode {
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.125em;
            letter-spacing: 1.2px;
            text-transform: uppercase;
            opacity: .5
        }

        .entry-mode-switcher-active {
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.125em;
            color: var(--color-grey);
        }
    }

    .entry-mode-switcher-caret {
        color: var(--color-grey);
        margin-right: -2px;
    }

    .popover-modal-static {
        // width: 400px;
    }

    .popover-modal-dialog.popover-modal-static {
        .popover-modal-outer-wrapper {
            transform-origin: right top;
            box-shadow: 0 0 12px 2px rgba(0, 0, 0, .15);
        }
        .popover-modal-outer-wrapper {
            left: auto;
            right: 0;
        }
    }

    .popover-modal-content {
        .popover-modal-content-footer {
            > .entry-mode-switcher-popover-button {
                width: auto;
                flex: none;
                margin-left: auto;
            }
        }
    }
}


</style>
