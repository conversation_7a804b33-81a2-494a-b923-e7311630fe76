@import "/assets/css/mixins";

.situation-crud-item {
    cursor: pointer;
    .situation-crud-item-bar {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20px;
        gap: 20px;

        min-height: 72px;

        background: rgba(var(--color-white-rgb), 0.05);
        border-radius: 4px;
    }

    .situation-crud-item-label {
        margin-right: auto;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;

        .situation-crud-item-text {
            font-style: normal;
            font-weight: 400;
            font-size: 14px;
            line-height: 1em;
            display: flex;
            align-items: center;
            color: var(--color-grey);

            gap: 10px;

            > span:not(:first-child, :has(.badge-simple)) {
                color: var(--color-white);
                display: flex;
                gap: 10px;
                &:before {
                    display: block;
                    color: var(--color-grey);
                    content: "/";
                }
            }

            em {
                font-style: normal;
                font-weight: 400;
                color: var(--color-grey);
                opacity: .5;
            }

            .badge-simple {
                background: var(--color-blue);
                color: var(--color-white);
                font-size: 10px;
                font-weight: 700;
                line-height: 1em;
                letter-spacing: 1px;
                text-transform: uppercase;
                padding: 2px 4px;
                border-radius: 2px;
                text-align: center;
                white-space: nowrap;
            }
        }
    }

    .situation-crud-item-stats {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
        text-align: right;

        dl {
            display: flex;
            align-items: flex-end;
            justify-content: center;
            flex-direction: column;
            gap: 4px;
            font-size: 12px;
            line-height: 14px;

            dd {
                font-weight: 700;
                color: var(--color-white);
            }
            dt {
                font-weight: 400;
                color: var(--color-blue-grey);
            }

            &.fade {
                dd {
                    color: var(--color-grey);
                    font-weight: 400;
                }
            }
        }
    }

    // <div class="situation-crud-item-stats">
    //     <dl>
    //         <dd>$683k</dd>
    //         <dt>Total value</dt>
    //     </dl>
}
