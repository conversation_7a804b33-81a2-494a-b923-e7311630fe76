<template>
  <div class="income-association-panel">
    <div class="income-association-panel-header">
      {{ title }}
      <Button
        @click="handleclick"
        :icon-only="true"
        variant="muted"
        svg-icon="dot-menu"
      >
      </Button>
    </div>

    <div class="income-association-panel-body">
      <div class="income-association-panel-body-item">
        <span class="income-association-panel-body-item-label">
          Description
        </span>
        <input
          class="income-association-panel-body-item-input"
          v-model="editableDescription"
          placeholder="Enter description"
          @change="updateField('description', editableDescription)"
        />
      </div>
      <div class="income-association-panel-body-divider"></div>

      <div class="income-association-panel-body-item">
        <span class="income-association-panel-body-item-label">
          Gross Income
        </span>
        <input
          class="income-association-panel-body-item-input"
          v-model="editableGrossIncome"
          placeholder="Enter gross income"
          numberFormatting
          @change="updateField('grossIncome', editableGrossIncome)"
        />
      </div>
      <div class="income-association-panel-body-divider"></div>

      <div class="income-association-panel-body-item">
        <span class="income-association-panel-body-item-label">Expenses</span>
        <span class="income-association-panel-body-item-value">
          <div>{{ federalExpensesLabel }}</div>
          <div>${{ federalExpenses }}</div>
        </span>
        <span
          v-if="secondExpense"
          class="income-association-panel-body-item-value"
        >
          <div>{{ medicareExpensesLabel }}</div>
          <div>${{ medicareExpenses }}</div>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from "vue";
import type { Ref } from "vue";
import Button from "../elements/Button.vue";
import TextInput from "../forms/TextInput.vue";

defineOptions();

// Generate a unique ID for this instance
const componentId =
  typeof useId === "function" ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => "income-association-panel-" + componentId);

export interface IncomeAssociationPanelProps {
  title: string;
  federalExpenses: string;
  federalExpensesLabel: string;
  medicareExpenses: string;
  medicareExpensesLabel: string;
  secondExpense: boolean;
}
const props = withDefaults(defineProps<IncomeAssociationPanelProps>(), {
  title: "Title",
  federalExpenses: "12k",
  federalExpensesLabel: "Federal - Taxes",
  medicareExpenses: "14k",
  medicareExpensesLabel: "Medicare - Premiums",
  secondExpense: false,
});

const emit = defineEmits<{
  change: [field: string, value: string];
}>();

const handleclick = () => {
  console.log("handleclick");
};

// Editable fields
const editableDescription = ref(props.description);
const editableGrossIncome = ref(props.grossIncome);

const updateField = (field: string, value: string) => {
  emit("change", field, value);
};
</script>

<style lang="scss">
@import "./IncomeAssociationPanel";
</style>
