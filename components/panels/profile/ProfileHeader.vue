<template>
    <div class="profile-header">
        <div>
            <UserAvatar :size="100" class="initials-circle" />
            <div>
                <div>{{ user?.userData.firstName }} {{ user?.userData.lastName }}</div>
                <div>{{ getUserRole(user) }}</div>
            </div>
        </div>
        <div class="profile-header-signout" @click="handleLogout">
            Sign out
            <SvgIcon shape="logout" width="20" height="20" />
        </div>
    </div>
    <div class="profile-tabs">
        <NuxtLink v-for="tab in tabs" :key="tab.name" :to="tab.link" activeClass="active-tab">
            {{ tab.text }}
            <span v-if="tab.notifications" class="dot"></span>
        </NuxtLink>
    </div>
</template>

<script setup lang="ts">
import SvgIcon from '~/components/images/SvgIcon.vue';
import { useOidcAuth } from '#imports';
import { getUserRole } from '~/utils';
import UserAvatar from '~/components/images/UserAvatar.vue';
import type { ProfileResponse } from '~/server/api/profile/index.get';

const { user, logout, currentProvider, clear } = useOidcAuth();
const { data: profileData } = await useApi<ProfileResponse>('/api/profile');

async function handleLogout() {
    try {
        // First clear the local session
        await logout('auth0');
        await clear(); // Clear the session data

        // Then redirect to the home page
        window.location.href = '/';
    } catch (error) {
        console.error('Logout failed:', error);
    }
}

const tabs = ref([
    {
        name: 'profile',
        text: 'Profile',
        link: '/client/settings/Profile',
    },
    {
        name: 'security',
        text: 'Login & Security',
        link: '/client/settings/Security',
    },
    {
        name: 'access',
        text: 'Access',
        link: '/client/settings/Access',
        notifications: computed(() => {
            return !!profileData?.value?.hasInvitations;
        }),
    },
]);
</script>

<style lang="scss">
.profile {
    &-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;

        &-signout {
            display: flex;
            flex-direction: row;
            gap: 10px;
            align-items: center;
            cursor: pointer;
            color: #97abcc;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 24px;
        }

        & > div:first-child {
            display: flex;
            align-items: center;

            & > div:first-child {
                width: 100px;
                height: 100px;
                background-color: #dee2e6;
                border-radius: 50%;
                margin-right: 1rem;
                border: 3px solid #97abcc;
                background-size: cover;
                background-position: center;
            }

            & > div:last-child {
                display: flex;
                flex-direction: column;

                & > div:first-child {
                    color: #e9edf1;
                    font-size: 18px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 30px;
                    word-wrap: break-word;
                }

                & > div:last-child {
                    color: #97abcc;
                    font-size: 14px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 400;
                    line-height: 30px;
                    word-wrap: break-word;
                }
            }
        }

        & > div:last-child {
            cursor: pointer;
            color: #97abcc;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 24px;
            word-wrap: break-word;
        }

        .initials-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: #dee2e6;
            color: #57719c;
            font-size: 36px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &-tabs {
        display: flex;
        justify-content: flex-start;
        padding-bottom: 20px;

        a {
            color: #57719c;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
            padding: 10px 20px;
            cursor: pointer;

            &:hover {
                text-decoration: none;
                color: #97abcc;
            }
        }

        .active-tab {
            color: #e9edf1;
        }

        .dot {
            height: 6px;
            width: 6px;
            background-color: red;
            border-radius: 50%;
            display: inline-block;
            margin-left: 3px;
            position: relative;
            bottom: 10px;
        }
    }
}
</style>
