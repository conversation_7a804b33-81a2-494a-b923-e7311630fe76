<template>
    <div class="line-item">
        <div class="line-item-header">
            <SvgIcon class="line-item-header-icon" :shape="'info'" width="100" height="100" />
            <span class="line-item-header-name">Tony</span>
            <span class="line-item-header-item">GT3RS</span>
        </div>
        <div class="line-item-body">
            <div class="line-item-body-nav">
                <span
                    class="line-item-body-nav-link"
                    :class="{ active: activeTab === 'overview' }"
                    @click="setActiveTab('overview')"
                >
                    Overview
                </span>
                <span
                    class="line-item-body-nav-link"
                    :class="{ active: activeTab === 'details' }"
                    @click="setActiveTab('details')"
                >
                    Details
                </span>
                <span
                    class="line-item-body-nav-link"
                    :class="{ active: activeTab === 'transactions' }"
                    @click="setActiveTab('transactions')"
                >
                    Transactions
                </span>
                <div class="line-item-body-right">
                    <SvgIcon class="line-item-body-right-svg" :shape="'camera'" width="20" height="20" />
                    <span class="line-item-body-right-text">Edit asset image</span>
                </div>
            </div>

            <!-- Content Area -->
            <div class="line-item-body-content">
                <!-- Overview Content -->
                <div class="line-item-body-section" v-show="activeTab === 'overview'">
                    <div class="line-item-body-kpi">
                        <div class="line-item-body-kpi-entity">
                            <span class="line-item-body-kpi-entity-name">VALUE</span>
                            <span class="line-item-body-kpi-entity-value">$30000</span>
                            <span class="line-item-body-kpi-entity-date">as of Jul 27, 2023</span>
                        </div>
                        <div class="line-item-body-kpi-entity">
                            <span class="line-item-body-kpi-entity-name">LIABILITY</span>
                            <span class="line-item-body-kpi-entity-value">$30000</span>
                            <span class="line-item-body-kpi-entity-date">as of Dec 23, 2022</span>
                        </div>
                        <div class="line-item-body-kpi-entity">
                            <span class="line-item-body-kpi-entity-name">EQUITY</span>
                            <span class="line-item-body-kpi-entity-value">$30000</span>
                            <span class="line-item-body-kpi-entity-date">as of Jan 15, 2024</span>
                        </div>
                    </div>
                    <div class="line-item-body-chart">
                        <span class="line-item-body-chart-title">Chart.js Line chart</span>
                        <span class="line-item-body-chart-description"
                            >Asset value over time, also shows debt/equity trend if applicable</span
                        >
                    </div>
                </div>

                <!-- Details Content -->
                <div
                    v-for="(item, index) in items"
                    :key="index"
                    class="line-item-body-section"
                    v-show="activeTab === 'details'"
                >
                    <div
                        :class="item.isEditing ? 'line-item-body-details-editing' : 'line-item-body-details'"
                        @click="toggleEditMode(index)"
                    >
                        <span class="line-item-body-details-title" v-if="!item.isEditing">{{ item.label }}</span>
                        <span class="line-item-body-details-description" v-if="!item.isEditing">{{
                            item.description
                        }}</span>
                        <div class="line-item-body-right-icon">
                            <SvgIcon
                                class="line-item-body-right-svg"
                                :shape="item.required ? 'asterisk' : 'keyboard-arrow-right'"
                                :width="item.required ? 10 : 24"
                                :height="item.required ? 10 : 24"
                            />
                        </div>

                        <!-- Editable content -->
                        <div v-if="item.isEditing" class="line-item-body-details-edit">
                            <span class="line-item-body-details-title">{{ item.label }}</span>
                            <TextInput
                                class="line-item-body-details-input"
                                v-model="item.editDescription"
                                size="sm"
                                @click.stop
                            />
                        </div>
                    </div>
                    <div v-if="item.isEditing && activeTab === 'details'" class="line-item-body-details-buttons">
                        <span class="line-item-body-details-buttons-time">Last updated 6 mo. ago</span>
                        <div class="line-item-body-details-buttons-actions">
                            <Button variant="muted" @click.stop="cancelEdit(index)">Cancel</Button>
                            <Button variant="primary" @click.stop="saveEdit(index)">Done</Button>
                        </div>
                    </div>
                </div>

                <!-- Transactions Content -->
                <div class="line-item-body-section" v-show="activeTab === 'transactions'">
                    <div class="line-item-body-transactions">
                        <span class="line-item-body-transactions-title">Recent Transactions</span>
                        <ul class="line-item-body-transactions-list">
                            Todo - not part of AC for this ticket.
                        </ul>
                    </div>
                </div>
                <div class="line-item-footer" v-show="activeTab === 'details'">
                    <SvgIcon class="line-item-body-right-svg" :shape="'trash'" width="20" height="20" /><span
                        >Delete asset</span
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TextInput from '../forms/TextInput.vue';
import SvgIcon from '../images/SvgIcon.vue';
import Button from '../elements/Button.vue';
import { onMounted } from 'vue';

// Manage active tab state
const activeTab = ref<'overview' | 'details' | 'transactions'>('overview');

// Manage details section
const items = ref([
    {
        label: 'Description',
        description: 'Description of property/item',
        editDescription: 'Description of property/item',
        isEditing: false,
        required: true,
    },
    {
        label: 'Address',
        description: 'The address of property',
        editDescription: 'The address of property',
        isEditing: false,
        required: true,
    },
    {
        label: 'Estimated value',
        description: 'The estimated or appraised value of the item/property',
        editDescription: 'The estimated or appraised value of the item/property',
        isEditing: false,
        required: true,
    },
    {
        label: 'Owner',
        description: 'The individual or entity that owns the property/item',
        editDescription: 'The individual or entity that owns the property/item',
        isEditing: false,
        required: false,
    },
    {
        label: 'Income',
        description: 'All income generated by this property/item',
        editDescription: 'All income generated by this property/item',
        isEditing: false,
        required: false,
    },
    {
        label: 'Liabilities',
        description: 'All debt associated with this property/item',
        editDescription: 'All debt associated with this property/item',
        isEditing: false,
        required: false,
    },
    {
        label: 'Expenses',
        description: 'All expenses and taxes associated with this property/item',
        editDescription: 'All expenses and taxes associated with this property/item',
        isEditing: false,
        required: false,
    },
]);

onMounted(() => {
    items.value.forEach(item => {
        if (item.editDescription && !item.description) {
            item.description = item.editDescription;
        }
    });
});

function setActiveTab(tab: 'overview' | 'details' | 'transactions') {
    activeTab.value = tab;
}

function toggleEditMode(index: number) {
    console.log('Toggling edit mode for index:', index);
    items.value[index].isEditing = !items.value[index].isEditing;
}

function saveEdit(index: number) {
    console.log('Saving edit for index:', index);
    items.value[index].description = items.value[index].editDescription;
    items.value[index].isEditing = false;
}

function cancelEdit(index: number) {
    console.log('Cancelling edit for index:', index);
    items.value[index].isEditing = false;
}
</script>

<style lang="scss">
.line-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #0d182a;
    box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.5);

    &-header {
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        justify-content: flex-end;
        align-content: flex-end;
        align-items: flex-start;
        padding: 20px;
        width: 600px;
        height: 200px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
        position: relative;

        &-icon {
            position: absolute;
            top: 20px;
            left: 10px;
            color: #57719c;
        }

        &-name {
            color: #e9edf1;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
        }

        &-item {
            color: white;
            font-size: 24px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
            padding-top: 10px;
        }
    }

    &-body {
        display: flex;
        flex-direction: column;
        position: relative;

        &-content {
            display: flex;
            gap: 3px;
        }

        &-nav {
            display: flex;
            flex-direction: row;
            width: 560px;

            &-link {
                padding: 20px 20px;
                color: #57719c;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
                cursor: pointer;

                &:hover {
                    color: #8ba9d0;
                }

                &.active {
                    color: #e9edf1; /* Active tab color */
                }
            }
        }

        &-right {
            display: flex;
            flex-direction: row;
            align-items: center;
            position: absolute;
            right: 10px;
            top: 20px;
            color: #97abcc;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 24px;
            word-wrap: break-word;
            cursor: pointer;
            gap: 10px;

            &:hover {
                color: #8ba9d0;
            }

            &-icon {
                position: absolute;
                right: 10px;
                color: #57719c;
            }
        }
        &-content {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        &-section {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        &-kpi {
            display: flex;
            flex-direction: row;
            gap: 10px;
            width: 100%;

            &-entity {
                display: flex;
                flex-direction: column;
                background-color: #19263c;
                padding: 20px;
                width: 100%;

                &-name {
                    color: #57719c;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 20px;
                    letter-spacing: 1.2px;
                    word-wrap: break-word;
                }

                &-value {
                    color: white;
                    font-size: 18px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                    padding-top: 10px;
                }

                &-date {
                    color: #57719c;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                    padding-top: 20px;
                }
            }
        }

        &-chart {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            align-items: center;
            justify-content: center;
            width: 560px;
            height: 200px;
            padding: 20px;
            background-color: #19263c;
            margin-top: 10px;

            &-title {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-description {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
            }
        }

        &-details {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.05);
            cursor: pointer;

            &-editing {
                display: flex;
                flex-direction: column;
                padding: 20px;
                cursor: pointer;
                background-color: rgba(255, 255, 255, 0.1);
            }

            &-title {
                color: #97abcc;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-description {
                color: #57719c;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-content {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                line-height: 20px;
                word-wrap: break-word;
            }
        }

        &-transactions {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: #19263c;

            &-title {
                color: #e9edf1;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-list {
                list-style: none;
                padding: 0;
            }

            &-item {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                line-height: 20px;
                word-wrap: break-word;
                padding: 5px 0;
            }
        }
    }

    &-footer {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 20px;
        color: #97abcc;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 24px;
        word-wrap: break-word;

        svg,
        span {
            cursor: pointer;

            &:hover {
                color: #8ba9d0;
            }
        }
    }

    .line-item-body-details-edit {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .line-item-body-details-input {
        color: white;
    }

    .line-item-body-details-buttons {
        display: flex;
        flex-direction: row;
        gap: 10px;
        padding: 20px;
        border-top: 1px solid rgba(233, 237, 241, 0.1);
        background-color: rgba(255, 255, 255, 0.1);
        justify-content: space-between;
        align-items: center;

        &-actions {
            display: flex;
            gap: 10px;
        }

        &-time {
            color: #97abcc;
            font-size: 12px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 12px;
            word-wrap: break-word;
        }
    }

    .line-item-body-details-button {
        background-color: #57719c;
        border: none;
        color: white;
        padding: 10px;
        font-size: 14px;
        cursor: pointer;
        border-radius: 4px;
        text-align: center;
    }

    .line-item-body-details-button:hover {
        background-color: #8ba9d0;
    }
}
</style>
