@import "/assets/css/mixins";

.earnings-input-panel {
  display: flex;
  flex-direction: column;
  width: 600px;
  background-color: #040714cc;
  padding: 20px;
  gap: 20px;

  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &-header-label {
    color: #97abcc;
  }

  &-header-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    cursor: pointer;
  }

  &-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
  }

  &-body-column {
    display: flex;
    flex-direction: column;
    min-width: 100%;
  }

  &-body-checkbox {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: rgba(255, 255, 255, 0.05);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    padding-left: 40px;
    min-width: 280px;
    padding-top: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  &-body-checkboxes {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-bottom: 20px;
    padding-right: 20px;
    padding-top: 20px;
  }

  &-body-row {
    display: flex;
    flex-direction: row;
    max-height: 90px;
  }

  &-footer-button {
    width: 100%;
  }
}

.earnings-input-panel-body-input-large {
  min-height: auto !important;
  height: auto !important;
  padding-bottom: 50px !important;
}

.earnings-input-panel-body-input-large {
  border-bottom: 10px solid rgba(255, 255, 255, 0.05);
}

.earnings-input-panel-body-row-large {
  display: flex;
  flex-direction: row;
  padding-bottom: 20px;
}

.earnings-input-panel-body-checkbox-text {
  color: #97abcc;
  font-size: 14px;
  font-family: "Oxygen", sans-serif;
  font-weight: 400;
  line-height: 20px;
  word-wrap: break-word;
}

.earnings-input-panel .text-input-9 {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.earnings-input-panel .text-input {
  border-bottom: none !important;
  min-height: 90px;
  padding: 20px !important; /* Expands the background */
  padding-bottom: 30px !important;
  box-sizing: border-box;
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  width: 100%;
}
.earnings-input-panel-body-row .text-input:first-child {
  border-right: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.earnings-input-panel .text-input .text-input-label {
  font-size: 12px;
  white-space: pre;
  font-family: "Oxygen", sans-serif;
  font-weight: 700;
  line-height: 18px;
  color: #97abcc;
  word-wrap: break-word;
  left: 20px !important; /* Moves the label */
  top: 60% !important;
  transform: translateY(-100%) !important; /* Centers vertically */
}

.earnings-input-panel .text-input .text-input-input {
  padding-left: 20px !important; /* Moves text inside the input */
  margin-top: 20px !important;
  color: #e9edf1;
  font-size: 14px;
  font-family: "Oxygen", sans-serif;
  font-weight: 400;
  line-height: 20px;
  word-wrap: break-word;
}

.earnings-input-panel-body-row-large .checkbox-input > label {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5px !important;
}

.earnings-input-panel-body-row-large .checkbox-input-label {
  color: #e9edf1;
  font-size: 10px;
  font-family: "Oxygen", sans-serif;
  font-weight: 400;
  line-height: 12px;
  word-wrap: break-word;
  max-width: 50px;
}

.earnings-input-panel
  .text-input.text-input-filled:not(
    :focus-within,
    .text-input-focused,
    .text-input-readonly
  )
  .text-input-input {
  color: #e9edf1 !important;
}
