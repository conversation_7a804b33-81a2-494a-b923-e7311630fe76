<template>
  <div :isOpen="isModalOpen" @close="closeModal" class="earnings-input-panel">
    <div class="earnings-input-panel-header">
      <span class="earnings-input-panel-header-icon">
        <span
          ><span class="earnings-input-panel-header-label">{{
            props.type
          }}</span></span
        >
        {{ props.title }}
      </span>
      <span class="earnings-input-panel-header-icons">
        <SvgIcon shape="search" width="20" height="20" color="#57719C" />
        <SvgIcon shape="close" width="20" height="20" color="#57719C" />
      </span>
    </div>
    <div class="earnings-input-panel-body">
      <div v-if="mode === 'w2'">
        <div class="earnings-input-panel-body-date">
          <TextInput placeholder="Date" label="Year / Month" />
        </div>
        <div class="earnings-input-panel-body-column">
          <div class="earnings-input-panel-body-row">
            <TextInput label="1    Wages, tips, other compensation" />
            <TextInput label="2    Federal income tax withheld" />
          </div>

          <div class="earnings-input-panel-body-row">
            <TextInput placeholder="Date" label="3    Social security wages" />
            <TextInput label="4    Social security tax withheld" />
          </div>

          <div class="earnings-input-panel-body-row">
            <TextInput placeholder="Date" label="5    Medicare wages & tips" />
            <TextInput placeholder="Date" label="6    Medicare tax withheld" />
          </div>

          <div class="earnings-input-panel-body-row">
            <TextInput placeholder="Date" label="7    Social security tips" />
            <TextInput placeholder="Date" label="8    Allocated tips" />
          </div>

          <div class="earnings-input-panel-body-row">
            <TextInput placeholder="Date" label="9" />
            <TextInput label="10    Dependent care benefits" />
          </div>

          <div class="earnings-input-panel-body-row">
            <TextInput placeholder="Date" label="11    Non-qualified plans" />
            <TextInput placeholder="Date" label="12a" />
          </div>

          <div class="earnings-input-panel-body-row-large">
            <div class="earnings-input-panel-body-checkbox">
              <span class="earnings-input-panel-body-checkbox-text">13</span>
              <span class="earnings-input-panel-body-checkboxes">
                <CheckBoxInput size="sm" label="statutory employee" />
                <CheckBoxInput size="sm" label="retirement plan" />
                <CheckBoxInput size="sm" label="third-party sick-pay" />
              </span>
            </div>
            <TextInput
              class="earnings-input-panel-body-input-large"
              label="12b"
            />
          </div>
        </div>
      </div>
      <div v-if="mode === '1099'">
        <div class="earnings-input-panel-body-date">
          <TextInput placeholder="Date" label="Year / Month" />
        </div>
        <div class="earnings-input-panel-body-column">
          <div class="earnings-input-panel-body-row">
            <TextInput label="1    Rents" />
            <TextInput label="2    Non-employee compensation" />
          </div>
        </div>
      </div>
      <div v-if="mode === 'default'">
        <div class="earnings-input-panel-body-date">
          <TextInput placeholder="Date" label="Year / Month" />
        </div>
        <div class="earnings-input-panel-body-column">
          <div class="earnings-input-panel-body-row">
            <TextInput :label="props.box1Label" />
            <TextInput :label="props.box2Label" />
          </div>
          <div class="earnings-input-panel-body-row">
            <TextInput :label="props.box3Label" />
            <TextInput label="" readonly />
          </div>
        </div>
      </div>
    </div>
    <div class="earnings-input-panel-footer">
      <Button
        class="earnings-input-panel-footer-button"
        size="md"
        variant="muted"
        >cancel</Button
      >
      <Button class="earnings-input-panel-footer-button" size="md">add</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from "vue";
import type { Ref } from "vue";
import SvgIcon from "../images/SvgIcon.vue";
import Button from "../elements/Button.vue";
import TextInput from "../forms/TextInput.vue";
import CheckBoxInput from "../forms/CheckBoxInput.vue";

/** TODO: Description of this component */
defineOptions();

const isModalOpen = ref(false);

function openModal() {
  isModalOpen.value = true;
}

function closeModal() {
  isModalOpen.value = false;
}

// Generate a unique ID for this instance
const componentId =
  typeof useId === "function" ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => "earnings-input-panel-" + componentId);

export interface EarningsInputPanelProps {
  type: string;
  title: string;
  mode: string;
  box1Label: string;
  box2Label: string;
  box3Label: string;
}
const props = withDefaults(defineProps<EarningsInputPanelProps>(), {
  type: "Add W2 to",
  title: "Employer Income",
  mode: "w2",
  box1Label: "1    Nonemployee compensation",
  box2Label: "2    Federal income tax withheld",
  box3Label: "3    State income tax withheld",
});

const emit = defineEmits<{
  change: [id: number];
}>();
</script>

<style lang="scss">
@import "./EarningsInputPanel";
</style>
