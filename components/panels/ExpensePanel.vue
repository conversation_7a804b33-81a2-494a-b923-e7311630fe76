<template>
    <div class="expense-panel">
        <div class="expense-panel-left">
            <div class="expense-panel-left-header">
                <span class="expense-panel-left-header-icon">
                    <BigIcon :shape="svgShape" :width="32" :height="32" color="#fff" />
                </span> 
                {{ title }}
            </div>
            <div class="expense-panel-left-body">
                <!-- <div class="expense-panel-left-body-row">
                    <div class="expense-panel-left-body-row-item" v-if="owner">
                        <div class="expense-panel-left-body-row-label">Owner</div>
                        <div class="expense-panel-left-body-row-value" v-text="owner"></div>
                    </div>
                </div> -->
                <div class="expense-panel-left-body-row">
                    <div class="expense-panel-left-body-row-item size-lg" v-if="monthly || monthly === 0">
                        <div class="expense-panel-left-body-row-label">Monthly</div>
                        <div class="expense-panel-left-body-row-value" v-text="'('+numberToUSD(monthly, false, true)+')'"></div>
                    </div>
                    <div class="expense-panel-left-body-row-item size-lg" v-if="annual || annual === 0">
                        <div class="expense-panel-left-body-row-label">Annually</div>
                        <div class="expense-panel-left-body-row-value" v-text="'('+numberToUSD(annual, false, true)+')'"></div>
                    </div>
                    <div class="expense-panel-left-body-row-item size-lg" v-if="percentOfTotal || percentOfTotal === 0">
                        <div class="expense-panel-left-body-row-label">% of total</div>
                        <div class="expense-panel-left-body-row-value" v-text="formattedPercentOfTotal"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="expense-panel-right">
            <div class="expense-panel-right-header">
                <div class="expense-panel-right-header-crumbs">
                    <span>Owner</span>    
                    <span>/</span>  
                    <span>Description</span>    
                    <span>/</span>  
                    <span>Amount</span>
                    <span>/</span>
                    <span>Frequency</span>
                </div>
                <div class="expense-panel-right-header-buttons">
                    <div class="expense-panel-right-header-buttons-icon" v-if="canAddItem" @click="addExpenseRow">
                        <button tabindex="-1">
                            <SvgIcon shape="plus" width="24" height="24" color="#57719C" />
                        </button>
                    </div>
                    <ElementsDeleteButtonWithConfirmation
                        :label="title"
                        @confirm="handleDeletePanel"
                    />
                </div>
            </div>
            <div class="expense-panel-right-body">
                <template v-if="$slots.default">
                    <slot></slot>
                </template>
                
                <div v-else class="expense-panel-right-body-placeholder">
                    Add an expense
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import BigIcon from '../images/BigIcon.vue';
import { numberToUSD } from '../../utils';
import type { TBigIconShapeKey } from '../images/BigIconShapes';

const intakeStore = useIntakeStore();

export interface ExpensePanelProps {
    title: string;
    owner?: string;
    monthly?: number;
    annual?: number;
    percentOfTotal?: number;
    svgShape: TBigIconShapeKey;
    canAddItem?: boolean;
}

const props = withDefaults(defineProps<ExpensePanelProps>(), {
    canAddItem: true,
});

const emit = defineEmits<{
    addItem: [],
    removeItem: [id: string]
    delete: []
}>()

// State
const expenseRows = ref<Array<any>>([]);

const formattedPercentOfTotal = computed(() => {
    if (!props.percentOfTotal) return;

    return (props.percentOfTotal <= 1)
        ? `${Math.round(props.percentOfTotal* 100)}%`
        : `${Math.round(props.percentOfTotal)}%`;
})

// Methods
function addExpenseRow(){
    emit('addItem')
};

function handleDeletePanel(){
    emit('delete');
}
</script>


<style lang="scss">
.expense-panel {
    display: flex;
    flex-direction: row;
    padding: 20px;
    background: rgba(86.51, 112.83, 156.33, 0.20);
    gap: 20px;

    &-left {
        display: flex;
        flex-direction: column;
        min-width: 300px;
        padding-bottom: 12px;

        &-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 20px;
            color: #E9EDF1;
            font-size: 16px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 30px;
            word-wrap: break-word;
            gap: 20px;

            &-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background-color: #57719C;
            }
        }

        &-body {
            display: flex;
            flex-direction: column;
            gap: 20px;

            &-row {
                display: flex;
                flex-direction: row;
                gap: 20px;

                &-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: flex-start;
                    gap: 10px;

                    &.size-lg {
                        .expense-panel-left-body-row-value {
                            font-size: 18px;
                        }
                    }
                }

                &-label {
                    color: #97ABCC;
                    font-size: 10px;
                    font-weight: 700;
                    text-transform: uppercase;
                    line-height: 20px;
                    letter-spacing: 1px;
                    word-wrap: break-word;
                }

                &-value {
                    color: #E9EDF1;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                & > div {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                }
            }
        }
    }

    &-right {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;

        &-body {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;

            &-placeholder {
                color: #57719C;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
                height: 100px;
            }
        }

        &-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-bottom: 10px;
            gap: 20px; 
            justify-content: space-between;

            &-buttons {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                gap: 10px;

                &-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 44px;
                    height: 44px;
                    background-color: rgba(86.51, 112.83, 156.33, 0.20);
                    border-radius: 3px;
                    cursor: pointer;

                    &:hover {
                        background-color: #0056b3;
                    }
                }

            }

            &-crumbs {
                display: flex;
                flex-direction: row;
                gap: 10px;
                color: #57719C;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
            }
        }
    }
}
</style>
