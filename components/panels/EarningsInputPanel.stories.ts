import EarningsInputPanel from "./EarningsInputPanel.vue";
import type { Meta, StoryObj } from "@storybook/vue3";
import { fn } from "@storybook/test";
import { ref } from "vue";

const meta: Meta<typeof EarningsInputPanel> = {
  component: EarningsInputPanel,
  tags: ["autodocs"],

  //👇 All props that we want mapped in Storybook UI
  argTypes: {
    // exampleSelectType: {
    //     control: { type: 'select' },
    //     options: ['foo', 'bar'],
    //     description: '',
    // },
    // exampleTextType: {
    //     control: {type: 'text'},
    //     description: '',
    // },
  },

  //👇 Emitted events and default args
  args: {
    onClick: fn(),
  },

  //👇 Our exports that end in "Data" are not stories.
  excludeStories: /.*Data$/,

  parameters: {
    a11y: {
      config: {
        // 👇 Add any accessibility rules that should be modified or ignored
        // rules: [{ id: 'label', enabled: false }],
      },
    },
  },

  //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
  // render: (args) => ({
  //     components: { EarningsInputPanel },
  //     setup() {
  //         const foo = ref('bar');
  //         return { args, foo };
  //     },
  //     template: `
  //         <EarningsInputPanel v-bind="args" :foo="foo">
  //             ${args.default}
  //         </EarningsInputPanel>
  //     `,
  // }),

  // 👇 Include a 3rem padding around the component
  decorators: [
    () => ({ template: '<div style="margin: 3em;"><story/></div>' }),
  ],
};

export default meta;
type Story = StoryObj<typeof EarningsInputPanel>;

export const DefaultW2: Story = {
  parameters: {
    // design: {
    //     type: 'figma',
    //     url: '',
    // },
  },
  args: {
    //
  },
};

export const Default1099: Story = {
  parameters: {
    // design: {
    //     type: 'figma',
    //     url: '',
    // },
  },
  args: {
    type: "Add 1099 MISC to",
    mode: "1099",
  },
};

export const Default: Story = {
  parameters: {
    // design: {
    //     type: 'figma',
    //     url: '',
    // },
  },
  args: {
    type: "Add to",
    mode: "default",
    title: "Pension Income",
  },
};
