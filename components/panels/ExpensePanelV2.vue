<template>
    <div class="expense-panel-v2">
        <div class="expense-panel-v2-header">
            <SvgIcon :shape="shape" color="rgba(151, 171, 204, 1)" />
            <div class="expense-panel-v2-header-information">
                <span class="expense-panel-v2-header-label">{{ label }}</span>
                <div class="expense-panel-v2-header-information-kpi">
                    <span class="expense-panel-v2-header-information-kpi-value"
                        >{{ month }}<span class="expense-panel-v2-header-information-kpi-type"> / mo</span></span
                    >
                    <span class="expense-panel-v2-header-information-kpi-value"
                        >{{ year }}<span class="expense-panel-v2-header-information-kpi-type"> / yr</span></span
                    ><span class="expense-panel-v2-header-information-kpi-value"
                        >{{ total }}<span class="expense-panel-v2-header-information-kpi-type"> / total</span></span
                    >
                </div>
            </div>
            <SvgIcon shape="settings" color="rgb(146, 152, 162)" />
        </div>
        <div class="expense-panel-v2-body">
            <div class="expense-panel-v2-body-table">
                <div class="expense-panel-v2-body-table-header">
                    <span class="expense-panel-v2-body-table-header-placeholder"></span>
                    <span class="checkbox-column"><CheckboxInput :show-label="false" size="sm" /></span>
                    <span>Description</span>
                    <span>Owner</span>
                    <span>Type</span>
                    <span>Amount</span>
                    <span>Annual</span>
                    <span>Frequency</span>
                </div>
                <button class="expense-panel-v2-add-line-button" tabindex="0">+ New line item</button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import CheckboxInput from '../forms/CheckboxInput.vue';

/** Updated Expanse Panel component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'expense-panel-v2-' + componentId);

export interface ExpenseItem {
    checked: boolean;
    description: string;
    owner: string;
    type: string;
    amount: number;
    annual: number;
    frequency: string;
}

export interface ExpensePanelV2Props {
    foo: string;
    shape: string;
    label: string;
    month: string;
    year: string;
    total: string;
    items: ExpenseItem[]; // <-- NEW
}

const props = withDefaults(defineProps<ExpensePanelV2Props>(), {
    foo: 'bar',
    shape: 'taxes',
    label: 'Entertainment',
    month: '100',
    year: '1200',
    total: '3%',
    items: () => [
        {
            checked: true,
            description: 'Netflix',
            owner: 'Joint',
            type: 'Recurring',
            amount: 12,
            annual: 144,
            frequency: 'Month',
        },
        {
            checked: true,
            description: 'Amazon Prime',
            owner: 'Joint',
            type: 'Recurring',
            amount: 99,
            annual: 1188,
            frequency: 'Year',
        },
        {
            checked: true,
            description: 'Apple TV',
            owner: 'Joint',
            type: 'Recurring',
            amount: 10,
            annual: 1200,
            frequency: 'Month',
        },
    ],
});

const emit = defineEmits<{
    change: [id: number];
}>();
</script>

<style lang="scss">
@import './ExpensePanelV2';
</style>
