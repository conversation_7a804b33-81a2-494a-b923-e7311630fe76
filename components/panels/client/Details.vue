<template>
    <div class="details">
        <div class="details-header">
            <div class="details-header-left">
                <NuxtLink class="cash-flow-header-back"><SvgIcon :shape="'back-arrow'" width="24" height="24" color="#57719C" /></NuxtLink>
                <span class="cash-flow-header-left-pfp" :style="{ backgroundImage: `url(${starkImage})` }"></span>
                <div class="details-header-left-title">
                    <span class="details-header-left-title-title">{{ clientLabel }}</span>
                    <span class="details-header-left-title-name">{{ clientName }}</span>
                </div>
            </div>
            <div class="details-header-right">
                <NuxtLink class="details-header-right-edit" :to="editLink"><svg-icon shape="dot-menu" width="20" height="20" /></NuxtLink>
                <NuxtLink class="details-header-right-mode" :to="modeLink">{{ modeLabel }}</NuxtLink>
            </div>
        </div>
        <div class="details-nav">
            <NuxtLink class="details-nav-item" :to="detailsLink">{{ detailsLabel }}</NuxtLink>
            <NuxtLink class="details-nav-item" :to="cashFlowLink">{{ cashFlowLabel }}</NuxtLink>
            <NuxtLink class="details-nav-item" :to="netWorthLink">{{ netWorthLabel }}</NuxtLink>
        </div>
        <div class="details-container">
            <div class="details-content">
                <div class="details-content-data">
                    <div class="details-content-data-name">{{ nameLabel }}</div>
                    <div class="details-content-data-value">{{ clientName }}</div>
                </div>
                <div class="details-content-data">
                    <div class="details-content-data-name">{{ contactInfoLabel }}</div>
                    <div class="details-content-data-value">
                        <div class="details-content-data-value-column">
                            <span class="details-content-data-value-column-label">{{ emailLabel }}</span>
                            <span>{{ email }}</span>
                        </div>
                        <div class="details-content-data-value-column">
                            <span class="details-content-data-value-column-label">{{ phoneLabel }}</span>
                            <span>{{ phone }}</span>
                        </div>
                    </div>
                </div>
                <div class="details-content-data">
                    <div class="details-content-data-name">{{ addressLabel }}</div>
                    <div class="details-content-data-value">{{ address }}</div>
                </div>
                <div class="details-content-data">
                    <div class="details-content-data-name">{{ roleLabel }}</div>
                    <div class="details-content-data-value">{{ role }}</div>
                </div>
                <div class="details-content-values"></div>
            </div>
            <div class="details-content-right">
                <div class="details-content-right-image-container">
                    <div class="details-content-right-image" :style="{ backgroundImage: `url(${starkImage})` }"></div>
                </div>
                <div class="details-content-right-container">
                    <div class="details-content-right-data">
                        <div class="details-content-right-data-name">{{ totalAssetsLabel }}</div>
                        <div class="details-content-right-data-value">{{ totalAssets }}</div> 
                    </div>
                    <div class="details-content-right-data">
                        <div class="details-content-right-data-name">{{ totalLiabilitiesLabel }}</div>
                        <div class="details-content-right-data-value">{{ totalLiabilities }}</div> 
                    </div>
                    <div class="details-content-right-data">
                        <div class="details-content-right-data-name">{{ totalExpensesLabel }}</div>
                        <div class="details-content-right-data-value">{{ totalExpenses }}</div> 
                    </div>
                    <div class="details-content-right-data">
                        <div class="details-content-right-data-name">{{ netWorthLabel }}</div>
                        <div class="details-content-right-data-value">{{ netWorth }}</div> 
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';
import SvgIcon from '../../images/SvgIcon.vue';
import starkImage from '../../../assets/images/stark.png';

defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'details-' + componentId);

export interface DetailsProps {
    clientLabel: string;
    clientName: string;
    editLink: string;
    modeLink: string;
    modeLabel: string;
    detailsLink: string;
    detailsLabel: string;
    cashFlowLink: string;
    cashFlowLabel: string;
    netWorthLink: string;
    nameLabel: string;
    contactInfoLabel: string;
    emailLabel: string;
    email: string;
    phoneLabel: string;
    phone: string;
    addressLabel: string;
    address: string;
    roleLabel: string;
    role: string;
    totalAssetsLabel: string;
    totalAssets: string;
    totalLiabilitiesLabel: string;
    totalLiabilities: string;
    totalExpensesLabel: string;
    totalExpenses: string;
    netWorthLabel: string;
    netWorth: string;
}

const props = withDefaults(defineProps<DetailsProps>(), {
    clientLabel: 'client',
    clientName: 'Tony Stark',
    editLink: './Edit.vue',
    modeLink: './Delete.vue',
    modeLabel: '$ ADVISORY MODE',
    detailsLink: './Details.vue',
    detailsLabel: 'Details',
    cashFlowLink: './cash-flow',
    cashFlowLabel: 'Cash Flow',
    netWorthLink: './net-worth',
    netWorthLabel: 'Net Worth',
    nameLabel: 'Name',
    contactInfoLabel: 'Contact Information',
    emailLabel: 'Email',
    email: '<EMAIL>',
    phoneLabel: 'Phone',
    phone: '+****************',
    addressLabel: 'Address',
    address: '1600 Amphitheatre Parkway, Mountain View, CA 94043',
    roleLabel: 'Role',
    role: 'User',
    totalAssetsLabel: 'Total assets',
    totalAssets: '$683K',
    totalLiabilitiesLabel: 'Total liabilities',
    totalLiabilities: '$150K',
    totalExpensesLabel: 'Total expenses',
    totalExpenses: '$15.7K',
    netWorthLabel: 'Net worth',
    netWorth: '$1.54M'
});

const emit = defineEmits<{
    change: [id: number]
}>();
</script>

// Inline CSS
<style lang="scss">
.details {
    display: flex;
    flex-direction: column;
    width: 100vh;
    padding: 40px;
    box-sizing: border-box;

    &-container {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-nav {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-bottom: 20px;
        gap: 40px;
        color: #97ABCC;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
        padding-left: 15px;

        &-item {
            cursor: pointer;

            &.active {
                color: #E9EDF1;
            }

            &:hover {
                color: #E9EDF1;
            }
        }
    }

    &-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-bottom: 20px;
        justify-content: space-between;

        &-back {
            cursor: pointer;
            padding-right: 16px;
        }

        &-left {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding-left: 15px;

            &-title {
                display: flex;
                flex-direction: column;

                &-title {
                    color: #57719C;
                    font-size: 10px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 14px;
                    letter-spacing: 1px;
                    word-wrap: break-word;
                }

                &-name {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                }
            }

            &-pfp {
                width: 40px;
                height: 40px;
                background-color: #dee2e6;
                border-radius: 50%;
                margin-right: 1rem;
                border: 3px solid #97ABCC;
                margin-left: 20px;
            }
        }

        &-right {
            display: flex;
            flex-direction: row;
            gap: 10px;

            &-edit {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #97ABCC;
                background-color: rgba(86.51, 112.83, 156.33, 0.20);
                border-radius: 4px;
                width: 40px;
                height: 40px;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif; 
                font-weight: 400;
            }

            &-mode {
                align-self: center;
                background-color: #055EFA;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }

    }

    &-content {
        display: flex;
        flex-direction: column;
        gap: 2px;
        width: 65vh;

        &-data {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            gap: 10px;
            color: #97ABCC;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 20px;
            word-wrap: break-word;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 20px;

            &-name {
                color: #97ABCC;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
                width: 35vh;
            }

            &-value {
                display: flex;
                flex-direction: column;
                color: #97ABCC;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
                width: 35vh;

                &-column {
                    display: flex;
                    flex-direction: column;
                    padding-bottom: 10px;

                    &-label {
                        color: #57719C;
                        font-size: 14px;
                        font-family: 'Oxygen', sans-serif;
                        font-weight: 400;
                        line-height: 20px;
                        word-wrap: break-word;
                    }
                }
            }
        }

        &-right {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            color: #97ABCC;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 20px;
            word-wrap: break-word;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 20px;
            width: 35vh;

            &-container {
                display: flex;
                flex-direction: column;
                gap: 10px;
                width: 100%;
            }

            &-data {
                display: flex;
                justify-content: flex-start;
                flex-direction: row;
                gap: 10px;

                &-name {
                    color: #57719C;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 400;
                    line-height: 14px;
                    word-wrap: break-word;
                    width: 50%;
                }

                &-value {
                    color: #E9EDF1;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 14px;
                    word-wrap: break-word;
                }
            }

            &-image {
                width: 140px;
                height: 140px;
                background-color: #dee2e6;
                border-radius: 50%;
                border: 3px solid #97ABCC;
                background-size: cover;
                background-position: center;

                &-container {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    padding-bottom: 20px;
                    border-bottom: 1px solid rgba(233, 237, 241, 0.1);
                    margin-bottom: 20px;
                    width: 100%;
                }
            }
        }
    }
}
</style>
// Imported CSS
<style lang="scss">
@import "./Details";
</style>
