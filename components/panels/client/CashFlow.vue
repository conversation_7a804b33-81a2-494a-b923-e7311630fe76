<template>
    <div class="cash-flow">
        <div class="cash-flow-header">
            <div class="cash-flow-header-left">
                <NuxtLink class="cash-flow-header-back"><SvgIcon shape="back-arrow" width="24" height="24" /></NuxtLink>
                <span class="cash-flow-header-left-pfp" :style="{ backgroundImage: `url(${starkImage})` }"></span>
                <div class="cash-flow-header-left-title">
                    <span class="cash-flow-header-left-title-title">client</span>
                    <span class="cash-flow-header-left-title-name">{{ clientName }}</span>
                </div>
            </div>
            <div class="cash-flow-header-right">
                <NuxtLink class="cash-flow-header-right-edit" to="./Edit.vue"><svg-icon shape="dot-menu" width="20" height="20" /></NuxtLink>
                <NuxtLink class="cash-flow-header-right-mode" to="./Delete.vue">$ ADVISORY MODE</NuxtLink>
            </div>
        </div>
        <div class="cash-flow-nav">
            <NuxtLink class="cash-flow-nav-item" to="./details.vue">Details</NuxtLink>
            <NuxtLink class="cash-flow-nav-item" to="./dash-flow.vue">Cash Flow</NuxtLink>
            <NuxtLink class="cash-flow-nav-item" to="./net-worth.vue">Net Worth</NuxtLink>
        </div>
        <div class="cash-flow-container">

            <!-- Income Section -->
            <div class="cash-flow-content">
                <div class="cash-flow-content-container">
                    <div class="cash-flow-content-left">Income</div>
                    <div class="cash-flow-content-right">
                        <div>ANNUAL</div>
                        <div>MONTHLY</div>
                    </div>
                </div>
                <div v-for="(item, index) in income" :key="'income-' + index" class="cash-flow-content-entry">
                    <div class="cash-flow-content-entry-left">{{ item.label }}</div>
                    <div class="cash-flow-content-entry-right">
                        <div>${{ item.annual }}</div>
                        <div>${{ item.monthly }}</div>
                    </div>
                </div>
                <div class="cash-flow-content-entry-total-right">
                    <div class="cash-flow-content-entry-total-right-label">Total</div>
                    <div class="cash-flow-content-entry-total-right-value">${{ totalIncome.annual }}</div>
                    <div class="cash-flow-content-entry-total-right-value">${{ totalIncome.monthly }}</div>
                </div>
            </div>

            <!-- Expenses Section -->
            <div class="cash-flow-content">
                <div class="cash-flow-content-container">
                    <div class="cash-flow-content-left">Expenses</div>
                    <div class="cash-flow-content-right">
                        <div>ANNUAL</div>
                        <div>MONTHLY</div>
                    </div>
                </div>
                <div v-for="(item, index) in expenses" :key="'expense-' + index" class="cash-flow-content-entry">
                    <div class="cash-flow-content-entry-left">{{ item.label }}</div>
                    <div class="cash-flow-content-entry-right">
                        <div>${{ item.annual }}</div>
                        <div>${{ item.monthly }}</div>
                    </div>
                </div>
                <div class="cash-flow-content-entry-total-right">
                    <div class="cash-flow-content-entry-total-right-label">Total</div>
                    <div class="cash-flow-content-entry-total-right-value">${{ totalExpenses.annual }}</div>
                    <div class="cash-flow-content-entry-total-right-value">${{ totalExpenses.monthly }}</div>
                </div>
            </div>

            <!-- Savings Section -->
            <div class="cash-flow-content">
                <div class="cash-flow-content-container">
                    <div class="cash-flow-content-left">Savings</div>
                    <div class="cash-flow-content-right">
                        <div>ANNUAL</div>
                        <div>MONTHLY</div>
                    </div>
                </div>
                <div v-for="(item, index) in savings" :key="'saving-' + index" class="cash-flow-content-entry">
                    <div class="cash-flow-content-entry-left">{{ item.label }}</div>
                    <div class="cash-flow-content-entry-right">
                        <div>${{ item.annual }}</div>
                        <div>${{ item.monthly }}</div>
                    </div>
                </div>
                <div class="cash-flow-content-entry-total">
                    <div class="cash-flow-content-entry-total-right">
                        <div class="cash-flow-content-entry-total-right-label">Total</div>
                        <div class="cash-flow-content-entry-total-right-value">${{ totalSavings.annual }}</div>
                        <div class="cash-flow-content-entry-total-right-value">${{ totalSavings.monthly }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import SvgIcon from '../../images/SvgIcon.vue';
import starkImage from '../../../assets/images/stark.png';

interface Item {
    label: string;
    annual: number;
    monthly: number;
}

// Static data based on your provided information
const income: Item[] = [
    { label: 'Earned', annual: 180000, monthly: 31667 }
];

const expenses: Item[] = [
    { label: 'Committed', annual: 30000, monthly: 2500 },
    { label: 'Discretionary', annual: 25600, monthly: 2133 },
    { label: 'Housing', annual: 12447, monthly: 1037 },
    { label: 'Auto Loans', annual: 16029, monthly: 1336 },
    { label: 'Business Loans', annual: 25000, monthly: 8768 },
    { label: 'Taxes', annual: 105210, monthly: 8768 }
];

const savings: Item[] = [
    { label: 'Cash', annual: 27000, monthly: 2500 },
    { label: 'Education', annual: 58000, monthly: 4830 },
    { label: 'Non-Qualified Savings', annual: 1200, monthly: 100 },
    { label: 'Retirement', annual: 28400, monthly: 10334 }
];

const props = defineProps<{
    clientName?: string;
}>();

const clientName = props.clientName || 'Tony Stark';

// Function to format numbers with commas
const formatNumber = (value: number) => value.toLocaleString();

const totalIncome = computed(() => {
    const totalAnnual = income.reduce((sum, item) => sum + item.annual, 0);
    const totalMonthly = income.reduce((sum, item) => sum + item.monthly, 0);
    return { annual: formatNumber(totalAnnual), monthly: formatNumber(totalMonthly) };
});

const totalExpenses = computed(() => {
    const totalAnnual = expenses.reduce((sum, item) => sum + item.annual, 0);
    const totalMonthly = expenses.reduce((sum, item) => sum + item.monthly, 0);
    return { annual: formatNumber(totalAnnual), monthly: formatNumber(totalMonthly) };
});

const totalSavings = computed(() => {
    const totalAnnual = savings.reduce((sum, item) => sum + item.annual, 0);
    const totalMonthly = savings.reduce((sum, item) => sum + item.monthly, 0);
    return { annual: formatNumber(totalAnnual), monthly: formatNumber(totalMonthly) };
});

// Format items with commas
// const formattedIncome = computed(() => income.map(item => ({
//     ...item,
//     annual: formatNumber(item.annual),
//     monthly: formatNumber(item.monthly)
// })));
//
// const formattedExpenses = computed(() => expenses.map(item => ({
//     ...item,
//     annual: formatNumber(item.annual),
//     monthly: formatNumber(item.monthly)
// })));
//
// const formattedSavings = computed(() => savings.map(item => ({
//     ...item,
//     annual: formatNumber(item.annual),
//     monthly: formatNumber(item.monthly)
// })));
</script>

<style lang="scss">
.cash-flow {
    display: flex;
    flex-direction: column;
    width: 60vw;
    padding: 40px;

    &-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-nav {
        display: flex;
        gap: 40px;
        padding-bottom: 20px;
        color: #97ABCC;
        font-size: 14px;
        font-weight: 700;
        padding-left: 15px;

        &-item {
            cursor: pointer;
            &.active, &:hover {
                color: #E9EDF1;
            }
        }
    }

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;

        &-back {
        color: #57719C;
        cursor: pointer;

        &:hover {
            color: #E9EDF1;
        }
    }

        &-left {
            display: flex;
            align-items: center;
            padding-left: 15px;

            &-title {
                display: flex;
                flex-direction: column;

                &-title {
                    color: #57719C;
                    font-size: 10px;
                    font-weight: 700;
                }

                &-name {
                    color: white;
                    font-size: 14px;
                    font-weight: 700;
                }
            }

            &-pfp {
                width: 40px;
                height: 40px;
                background-color: #dee2e6;
                border-radius: 50%;
                margin-right: 1rem;
                border: 3px solid #97ABCC;
                margin-left: 20px;
                background-size: cover;
                background-position: center;
            }
        }

        &-right {
            display: flex;
            gap: 10px;

            &-edit {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #97ABCC;
                background-color: rgba(86, 112, 156, 0.20);
                border-radius: 4px;
                width: 40px;
                height: 40px;
                font-size: 14px;
                font-weight: 400;

                &:hover {
                    background-color: rgba(86, 112, 156, 0.40);
                }
            }

            &-mode {
                background-color: #055EFA;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                align-self: center;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }

    &-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-container {
            display: flex;
            flex-direction: row;
            width: 100%;
        }

        &-entry {
            display: flex;
            flex-direction: row;
            width: 100%;
            

            &-left, &-right {
                div {
                    box-sizing: border-box;
                    padding: 10px 20px;
                    border-left: 1px solid rgba(255, 255, 255, 0.05);
                    min-width: 66px;
                }
            }

            &-left {
                display: flex;
                color: #E9EDF1;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                word-wrap: break-word;
                width: 100%;
                align-items: center;
                padding: 10px 20px;
            }

            &-right {
                font-size: 14px;
                color: #97ABCC;
                display: flex;
                flex-direction: row;
                min-width: 200px;

                div {
                    min-width: 100px;
                    display: flex;
                    justify-content: flex-end;
                }
            }

            &-total-right {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                padding: 20px 20px 20px 0;
                border-top: 1px solid rgba(255, 255, 255, 0.05);

                &-label {
                    color: #97ABCC;
                }
                &-value {
                    min-width: 100px;
                    display: flex;
                    justify-content: flex-end;
                    color: #E9EDF1;
                }
            }
        }

        &-left, &-right {
            div {
                box-sizing: border-box;
                padding: 10px 20px;
                border-left: 1px solid rgba(255, 255, 255, 0.05);
                min-width: 66px;
            }
        }

        &-left {
            display: flex;
            font-size: 14px;
            font-weight: 700;
            color: #E9EDF1;
            background-color: rgba(255, 255, 255, 0.05);
            width: 100%;
            align-items: center;
            box-sizing: border-box;
            padding: 10px 20px;
        }

        &-right {
            font-size: 14px;
            color: #97ABCC;
            display: flex;
            flex-direction: row;
            background-color: rgba(255, 255, 255, 0.05);
            min-width: 200px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);

            div {
                min-width: 100px;
            }
        }
    }
}
</style>
