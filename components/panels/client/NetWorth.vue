<template>
    <div class="net-worth">
        <div class="net-worth-header">
            <div class="net-worth-header-left">
                <NuxtLink class="net-worth-header-back" to="/"><SvgIcon :shape="'back-arrow'" width="24" height="24" /></NuxtLink>
                <span class="net-worth-header-left-pfp" :style="{ backgroundImage: `url(${starkImage})` }"></span>
                <div class="net-worth-header-left-title">
                    <span class="net-worth-header-left-title-title">client</span>
                    <span class="net-worth-header-left-title-name">{{ clientName }}</span>
                </div>
            </div>
            <div class="net-worth-header-right">
                <NuxtLink class="net-worth-header-right-edit" to="/edit"><SvgIcon shape="dot-menu" width="20" height="20" /></NuxtLink>
                <NuxtLink class="net-worth-header-right-mode" to="/delete">$ ADVISORY MODE</NuxtLink>
            </div>
        </div>
        <div class="net-worth-nav">
            <NuxtLink class="net-worth-nav-item" to="/details">Details</NuxtLink>
            <NuxtLink class="net-worth-nav-item" to="/cashflow">Cash Flow</NuxtLink>
            <NuxtLink class="net-worth-nav-item" to="/networth">Net Worth</NuxtLink>
        </div>
        <div class="net-worth-container">

            <!-- Assets Section -->
            <div class="net-worth-content">
                <div class="net-worth-content-container">
                    <div class="net-worth-content-left">Assets</div>
                    <div class="net-worth-content-right">
                        <div>VALUE</div>
                    </div>
                </div>
                <div v-for="(item, index) in assets" :key="'asset-' + index" class="net-worth-content-entry">
                    <div class="net-worth-content-entry-left">{{ item.label }}</div>
                    <div class="net-worth-content-entry-right">
                        <div>${{ item.monthly }}</div>
                    </div>
                </div>
                <div class="net-worth-content-entry-total-right">
                    <div class="net-worth-content-entry-total-right-label">Total</div>
                    <div class="net-worth-content-entry-total-right-value">${{ totalAssets.monthly }}</div>
                </div>
            </div>

            <!-- Liabilities Section -->
            <div class="net-worth-content">
                <div class="net-worth-content-container">
                    <div class="net-worth-content-left">Liabilities</div>
                    <div class="net-worth-content-right">
                        <div>BALANCE</div>
                    </div>
                </div>
                <div v-for="(item, index) in liabilities" :key="'liability-' + index" class="net-worth-content-entry">
                    <div class="net-worth-content-entry-left">{{ item.label }}</div>
                    <div class="net-worth-content-entry-right">
                        <div>${{ item.monthly }}</div>
                    </div>
                </div>
                <div class="net-worth-content-entry-total-right">
                    <div class="net-worth-content-entry-total-right-label">Total</div>
                    <div class="net-worth-content-entry-total-right-value">${{ totalLiabilities.monthly }}</div>
                </div>
            </div>

            <!-- Net Worth Section -->
            <div class="net-worth-content">
                <div class="net-worth-content-container">
                    <div class="net-worth-content-left">Net Worth</div>
                    <div class="net-worth-content-right">
                        <div>TOTAL</div>
                    </div>
                </div>
                <div class="net-worth-content-entry-total-right">
                    <div class="net-worth-content-entry-total-right-label">Total</div>
                    <div class="net-worth-content-entry-total-right-value">${{ netWorth.monthly }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import SvgIcon from '../../images/SvgIcon.vue';
import starkImage from '../../../assets/images/stark.png';

interface Item {
    label: string;
    value: number;
    balance: number;
}

const assets: Item[] = [
    { label: 'Real Estate', annual: 500000, monthly: 41667 },
    { label: 'Investments', annual: 300000, monthly: 25000 },
    { label: 'Savings', annual: 50000, monthly: 4167 },
    { label: 'Personal Property', annual: 200000, monthly: 16667 }
];

const liabilities: Item[] = [
    { label: 'Mortgage', annual: 100000, monthly: 8333 },
    { label: 'Loans', annual: 50000, monthly: 4167 },
    { label: 'Credit Card Debt', annual: 20000, monthly: 1667 }
];

const props = defineProps<{
    clientName?: string;
}>();

const clientName = props.clientName || 'Tony Stark';

const formatNumber = (value: number) => value.toLocaleString();

const totalAssets = computed(() => {
    const totalAnnual = assets.reduce((sum, item) => sum + item.annual, 0);
    const totalMonthly = assets.reduce((sum, item) => sum + item.monthly, 0);
    return { annual: totalAnnual, monthly: totalMonthly };
});

const totalLiabilities = computed(() => {
    const totalAnnual = liabilities.reduce((sum, item) => sum + item.annual, 0);
    const totalMonthly = liabilities.reduce((sum, item) => sum + item.monthly, 0);
    return { annual: totalAnnual, monthly: totalMonthly };
});

const netWorth = computed(() => {
    const annualNetWorth = totalAssets.value.annual - totalLiabilities.value.annual;
    const monthlyNetWorth = totalAssets.value.monthly - totalLiabilities.value.monthly;
    return { annual: formatNumber(annualNetWorth), monthly: formatNumber(monthlyNetWorth) };
});
</script>

<style lang="scss">
.net-worth {
    display: flex;
    flex-direction: column;
    width: 60vw;
    padding: 40px;

    &-container {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    &-nav {
        display: flex;
        gap: 40px;
        padding-bottom: 20px;
        color: #97ABCC;
        font-size: 14px;
        font-weight: 700;
        padding-left: 15px;

        &-item {
            cursor: pointer;
            &.active, &:hover {
                color: #E9EDF1;
            }
        }
    }

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 20px;

        &-back {
            color: #57719C;
            cursor: pointer;

            &:hover {
                color: #E9EDF1;
            }
        }

        &-left {
            display: flex;
            align-items: center;
            padding-left: 15px;

            &-title {
                display: flex;
                flex-direction: column;

                &-title {
                    color: #57719C;
                    font-size: 10px;
                    font-weight: 700;
                }

                &-name {
                    color: white;
                    font-size: 14px;
                    font-weight: 700;
                }
            }

            &-pfp {
                width: 40px;
                height: 40px;
                background-color: #dee2e6;
                border-radius: 50%;
                margin-right: 1rem;
                border: 3px solid #97ABCC;
                margin-left: 20px;
                background-size: cover;
                background-position: center;
            }
        }

        &-right {
            display: flex;
            gap: 10px;

            &-edit {
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                color: #97ABCC;
                background-color: rgba(86, 112, 156, 0.20);
                border-radius: 4px;
                width: 40px;
                height: 40px;
                font-size: 14px;
                font-weight: 400;

                &:hover {
                    background-color: rgba(86, 112, 156, 0.40);
                }
            }

            &-mode {
                background-color: #055EFA;
                color: white;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                align-self: center;
                cursor: pointer;

                &:hover {
                    background-color: #0056b3;
                }
            }
        }
    }

    &-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        &-container {
            display: flex;
            flex-direction: row;
            width: 100%;
        }

        &-entry {
            display: flex;
            flex-direction: row;
            width: 100%;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);

            &-left, &-right {
                div {
                    box-sizing: border-box;
                    padding: 10px 20px;
                    border-left: 1px solid rgba(255, 255, 255, 0.05);
                    min-width: 66px;
                }
            }

            &-left {
                display: flex;
                color: #E9EDF1;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                word-wrap: break-word;
                width: 100%;
                align-items: center;
                padding-left: 20px;
            }

            &-right {
                font-size: 14px;
                color: #97ABCC;
                display: flex;
                flex-direction: row;

                div {
                    display: flex;
                    justify-content: flex-end;
                    min-width: 106px;
                    max-width: 106px;
                }
            }

            &-total-right {
                display: flex;
                flex-direction: row;
                justify-content: flex-end;
                align-items: center;
                padding: 20px 20px 20px 0;
                border-top: 1px solid rgba(255, 255, 255, 0.05);

                &-label {
                    color: #97ABCC;
                }
                &-value {
                    display: flex;
                    justify-content: flex-end;
                    color: #E9EDF1;
                    min-width: 106px;
                    max-width: 106px;
                }
            }
        }

        &-left, &-right {
            div {
                box-sizing: border-box;
                padding: 10px 20px;
                border-left: 1px solid rgba(255, 255, 255, 0.05);
                min-width: 66px;
            }
        }

        &-left {
            display: flex;
            font-size: 14px;
            font-weight: 700;
            color: #E9EDF1;
            background-color: rgba(255, 255, 255, 0.05);
            width: 100%;
            align-items: center;
            box-sizing: border-box;
            padding-left: 20px;
        }

        &-right {
            font-size: 14px;
            color: #97ABCC;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            min-width: 106px;
            max-width: 106px;

            div {
                display: flex;
                justify-content: flex-end;
                min-width: 100%;
            }
        }
    }
}
</style>
