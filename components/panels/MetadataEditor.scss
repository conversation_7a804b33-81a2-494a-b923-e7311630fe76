@import '/assets/css/mixins';

.metadata-editor {
    display: flex;
    flex-direction: column;
    width: 360px;
    background-color: rgb(48 60 80);
}

.metadata-editor-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 20px;
    padding-right: 0px;
    color: white;
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
    align-items: center;
}

.metadata-editor-actions {
    display: flex;
    flex-direction: row;
    color: rgba(255, 255, 255, 0.5);
}

.metadata-editor-actions-svg {
    margin: 5px;
    cursor: pointer;

    &:hover {
        color: #97abcc;
    }
}

.metadata-editor-input {
    color: var(--secondary-grey, #97abcc);
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.metadata-editor-highlighted {
    color: #d6dce4;
}

.metadata-editor-input .text-input-input {
    color: #fff !important;
    opacity: 1;
    transition: color 0.2s;
}

.metadata-editor-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.metadata-editor-info-row-tooltip {
    cursor: pointer;

    &:hover {
        color: #d6dce4;
    }
}

.metadata-editor-icon {
    display: flex;
    flex-direction: row;
    padding-top: 10px;
}

.metadata-editor-checkbox-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 10px;
    align-items: flex-start;
    padding-top: 10px;
}
