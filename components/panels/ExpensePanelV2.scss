@import '/assets/css/mixins';

.expense-panel-v2 {
    display: flex;
    flex-direction: column;
    width: 980px;
    background: rgb(37, 49, 70);
}

.expense-panel-v2-header-information-kpi {
    display: flex;
    flex-direction: row;
    gap: 20px;
    padding-right: 20px;
}

.expense-panel-v2-header-information-kpi-value {
    color: white;
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}

.expense-panel-v2-header-information-kpi-type {
    color: white;
    opacity: 0.5;
    font-size: 12px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}

.expense-panel-v2-body-table-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.expense-panel-v2-body-table-row > span {
    padding: 5px;
}

.expense-panel-v2-body-table-header-placeholder {
    width: 20px;
    height: 40px;
}

.expense-panel-v2-header-information {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
}

.expense-panel-v2-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 10px;
}

.expense-panel-v2-header-label {
    margin-left: 10px;
    color: white;
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}

.expense-panel-v2-body-table-header-placeholder {
    width: 20px;
    height: 40px;
}

.expense-panel-v2-checkbox-column {
    padding: 13px;
    background-color: rgb(48 60 79);
    margin: 1px;
}

.expense-panel-v2-body-table-header {
    display: flex;
    flex-direction: row;
    width: 100%;
    color: rgba(255, 255, 255, 0.5);
    font-size: 10px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.2px;
    word-wrap: break-word;

    span {
        display: flex;
        align-items: center;
    }

    span:first-child {
        flex: 0 0 20px;
        min-width: 20px;
        max-width: 20px;
    }

    span:nth-child(2) {
        flex: 0 0 auto; // shrink to checkbox width
        padding: 13px;
    }

    span:nth-child(3) {
        flex: 2;
        justify-content: flex-start;
        padding: 10px;
    }

    span:not(:first-child):not(:nth-child(2)):not(:nth-child(3)) {
        flex: 1;
        justify-content: center;
        padding: 10px;
        justify-content: flex-end;
    }
}

.expense-panel-v2-add-line-button {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 10px;
    width: 100%;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}
