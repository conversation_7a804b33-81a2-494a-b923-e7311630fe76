<template>
    <div class="metadata-editor">
        <div class="metadata-editor-header">
            {{ title }}
            <div class="metadata-editor-actions">
                <SvgIcon class="metadata-editor-actions-svg" shape="dot-menu" />
                <SvgIcon class="metadata-editor-actions-svg" shape="close" />
            </div>
        </div>
        <div class="metadata-editor-input">
            <div class="metadata-editor-row">
                <span>{{ category }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="metadata-editor-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('category')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'category'" class="tooltip">
                        The name of your custom expense category.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedCategory" :options="['Entertainment']" />
        </div>
        <div class="metadata-editor-input">
            <div class="metadata-editor-row">
                <span
                    >Is this expense <span class="metadata-editor-highlighted">discretionary</span>,
                    <span class="metadata-editor-highlighted">committed,</span> or charitable?</span
                >
                <div class="tooltip-container">
                    <SvgIcon
                        class="metadata-editor-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('expenseType')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'expenseType'" class="tooltip">
                        Set the default classification for this category to facilitate high-level budget analysis.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedExpenseType" :options="['Discretionary', 'Commited', 'Charitable']" />
        </div>

        <div class="metadata-editor-input">
            <div class="metadata-editor-row">
                <span>Category icon</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="metadata-editor-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('icon')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'icon'" class="tooltip">
                        Choose an icon to represent this category.
                    </div>
                </div>
            </div>
            <div class="metadata-editor-icon">
                <SvgIcon
                    class="metadata-editor-info-row-tooltip"
                    width="30px"
                    height="30px"
                    :shape="selectedIconShape"
                />
                <MetaDropdown class="icon-selection-dropdown" v-model="selectedIconShape" :options="iconOptions" />
            </div>
        </div>
        <div class="metadata-editor-input">
            <div class="metadata-editor-row">
                <span>{{ columns }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="metadata-editor-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('columns')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'columns'" class="tooltip">
                        Select which details appear for expenses within this category.
                    </div>
                </div>
            </div>
            <div class="metadata-editor-checkbox-group">
                <CheckboxInput label="Description" />
                <CheckboxInput label="Owner" />
                <CheckboxInput label="Type" />
                <CheckboxInput label="Amount" />
                <CheckboxInput label="Anual total" />
                <CheckboxInput label="Frequency" />
                <CheckboxInput label="Start date" />
                <CheckboxInput label="End date" />
                <CheckboxInput label="Discretionary status" />
            </div>
        </div>
        <div class="metadata-editor-input">
            <div class="metadata-editor-row">
                <span>{{ metrics }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="metadata-editor-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('metrics')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'metrics'" class="tooltip">
                        Choose which aggregated financial metrics are shown for this category.
                    </div>
                </div>
            </div>
            <div class="metadata-editor-checkbox-group">
                <CheckboxInput label="Monthly total" />
                <CheckboxInput label="Annual total" />
                <CheckboxInput label="Percentage of income" />
                <CheckboxInput label="Commited vs discretionary" />
                <CheckboxInput label="Commited" />
                <CheckboxInput label="Discretionary" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import MetaDropdown from '../forms/MetaDropdown.vue';
import CheckboxInput from '../forms/CheckboxInput.vue';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = getCurrentInstance()?.uid || Math.random().toString(36).substr(2, 9);
const elementId = computed(() => 'metadata-editor-' + componentId);

export interface MetadataEditorProps {
    foo: string;
    title: string;
    category: string;
    columns: string;
    metrics: string;
}
const props = withDefaults(defineProps<MetadataEditorProps>(), {
    foo: 'bar',
    title: 'Title',
    category: 'category',
    columns: 'Columns to display in list',
    metrics: 'Metrics to display',
});

const selectedCategory = ref();
const selectedExpenseType = ref();
const selectedIconShape = ref('info');
const selectedColumns = ref();
const activeTooltip = ref<string | null>(null);

// Define icon options as a computed property for better performance
const iconOptions = [
    'account',
    'asterisk',
    'arrow-back',
    'arrow-circle-right',
    'arrow-forward',
    'assets',
    'back-arrow',
    'budget',
    'camera',
    'calculator',
    'calendar',
    'chat',
    'checkbox',
    'checkmark',
    'close',
    'dashboard',
    'debt',
    'dollar',
    'dollar-sign',
    'dot-menu',
    'download',
    'drag',
    'edit',
    'eye',
    'filter',
    'financial-profile',
    'flag',
    'hide',
    'grid',
    'grid-small',
    'inbox',
    'info',
    'keyboard-arrow-down',
    'keyboard-arrow-left',
    'keyboard-arrow-right',
    'keyboard-arrow-up',
    'list',
    'lock',
    'logout',
    'long-arrow',
    'minus',
    'payments',
    'percent',
    'person',
    'plus',
    'priority',
    'reports',
    'reply',
    'search',
    'settings',
    'show',
    'taxes',
    'thumbnail',
    'thumbs-down',
    'thumbs-up',
    'timeline',
    'trash',
    'unlink',
    'upload',
    'people',
    'pending',
    'form',
];

// Tooltip functions
function showTooltip(tooltipType: string) {
    activeTooltip.value = tooltipType;
}

function hideTooltip() {
    activeTooltip.value = null;
}

const emit = defineEmits<{
    change: [id: number];
}>();
</script>

<style lang="scss">
@import './MetadataEditor';

// Specific styling for the icon selection dropdown
.icon-selection-dropdown {
    .meta-dropdown-select {
        color: rgb(48 60 80);

        &:focus,
        &:hover {
            color: #fff;
        }
    }
}

.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip {
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translateX(-50%);
    background: #23272f;
    color: #e6eaf0;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 220px;
    max-width: 340px;
    white-space: normal;
    text-align: center;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
    margin-bottom: 12px;

    &::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #23272f;
    }
}
</style>
