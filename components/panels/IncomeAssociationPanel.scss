@import "/assets/css/mixins";

.income-association-panel {
  display: flex;
  flex-direction: column;
  width: 463px;
  gap: 10px;

  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    width: 100%;
    color: #e9edf1;
    font-size: 18px;
    font-family: "Oxygen";
    font-weight: 700;
    line-height: 44px;
    word-wrap: break-word;
  }

  &-body {
    display: flex;
    flex-direction: column;
  }
}

.income-association-panel-body-item {
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 10px;
  background-color: rgba(255, 255, 255, 0.05);

  &-value {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    color: #e9edf1;
    font-size: 14px;
    font-family: Oxygen, sans-serif;
    font-weight: 400;
    line-height: 20px;
    word-wrap: break-word;
  }

  &-input {
    &:focus {
      outline: none;
    }

    background-color: transparent;
  }
}

.income-association-panel-body-item-label {
  color: #97abcc;
  font-size: 14px;
  font-family: Oxygen, sans-serif;
  font-weight: 400;
  line-height: 20px;
  word-wrap: break-word;
}

.income-association-panel-body-divider {
  width: 100%;
  align-self: center;
  height: 1px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}
