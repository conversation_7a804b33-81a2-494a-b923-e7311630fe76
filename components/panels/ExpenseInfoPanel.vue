<template>
    <div class="expense-info-panel">
        <div class="expense-info-panel-title">
            {{ title }}
            <div class="expense-info-panel-actions">
                <SvgIcon class="expense-info-panel-actions-svg" shape="dot-menu" />
                <SvgIcon class="expense-info-panel-actions-svg" shape="close" />
            </div>
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>{{ ownership }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('ownership')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'ownership'" class="tooltip">
                        Indicate if this expense is shared with another person or entity.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedOption" :options="['Joint', 'Single']" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>{{ category }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('category')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'category'" class="tooltip">
                        Assign this expense to a specific category to customize your organization and reporting.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedCategory" :options="['Entertainment']" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>
                    Is this expense <span class="expense-info-panel-highlighted">discretionary</span>,
                    <span class="expense-info-panel-highlighted">committed,</span> or charitable?
                </span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('expenseType')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'expenseType'" class="tooltip">
                        Classify this expense for high-level budget analysis.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedExpenseType" :options="['Discretionary', 'Commited', 'Charitable']" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>{{ type }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('type')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'type'" class="tooltip">
                        Define if this is a one-time or recurring expense.
                    </div>
                </div>
            </div>
            <MetaDropdown v-model="selectedType" :options="['Recurring', 'One time']" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>{{ recurrence }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('recurrence')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'recurrence'" class="tooltip">
                        Set the frequency and specific day for recurring expenses.
                    </div>
                </div>
            </div>
            <div class="expense-info-panel-double-inputs">
                <MetaDropdown
                    class="expense-info-panel-inputs"
                    v-model="selectedFrequency"
                    :options="['Monthly', 'Weekly', 'Bi-weekly']"
                />
                <span class="expense-info-panel-preposition">on the</span>
                <MetaDropdown class="expense-info-panel-inputs" v-model="selectedDetail" :options="['1st', '15th']" />
            </div>
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>Does this expense have an <span class="expense-info-panel-highlighted">start date</span>?</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('startDate')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'startDate'" class="tooltip">
                        Specify the date this expense begins.
                    </div>
                </div>
            </div>
            <TextInput class="expense-info-panel-text-input" :show-border-on-focus="true" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>Does this expense have an <span class="expense-info-panel-highlighted">end date</span>?</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('endDate')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'endDate'" class="tooltip">
                        Specify the date this expense is expected to end.
                    </div>
                </div>
            </div>
            <TextInput class="expense-info-panel-text-input" :show-border-on-focus="true" />
        </div>
        <div class="expense-info-panel-input">
            <div class="expense-info-panel-row">
                <span>{{ rate }}</span>
                <div class="tooltip-container">
                    <SvgIcon
                        class="expense-panel-info-row-tooltip"
                        width="20px"
                        height="20px"
                        shape="info"
                        @mouseenter="showTooltip('rate')"
                        @mouseleave="hideTooltip"
                    />
                    <div v-if="activeTooltip === 'rate'" class="tooltip">
                        Project future cost increases for this expense.
                    </div>
                </div>
            </div>
            <TextInput class="expense-info-panel-text-input" :show-border-on-focus="true" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import TextInput from '../forms/TextInput.vue';
import MetaDropdown from '../forms/MetaDropdown.vue';
/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'expense-info-panel-' + componentId);

export interface ExpenseInfoPanelProps {
    foo: string;
    title: string;
    ownership: string;
    category: string;
    type: string;
    recurrence: string;
    rate: string;
    showBorderOnFocus?: boolean;
}
const props = withDefaults(defineProps<ExpenseInfoPanelProps>(), {
    foo: 'bar',
    title: 'Label',
    ownership: `Is this expense jointly owned?`,
    category: 'Expense category',
    type: 'What type of expense is this?',
    recurrence: 'When does this expense recur?',
    rate: 'Inflation rate for this expense',
    showBorderOnFocus: false,
});
const selectedOption = ref();
const selectedCategory = ref();
const selectedType = ref();
const selectedExpenseType = ref();
const selectedFrequency = ref();
const selectedDetail = ref();

const activeTooltip = ref<string | null>(null);

function showTooltip(tooltipType: string) {
    activeTooltip.value = tooltipType;
}
function hideTooltip() {
    activeTooltip.value = null;
}

const emit = defineEmits<{
    change: [id: number];
}>();
</script>

<style lang="scss">
@import './ExpenseInfoPanel';

.tooltip-container {
    position: relative;
    display: inline-block;
}

.tooltip {
    position: absolute;
    left: 50%;
    bottom: 100%;
    transform: translateX(-50%);
    background: #23272f;
    color: #e6eaf0;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    min-width: 220px;
    max-width: 340px;
    white-space: normal;
    text-align: center;
    z-index: 1000;
    pointer-events: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.18);
    margin-bottom: 12px;

    &::before {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-top: 8px solid #23272f;
    }
}
</style>
