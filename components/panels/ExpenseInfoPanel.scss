@import '/assets/css/mixins';

.expense-info-panel {
    display: flex;
    flex-direction: column;
    width: 360px;
    background-color: rgb(48 60 80);
}

.expense-info-panel-actions {
    display: flex;
    flex-direction: row;
    color: rgba(255, 255, 255, 0.5);
}

.expense-info-panel-actions-svg {
    margin: 5px;
    cursor: pointer;

    &:hover {
        color: #97abcc;
    }
}

.expense-info-panel-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 20px;
    padding-right: 0px;
    color: white;
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}

.expense-info-panel-highlighted {
    color: #d6dce4;
}

.expense-info-panel-input {
    color: var(--secondary-grey, #97abcc);
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
    display: flex;
    flex-direction: column;
    padding: 20px;
}

.expense-info-panel-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.expense-info-panel-text-input .text-input-input {
    color: #fff !important;
    opacity: 1;
    transition: color 0.2s;
}

.expense-info-panel-input:focus-within {
    background-color: rgb(58 70 89);
    transition: background 0.2s;
}

.expense-panel-info-row-tooltip {
    cursor: pointer;

    &:hover {
        color: #d6dce4;
    }
}

.expense-info-panel-double-inputs {
    display: flex;
    flex-direction: row;
    width: calc(100% - 20px);
    justify-content: space-between;
    align-items: center;
}

.expense-info-panel-inputs {
    max-width: 100px;
}

.expense-info-panel-preposition {
    margin-left: -30px;
}
