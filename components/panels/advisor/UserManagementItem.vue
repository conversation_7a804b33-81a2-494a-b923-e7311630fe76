﻿<template>
    <div class="cards-list-item">
        <div class="cards-equity-bar">
            <div class="cards-equity-bar-left">
                <UserAvatar :user="userItem" />
                <div class="cards-equity-bar-left-description">
                    <div class="cards-equity-bar-left-description-name">{{ userItem.firstName }} {{ userItem.lastName }}</div>
                    <div class="cards-equity-bar-left-description-title">{{ userItem.role }}</div>
                </div>
            </div>
            <div class="cards-equity-bar-right" v-if="userItem.status === 'Joined' || userItem.status === 'Self' || userItem.status === 'Impersonated'">
                <div class="cards-equity">
                    <span class="cards-equity-value">{{ formatAmount(userItem.totalAssets) }}</span>
                    <span class="cards-equity-label">Total Assets</span>
                </div>
                <div class="cards-equity">
                    <span class="cards-equity-value">{{ formatAmount(userItem.totalLiabilities) }}</span>
                    <span class="cards-equity-label">Total Liabilities</span>
                </div>
                <div class="cards-equity">
                    <span class="cards-equity-value">{{ formatAmount(userItem.netWorth) }}</span>
                    <span class="cards-equity-label">Net Worth</span>
                </div>
                <div class="cards-equity" v-if="userItem.accounts && userItem.accounts > 1">
                    <span class="cards-equity-value">{{ userItem.accounts }}</span>
                    <span class="cards-equity-label">Accounts</span>
                </div>
                <div class="cards-equity">
                    <span class="cards-equity-value">{{ userItem.onboarded }}%</span>
                    <span class="cards-equity-label">Onboarded</span>
                </div>
                <div class="cards-joined-date" v-if="userItem.status === 'Joined'">
                    <span class="cards-joined-date-value">{{ distanceToNow(userItem.joinDate) }}</span>
                    <span class="cards-joined-date-label">Joined</span>
                </div>
                <div class="cards-actions" v-if="userItem.status === 'Joined'">
                    <Button :svg-icon="{shape: 'checkmark'}" @click.prevent="$emit('impersonate', userItem)">Impersonate</Button>
                </div>
                <div class="cards-actions" v-if="userItem.status === 'Impersonated'">
                    <Button :svg-icon="{shape: 'close'}" @click.prevent="$emit('stopImpersonate', userItem)">Stop</Button>
                </div>
            </div>
            <div class="cards-equity-bar-right" v-if="userItem.status === 'Pending'">
                <div class="cards-joined-date">
                    <span class="cards-joined-date-label">Status</span>
                    <span class="cards-joined-date-value">Pending</span>
                </div>
                <div class="cards-actions cards-actions-pending">
                    <svg-icon shape="pending" width="20" height="20"/>
                </div>
            </div>
            <div class="cards-equity-bar-right" v-if="userItem.status === 'Declined' || userItem.status === 'Revoked'">
                <div class="cards-joined-date">
                    <span class="cards-joined-date-label">Status</span>
                    <span class="cards-joined-date-value">{{ userItem.status }}</span>
                </div>
                <div class="cards-actions cards-actions-declined">
                    <svg-icon shape="close" width="20" height="20"/>
                </div>
                <div class="cards-actions">
                    <Button :svg-icon="{shape: 'plus'}" @click.prevent="$emit('requestAccess', userItem)">Re-Request Access</Button>
                </div>
            </div>
            <div class="cards-equity-bar-right" v-if="userItem.status === 'None'">
                <Button :svg-icon="{shape: 'plus'}" @click.prevent="$emit('requestAccess', userItem)">Request Access</Button>
            </div>
        </div>
    </div>

</template>

<script setup lang="ts">
import type {UserManagementUser} from "~/server/api/advisors/users.get";
import SvgIcon from "~/components/images/SvgIcon.vue";
import UserAvatar from "~/components/images/UserAvatar.vue";
import Button from "~/components/elements/Button.vue";
import {formatAmount, distanceToNow} from "~/utils";

defineEmits(['requestAccess', 'impersonate', 'stopImpersonate']);
const {userItem} = defineProps<{ userItem: UserManagementUser }>();

</script>

<style scoped lang="scss">
@import "./UserManagementItem";
</style>