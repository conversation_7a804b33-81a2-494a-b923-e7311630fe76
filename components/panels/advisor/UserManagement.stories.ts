import UserManagement from './UserManagement.vue';
import type { Meta, StoryObj } from '@storybook/vue3';

const meta: Meta<typeof UserManagement> = {
    component: UserManagement,
    tags: ['autodocs'],

    argTypes: {
    },

    //👇 Emitted events and default args
    args: {
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof UserManagement>;

export const Default: Story = {
    parameters: {
    },
    args: {
    }
};
