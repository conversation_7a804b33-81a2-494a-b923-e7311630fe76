﻿@import "/assets/css/mixins";

.content-top {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 20px 0;
  gap: 20px;
  flex: none;
  align-self: stretch;
  flex-grow: 0;

  &-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    gap: 20px;
    flex: none;
    align-self: stretch;
    flex-grow: 1;

    h2 {
      color: #E9EDF1;
      font-weight: 700;
      font-size: 18px;
      line-height: 30px;
      letter-spacing: -0.02em;
    }
  }

  &-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    flex-grow: 1;
  }

  &-filter-bar {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    gap: 40px;
    flex: none;
    align-self: stretch;
    flex-grow: 1;
  }

  &-search {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    gap: 20px;

    flex: none;
    flex-grow: 1;

    position: relative;

    &-icon {
      color: var(--color-blue-grey);
      position: absolute;
      top: 50%;
      left: 20px;
      width: 24px;
      height: 24px;
      margin-top: -12px;
      pointer-events: none;
    }

    &-input {
      appearance: none;
      border: none;
      outline: none;
      background: none transparent;
      padding-left: 52px;

      // background: rgba(255, 0, 0, .2);
      flex: 1 1 auto;
      width: 100%;
      height: 30px;

      color: var(--color-white);
      @include placeholder {
        color: rgba(var(--color-grey-rgb), .5);
      }
    }

    &-clear {
      border-radius: 4px;
      user-select: none;
      background: var(--color-blue-grey);
      color: var(--color-white);

      font-size: 16px;
      font-weight: 400;
      line-height: 1.125em;
      letter-spacing: -0.32px;
      height: 30px;
      padding: 2px 10px;
    }
  }

  &-filters {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 0;
    gap: 1px;

    background: rgba(87, 113, 156, 0.2);
    border-radius: 4px;

    flex: none;
    flex-grow: 0;

    &-sort {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 20px;
      gap: 10px;
      border-right: 2px solid #0D182A;

      flex: none;
      flex-grow: 0;

      &-directions {
        margin: 10px 0;
        align-items: center;
      }

      &-label {
        text-transform: uppercase;
        color: #E9EDF1;
        opacity: 0.5;

        font-weight: 700;
        font-size: 12px;
        line-height: 24px;
        display: flex;
        align-items: center;
        letter-spacing: 0.1em;

        flex: none;
        flex-grow: 0;
      }

      &-value {
        color: #97ABCC;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        display: flex;
        align-items: center;
        flex: none;
        flex-grow: 0;
      }
    }

    &-divider {
      width: 1px;
      height: 200%;

      flex: none;
      align-self: stretch;
      flex-grow: 0;
    }

    &-filter {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 10px 20px;
      gap: 10px;
      flex: none;
      flex-grow: 0;

      font-weight: 700;
      font-size: 14px;
      line-height: 24px;

      color: #97ABCC;
    }

    &-icon {
      color: #57719C;
    }
  }
}

.cards {
  font-family: 'Oxygen', sans-serif;
  font-style: normal;
  border-radius: 4px;

  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  gap: 10px;
  flex: none;
  flex-grow: 1;

  &-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0;
    gap: 1px;
    flex: none;
    align-self: stretch;
    flex-grow: 1;
  }
}
