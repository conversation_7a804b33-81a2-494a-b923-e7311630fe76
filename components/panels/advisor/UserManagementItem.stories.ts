// noinspection JSUnusedGlobalSymbols

import UserManagementItem from './UserManagementItem.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import type {UserManagementItemProps} from "~/components/panels/advisor/UserManagementItem.vue";

const meta: Meta<typeof UserManagementItem> = {
    component: UserManagementItem,
    tags: ['autodocs'],

    argTypes: {
    },

    //👇 Emitted events and default args
    args: {
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof UserManagementItem>;

export const Joined: Story = {
    parameters: {},
    args: {
        item: {
            _id: 'abcd1',
            firstName: 'Tony',
            lastName: 'Stark',
            role: 'Client',
            status: 'Joined',
            totalAssets: 683000,
            totalLiabilities: 150000,
            netWorth: 1540000,
            accounts: 2,
            onboarded: 100,
            joinDate: new Date(2024, 5, 10)
        }
    }
};

export const Pending: Story = {
    parameters: {},
    args: {
        item: {
            _id: 'abcd4',
            firstName: 'Natasha',
            lastName: 'Romanov',
            role: 'Assassin',
            status: 'Pending'
        }
    }
}
