<template>
  <NuxtLayout name="app">
    <Modal :dynamic-placement="false" :with-overlay="true" :is-open="invitePopover"
                  @close="handleInviteClose">
      <InviteUserModal @close="handleInviteClose" @send="handleInviteSend" />
    </Modal>
    <div class="content-top">
      <div class="content-top-title">
        <h2>People</h2>
        <div class="content-top-actions">
          <Button :svg-icon="{shape: 'plus'}" @click.prevent="handleInviteToggle">Invite User</Button>
        </div>
      </div>

      <div class="content-top-filter-bar">
        <div class="content-top-search">
          <SvgIcon shape="search" class="content-top-search-icon"/>
          <input type="text" v-model="filter" class="text-input content-top-search-input" placeholder="Search">
          <button type="button" v-if="hasFilter" class="content-top-search-clear" @keydown.escape="clearFilter"
                  @click.prevent="clearFilter">
            clear
          </button>
        </div>
        <div class="content-top-filters">
          <button @click.prevent="handleSortToggle" class="content-top-filters-sort">
            <div class="content-top-filters-sort-label">Sort By</div>
            <div class="content-top-filters-sort-value" v-text="formattedSort"></div>
          </button>
          <PopoverModal :dynamic-placement="false" :with-overlay="false" :is-open="sortPopover"
                        @close="handleSortClose">
            <PopoverModalContent>
              <CheckboxGroup
                  v-model:options="sortDirectionOptions"
                  v-model:selected="selectedSortDirection"
                  size="sm"
                  mode="radio"
                  variant="buttons"
                  class="content-top-filters-sort-directions"
              />
              <CheckboxGroup
                  v-model:options="sortOptions"
                  v-model:selected="selectedSort"
                  size="sm"
                  mode="radio"
              />
            </PopoverModalContent>
          </PopoverModal>
          <button @click.prevent="handleFilterToggle" class="content-top-filters-filter">
            <SvgIcon shape="filter" class="content-top-filters-icon"/>
            <div v-text="formattedFilter"></div>
          </button>
          <PopoverModal :dynamic-placement="false" :with-overlay="false" :is-open="filterPopover"
                        @close="handleFilterClose">
            <PopoverModalContent>
              <CheckboxGroup
                  v-model:options="filterOptions"
                  v-model:selected="selectedFilter"
                  size="sm"
                  mode="radio"
              />
            </PopoverModalContent>
          </PopoverModal>
        </div>
      </div>
    </div>

    <div class="cards">
      <div class="cards-list">
        <UserManagementItem v-for="user in filteredUsers" :key="user._id" :item="user" />
      </div>
    </div>
  </NuxtLayout>
</template>

<script setup lang="ts">
import {computed, ref, watch} from "vue";
import UserManagementItem, {type UserManagementItemProps} from "./UserManagementItem.vue";
import InviteUserModal from "../../modals/InviteUserModal.vue";
import Button from "../../elements/Button.vue";
import CheckboxGroup, {type CheckboxGroupOption} from "../../forms/CheckboxGroup.vue";
import SvgIcon from "../../images/SvgIcon.vue";
import PopoverModal from "../../modals/PopoverModal.vue";
import PopoverModalContent from "../../modals/PopoverModalContent.vue";
import Modal from "../../modals/Modal.vue";

const invitePopover = ref(false);
function handleInviteToggle() {
  if (invitePopover.value) {
    handleInviteClose();
  } else {
    handleInviteOpen();
  }
}
function handleInviteOpen() {
  invitePopover.value = true;
}
function handleInviteClose() {
  invitePopover.value = false;
}

function handleInviteSend(invite) {
  console.log(invite);
  handleInviteClose();
}

const filter = ref('');
const hasFilter = computed(() => !!filter.value);

function clearFilter() {
  filter.value = '';
}

const sortPopover = ref(false);

function handleSortToggle() {
  if (sortPopover.value) {
    handleSortClose();
  } else {
    handleSortOpen();
  }
}

function handleSortOpen() {
  sortPopover.value = true;
}

function handleSortClose() {
  sortPopover.value = false;
  console.log(selectedSort.value);
}

const sortDirectionOptions = ref<CheckboxGroupOption[]>([
{
  name: 'asc',
  label: 'Ascending'
}, {
  name: 'desc',
  label: 'Descending'
}]);

const sortOptions = ref<CheckboxGroupOption[]>([
{
  name: 'date',
  label: 'Registered Date'
}, {
  name: 'firstName',
  label: 'First Name'
}, {
  name: 'lastName',
  label: 'Last Name'
}, {
  name: 'totalAssets',
  label: 'Total Assets'
}, {
  name: 'totalLiabilities',
  label: 'Total Liabilities'
}, {
  name: 'netWorth',
  label: 'Net Worth'
}, {
  name: 'accounts',
  label: 'Accounts'
}, {
  name: 'onboarded',
  label: 'Onboarded %'
}, {
  name: 'status',
  label: 'Status'
}]);
const selectedSort = ref('date');
const formattedSort = computed(() => {
  if (selectedSort.value) {
    return sortOptions.value.find((option) => option.name === selectedSort.value)?.label ?? selectedSort.value;
  }
});
const selectedSortDirection = ref('asc');

watch(selectedSort, () => {
  handleSortClose();
});
watch(selectedSortDirection, () => {
  handleSortClose();
});

const filterPopover = ref(false);

function handleFilterToggle() {
  if (filterPopover.value) {
    handleFilterClose();
  } else {
    handleFilterOpen();
  }
}

function handleFilterOpen() {
  filterPopover.value = true;
}

function handleFilterClose() {
  filterPopover.value = false;
  console.log(selectedFilter.value);
}

const filterOptions = ref<CheckboxGroupOption[]>([
  {
    label: 'None',
    name: 'filter'
  },
  {
    label: 'Accepted',
    name: 'accepted'
  },
  {
    label: 'Pending',
    name: 'pending'
  },
  {
    label: 'Declined',
    name: 'declined'
  },
  {
    label: 'Revoked',
    name: 'revoked'
  },    
  {
    label: 'Onboarded',
    name: 'onboarded'
  },
  {
    label: 'Intake',
    name: 'intake'
  },
  {
    label: 'Clients',
    name: 'clients'
  },
  {
    label: 'Advisors',
    name: 'advisors'
  }
]);
const selectedFilter = ref('filter');
const formattedFilter = computed(() => {
  if (selectedFilter.value) {
    const filter = filterOptions.value.find((option) => option.name === selectedFilter.value)?.label ?? selectedFilter.value;
    return filter === 'None' ? 'Filter' : filter;
  }
});
watch(selectedFilter, () => {
  handleFilterClose();
});

const userList : UserManagementItemProps[] = [
  {
    _id: 'abcd1',
    firstName: 'Tony',
    lastName: 'Stark',
    role: 'Client',
    status: 'Joined',
    totalAssets: 683000,
    totalLiabilities: 150000,
    netWorth: 1540000,
    accounts: 2,
    onboarded: 100,
    joinDate: new Date(2024, 5, 10)
  },
  {
    _id: 'abcd2',
    firstName: 'Steve',
    lastName: 'Rogers',
    role: 'Client',
    status: 'Joined',
    totalAssets: 0,
    totalLiabilities: 0,
    netWorth: 10000,
    accounts: 1,
    onboarded: 72,
    joinDate: new Date(2023, 0, 10)
  },
  {
    _id: 'abcd3',
    firstName: 'Bruce',
    lastName: 'Banner',
    role: 'Advisor',
    status: 'Joined',
    totalAssets: 0,
    totalLiabilities: 0,
    netWorth: 10000,
    accounts: 1,
    onboarded: 72,
    joinDate: new Date(2023, 0, 10)
  },
  {
    _id: 'abcd4',
    firstName: 'Natasha',
    lastName: 'Romanov',
    role: 'Assassin',
    status: 'Pending'
  }
]

const filteredUsers = computed(() => {
  let users = userList;
  if (filter.value) {
    users = users.filter(user => 
        user.firstName.toLowerCase().includes(filter.value.toLowerCase()) ||
        user.lastName.toLowerCase().includes(filter.value.toLowerCase())
    );
  }
  if (selectedFilter.value && selectedFilter.value !== 'filter') {
    switch (selectedFilter.value) {
      case 'accepted':
        users = users.filter(user => user.status === "Joined");
        break;
      case 'pending':
        users = users.filter(user => user.status === "Pending");
        break;
      case 'declined':
        users = users.filter(user => user.status === "Declined");
        break;
      case 'revoked':
        users = users.filter(user => user.status === "Revoked");
        break;
      case 'onboarded':
        users = users.filter(user => user.status === "Joined" && user.onboarded === 100);
        break;
      case 'intake':
        users = users.filter(user => user.status === "Joined" && (user.onboarded ?? 0) < 100);
        break;
      case 'clients':
        users = users.filter(user => user.role === 'Client');
        break;
      case 'advisors':
        users = users.filter(user => user.role === 'Advisor');
        break;
    }
  }
  
  users = users.sort((user1, user2) => {
    const direction = selectedSortDirection.value === 'asc' ? 1 : -1;
    switch(selectedSort.value) {
      case 'date':
        return direction * ((user1.joinDate?.getTime() ?? Number.MAX_VALUE) - (user2.joinDate?.getTime() ?? Number.MAX_VALUE));
      case 'firstName':
        return direction * user1.firstName.localeCompare(user2.firstName);
      case 'lastName':
        return direction * user1.lastName.localeCompare(user2.lastName);
      case 'totalAssets':
        return direction * ((user1.totalAssets ?? 0) - (user2.totalAssets ?? 0));
      case 'totalLiabilities':
        return direction * ((user1.totalLiabilities ?? 0) - (user2.totalLiabilities ?? 0));
      case 'netWorth':
        return direction * ((user1.netWorth ?? 0) - (user2.netWorth ?? 0));
      case 'accounts':
        return direction * ((user1.accounts ?? 0) - (user2.accounts ?? 0));
      case 'onboarded':
        return direction * ((user1.onboarded ?? 0) - (user2.onboarded ?? 0));
      case 'status':
        return direction * user1.status.localeCompare(user2.status);
    }
    return 0;
  });
  
  return users;
});

</script>

<style lang="scss">
@import "./UserManagement";
</style>