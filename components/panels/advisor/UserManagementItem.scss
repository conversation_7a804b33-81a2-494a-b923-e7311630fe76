﻿.cards {
  &-list-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;

    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 20px;
    gap: 20px;
    flex: none;
    align-self: stretch;
  }

  &-equity-bar {
    border-radius: 4px;

    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    gap: 20px;
    flex: 0 0 100%;
    align-self: stretch;

    &-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 20px;
      flex: 1 1 33%;

      &-description {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        padding: 0;
        gap: 10px;

        &-name {
          color: #E9EDF1;
          font-weight: 400;
          font-size: 14px;
          line-height: 12px;
          word-wrap: break-word;
        }

        &-title {
          font-weight: 700;
          font-size: 10px;
          line-height: 14px;
          letter-spacing: 0.1em;
          text-transform: uppercase;
          color: #57719C;
        }
      }
    }

    &-right {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      padding: 0;
      gap: 40px;
      flex: 1 1 auto;
    }
  }

  &-equity {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    padding: 0;
    gap: 10px;
    font-size: 12px;
    line-height: 14px;
    text-align: right;

    flex: none;

    &-value {
      font-weight: 700;
      color: #E9EDF1;
      flex: none;
    }

    &-label {
      font-weight: 400;
      color: #57719C;

      display: flex;
      align-items: center;
      flex: none;
    }
  }

  &-joined-date {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    padding: 0;
    gap: 10px;

    flex: none;

    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    text-align: right;

    &-value {
      display: flex;
      align-items: center;
      color: #97ABCC;

      flex: none;
    }

    &-label {
      display: flex;
      align-items: center;
      color: #57719C;

      flex: none;
    }
  }

  &-actions {
    flex: none;
    width: fit-content;
    height: 20px;

    color: var(--color-blue-grey, #57719C);

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &-pending {
      color: #FFBA53;
    }

    &-declined {
      color: #ec1a57;
    }

  }

  &-avatar {
    width: 40px;
    height: 40px;
    box-sizing: border-box;
    border: 1px solid var(--color-secondary-blue-grey, #57719C);
    border-radius: 50%;
    background: var(--color-secondary-blue-grey, #57719C);
    background-size: cover;
    background-position: center;
    color: var(--color-primary-white, #e9edf1);
    font-weight: 700;
    font-size: 14px;
    line-height: 30px;
    cursor: pointer;
    user-select: none;

    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0;
    gap: 10px;
    flex: none;
    
    &-image {
      border-radius: 50%;
    }
  }
}