import OverhaulLineItem from './OverhaulLineItem.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof OverhaulLineItem> = {
    component: OverhaulLineItem,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { OverhaulLineItem },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <OverhaulLineItem v-bind="args" :foo="foo">
    //             ${args.default}
    //         </OverhaulLineItem>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em; width: 80dvw"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof OverhaulLineItem>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        status: 'complete',
        description: '4232 University Dr',
        kind: 'Rental property',
        kpis: [
            {
                label: 'Expense',
                value: '7.2K',
                suffix: '/ mo',
                formatCurrency: true
            },
            {
                label: 'Liability #1',
                value: '7.2K',
                suffix: '/ mo',
                formatCurrency: true
            },
            {
                label: 'Liability #2',
                value: '7.2K',
                suffix: '/ mo',
                formatCurrency: true
            },
            {
                label: 'Balance',
                value: '520 K',
                formatCurrency: true
            },
            {
                label: 'Value',
                value: '830 K',
                formatCurrency: true
            },
            {
                label: 'Net Worth',
                value: '310 K',
                formatCurrency: true
            },
        ]
    }
};

export const InProgress: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        status: 'in-progress',
        description: 'Helicopters',
        mode: 'simple'
    }
};

export const NotStarted: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        description: 'Primary Residence',
        status: 'not-started'
    }
};
