@import "/assets/css/mixins";

.overhaul-existing-line-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    gap: 10px;
    background: rgba(var(--color-white-rgb), 0.1);
    transition: background .2s;
    cursor: pointer;

    &:first-child {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
    }

    &:last-child {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    &:hover {
        background: rgba(var(--color-white-rgb), 0.15);
    }

    .overhaul-existing-line-item-left {
        display: flex;
        flex-direction: column;
        gap: 5px;

        h5 {
            font-weight: 600;
            font-size: 14px;
            line-height: 1.45em;
        }

        span {
            font-weight: 400;
            font-size: 14px;
            line-height: 1.45em;
            color: var(--color-grey);
        }
    }

    .overhaul-existing-line-item-right {
        display: flex;
        align-items: center;
        gap: 10px;

        span {
            font-weight: 800;
            font-size: 14px;
            line-height: 1.45em;
            color: var(--color-white);
        }
    }
}
