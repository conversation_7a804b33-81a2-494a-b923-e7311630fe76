<template>
    <div class="overhaul-existing-line-item">
        <div class="overhaul-existing-line-item-left">
            <h5 v-text="description"></h5>
            <span v-if="originationDate" v-text="originationDate"></span>
        </div>

        <div class="overhaul-existing-line-item-right">
            <span v-text="primaryStat"></span>
            <ImagesSvgIcon shape="plus" width="24" height="24" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import type { TOverhaulItemCategory } from '~/stores/overhaul';

/** TODO: Description of this component */
defineOptions();

export interface OverhaulExistingLineItemProps {
    item: TPopulatedItem;
}
const props = defineProps<OverhaulExistingLineItemProps>()

const description = computed(() => 
    (props.item.description)
        ? props.item.description
        : 'New ' + props.item.kindLabel
)

const originationDate = computed(() => {
    const date = props.item.fields.find((f) => f.name === 'originationDate')?.value
    return (date)
        ? 'Opened ' + date
        : null;
})

const primaryStat = computed(() => {
    const categoryToPrimaryStatMap: Record<TOverhaulItemCategory, string | null> = {
        Asset: 'value',
        Liability: 'liability',
        Expense: 'expense',
        Insurance: null,
        Income: 'income',
    }
    
    const primaryStatKind = categoryToPrimaryStatMap[props.item.category ?? ''] ?? null;

    const stat = props.item.stats?.find((s) => s.kind === primaryStatKind)
    
    if (!stat) return null;
    
    return (stat.hasOwnProperty('frequency'))
        ? abbreviateNumber(stat.value) + ' /' + stat.frequency
        : abbreviateNumber(stat.value); 
})
</script>

<style lang="scss">
@import "./OverhaulExistingLineItem";
</style>
