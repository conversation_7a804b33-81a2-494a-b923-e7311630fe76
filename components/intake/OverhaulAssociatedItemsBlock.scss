@import "/assets/css/mixins";

.overhaul-associated-items-block {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 20px 0;
    position: relative;
    transition: background .2s ease-in-out;

    &.overhaul-associated-items-block-collapsed {
        &:hover {
            background: rgba(var(--color-white-rgb), 0.1);    
        }
    }

    &.overhaul-associated-items-block-expanded {
        background: rgba(var(--color-white-rgb), 0.1);
    }

    .overhaul-associated-items-block-toggle-overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        cursor: pointer;
    }

    .overhaul-associated-items-block-title {
         display: flex;
         align-items: center;
         justify-content: space-between;
         padding: 0 20px;

         h5 {
             font-style: normal;
             font-weight: 700;
             font-size: 14px;
             line-height: 1.45em;
             letter-spacing: -0.02em;

             color: var(--color-grey);
         }
     }

    .overhaul-associated-items-block-item-line {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;
        padding: 0 20px;
        
        color: var(--color-white);

        dt {
            color: var(--color-white);
        }

        dd {
            font-weight: 800;
            color: var(--color-white);
        }
    }

    .overhaul-associated-items-block-item {
        display: flex;
        align-items: stretch;
        flex-direction: column;
        gap: 10px;
        border-top: 1px solid rgba(var(--color-white-rgb), 0.1);
        padding: 20px;
    }

    .overhaul-associated-items-block-title + .overhaul-associated-items-block-item {
        margin-top: 10px;
    }

    .overhaul-associated-items-block-item-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.45em;
    }

    .overhaul-associated-items-block-item-stat {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;
        
        color: var(--color-white);

        dt {
            color: var(--color-grey);
        }

        dd {
            font-weight: 800;
            color: var(--color-white);
        }
    }

    .overhaul-associated-items-block-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        padding: 20px 20px 0;
        border-top: 1px solid rgba(var(--color-white-rgb), 0.1)
    }
    
    .overhaul-associated-items-block-placeholder {
        padding: 0 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        color: var(--color-blue-grey);
    }

    .overhaul-associated-items-block-empty {
        margin: 0 20px 5px;
        padding: 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        font-style: italic;

        color: var(--color-blue-grey);
        background: rgba(var(--color-white-rgb), 0.05);
    }
}
