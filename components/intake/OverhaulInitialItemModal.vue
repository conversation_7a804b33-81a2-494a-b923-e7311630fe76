<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="overhaul-initial-item-modal">
        <template #header>
            <div class="overhaul-initial-item-modal-header">
                <div>
                    <h4
                        v-if="category"
                        v-html="pluralize(category)"
                        class="overhaul-initial-item-modal-header-title"
                    ></h4>
                </div>

                <div class="overhaul-initial-item-modal-header-actions">
                    <Button tabindex="-1" @click="handleModalClose" variant="muted"> Finish Later </Button>
                </div>
            </div>
        </template>
        <div class="overhaul-initial-item-modal-layout">
            <div class="overhaul-initial-item-modal-layout-left">
                <div class="overhaul-initial-item-modal-icon">
                    <BigIcon :shape="item?.iconShape ?? 'bungalow'" :width="100" :height="100" />
                </div>

                <h3 class="overhaul-initial-item-modal-title">
                    {{ kindLabelPlural }}
                </h3>

                <template v-if="view === 'mode'">
                    <p class="overhaul-initial-item-modal-description">
                        Would you like to enter details about each {{ toLowerCase(kindLabel) }} now or use simple mode
                        and enter each one later?
                    </p>
                </template>

                <template v-else>
                    <p class="overhaul-initial-item-modal-description">
                        How many {{ toLowerCase(kindLabelPlural) }} do you have?
                    </p>
                    <p class="overhaul-initial-item-modal-disclaimer">
                        If you would prefer to enter a total for all of your {{ toLowerCase(kindLabel) }} assets, you
                        may switch to simple entry mode.
                    </p>

                    <div class="overhaul-initial-item-modal-count-box">
                        <div class="overhaul-initial-item-modal-count-fields">
                            <button class="overhaul-initial-item-modal-count-button" @click="count--">
                                <SvgIcon shape="minus" :width="40" :height="40" />
                            </button>
                            <input
                                class="overhaul-initial-item-modal-count-input"
                                type="number"
                                v-model="count"
                                min="1"
                                max="10"
                                step="1"
                            />
                            <button class="overhaul-initial-item-modal-count-button" @click="count++">
                                <SvgIcon shape="plus" :width="40" :height="40" />
                            </button>
                        </div>

                        <Button
                            @click.prevent="handleConfirmStandardMode"
                            :svg-icon="{ shape: 'arrow-forward', position: 'right' }"
                            >Confirm & Add {{ kindLabelPlural }}</Button
                        >
                    </div>

                    <button class="overhaul-initial-item-modal-switch-button" @click="view = 'mode'">
                        <SvgIcon shape="arrow-back" :width="20" :height="20" />
                        Switch to Simple Mode
                    </button>
                </template>
            </div>

            <div class="overhaul-initial-item-modal-layout-right" v-if="view === 'mode'">
                <div class="overhaul-initial-item-modal-box-options">
                    <div class="overhaul-initial-item-modal-box">
                        <div class="overhaul-initial-item-modal-box-heading">
                            <h5>Enter individual {{ toLowerCase(kindLabelPlural) }} <strong>now</strong></h5>
                            <span class="overhaul-initial-item-modal-pill">Standard</span>
                        </div>
                        <p class="overhaul-initial-item-modal-disclaimer">
                            Start adding information about each individual {{ toLowerCase(kindLabel) }} now.
                        </p>
                        <Button
                            @click.prevent="view = 'count'"
                            :svg-icon="{ shape: 'arrow-forward', position: 'right' }"
                            >Enter everything now</Button
                        >
                    </div>

                    <div class="overhaul-initial-item-modal-box">
                        <div class="overhaul-initial-item-modal-box-heading">
                            <h5>Enter individual {{ toLowerCase(kindLabelPlural) }} <strong>later</strong></h5>
                            <span class="overhaul-initial-item-modal-pill">Simple</span>
                        </div>
                        <p class="overhaul-initial-item-modal-disclaimer">
                            You will only be required to enter one number for your total
                            {{ toLowerCase(kindLabel) }} value, liability, and expenses related to all of your
                            {{ toLowerCase(kindLabelPlural) }}. Switch to standard mode at any time to enter detail
                            about every helicopter.
                        </p>
                        <Button
                            @click.prevent="handleConfirmSimpleMode"
                            :svg-icon="{ shape: 'arrow-forward', position: 'right' }"
                            >Start with Simple Mode</Button
                        >
                    </div>
                </div>
            </div>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import Button from '../elements/Button.vue';
import Modal from '~/components/modals/Modal.vue';
import BigIcon from '../images/BigIcon.vue';
import SvgIcon from '../images/SvgIcon.vue';
import { pluralize, toLowerCase } from '~/utils';
import { useOverhaulStore } from '../../stores/overhaul';

const overhaulStore = useOverhaulStore();

/** TODO: Description of this component */
defineOptions();

export interface OverhaulInitialItemModalProps {
    category: TOverhaulCategory;
    selectedItemId: string | null;
    isOpen: boolean;
    entryMode?: 'simple' | 'standard' | null;
}
const props = defineProps<OverhaulInitialItemModalProps>();

const emit = defineEmits<{
    close: [];
    confirm: [newItemId: string];
}>();

const item = computed(() => {
    return props.selectedItemId ? (overhaulStore.allItems[props.selectedItemId] ?? null) : null;
});
const view: Ref<'mode' | 'count'> = ref('mode');
const count: Ref<number> = ref(1);

const kindLabel = computed(() => {
    return item.value ? item.value.kindLabel : '';
});

const kindLabelPlural = computed(() => {
    return kindLabel.value ? pluralize(kindLabel.value) : '';
});

function handleModalClose() {
    emit('close');
}
function handleConfirmStandardMode() {
    // Update the placeholder item to Standard mode
    overhaulStore.updateItemMode(item.value, 'Standard');

    // If they added additional items, add them now
    if (count.value > 1) {
        for (let i = 0; i < count.value - 1; i++) {
            overhaulStore.addItem(props.category, item.value, 'Standard');
        }
    }

    emit('confirm', props.selectedItemId ?? '');
    setTimeout(() => (view.value = 'mode'), 1000);
}
function handleConfirmSimpleMode() {
    console.log('!!!!!!!!confirming simple mode');
    overhaulStore.updateItemMode(item.value, 'Simple');
    // const newItem = overhaulStore.addItem(props.category, item.value?.kind, 'Simple')
    emit('confirm', props.selectedItemId ?? '');
    setTimeout(() => (view.value = 'mode'), 1000);
}
</script>

<style lang="scss">
@import './OverhaulInitialItemModal';
</style>
