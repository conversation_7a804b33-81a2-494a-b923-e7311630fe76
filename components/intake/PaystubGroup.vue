<template>
    <div class="paystub-group">
        <div
            class="paystub-group-existing"
            v-if="existingPaystubs?.length"
            :class="isExpanded ? 'paystub-group-existing-expanded' : 'paystub-group-existing-collapsed'"
        >
            <div class="paystub-group-title">
                <h5>Paystubs</h5>
            </div>
            <template v-if="isExpanded">
                <div class="paystub-group-expanded-item" v-for="paystub in mappedExistingPaystubs" :key="paystub.id">
                    <div class="paystub-group-title">
                        <h5 v-text="paystub.displayDate"></h5>
                        <DropdownMenu>
                            <DropdownMenuRow icon-shape="edit" @click.prevent="handleOpenEditModal(paystub._id)">
                                Edit
                            </DropdownMenuRow>
                            <DropdownMenuRow icon-shape="trash" @click.prevent="handleDeletePaystub(paystub._id)">
                                Delete
                            </DropdownMenuRow>
                        </DropdownMenu>
                    </div>
                    <dl class="paystub-group-expanded-item-stat" v-for="stat in paystub.expandedStats">
                        <dt v-html="stat.label"></dt>
                        <dd v-html="stat.value"></dd>
                    </dl>

                    <!--<OverhaulIncome1099Modal-->
                    <!--    v-if="kind === '1099'"-->
                    <!--    :item="item"-->
                    <!--    :form="form"-->
                    <!--    :is-open="activeEditModal === form.id"-->
                    <!--    @close="handleCloseEditModal"-->
                    <!--/>-->

                    <!--<OverhaulIncomeW2Modal-->
                    <!--    v-if="kind === 'w2'"-->
                    <!--    :item="item"-->
                    <!--    :form="form"-->
                    <!--    :is-open="activeEditModal === form.id"-->
                    <!--    @close="handleCloseEditModal"-->
                    <!--/>-->
                </div>

                <div v-if="!existingPaystubs?.length" class="paystub-group-empty">
                    There are no associated paystubs.
                </div>

                <div class="paystub-group-expanded-actions">
                    <ElementsButton type="button" variant="muted" size="sm" @click="handleCancel"
                        >Collapse</ElementsButton
                    >
                    <ElementsButton type="button" size="sm" @click="handleOpenNewModal">Add</ElementsButton>
                </div>
            </template>

            <template v-else>
                <div class="paystub-group-toggle-overlay" @click.prevent="isExpanded = true"></div>
                <template v-if="mappedExistingPaystubs?.length">
                    <dl
                        v-for="(paystub, mappedPaystubsIndex) in mappedExistingPaystubs"
                        class="paystub-group-item-line"
                        :class="{ 'paystub-group-item-line-fade': mappedPaystubsIndex > 0 }"
                    >
                        <dt v-text="paystub.displayDate"></dt>
                        <dd v-text="paystub.grossIncome"></dd>
                    </dl>
                </template>

                <template v-else>
                    <p class="paystub-group-placeholder" v-text="placeholderText"></p>
                </template>
            </template>
        </div>

        <OverhaulModalRelationshipRow
            v-if="!existingPaystubs?.length"
            title="Paystubs"
            description="Add and manage paystubs for this income."
            @click.prevent="handleOpenNewModal"
        />

        <!-- New Form Modals -->
        <!--<OverhaulIncome1099Modal v-if="kind === '1099'" :item="item" :is-open="modalIsOpen" @close="handleModalClose" />-->
        <!--<OverhaulIncomeW2Modal v-if="kind === 'w2'" :item="item" :is-open="modalIsOpen" @close="handleModalClose" />-->

        <AddPaystubManualModal
            :is-open="modalIsOpen"
            :income-employee-id="incomeItem?.id"
            :existing-paystub="editingPaystub"
            @close="handleModalClose"
            @new="handleNewPaystub"
            @update="handlePaystubUpdate"
        />

        <PaystubNewModal :is-open="newPaystubModalIsOpen" @new="handleNewOcrResponse" @close="handleCloseNewModal" />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulModalRelationshipRow from './income/OverhaulModalRelationshipRow.vue';
import SubModal from '~/components/modals/SubModal.vue';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';
import TextInput from '~/components/forms/TextInput.vue';
import OverhaulIncomeFormInputGroup from './income/OverhaulIncomeFormInputGroup.vue';
import OverhaulIncome1099Modal from './income/OverhaulIncome1099Modal.vue';
import OverhaulIncomeW2Modal from './income/OverhaulIncomeW2Modal.vue';
import DropdownMenu from '~/components/modals/DropdownMenu.vue';
import DropdownMenuRow from '~/components/modals/DropdownMenuRow.vue';
import AddPaystubManualModal from '~/components/modals/AddPaystubManualModal.vue';
import PaystubNewModal from '~/components/modals/PaystubNewModal.vue';
import { formatDate, isValidDate } from '~/utils';
import type { Paystub } from '~/types/paystub';

/** The inline display and modal for viewing / managing a 1099 form for income */
defineOptions();

export interface PaystubGroupProps {
    incomeItem?: TPopulatedItem;
    // paystubs?: any[];
}
const props = defineProps<PaystubGroupProps>();

// const emit = defineEmits<{}>()

const overhaulStore = useOverhaulStore();

const isExpanded = ref(false);
function handleCancel() {
    isExpanded.value = false;
}

// const existingPaystubs = reactive(props.incomeItem?.paystubItems ?? []);
const existingPaystubs = computed(() => props.incomeItem?.paystubItems);

const mappedExistingPaystubs = computed(() => {
    const arr = existingPaystubs.value?.map(paystub => {
        const incomeSum = paystub.incomeItems?.length
            ? paystub.incomeItems
                  .reduce((sum, item) => sum + (Number(item.current) || 0), 0)
                  .toLocaleString('en-US', {
                      style: 'currency',
                      currency: 'USD',
                  })
            : null;

        const payDate = paystub.payDate.replace(/Z$/, '');

        const displayDate = payDate ? formatDate(payDate, 'MMM dd, yyyy') : null;

        return {
            displayDate: displayDate ? displayDate : '-',
            grossIncome: incomeSum ? incomeSum : '-',
            expandedStats: [
                {
                    label: 'Gross Income',
                    value: incomeSum,
                },
            ],
            ...paystub,
        };
    });
    return arr?.sort((a, b) => new Date(b.payDate ?? 0) - new Date(a.payDate ?? 0));
});

const paystubSelectedForEdit = ref<string | null>(null);

const editingPaystub = computed(() => {
    if (pendingOcrData.value) {
        return pendingOcrData.value;
    }
    if (!paystubSelectedForEdit.value) return null;
    return existingPaystubs.value?.find(p => p._id === paystubSelectedForEdit.value);
});

const newPaystubModalIsOpen = ref(false);
function handleOpenNewModal() {
    paystubSelectedForEdit.value = null;
    newPaystubModalIsOpen.value = true;
}

function handleCloseNewModal() {
    newPaystubModalIsOpen.value = false;
}

const modalIsOpen = ref(false);
function handleModalClose() {
    modalIsOpen.value = false;
    paystubSelectedForEdit.value = null;
}
function handleModalOpen() {
    modalIsOpen.value = true;
}

const activeEditModal = ref<string | null>(null);
function handleOpenEditModal(id: string) {
    paystubSelectedForEdit.value = id;
    handleModalOpen();
}
function handleDeletePaystub(paystubId: string) {
    overhaulStore.deletePaystubFromItem(paystubId, props.incomeItem?.id);

    // Remove that paystub from the local list of paystubs
    // const index = existingPaystubs.findIndex(e => e._id === paystubId);
    // if (index >= 0) {
    //     existingPaystubs.splice(index, 1);
    // }
}

function handleNewPaystub(paystub?: Paystub) {
    // existingPaystubs.push(paystub);
    handleModalClose();
}

function handlePaystubUpdate(paystub: Paystub) {
    handleModalClose();
}

const pendingOcrData = ref<Paystub | null>(null);
function handleNewOcrResponse(paystub: Paystub) {
    newPaystubModalIsOpen.value = false;
    pendingOcrData.value = paystub;
    handleModalOpen();
}
//
// onMounted(() => {
//     const paystubIds = props.incomeItem?.paystubs;
//     if (paystubIds) {
//         const fetch = useRequestFetch();
//         paystubIds.forEach(paystubId => {
//             fetch('/api/paystub?id=' + paystubId).then(e => {
//                 if (!existingPaystubs.find(p => p._id === e.paystubId)) {
//                     existingPaystubs.push(e);
//                 }
//             });
//         });
//     }
// });
</script>

<style lang="scss">
@import './PaystubGroup';
</style>
