<template>
    <OverhaulAssociatedItemsBlock
        v-for="category in categoriesToDisplay"
        :item="item"
        :category="category"
    />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulExistingLineItem from './OverhaulExistingLineItem.vue';
import { getCategoryVariants } from '~/stores/overhaul';
import { abbreviateFrequency } from '~/utils';
import OverhaulAssociatedItemsBlock from "~/components/intake/OverhaulAssociatedItemsBlock.vue";
import DropdownMenu from '../modals/DropdownMenu.vue';
import DropdownMenuRow from '../modals/DropdownMenuRow.vue';
import OverhaulCreateAndLinkItemModal from './OverhaulCreateAndLinkItemModal.vue';

/** Section of the intake item modal that displays and manages associated items (e.g. a loan tied to an asset) */
defineOptions();

const overhaulStore = useOverhaulStore();

export interface OverhaulAssociatedItemsBlockProps {
    item: TPopulatedItem;
}
const props = defineProps<OverhaulAssociatedItemsBlockProps>()

const categoriesToDisplay = computed(() => {
    switch(props.item.category) {
        case 'Asset':
            return ['liabilities', 'expenses', 'income'];
        case 'Liability':
            return ['assets', 'expenses'];
        case 'Insurance':
            return [];
        case 'Expense':
            return ['assets', 'liabilities'];
        case 'Income':
            return ['assets', 'expenses'];
        default:
            return [];
    }
})

</script>

<style lang="scss">
@import "./OverhaulAssociatedItemsBlock";
</style>
