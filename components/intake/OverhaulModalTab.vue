<template>
    <button class="overhaul-modal-tab" :class="topLevelClassObj">
        <div class="overhaul-modal-tab-content" v-if="false">
            <span v-if="item.entryMode === 'Simple'" class="overhaul-modal-tab-simple-badge">Simple</span>
            <span v-for="item in leftMarkup" v-html="item"></span>
        </div>
        <div class="overhaul-modal-tab-content">
            <div class="overhaul-modal-tab-label" v-html="labelHtml"></div>
            <!-- <div class="overhaul-modal-tab-stats" v-if="statsHtml" v-html="statsHtml"></div> -->
            <div class="overhaul-modal-tab-stats" v-if="stats">
                <span class="overhaul-modal-tab-percentage" v-text="stats.percentage"></span>
                <div class="overhaul-modal-tab-delta" v-if="stats.delta">
                    <SvgIcon v-if="stats.delta.dir === 'plus'" class="overhaul-modal-tab-delta-dir dir-plus" shape="plus" width="14" height="14" />
                    <SvgIcon v-if="stats.delta.dir === 'minus'" class="overhaul-modal-tab-delta-dir dir-minus" shape="minus" width="14" height="14" />
                    
                    <div class="overhaul-modal-tab-delta-value" v-text="stats.delta.value"></div>
                </div>
            </div>
        </div>
        <SvgIcon class="overhaul-modal-tab-arrow" shape="keyboard-arrow-right" width="24" height="24" />
    </button>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';

import SvgIcon from '../images/SvgIcon.vue';
import type { TPopulatedItem } from '~/stores/overhaul';

/** A button tab on the left side of OverhaulModal */
defineOptions();

export interface OverhaulModalTabProps {
    item: TPopulatedItem;
    isActive?: boolean;
}
const props = defineProps<OverhaulModalTabProps>()

const topLevelClassObj = computed(() => [
    (props.item.entryMode === 'Simple') ? 'overhaul-modal-tab-simple' : 'overhaul-modal-tab-standard',
    props.isActive ? 'overhaul-modal-tab-active' : '',
]);

const leftMarkup = computed(() => {
    return [
        props.item?.kind,
        props.item?.description
    ]
})

const labelHtml = computed(() => {
    if (props.item.entryMode === 'Simple') {
        return `
            <span class="overhaul-modal-tab-pill">Simple</span>
            <span class="overhaul-modal-tab-bold">${props.item.kindLabel}</span>
        `
    } else {
        if (props.item.description) {
            return `<span class="overhaul-modal-tab-bold">${props.item.kindLabel}</span>
            <span class="overhaul-modal-tab-fade">${props.item.description}</span>`
        } else {
            return `<span class="overhaul-modal-tab-bold">New ${props.item.kindLabel}</span>`
        }
    }
})

const stats = computed(() => {
    if (!props.item.stats) return null;
    const netWorthStat = props.item.stats.find((stat) => stat.kind === 'net-worth');
    
    if (netWorthStat) {
        const valueArr = abbreviateNumber(Math.abs(netWorthStat.value), true)
        return {
            percentage: null,
            delta: {
                dir: (netWorthStat.value > 0) ? 'plus' : 'minus',
                value: valueArr
            }
        }
    } else {
        return null;
    }   
})
</script>

<style lang="scss">
@import "./OverhaulModalTab";
</style>
