<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="overhaul-modal">
        <template #header>
            <div class="overhaul-modal-header">
                <h4 class="overhaul-modal-header-title" v-text="categoryVariants.upperPlural"></h4>

                <div class="overhaul-modal-header-actions">
                    <Button variant="muted" svg-icon="plus" @click.precent="handleAddItemModalOpen">
                        Add {{ categoryVariants.upperSingular }}
                    </Button>

                    <Button @click.prevent="handleModalClose"> Done </Button>
                </div>
            </div>
        </template>

        <div class="overhaul-modal-content">
            <div class="overhaul-modal-tabs-column">
                <div class="overhaul-modal-fields-header">
                    <div class="overhaul-modal-filters">
                        <ToggleTag
                            v-for="filter in filters"
                            v-bind="filter"
                            :key="filter.label"
                            :is-selected="activeFilters.has(filter.name)"
                            @click.prevent="toggleFilter(filter.name)"
                        />
                    </div>
                </div>
                <div class="overhaul-modal-tabs">
                    <!-- Left column navigation of all items -->
                    <OverhaulModalTab
                        v-for="item in filteredItems"
                        :key="item.id"
                        :item="item"
                        @click.prevent="selectedItemId = item.id"
                        :is-active="selectedItemId === item.id"
                    />
                </div>
            </div>

            <div class="overhaul-modal-fields-column" v-if="selectedItem">
                <div class="overhaul-modal-fields-header">
                    <h4>{{ description }}</h4>
                    <DropdownMenu>
                        <DropdownMenuRow @click.prevent="handleDeleteSelectedItem" icon-shape="trash">
                            Delete
                        </DropdownMenuRow>
                    </DropdownMenu>
                </div>
                <div class="overhaul-modal-input-groups" v-if="selectedItem">
                    <SituationInputGroup
                        v-for="field in selectedItem.fields.filter(f => !f.hiddenInModal)"
                        v-bind="field"
                        :key="selectedItem.id + field.name"
                        @change="payload => handleItemFieldChange(field.name, payload)"
                    />

                    <template v-if="selectedItem.kind === 'IncomeEmployee' && selectedItem.entryMode === 'Standard'">
                        <PaystubGroup :income-item="selectedItem" />
                        <!--<OverhaulFormGroup :item="selectedItem" kind="1099" />-->
                        <!--<OverhaulFormGroup :item="selectedItem" kind="w2" />-->
                    </template>

                    <OverhaulAssociatedItems v-if="selectedItem.entryMode === 'Standard'" :item="selectedItem" />
                </div>
            </div>
        </div>

        <OverhaulAddObjectModal :category="category" :is-open="addItemModalIsOpen" @close="handleAddItemModalClose" />

        <!--<AddPaystubManualModal-->
        <!--    :is-open="paystubModalIsVisible"-->
        <!--    :income-employee-id="selectedItem?.id"-->
        <!--    @close="handlePaystubModalClose"-->
        <!--/>-->
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import type { Ref } from 'vue';
import Button from '~/components/elements/Button.vue';
import Modal from '~/components/modals/Modal.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';
import SituationInputGroup, { type SituationInputGroupProps } from '../situations/SituationInputGroup.vue';
import OverhaulModalTab from './OverhaulModalTab.vue';
import OverhaulAssociatedItemsBlock from './OverhaulAssociatedItemsBlock.vue';
import OverhaulAssociatedItems from './OverhaulAssociatedItems.vue';
import ToggleTag from '../elements/ToggleTag.vue';
import type { TOverhaulCategory } from '~/stores/overhaul';
import { getCategoryVariants } from '~/stores/overhaul';
import OverhaulAddObjectModal from './OverhaulAddObjectModal.vue';
import DropdownMenuRow from '../modals/DropdownMenuRow.vue';
import DropdownMenu from '../modals/DropdownMenu.vue';
import OverhaulFormGroup from './OverhaulFormGroup.vue';
import OverhaulIncome1099 from './income/OverhaulIncome1099.vue';
import OverhaulIncomeW2Modal from './income/OverhaulIncomeW2Modal.vue';
import AddPaystubManualModal from '~/components/modals/AddPaystubManualModal.vue';
import OverhaulModalRelationshipRow from '~/components/intake/income/OverhaulModalRelationshipRow.vue';
import PaystubGroup from './PaystubGroup.vue';

/** The modal used to navigate between editable items */
defineOptions();

export interface OverhaulModalProps {
    isOpen: boolean;

    category: TOverhaulCategory;

    // If a specific item should be selected when the modal opens
    initialItem?: string | null;
}
const props = withDefaults(defineProps<OverhaulModalProps>(), {});

const emit = defineEmits<{
    close: [];
}>();

const overhaulStore = useOverhaulStore();

function handleModalClose() {
    emit('close');
}
// function handleModalOpen() {
//     focusFirstItem();
// }

const addItemModalIsOpen = ref(false);
function handleAddItemModalClose() {
    addItemModalIsOpen.value = false;
}
function handleAddItemModalOpen() {
    addItemModalIsOpen.value = true;
}

// async function focusFirstItem() {
//     await nextTick();
//     // TODO: Make this happen
//     // if (!simpleMode.value) {
//     //     const firstKey = Object.keys(props.standard)[0];
//     //     const firstItem = props.standard[firstKey];
//     //     if (firstItem) {
//     //         standardSelectedItem.value = firstItem.id;
//     //     }
//     // } else {
//     //     await nextTick();
//     //     setTimeout(() => {
//     //         simpleModeInputs.value?.querySelector('.collapsible-input-group-overlay')?.click();
//     //     }, 50)
//     // }
// }

const selectedItemId: Ref<string | null> = ref(null);
const selectedItem = computed(() => {
    if (!selectedItemId.value) return null;
    return items.value.find(item => item.id === selectedItemId.value);
});
const items = computed(() => {
    switch (props.category) {
        case 'liabilities':
            return overhaulStore.liabilityItemsArr;
        case 'income':
            return overhaulStore.incomeItemsArr;
        case 'expenses':
            return overhaulStore.expenseItemsArr;
        case 'insurance':
            return overhaulStore.insuranceItemsArr;
        default:
            return overhaulStore.assetItemsArr;
    }
});

// An item is considered "started" once the user has selected an entryMode
const startedItems = computed(() => items.value.filter(item => item.entryMode));

// The variants of the category (e.g. asset, asets, Asset, Assets)
const categoryVariants = computed(() => getCategoryVariants(props.category));

function handleItemFieldChange(fieldName: string, payload: any) {
    // console.log(selectedItem.value?.id, fieldName, payload)
    if (!selectedItemId.value) return;
    overhaulStore.updateItemField(props.category, selectedItemId.value, fieldName, payload);
}

function handleDeleteSelectedItem() {
    if (!selectedItemId.value) return;
    overhaulStore.deleteItem(props.category, selectedItemId.value);
    selectedItemId.value = null;
}

const description = computed(() => {
    if (selectedItem.value?.entryMode === 'Simple') {
        return selectedItem.value?.kindLabel ?? '';
    } else {
        return selectedItem.value?.description ?? 'New ' + (selectedItem.value?.kindLabel ?? 'Item');
    }
});

const filters = computed(() => {
    const ii = items.value?.reduce((acc, item) => {
        console.log(item.kind);
        if (!acc.has(item.name)) {
            acc.set(item.name, {
                label: item.kindLabel,
                name: item.name,
                count: 1,
            });
        } else {
            const obj = acc.get(item.name);
            obj.count++;
            acc.set(obj.name, obj);
        }
        return acc;
    }, new Map<string, any>());

    return Array.from(ii.values());
});

const activeFilters = reactive(new Set<string>());
async function toggleFilter(filter: string) {
    if (activeFilters.has(filter)) {
        activeFilters.delete(filter);
    } else {
        activeFilters.add(filter);
    }

    // If the currently selected item is now filtered out, select the first remaining item
    await nextTick();
    if (!activeFilters.has(selectedItem.value?.name)) {
        console.log(filteredItems.value?.[0]?.id);
        selectedItemId.value = filteredItems.value?.[0]?.id;
    }
}
const filteredItems = computed(() => {
    if (activeFilters.size === 0) return startedItems.value;
    return startedItems.value.filter(item => activeFilters.has(item.name));
});

const paystubModalIsVisible = ref(false);
function showPaystubModal() {
    paystubModalIsVisible.value = true;
}

function handlePaystubModalClose() {
    paystubModalIsVisible.value = false;
}

const selectedItemPaystubs = computed(() => {
    return selectedItem.value?.paystubs;
});

watch(
    () => props.isOpen,
    newValue => {
        if (newValue && props.initialItem) {
            selectedItemId.value = props.initialItem;
        }
    },
);
</script>

<style lang="scss">
@import './OverhaulModal';
</style>
