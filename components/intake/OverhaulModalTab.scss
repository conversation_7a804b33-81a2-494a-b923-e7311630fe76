@import "/assets/css/mixins";

.overhaul-modal-tab {
    width: 100%;
    display:flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
    padding: 20px;

    --overhaul-modal-tab-text: var(--color-white);
    --overhaul-modal-tab-bg: var(--color-blue-grey);
    --overhaul-modal-tab-fade: var(--color-grey);
    --overhaul-modal-tab-transition: .2s ease-in-out;

    color: var(--overhaul-modal-tab-text);
    background: var(--overhaul-modal-tab-bg);
    transition: color var(--overhaul-modal-tab-transition), background var(--overhaul-modal-tab-transition);

    font-size: 14px;
    line-height: 1.45em;
    border-radius: 4px;

    &.overhaul-modal-tab-active {
        --overhaul-modal-tab-bg: var(--color-white);
        --overhaul-modal-tab-text: var(--color-dark-grey);
        --overhaul-modal-tab-fade: var(--color-dark-grey);
        .overhaul-modal-tab-arrow  {
            opacity: 1;
            color: var(--color-grey);
            transition: color var(--overhaul-modal-tab-transition);
        }
        .overhaul-modal-tab-delta-dir {
            color: var(--overhaul-modal-active-dir-color);
            transition: color var(--overhaul-modal-tab-transition);
        }
    }

    &:hover:not(.overhaul-modal-tab-active) {
        // TODO: Light mode
        --overhaul-modal-tab-bg: #4A6085;
    }

    .overhaul-modal-tab-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        flex: 1 1 auto;
    }

    .overhaul-modal-tab-label {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
    }

    .overhaul-modal-tab-arrow {
        transition: opacity var(--overhaul-modal-tab-transition), color var(--overhaul-modal-tab-transition);
        width: 24px;
        flex: none;
        opacity: .5;
    }

    .overhaul-modal-tab-bold {
        color: var(--overhaul-modal-tab-text);
        transition: color var(--overhaul-modal-tab-transition);
        font-weight: 700;
    }

    .overhaul-modal-tab-fade {
        color: var(--overhaul-modal-tab-fade);
        transition: color var(--overhaul-modal-tab-transition);
        font-weight: 400;
    }

    .overhaul-modal-tab-pill {
        background: var(--overhaul-modal-tab-text);
        color: var(--overhaul-modal-tab-bg);
        transition: color var(--overhaul-modal-tab-transition), background var(--overhaul-modal-tab-transition);
        height: 20px;
        border-radius: 10px;
        padding: 0 10px;
        text-align: center;
        display: flex;
        align-items: center;
        
        font-weight: 700;
        font-size: 10px;
        line-height: 10px;
        text-transform: uppercase;
        letter-spacing: 0.1em;
    }

    .overhaul-modal-tab-stats {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }

    .overhaul-modal-tab-percentage {
        color: var(--overhaul-modal-tab-fade);
        transition: color var(--overhaul-modal-tab-transition);
        font-weight: 400;
    }

    .overhaul-modal-tab-delta {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 2px;
    }

    .overhaul-modal-tab-delta-dir {
        --overhaul-modal-active-dir-color: var(--color-blue);
        &.dir-minus {
            --overhaul-modal-active-dir-color: var(--color-red);
        }
    }

    .overhaul-modal-tab-delta-value {
        color: var(--overhaul-modal-tab-text);
        transition: color var(--overhaul-modal-tab-transition);
        font-weight: 700;

        &:has(span) {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 2px;
        }
    }
            

    // <div class="overhaul-modal-tab-stats">
    //     <span class="overhaul-modal-tab-percentage">87.5%</span>
    //     <div class="overhaul-modal-tab-delta">
    //         <div class="overhaul-modal-tab-delta-dir">+</div>
    //         <div class="overhaul-modal-tab-delta-value">$3.7M</div>
    //     </div>
    // </div>

    &.overhaul-modal-tab-simple {
        .overhaul-modal-tab-label {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;
            gap: 10px;
        }
    }
}
