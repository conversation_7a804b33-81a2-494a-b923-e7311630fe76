@import "/assets/css/mixins";

.overhaul-add-object-modal {
    .overhaul-add-object-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    .overhaul-add-object-modal-header-title {
        font-weight: 700;
        font-size: 18px;
        line-height: 30px;

        letter-spacing: -0.02em;

        color: #E9EDF1;
    }

    .overhaul-add-object-modal-header-actions {
        display: flex;
        align-items: center;
        gap: 5px;
        justify-content: flex-end;
        gap: 10px;
    }

    .overhaul-add-object-modal-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 10px;

        .overhaul-add-object-modal-grid-item {
            > * {
                width: 100%;
            }
        }
    }
    .modal-dialog-panel {
        height: 100%;
    }
}
