@import "/assets/css/mixins";

.overhaul-modal {
    .modal-dialog-panel {
        width: 100%;
        max-width: 1020px;
        height: 100%;
    }
    .overhaul-modal-content {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
    }

    .overhaul-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .overhaul-modal-header-title {
        font-weight: 700;
        font-size: 18px;
        line-height: 1.5em;
        color: var(--color-white);
    }

    .overhaul-modal-header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }

    .overhaul-modal-tabs-column {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .overhaul-modal-tabs-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;
    }

    .overhaul-modal-filters {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap:wrap;
        flex: 1 1 auto;
        gap: 4px;
    }

    .overhaul-modal-tabs {
        border-radius: 4px;
        overflow:hidden;
        width: 100%;
        align-self: flex-start;

        display: grid;
        gap: 1px;
    }

    .overhaul-modal-input-groups {
        display: flex;
        flex-direction: column;
        align-items: stretch;
        gap: 1px;
    }

    .overhaul-modal-fields-column {
        display: flex;
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }

    .overhaul-modal-fields-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 44px;

        h4 {
            font-style: normal;
            font-weight: 700;
            font-size: 18px;
            line-height: 1.45em;
        }
    }
}
