@import "/assets/css/mixins";

.overhaul-initial-item-modal {
    .modal-dialog-panel {
        width: 100%;
        max-width: 1020px;
    }

    .overhaul-initial-item-modal-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .overhaul-initial-item-modal-header-title {
        font-style: normal;
        font-weight: 700;
        font-size: 18px;
        line-height: 1.5em;
        display: flex;
        align-items: center;
        text-transform: capitalize;

        color: var(--color-white);
    }

    .overhaul-initial-item-modal-header-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-left: auto;
    }

    .overhaul-initial-item-modal-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .overhaul-initial-item-modal-layout-left {
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 20px;
    }

    .overhaul-initial-item-modal-icon {
        width: 140px;
        height: 140px;
        background: var(--color-blue-grey);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .overhaul-initial-item-modal-title {
        font-weight: 700;
        font-size: 40px;
        line-height: 1.2em;
        letter-spacing: -0.02em;

        color: var(--color-white);
    }
    
    .overhaul-initial-item-modal-description {
        font-style: normal;
        font-weight: 700;
        font-size: 24px;
        line-height: 1.2em;

        letter-spacing: -0.02em;

        color: var(--color-grey);
    }

    .overhaul-initial-item-modal-disclaimer {
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;
        
        color: var(--color-blue-grey);
    }

    .overhaul-initial-item-modal-box-options {
        border: 1px solid var(--color-blue-grey);
        border-radius: 4px;
    }

    .overhaul-initial-item-modal-box {
        padding: 20px;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 20px;
        &:not(:last-child) {
            border-bottom: 1px solid var(--color-blue-grey);
        }
    }

    .overhaul-initial-item-modal-box-heading {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h5 {         
            font-weight: 700;
            font-size: 18px;
            line-height: 1.2em;

            letter-spacing: -0.02em;

            color: var(--color-grey);

            strong {
                font-weight: 700;
                color: var(--color-white);
            }
        }

        .overhaul-initial-item-modal-pill {
            background: var(--color-blue-grey);
            height: 20px;
            border-radius: 10px;
            padding: 0 10px;
            text-align: center;
            display: flex;
            align-items: center;
            
            font-weight: 700;
            font-size: 10px;
            line-height: 10px;
            text-transform: uppercase;
            letter-spacing: 0.1em;

            color: var(--color-white);
        }
    }

    .overhaul-initial-item-modal-count-box {
        border: 1px solid var(--color-blue-grey);
        padding: 20px;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 20px;
    }

    .overhaul-initial-item-modal-count-fields {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .overhaul-initial-item-modal-count-button {
            width: 40px;
            height: 40px;
            color: var(--color-blue-grey);
        }

        .overhaul-initial-item-modal-count-input {
            appearance: none;
            border: none;
            background: transparent none;
            font-weight: 700;
            font-size: 24px;
            line-height: 1.2em;
            text-align: center;
            letter-spacing: -0.02em;
            color: var(--color-white);
        }
    }

    .overhaul-initial-item-modal-switch-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.25em;
        color: var(--color-grey);
    }

    // <div class="overhaul-initial-item-modal-count-fields">
    //     <button class="overhaul-initial-item-modal-count-button" @click="count--">
    //         -
    //     </button>
    //     <input
    //         type="number"
    //         v-model="count"
    //         min="1"
    //         max="10"
    //         step="1"
    //     />
    //     <button class="overhaul-initial-item-modal-count-button" @click="count++">
    //         +
    //     </button>
    // </div>
}
