<template>
    <div class="overhaul-associated-items-block" :class="topLevelClasses">       
        <div class="overhaul-associated-items-block-title">
            <h5 v-text="capitalize(category)"></h5>
        </div>

        <template v-if="isExpanded">
            <div
                class="overhaul-associated-items-block-item"
                v-for="associatedItem in associatedItems"
            >
                <div class="overhaul-associated-items-block-item-title">
                    <h5 v-text="associatedItem.description"></h5>

                    <DropdownMenu>
                        <DropdownMenuRow icon-shape="unlink" @click.prevent="handleUnlink(associatedItem)">
                            Unlink from {{ itemCategoryVariants.lowerSingular }}
                        </DropdownMenuRow>
                    </DropdownMenu>
                </div>
                <dl
                    class="overhaul-associated-items-block-item-stat"
                    v-for="stat in associatedItem.expandedStats"
                >
                    <dt v-html="stat.label"></dt>
                    <dd v-html="stat.value"></dd>
                </dl>
            </div>

            <div v-if="!associatedItems?.length" class="overhaul-associated-items-block-empty">
                There are no associated <span v-text="category"></span>.
            </div>

            <div class="overhaul-associated-items-block-actions">
                <ElementsButton
                    type="button"
                    variant="muted"
                    size="sm"
                    @click="handleCancel"
                >Cancel</ElementsButton>
                <ElementsButton
                    type="button"
                    size="sm"
                    @click="handleLinkExisting"
                    v-if="canLinkExisting"
                >Link Existing</ElementsButton>
                <ElementsButton
                    type="button"
                    size="sm"
                    @click="handleLinkNewModalOpen"
                >Add</ElementsButton>
            </div>
        </template>

        <!-- Collapsed -->
        <template v-else>
            <div
                class="overhaul-associated-items-block-toggle-overlay"
                @click.prevent="isExpanded = true"
            ></div>
            <template v-if="associatedItems?.length">
                <dl class="overhaul-associated-items-block-item-line" v-for="item in associatedItems">
                    <dt v-text="item.description"></dt>
                    <dd v-text="item.statPrimary"></dd>
                </dl>
            </template>

            <template v-else>
                <p class="overhaul-associated-items-block-placeholder" v-text="placeholderText"></p>
            </template>

        </template>
    </div>

    <ModalsSubModal
        :is-open="linkExistingModalIsOpen"
        @close="handleLinkExistingModalClose"
        :show-footer="false"
        :title="`Link existing ${itemCategoryVariants.lowerSingular} to <strong>${item.description ?? 'this item'}</strong>`"
        subtitle="Sub"
    >
        <OverhaulExistingLineItem
            v-for="item in unlinkedItemsOfCategory"
            :item="item"
            @click.prevent="() => associatedExistingItem(item)"
        />
    </ModalsSubModal>
    
    <OverhaulCreateAndLinkItemModal
        :is-open="linkNewModalIsOpen"
        :parent-item="item"
        :category="category"
        @save="() => associatedExistingItem(item)"
        @close="handleLinkNewModalClose"
    />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulExistingLineItem from './OverhaulExistingLineItem.vue';
import { getCategoryVariants } from '~/stores/overhaul';
import { abbreviateFrequency } from '~/utils';
import DropdownMenu from '../modals/DropdownMenu.vue';
import DropdownMenuRow from '../modals/DropdownMenuRow.vue';
import OverhaulCreateAndLinkItemModal from './OverhaulCreateAndLinkItemModal.vue';

/** Section of the intake item modal that displays and manages associated items (e.g. a loan tied to an asset) */
defineOptions();

const overhaulStore = useOverhaulStore();

export interface OverhaulAssociatedItemsBlockProps {
    item: TPopulatedItem;
    category: TOverhaulCategory;
}
const props = defineProps<OverhaulAssociatedItemsBlockProps>()

const topLevelClasses = computed(() => {
    return [
        (isExpanded.value) ? 'overhaul-associated-items-block-expanded' : 'overhaul-associated-items-block-collapsed',
    ]
})

const itemCategoryVariants = computed(() => {
    return getCategoryVariants(props.category);
})

// All existing items of the category that are not associated with this item, and are in Standard mode
const unlinkedItemsOfCategory = computed(() => {
    return allItemsOfCategory.value
        .filter(i => 
            // TODO: Also filter out items that are associated to this item (e.g. have the parent's id in their associatedAssets)
            !(props.item[associationPropertyOfCategory.value]?.has(i.id)) // Not already associated
            && i.entryMode === 'Standard'
        );
})

const canLinkExisting = computed(() => {
    return unlinkedItemsOfCategory.value.length > 0;
})

const allItemsOfCategory = computed(() => {
    switch(props.category) {
        case 'assets':
            return overhaulStore.assetItemsArr;
        case 'liabilities':
            return overhaulStore.liabilityItemsArr;
        case 'income':
            return overhaulStore.incomeItemsArr;
        case 'expenses':
            return overhaulStore.expenseItemsArr;
        default:
            return [];
    }
})

const categoryVariants = computed(() => 
    getCategoryVariants(props.category)
)

const placeholderText = computed(() => {
    switch(props.category) {
        case 'assets':
            return 'Any property or investment associated with this ' + (props.item.unit ?? 'item');
        case 'liabilities':
            return 'Any debt associated with this ' + (props.item.unit ?? 'item');
        case 'income':
            return 'Any income associated with this ' + (props.item.unit ?? 'item');
        case 'expenses':
            return 'Any expense associated with this ' + (props.item.unit ?? 'item');
        case 'insurance':
            return 'Any expense associated with this ' + (props.item.unit ?? 'item');
        default:
            return '';
    }
})

const associationPropertyOfCategory = computed(() => {
    switch(props.category) {
        case 'assets':
            return 'associatedAssets';
        case 'liabilities':
            return 'associatedLiabilities';
        case 'income':
            return 'associatedIncome';
        case 'expenses':
            return 'associatedExpenses';
        default:
            return '';
    }
})

const associatedItems = computed(() => {
    const associatedVia = new Set(props.item.associatedVia ?? []);

    // If the category prop doesn't map to an associated property (e.g. associatedAssets),
    // AND there are no associatedVia (e.g. relations from other items), bail
    if (!associationPropertyOfCategory.value && !associatedVia.size) return;

    const associatedItemsOfCategory = new Set([
        ...(props.item[associationPropertyOfCategory.value] ?? []),
        ...associatedVia
    ]);

    if (!associatedItemsOfCategory?.size) return;

    // Go through ALL items of category (e.g. all assets) and return only the ones that are associated with this item
    return allItemsOfCategory.value.filter((item) => associatedItemsOfCategory.has(item.id))
        .map((item) => {
            return {
                ...item,
                expandedStats: getExpandedStats(item)
            }
        });
})

const isExpanded = ref(false)

function handleCancel() {
    isExpanded.value = false;
}

function handleLinkExisting() {
    linkExistingModalIsOpen.value = true;
}

// Link an existing item
const linkExistingModalIsOpen = ref(false)
function handleLinkExistingModalClose() {
    linkExistingModalIsOpen.value = false;
}
function handleLinkExistingModalOpen() {
    linkExistingModalIsOpen.value = true;
}
function associatedExistingItem(associatedItem: TPopulatedItem) {
    overhaulStore.associateItemToItem(props.item.id, props.category, associatedItem.id);
    handleLinkExistingModalClose()
}

// Create a new linked item
const linkNewModalIsOpen = ref(false)
function handleLinkNewModalClose() {
    linkNewModalIsOpen.value = false;
}
function handleLinkNewModalOpen() {
    linkNewModalIsOpen.value = true;
}

function handleUnlink(associatedItem: TPopulatedItem) {
    linkNewModalIsOpen.value = false;
    
    overhaulStore.dissociateItemFromItem(props.item.id, associatedItem.id);
}

// Given an item, return an array of objects to populate the expanded view of an associated item
function getExpandedStats(item: TPopulatedItem) {
    if (item.category === 'Liability') {
        const numbers = {
            amount: item.stats?.find(e => e.kind === 'liability')?.value,
            originationDate: item.fields.find(e => e.name === 'originationDate')?.value,
            termLength: item.fields.find(e => e.name === 'termLength')?.value,
            payment: item.fields?.find(e => e.name === 'minimumPayment')?.value,
        }

        const expandedStats = []

        if (numbers.amount || numbers.amount === 0) {
            expandedStats.push({label: 'Loan amount', value: abbreviateNumber(numbers.amount)});
        }
        if (numbers.originationDate) {
            expandedStats.push({label: 'Origination date', value: numbers.originationDate});
        }
        if (numbers.termLength) {
            const years = Math.floor(numbers.termLength / 12);
            const months = numbers.termLength % 12;
            const termStr = [
                (years) ? `${years} Yrs` : '',
                (months) ? `${months} Mths` : '',
            ].filter(e => e) // Remove empty strings
             .join(' ');

            if (numbers.originationDate) {
                console.log('originationDate', numbers.originationDate);
                expandedStats.push({label: 'Term / maturity', value: `${termStr} / ${numbers.originationDate}`});
            } else {
                expandedStats.push({label: 'Term', value: termStr});
            }
        }
        if (numbers.payment?.number && numbers.payment?.frequency) {
            expandedStats.push({label: 'Payment', value: `${abbreviateNumber(numbers.payment.number)} / ${abbreviateFrequency(numbers.payment.frequency)}`});
        }
        
        return expandedStats;
    }

    else if (item.category === 'Asset') {
        const amountStat = item.stats?.find(e => e.kind === 'asset');
        if (amountStat) {
            return [
                {label: 'Value', value: '$' + amountStat.value}
            ]
        }
    }

    else if (item.category === 'Expense') {
        const amountStat = item.stats?.find(e => e.kind === 'expense');
        if (amountStat) {
            return [
                {label: 'Amount', value: '$' + amountStat.value + ' / ' + amountStat.frequency}
            ]
        }
    }   
}

</script>

<style lang="scss">
@import "./OverhaulAssociatedItemsBlock";
</style>
