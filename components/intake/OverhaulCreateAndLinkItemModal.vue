<template>
    <SubModal
        :is-open="isOpen"
        :show-footer="shouldShowFooter"
        confirm-text="Save and Link"
        :title="`Create a new ${categoryVariants.lowerSingular} for <strong>${parentItem?.description ?? 'this item'}</strong>`"
        subtitle="Sub"
        @close="handleClose"
        @confirm="handleConfirm"
    >
        <div class="overhaul-create-and-link-modal">
            <template v-if="populatedItem">
                <div v-for="field in populatedItem.fields" :key="field.name">
                    <!-- TODO: Temporarily hiding "owner" fields -->
                    <SituationInputGroup
                        v-bind="field"
                        v-if="field && field?.name !== 'owner'"
                        :key="field.name"
                        @change="(payload) => handleItemFieldChange(field, payload)"
                    />
                </div>
            </template>
            
            <SelectInput
                v-else
                v-model="newItemKind"
                label="Type"
                size="sm"
                :options="kindOptions" 
            />
        </div>
    </SubModal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import { allAssetTypes, allLiabilityTypes, allIncomeTypes, allExpenseTypes, allInsuranceTypes } from '../situations/data/intake-data';
import SelectInput from '../forms/SelectInput.vue';
import SituationInputGroup, { type SituationInputGroupProps } from '../situations/SituationInputGroup.vue';
import SubModal from '../modals/SubModal.vue';

/** TODO: Description of this component */
defineOptions();

const overhaulStore = useOverhaulStore();

export interface OverhaulCreateAndLinkItemModalProps {
    isOpen: boolean;
    category: TOverhaulCategory;
    parentItem: TPopulatedItem;
}

const props = defineProps<OverhaulCreateAndLinkItemModalProps>()

const emit = defineEmits<{
    close: []
    confirm: []
}>()

const categoryVariants = computed(() => 
    getCategoryVariants(props.category)
)

const allKindsOfCategory = computed(() => {
    switch(getCategoryVariants(props.category)?.lowerSingular) {
        case 'asset':
            return allAssetTypes;
        case 'liability':
            return allLiabilityTypes;
        case 'income':
            return allIncomeTypes;
        case 'expense':
            return allExpenseTypes;
        case 'insurance':
            return allInsuranceTypes;
        default:
            return [];
    }   
})

const kindOptions = computed(() => {
    return allKindsOfCategory.value.map((kind) => {
        return {
            value: kind.kind,
            label: kind.label
        }
    })
})

const shouldShowFooter = computed(() => {
    return !!populatedItem.value;
})

const newItemKind = ref<string|null>(null);

// rawItem is in the format it will be saved in the store
// As fields are updated, changes will be stored in this object
// which will ultimately be passed to pinia for saving
const rawItem = ref<TOverhaulItem|null>(null);

// The "populated" version of the item, only used get metadata for each field on the item
const populatedItem = ref<TPopulatedItem|null>(null);

watch(() => newItemKind.value, (newValue) => {
    console.log('newItemKind', newValue);
    if (newValue) {
        populateItemFields(newValue);
    }
})

function handleClose() {
    emit('close');
    newItemKind.value = null;
    populatedItem.value = null;
    rawItem.value = null;
}

function populateItemFields(kind: string) {
    // Generate a new, empty item of the specified kind
    // This is just generating the object, it is not being saved anywhere or even added to the store
    const generatedItem = overhaulStore.generateItem(props.category, kind, 'Standard');

    if (generatedItem) {
        rawItem.value = generatedItem;
        
        const populated = overhaulStore.populateRawItem(generatedItem);
        populatedItem.value = populated;
    }
}

function handleItemFieldChange(field: SituationInputGroupProps, payload: any) {
    
    if (!rawItem.value?.fields.hasOwnProperty(field.name)) return;

    if (
        (field.type === 'currency-frequency' || field.type === 'number-frequency')
            && typeof payload === 'object'
            && payload !== null
            && payload.hasOwnProperty('frequency')
            && payload.hasOwnProperty('number')
        ) {
        rawItem.value.fields[field.name] = payload.number;
        rawItem.value.fields[field.frequencyName ?? 'paymentFrequency'] = (payload.frequency ?? 'Monthly');
        console.log(
            [field.name, payload.value],
            [(field.frequencyName ?? 'paymentFrequency'), (payload.frequency ?? 'Monthly')]
        )
    } else {
        rawItem.value.fields[field.name] = payload;
        console.log([field.name, payload]);
    }

    console.log('handleItemFieldChange', payload);
}

function handleConfirm() {
    if (!rawItem.value) return;

    const newItem = overhaulStore.addAndLinkNewItem(props.parentItem, rawItem.value)
    // if (newItem) {
    //     overhaulStore.linkItemToItem(props.parentItem?.id, newItem.id);
    // }

    handleClose();
}

</script>

<style lang="scss">
.overhaul-create-and-link-modal {
    
}
</style>

