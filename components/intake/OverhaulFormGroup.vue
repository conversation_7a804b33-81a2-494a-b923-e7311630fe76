<template>
    <div class="overhaul-form-group">
        <div
            class="overhaul-form-group-existing"
            v-if="existingFormsOfKind?.length"
            :class="isExpanded ? 'overhaul-form-group-existing-expanded' : 'overhaul-form-group-existing-collapsed'"
        >
            <div class="overhaul-form-group-title">
                <h5 v-text="capitalize(kind) + ' Form' + (existingFormsOfKind.length > 1 ? 's' : '')"></h5>
            </div>
            <template v-if="isExpanded">
                <div class="overhaul-form-group-expanded-item" v-for="form in mappedExistingForms">
                    <div class="overhaul-form-group-title">
                        <h5 v-text="form.description"></h5>

                        <DropdownMenu>
                            <DropdownMenuRow icon-shape="edit" @click.prevent="handleOpenEditModal(form.id)">
                                Edit
                            </DropdownMenuRow>
                            <DropdownMenuRow icon-shape="trash" @click.prevent="handleDeleteForm(form.id)">
                                Delete
                            </DropdownMenuRow>
                        </DropdownMenu>
                    </div>
                    <dl
                        class="overhaul-form-group-expanded-item-stat"
                        v-for="stat in form.expandedStats ?? [{ label: 'None', value: '0' }]"
                    >
                        <dt v-html="stat.label"></dt>
                        <dd v-html="stat.value"></dd>
                    </dl>

                    <OverhaulIncome1099Modal
                        v-if="kind === '1099'"
                        :item="item"
                        :form="form"
                        :is-open="activeEditModal === form.id"
                        @close="handleCloseEditModal"
                    />

                    <OverhaulIncomeW2Modal
                        v-if="kind === 'w2'"
                        :item="item"
                        :form="form"
                        :is-open="activeEditModal === form.id"
                        @close="handleCloseEditModal"
                    />
                </div>

                <div v-if="!existingFormsOfKind?.length" class="overhaul-form-group-empty">
                    There are no associated <span v-text="capitalize(kind)"></span>s.
                </div>

                <div class="overhaul-form-group-expanded-actions">
                    <ElementsButton type="button" variant="muted" size="sm" @click="handleCancel"
                        >Collapse</ElementsButton
                    >
                    <ElementsButton type="button" size="sm" @click="handleOpenNewModal">Add</ElementsButton>
                </div>
            </template>

            <template v-else>
                <div class="overhaul-form-group-toggle-overlay" @click.prevent="isExpanded = true"></div>
                <template v-if="mappedExistingForms?.length">
                    <dl class="overhaul-form-group-item-line" v-for="form in mappedExistingForms">
                        <dt v-text="form.description"></dt>
                        <dd v-text="form.value"></dd>
                    </dl>
                </template>

                <template v-else>
                    <p class="overhaul-form-group-placeholder" v-text="placeholderText"></p>
                </template>
            </template>
        </div>

        <OverhaulModalRelationshipRow
            v-if="!existingFormsOfKind?.length"
            :title="capitalize(kind) + ' Forms'"
            :description="'View and manage the ' + capitalize(kind) + ' form(s) for this income.'"
            @click.prevent="handleModalOpen"
        />

        <!-- New Form Modals -->
        <OverhaulIncome1099Modal v-if="kind === '1099'" :item="item" :is-open="modalIsOpen" @close="handleModalClose" />
        <OverhaulIncomeW2Modal v-if="kind === 'w2'" :item="item" :is-open="modalIsOpen" @close="handleModalClose" />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulModalRelationshipRow from './income/OverhaulModalRelationshipRow.vue';
import SubModal from '~/components/modals/SubModal.vue';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';
import TextInput from '~/components/forms/TextInput.vue';
import OverhaulIncomeFormInputGroup from './income/OverhaulIncomeFormInputGroup.vue';
import OverhaulIncome1099Modal from './income/OverhaulIncome1099Modal.vue';
import OverhaulIncomeW2Modal from './income/OverhaulIncomeW2Modal.vue';
import DropdownMenu from '~/components/modals/DropdownMenu.vue';
import DropdownMenuRow from '~/components/modals/DropdownMenuRow.vue';

/** The inline display and modal for viewing / managing a 1099 form for income */
defineOptions();

export interface OverhaulFormGroupProps {
    item: TPopulatedItem;
    kind: 'w2' | '1099';
}
const props = defineProps<OverhaulFormGroupProps>();

// const emit = defineEmits<{}>()

const overhaulStore = useOverhaulStore();

const isExpanded = ref(false);
function handleCancel() {
    isExpanded.value = false;
}

const existingFormsOfKind = computed(() => {
    return props.item.forms?.filter(form => form.kind === props.kind) ?? [];
});

const mappedExistingForms = computed(() => {
    const forms = existingFormsOfKind.value;
    switch (props.kind) {
        case 'w2':
            return forms.map(form => ({
                description: form.yearMonth,
                value: form.wages ? abbreviateNumber(form.wages ?? 0, true, false) : null,
                expandedStats: [
                    {
                        label: 'Income',
                        value: abbreviateNumber(form.wages ?? 0, true, false),
                    },
                ],
                ...form,
            }));
        case '1099':
            return forms.map(form => ({
                description: form.yearMonth,
                value: form.nonemployeeCompensation
                    ? abbreviateNumber(form.nonemployeeCompensation ?? 0, true, false)
                    : null,
                expandedStats: [
                    {
                        label: 'Income',
                        value: abbreviateNumber(form.nonemployeeCompensation ?? 0, true, false),
                    },
                ],
                ...form,
            }));
        default:
            return forms;
    }
});

const modalIsOpen = ref(false);
function handleModalClose() {
    modalIsOpen.value = false;
}
function handleModalOpen() {
    modalIsOpen.value = true;
}
function handleOpenNewModal() {
    console.log('handleOpenNewModal');
    modalIsOpen.value = true;
}

const activeEditModal = ref<string | null>(null);
function handleOpenEditModal(formId: string) {
    activeEditModal.value = formId;
}
function handleCloseEditModal() {
    activeEditModal.value = null;
}
function handleDeleteForm(formId: string) {
    console.log('handleDeleteForm', formId);
    overhaulStore.deleteFormFromItem(props.item.id, formId);
}
</script>

<style lang="scss">
@import './OverhaulFormGroup';
</style>
