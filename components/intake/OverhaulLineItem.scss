@import "/assets/css/mixins";

.overhaul-line-item {
    font-family: 'Oxygen', sans-serif;
    background: rgba(var(--color-white-rgb),.05);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    height: 80px;
    gap: 20px;
    padding: 0 20px;
    cursor: pointer;
    transition: background .2s;

    &:hover {
        background: rgba(var(--color-white-rgb),.1);
    }

    --overhaul-line-item-label-color: var(--color-white);

    &.overhaul-line-item-not-started {
        --overhaul-line-item-label-color: rgba(var(--color-grey-rgb), .5);
        .overhaul-line-item-label {
            font-weight: 600;
        }
    }

    .overhaul-line-item-handle {
        background: rgba(var(--color-blue-grey-rgb), 0.10);
        width: 22px;
        height: 100%;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: grab;
    }

    &.overhaul-line-item-draggable {
        padding-left: 0;
    }

    .overhaul-line-item-click-target {
        flex: 1 1 auto;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
    }

    .overhaul-line-item-icon {
        color: var(--overhaul-line-item-label-color);
    }

    .overhaul-line-item-label {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        color: var(--overhaul-line-item-label-color);
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        gap: 4px;

        .overhaul-line-item-sub-label {
            color: var(--color-grey);
            font-weight: 400;
        }

        .overhaul-line-item-pill {
            background: var(--color-blue-grey);
            height: 20px;
            border-radius: 10px;
            padding: 0 10px;
            text-align: center;
            display: flex;
            align-items: center;
            
            font-weight: 700;
            font-size: 10px;
            line-height: 10px;
            text-transform: uppercase;
            letter-spacing: 0.1em;

            color: var(--color-white);
        }
    }

    .overhaul-line-item-status {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
        width: 40%;
        flex: auto;
        min-width: 150px;
    }

    .overhaul-line-item-progress-label {
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
        flex: none;
        color: rgba(var(--color-grey-rgb), .5);
    }

    .overhaul-line-item-pending-state {
        display: flex;
        flex: auto;
        align-items: center;
        justify-content: flex-end;
        margin-left: auto;
        gap: 20px;
    }

    .overhaul-line-item-progress-bar {
        flex: auto;
        height: 2px;
        border-radius: 1px;
        background: rgba(var(--color-white-rgb), 0.2);
        position: relative;
        overflow:hidden;
        max-width: 320px;

        .overhaul-line-item-progress-bar-fill {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: var(--color-grey);
        }
    }

    .overhaul-line-item-cta {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        text-align: right;
        gap: 10px;
        color: rgba(var(--color-grey-rgb), .5);
        span {
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
            line-height: 20px; /* 166.667% */
        }
        .svg-icon {
            color: rgba(var(--color-white-rgb), .5);
        }
    }

    .overhaul-line-item-kpis {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 40px;
        flex: auto;
    }

    .overhaul-line-item-kpi {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        flex-direction: column;
        gap: 4px;
        text-align: right;
        white-space: nowrap;
        flex-wrap: nowrap;

        dt {
            color: rgba(var(--color-white-rgb), .5);
            text-align: right;
            font-size: 12px;
            font-weight: 700;
            line-height: 14px;
            letter-spacing: 1.2px;
            text-transform: uppercase;
        }

        dd {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            color: var(--color-white);
            text-align: right;
            font-size: 14px;
            font-weight: 700;
            line-height: 18px;
        }
    }
    
    .overhaul-line-item-kpi-sign {
        margin-right: 3px;
        color: var(--color-green);

        &.overhaul-line-item-kpi-sign-negative {
            color: var(--color-red);
        }
    }

    .overhaul-line-item-kpi-prefix {
        opacity: .5;
        margin-right: 2px;
        font-weight: 500;
    }

    .overhaul-line-item-kpi-value {
        color: var(--color-white);
    }

    .overhaul-line-item-kpi-suffix {
        opacity: .5;
        margin-left: 4px;
        font-weight: 400;
    }


    &.overhaul-line-item-entry-mode-simple {
        .overhaul-line-item-sub-label {
            color: var(--color-white);
            font-weight: 600;
        }
    }
}
