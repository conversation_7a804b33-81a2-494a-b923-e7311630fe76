<template>
    <div class="overhaul-line-item" :class="topLevelClass">
        <div
            class="overhaul-line-item-handle"
            v-if="draggable"
        >
            <SvgIcon 
                shape="drag"
                :width="14"
                :height="14"
            />
        </div>

        <div class="overhaul-line-item-click-target" @click.prevent="emit('clicked')">
            <div class="overhaul-line-item-icon">
                <BigIcon
                    :shape="iconShape"
                    :width="40"
                    :height="40"
                />
            </div>

            <div class="overhaul-line-item-label">
                <span class="overhaul-line-item-pill" v-if="entryMode === 'Simple'">Simple</span>
                <span class="overhaul-line-item-sub-label" v-text="kindLabel"></span>
                <span v-text="description" v-if="description"></span>
            </div>

            <div class="overhaul-line-item-kpis" v-if="status === 'complete' && kpis?.length">
                <dl class="overhaul-line-item-kpi" v-for="kpi in kpis">
                    <dt v-text="kpi.label"></dt>
                    <dd>
                        <span v-if="kpi.label === 'Net Worth' && kpi.direction !== 'neg'" class="overhaul-line-item-kpi-sign">+</span>
                        <span v-else-if="kpi.direction === 'neg'" class="overhaul-line-item-kpi-sign overhaul-line-item-kpi-sign-negative">-</span>
                        <span class="overhaul-line-item-kpi-prefix" v-if="kpi.formatCurrency">$</span>
                        <span class="overhaul-line-item-kpi-value" v-text="kpi.formattedValue ?? kpi.value"></span>
                        <span class="overhaul-line-item-kpi-suffix" v-if="kpi.suffix" v-text="kpi.suffix"></span>
                    </dd>
                </dl>
            </div>

            <div class="overhaul-line-item-pending-state" v-if="status !== 'complete'">
                <div class="overhaul-line-item-status" v-if="status === 'in-progress'">
                    <span class="overhaul-line-item-progress-label" v-text="progressLabel"></span>
                    <div class="overhaul-line-item-progress-bar">
                        <div class="overhaul-line-item-progress-bar-fill" :style="progressBarStyle"></div>
                    </div>                
                </div>

                <div class="overhaul-line-item-cta">
                    <span>Click to {{ status === 'not-started' ? 'Start' : 'Finish'}}</span>
                    <SvgIcon shape="arrow-forward" :width="20" :height="20" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import BigIcon from '../images/BigIcon.vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { TOverhaulLineItem } from '~/stores/overhaul';

/** An individual line item shown on the intake screen */
defineOptions();

const props = withDefaults(defineProps<TOverhaulLineItem>(), {
    draggable: true,
    iconShape: 'bungalow'
})

const emit = defineEmits<{
    clicked: []
}>()

const topLevelClass = computed(() => [
    props.draggable ? 'overhaul-line-item-draggable' : '',
    props.status ? `overhaul-line-item-${props.status}` : '',
    'overhaul-line-item-entry-mode-'+
        ((props.entryMode === 'Simple') ? 'simple' : 'standard'),
])

const progressLabel = computed(() => 
    (props.progress)
        ? Math.round((props.progress?.percentage ?? 0) * 100) + '% Complete'
        : '0% Complete'
)

const progressBarStyle = computed(() => 
    (props.progress)
        ? 'width:' + Math.round((props.progress?.percentage ?? 0) * 100) + '%'
        : ''
)
</script>

<style lang="scss">
@import "./OverhaulLineItem";
</style>
