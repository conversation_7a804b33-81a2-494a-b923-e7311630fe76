<template>
    <div class="overhaul-intake-review-group">
        <div class="overhaul-intake-review-group-icon">
            <BigIcon
                :shape="svgShape"
                :width="50"
                :height="50"
            />
        </div>

        <div class="overhaul-intake-review-group-text">
            <h4 v-text="label"></h4>

            <div class="overhaul-intake-review-group-progress">
                <p><span v-text="completionPercentage"></span> Complete</p>

                <div class="overhaul-intake-review-group-progress-bar" :style="{'--progress-bar-width': completionPercentage}">

                </div>
            </div>

            <p class="overhaul-intake-review-group-instructions" v-text="instructions"></p>
        </div>

        <div class="overhaul-intake-review-group-right">
            <p class="overhaul-intake-review-group-count"> <em v-text="completedItemsCount ?? 0"></em> / <em v-text="itemsCount"></em> Selections complete</p>

            <div class="overhaul-intake-review-group-items">
                <OverhaulReviewLineItem
                    v-for="item in transformedItems"
                    :item="item"
                    @click.prevent="() => handleLineItemClick(item)"
                />

                <!-- <SituationItem
                    v-for="item in transformedItems"
                    v-bind="item"

                    @simple-change="(...e) => emit('simple-change', ...e)"
                    @standard-change="(...e) => emit('standard-change', ...e)"
                    @entry-mode-change="(...e) => emit('entry-mode-change', item.name, ...e)"
                    @remove-item="(...e) => emit('remove-item', item.name, ...e)"
                    @add-item="emit('add-item', item.name)"
                    @remove-group="emit('remove-group', item)"
                /> -->
            </div>
        </div>
    </div>

    <OverhaulModal
        :is-open="modalIsOpen"
        :initial-item="clickedItem"
        :category="category"
        @close="handleModalClose"
    />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SituationItem from '../situations/SituationItem.vue';
import type { SituationItemProps } from '../situations/SituationItem.vue';
import SituationLineItem from '../situations/SituationLineItem.vue';
import type { SituationLineItemProps } from '../situations/SituationLineItem.vue';
import type { SituationEntryMode, SituationItemEmits } from '../situations/SituationItem.vue';
import BigIcon from '../images/BigIcon.vue';
import OverhaulReviewLineItem from './OverhaulReviewLineItem.vue';
import OverhaulModal from '~/components/intake/OverhaulModal.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';

/** TODO: A listing of items, grouped by category, to show on the intake review screen */
defineOptions();

export interface TempSituation {
    title: string;
    itemsCount?: number | null;
    entryMode: 'simple' | 'standard';
    isCompleted: boolean;
}

export interface OverhaulIntakeReviewGroupProps {
    category: TOverhaulCategory;
    svgShape: TBigIconShapeKey;
    label: string;
    instructions: string;
    completion: number;
    items?: TPopulatedItem[];
}

const props = defineProps<OverhaulIntakeReviewGroupProps>()

const transformedItems = computed(() => props.items?.map(item => {
    return {
        ...item,
        title: item.kind,
        displayMode: 'line'
    }
}));

const completionPercentage = computed(() => `${props.completion}%`)

const itemsCount = computed(() => props.items?.length ?? 0);
const completedItemsCount = computed(() => 
    props.items?.filter(e => e.progress?.percentage === 1).length
);

const clickedItem: Ref<string|null> = ref(null);
const modalIsOpen = ref(false);
function handleModalOpen() {
    modalIsOpen.value = true;
}
function handleModalClose() {
    modalIsOpen.value = false;
    clickedItem.value = null;
}
async function handleLineItemClick(item: TOverhaulLineItem) {
    if (props.category === 'expenses') {
        await navigateTo('/client/intake/expenses')
    } else {
        clickedItem.value = item.id;
        await nextTick();
        handleModalOpen();
    }
}
</script>

<style lang="scss">
@import "./OverhaulIntakeReviewGroup";
</style>
