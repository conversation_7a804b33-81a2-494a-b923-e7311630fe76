<template>
    <div class="overhaul-income-1099">
        <OverhaulModalRelationshipRow
            v-if="existing1099"
            title="1099"
            :stats="existing1099StatsArray"
            :has-arrow="false"
            @click.prevent="handleModalOpen"
        />

        <OverhaulModalRelationshipRow
            v-if="!existing1099"    
            title="1099 Form"
            description="View and manage the 1099 form for this income."
            @click.prevent="handleModalOpen"
        />

        <SubModal
            :is-open="modalIsOpen"
            :show-footer="true"
            :confirm-text="existing1099 ? 'Save' : 'Add'"
            :title="`Add 1099-MISC to to <strong>${item.description ?? 'this item'}</strong>`"
            @close="handleModalClose"
            @confirm="handleModalConfirm"
        >
            <div class="overhaul-income-input-row">
                <OverhaulIncomeFormInputGroup
                    v-model="yearMonth"
                    format="string"
                    label="Year / Month"
                />
            </div>
            <div class="overhaul-income-input-row">
                <OverhaulIncomeFormInputGroup
                    v-model="nonemployeeCompensation"
                    label="Nonemployee compensation"
                />
                <OverhaulIncomeFormInputGroup
                    v-model="federalIncomeTaxWithheld"
                    label="Federal income tax withheld"
                />
            </div>

            <div class="overhaul-income-input-row">
                <OverhaulIncomeFormInputGroup
                    v-model="stateIncomeTaxWithheld"
                    label="State income tax withheld"
                />
            </div>
        </SubModal>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulModalRelationshipRow from './OverhaulModalRelationshipRow.vue';
import SubModal from '~/components/modals/SubModal.vue';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';
import TextInput from '~/components/forms/TextInput.vue';
import OverhaulIncomeFormInputGroup from './OverhaulIncomeFormInputGroup.vue';

/** The inline display and modal for viewing / managing a 1099 form for income */
defineOptions();

export interface OverhaulIncome1099Props {
    item: TPopulatedItem;
}
const props = withDefaults(defineProps<OverhaulIncome1099Props>(), {
    
})

const emit = defineEmits<{
    add: [fields: {[key: string]: any}]
}>()

const yearMonth = ref<string|number|null>(null);
const nonemployeeCompensation = ref<number|null>(null);
const federalIncomeTaxWithheld = ref<number|null>(null);
const stateIncomeTaxWithheld = ref<number|null>(null);

const existing1099 = computed(() => {
    return props.item.forms?.find(form => form.kind === '1099')
})

const existing1099StatsArray = computed(() => {
    if (!existing1099.value) return [];
    
    const value = abbreviateNumber(existing1099.value?.nonemployeeCompensation ?? 0, true, false);
    
    return [
        {
            label: existing1099.value.yearMonth ?? '1099',
            value: value
        }
    ]
})

onMounted(async () => {
    await nextTick();
    syncExistingFormToRefs()
});

watch(existing1099, (newValue) => {
    syncExistingFormToRefs(newValue);
});

function syncExistingFormToRefs(form?: {[key: string]: any}) {
    const formData = form ?? existing1099.value;
    if (formData) {
        yearMonth.value = formData.yearMonth;
        nonemployeeCompensation.value = formData.nonemployeeCompensation;
        federalIncomeTaxWithheld.value = formData.federalIncomeTaxWithheld;
        stateIncomeTaxWithheld.value = formData.stateIncomeTaxWithheld;
    }
}

// W2 fields
// wages
// federalIncomeTaxWithheld
// socialSecurityWages
// socialSecurityTaxWithheld
// medicareWages
// medicareTaxWithheld
// socialSecurityTips
// allocatedTips
// dependentCareBenefits
// nonQualifiedPlans
// twelveA
// thirteen
// twelveB

const modalIsOpen = ref(false);
function handleModalClose() {
    modalIsOpen.value = false;
}
function handleModalOpen() {
    modalIsOpen.value = true;
}
function handleModalConfirm() {
    modalIsOpen.value = false;
    emit('add', dataObj.value);
}
const dataObj = computed(() => ({
    yearMonth: yearMonth.value,
    nonemployeeCompensation: nonemployeeCompensation.value,
    federalIncomeTaxWithheld: federalIncomeTaxWithheld.value,
    stateIncomeTaxWithheld: stateIncomeTaxWithheld.value,
}))

</script>

<style lang="scss">
// .overhaul-income-1099 {
    .overhaul-income-form-input-group {
        padding: 20px;
        .overhaul-income-form-input-group-label {
            font-weight: 700;
            font-size: 12px;
            line-height: 1.5em;
            letter-spacing: -0.02em;

            color: var(--color-grey);

            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 10px;

            .overhaul-income-form-input-group-label-primary {
                font-weight: 400;
                font-size: 14px;
            }
        }

        .text-input {
            margin-bottom: -12px;
            &:has(.text-input-icon) {
                margin-left: -7px;
            }
        }
    }

    .overhaul-income-input-row {
        display: grid;
        gap: 0;
        background: rgba(var(--color-white-rgb), .05);
        grid-template-columns: repeat(1, minmax(0, 1fr));
        
        border-bottom: 1px solid rgba(var(--color-white-rgb), .1);

        &:last-child {
            border-bottom: none;
        }
        
        // @for $i from 2 through 6 {
            &:has(> :last-child:nth-child(2)) {
                grid-template-columns: repeat(2, 1fr);
                > :first-child {
                    border-right: 1px solid rgba(var(--color-white-rgb), .1);
                }
            }
        // }
            // > :last-child {
            //     border-left: 1px solid rgba(var(--color-white-rgb), .1);
            // }
        // }
    }
// }
</style>

