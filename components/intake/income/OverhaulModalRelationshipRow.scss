@import "/assets/css/mixins";

.overhaul-modal-relationship-row {
    display: flex;
    gap: 10px;
    background: rgba(var(--color-white-rgb), 0.05);
    padding: 20px;
    position: relative;
    transition: background .2s ease-in-out;
    align-items: center;
    cursor: pointer;

    &:hover {
        background: rgba(var(--color-white-rgb), 0.1);
    }
    
    .overhaul-modal-relationship-row-text {
        flex: 1 1 auto;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        gap: 10px;
        flex-direction: column;
    }

    .overhaul-modal-relationship-row-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h5 {
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 1.45em;
            letter-spacing: -0.02em;

            color: var(--color-grey);
        }
    }

    .overhaul-modal-relationship-row-description {
        font-size: 14px;
        font-weight: 400;
        line-height: 1.45em;
        color: var(--color-blue-grey);
        transition: color .2s ease-in-out;
        user-select: none;

        strong {
            color: var(--color-white);
        }

        em {
            color: var(--color-grey);
            font-style: normal;
        }
    }

    .overhaul-modal-relationship-row-stat {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;
        
        color: var(--color-white);

        dd {
            font-weight: 800;
            color: var(--color-white);
        }
    }

    .overhaul-modal-relationship-row-arrow {
        flex: 0;
        width: 24px;
        height: 24px;
        color: var(--color-blue-grey);
    }
}
