<template>
    <SubModal
        :is-open="isOpen"
        :show-footer="true"
        :confirm-text="existingForm ? 'Save' : 'Add'"
        :title="`Add W2 to to <strong>${item.description ?? 'this item'}</strong>`"
        @close="handleModalClose"
        @confirm="handleModalConfirm"
    >
        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="yearMonth"
                format="string"
                label="Year / Month"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="wages"
                label="Wages, tips, other compensation"
            />
            <OverhaulIncomeFormInputGroup
                v-model="federalIncomeTaxWithheld"
                label="Federal income tax withheld"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="socialSecurityWages"
                label="Social security wages"
            />
            <OverhaulIncomeFormInputGroup
                v-model="socialSecurityTaxWithheld"
                label="Social security tax withheld"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="medicareWages"
                label="Medicare wages &amp; tips"
            />
            <OverhaulIncomeFormInputGroup
                v-model="medicareTaxWithheld"
                label="Medicare tax withheld"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="socialSecurityTips"
                label="Social security tips"
            />

            <OverhaulIncomeFormInputGroup
                v-model="allocatedTips"
                label="Allocated tips"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="dependentCareBenefits"
                label="Dependent care benefits"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="nonQualifiedPlans"
                label="Non-qualified plans"
            />
            <OverhaulIncomeFormInputGroup
                v-model="twelveA"
                label="12a"
            />
        </div>

        <div class="overhaul-income-input-row">
            <OverhaulIncomeFormInputGroup
                v-model="thirteen"
                label="13"
            />
            <OverhaulIncomeFormInputGroup
                v-model="twelveB"
                label="12b"
            />
        </div>




    </SubModal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import OverhaulModalRelationshipRow from './OverhaulModalRelationshipRow.vue';
import SubModal from '~/components/modals/SubModal.vue';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';
import TextInput from '~/components/forms/TextInput.vue';
import OverhaulIncomeFormInputGroup from './OverhaulIncomeFormInputGroup.vue';
import { useOverhaulStore } from "~/stores/overhaul";

/** The inline display and modal for viewing / managing a W2 form for income */
defineOptions();

const overhaulStore = useOverhaulStore();

export interface OverhaulIncomeW2ModalProps {
    item: TPopulatedItem;
    form?: {[key: string]: any};
    isOpen?: boolean;
}
const props = withDefaults(defineProps<OverhaulIncomeW2ModalProps>(), {
    isOpen: false
})

const emit = defineEmits<{
    close: [],
    add: [fields: {[key: string]: any}]
}>()

const yearMonth = ref<string|number|null>(null);
// const nonemployeeCompensation = ref<number|null>(null);
// const federalIncomeTaxWithheld = ref<number|null>(null);
// const stateIncomeTaxWithheld = ref<number|null>(null);

const wages = ref<number|null>(null);
const federalIncomeTaxWithheld = ref<number|null>(null);
const socialSecurityWages = ref<number|null>(null);
const socialSecurityTaxWithheld = ref<number|null>(null);
const medicareWages = ref<number|null>(null);
const medicareTaxWithheld = ref<number|null>(null);
const socialSecurityTips = ref<number|null>(null);
const allocatedTips = ref<number|null>(null);
const dependentCareBenefits = ref<number|null>(null);
const nonQualifiedPlans = ref<number|null>(null);
const twelveA = ref<number|null>(null);
const thirteen = ref<number|null>(null);
const twelveB = ref<number|null>(null);

const existingForm = computed(() => props.form)

onMounted(async () => {
    await nextTick();
    syncExistingFormToRefs()
});

function handleModalClose() {
    emit('close');
}

watch(() => props.isOpen, (newValue) => {
    if (newValue) {
        syncExistingFormToRefs();
    }
})

watch(() => props.form, (newValue) => {
    syncExistingFormToRefs(newValue);
});

function syncExistingFormToRefs(form?: {[key: string]: any}) {
    const formData = form ?? (existingForm.value ?? false);
    if (formData) {
        yearMonth.value = formData.yearMonth;
        wages.value = formData.wages;
        federalIncomeTaxWithheld.value = formData.federalIncomeTaxWithheld;
        socialSecurityWages.value = formData.socialSecurityWages;
        socialSecurityTaxWithheld.value = formData.socialSecurityTaxWithheld;
        medicareWages.value = formData.medicareWages;
        medicareTaxWithheld.value = formData.medicareTaxWithheld;
        socialSecurityTips.value = formData.socialSecurityTips;
        allocatedTips.value = formData.allocatedTips;
        dependentCareBenefits.value = formData.dependentCareBenefits;
        nonQualifiedPlans.value = formData.nonQualifiedPlans;
        twelveA.value = formData.twelveA;
        thirteen.value = formData.thirteen;
        twelveB.value = formData.twelveB;
    } else {
        yearMonth.value = null;
        wages.value = null;
        federalIncomeTaxWithheld.value = null;
        socialSecurityWages.value = null;
        socialSecurityTaxWithheld.value = null;
        medicareWages.value = null;
        medicareTaxWithheld.value = null;
        socialSecurityTips.value = null;
        allocatedTips.value = null;
        dependentCareBenefits.value = null;
        nonQualifiedPlans.value = null;
        twelveA.value = null;
        thirteen.value = null;
        twelveB.value = null;
    }
}

// W2 fields
// wages
// federalIncomeTaxWithheld
// socialSecurityWages
// socialSecurityTaxWithheld
// medicareWages
// medicareTaxWithheld
// socialSecurityTips
// allocatedTips
// dependentCareBenefits
// nonQualifiedPlans
// twelveA
// thirteen
// twelveB

async function handleModalConfirm() {
    const existingFormId = existingForm.value?.id ?? undefined
    console.log('handleModalConfirm', props.item.id, dataObj.value, existingFormId);
    overhaulStore.upsertW2(props.item.id, dataObj.value, existingFormId);
    await nextTick()
    emit('close');
}
const dataObj = computed(() => ({
    yearMonth: yearMonth.value,
    wages: wages.value,
    federalIncomeTaxWithheld: federalIncomeTaxWithheld.value,
    socialSecurityWages: socialSecurityWages.value,
    socialSecurityTaxWithheld: socialSecurityTaxWithheld.value,
    medicareWages: medicareWages.value,
    medicareTaxWithheld: medicareTaxWithheld.value,
    socialSecurityTips: socialSecurityTips.value,
    allocatedTips: allocatedTips.value,
    dependentCareBenefits: dependentCareBenefits.value,
    nonQualifiedPlans: nonQualifiedPlans.value,
    twelveA: twelveA.value,
    thirteen: thirteen.value,
    twelveB: twelveB.value
}))

</script>

<style lang="scss">
.overhaul-income-form-input-group {
    padding: 20px;
    .overhaul-income-form-input-group-label {
        font-weight: 700;
        font-size: 12px;
        line-height: 1.5em;
        letter-spacing: -0.02em;

        color: var(--color-grey);

        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;

        .overhaul-income-form-input-group-label-primary {
            font-weight: 400;
            font-size: 14px;
        }
    }

    .text-input {
        margin-bottom: -12px;
        &:has(.text-input-icon) {
            margin-left: -7px;
        }
    }
}

.overhaul-income-input-row {
    display: grid;
    gap: 0;
    background: rgba(var(--color-white-rgb), .05);
    grid-template-columns: repeat(1, minmax(0, 1fr));

    border-bottom: 1px solid rgba(var(--color-white-rgb), .1);

    &:last-child {
        border-bottom: none;
    }

    // @for $i from 2 through 6 {
    &:has(> :last-child:nth-child(2)) {
        grid-template-columns: repeat(2, 1fr);
        > :first-child {
            border-right: 1px solid rgba(var(--color-white-rgb), .1);
        }
    }
    // }
    // > :last-child {
    //     border-left: 1px solid rgba(var(--color-white-rgb), .1);
    // }
    // }
}
// }
</style>

