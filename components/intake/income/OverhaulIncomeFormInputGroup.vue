<template>
    <div class="overhaul-income-form-input-group">
        <label class="overhaul-income-form-input-group-label">
            <span v-text="label" class="overhaul-income-form-input-group-label-primary"></span>
            <span v-if="subLabel" v-text="subLabel"></span>
        </label>
        <TextInput
            v-bind="textInputProps"
            v-model="model"
        />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import TextInput from '~/components/forms/TextInput.vue';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'overhaul-income-form-input-group-' + componentId);

const model = defineModel()

export interface OverhaulIncomeFormInputGroupProps {
    label: string;
    subLabel?: string;
    format?: 'string'|'number'|'currency';
}
const props = withDefaults(defineProps<OverhaulIncomeFormInputGroupProps>(), {
    format: 'currency'
})

const textInputProps = computed(() => ({
    placeholder:"-",
    numberFormatting: (props.format === 'currency') ? true : false,
    size:"sm",
    withBorder:false,
    svgIcon:(props.format !== 'string') ? {
        shape: 'dollar',
        position: 'left',
    } : null
}))

const emit = defineEmits<{
    change: [id: number]
}>()
</script>

<style lang="scss">
.overhaul-income-form-input-group {
    padding: 20px;
    .overhaul-income-form-input-group-label {
        font-weight: 700;
        font-size: 12px;
        line-height: 1.5em;
        letter-spacing: -0.02em;

        color: var(--color-grey);

        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;

        .overhaul-income-form-input-group-label-primary {
            font-weight: 400;
            font-size: 14px;
        }
    }

    .text-input {
        margin-bottom: -12px;
        &:has(.text-input-icon) {
            margin-left: -7px;
        }
    }
}
</style>

