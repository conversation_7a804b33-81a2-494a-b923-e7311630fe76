import OverhaulModalRelationshipRow from './OverhaulModalRelationshipRow.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof OverhaulModalRelationshipRow> = {
    component: OverhaulModalRelationshipRow,
    tags: ['autodocs'],

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof OverhaulModalRelationshipRow>;

export const Default: Story = {
    parameters: {
        
    },
    args: {
        title: '1099',
        description: 'Please enter all 1099s from this employer'
    }
};
