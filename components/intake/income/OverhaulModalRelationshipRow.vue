<template>
    <div class="overhaul-modal-relationship-row">
        <div class="overhaul-modal-relationship-row-text">
            <div class="overhaul-modal-relationship-row-title">
                <h5 v-text="title"></h5>
            </div>
            <p class="overhaul-modal-relationship-row-description" v-text="description"></p>
            <dl class="overhaul-modal-relationship-row-stat" v-for="stat in stats">
                <dt v-html="stat.label"></dt>
                <dd v-html="stat.value"></dd>
            </dl>
        </div>
        <div class="overhaul-modal-relationship-row-arrow" v-if="hasArrow">
            <SvgIcon shape="keyboard-arrow-right" width="24" height="24" />
        </div>
    </div>
</template>

<script setup lang="ts">
import SvgIcon from '../../images/SvgIcon.vue';

/** A static row displaying an item's relationship on the right column of the OverhaulModal */
defineOptions();

export interface OverhaulModalRelationshipRowProps {
    title: string;
    description?: string;
    stats?: Array<{ label: string; value: string }>;
    hasArrow?: boolean;
}
const props = withDefaults(defineProps<OverhaulModalRelationshipRowProps>(), {
    hasArrow: true,
});
</script>

<style lang="scss">
@import './OverhaulModalRelationshipRow';
</style>
