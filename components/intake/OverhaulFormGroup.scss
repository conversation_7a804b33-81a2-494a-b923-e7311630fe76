@import "/assets/css/mixins";

.overhaul-form-group {
    .overhaul-form-group-existing {
        display: flex;
        gap: 10px;
        background: rgba(var(--color-white-rgb), 0.05);
        padding: 20px;
        position: relative;
        transition: background .2s ease-in-out;
        align-items: stretch;
        flex-direction: column;
        justify-content: flex-start;

        &:hover {
            background: rgba(var(--color-white-rgb), 0.1);
        }

        &.overhaul-form-group-existing-collapsed {
            &:hover {
                background: rgba(var(--color-white-rgb), 0.1);
                cursor: pointer;
            }
        }

        &.overhaul-form-group-existing-expanded {
            background: rgba(var(--color-white-rgb), 0.1);
        }
    }

    .overhaul-form-group-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        h5 {
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 1.45em;
            letter-spacing: -0.02em;

            color: var(--color-grey);
        }
    }

    .overhaul-form-group-item-line {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;

        color: var(--color-white);

        dt {
            color: var(--color-white);
        }

        dd {
            font-weight: 800;
            color: var(--color-white);
        }
    }

    .overhaul-form-group-toggle-overlay {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        cursor: pointer;
    }

    .overhaul-form-group-expanded-item {
        width: calc(100% + 40px);
        display: flex;
        align-items: stretch;
        flex-direction: column;
        gap: 10px;
        border-top: 1px solid rgba(var(--color-white-rgb), 0.1);
        padding: 20px;
        margin-left: -20px;
        margin-right: -20px;
    }

    .overhaul-form-group-title + .overhaul-form-group-expanded-item {
        margin-top: 10px;
    }

    .overhaul-form-group-expanded-item-stat {
        display: flex;
        align-items: center;
        justify-content: space-between;

        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;

        color: var(--color-white);

        dt {
            color: var(--color-grey);
        }

        dd {
            font-weight: 800;
            color: var(--color-white);
        }
    }

    .overhaul-form-group-expanded-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
        padding: 20px 20px 0;
        margin-left: -20px;
        margin-right: -20px;
        border-top: 1px solid rgba(var(--color-white-rgb), 0.1)
    }

    .overhaul-form-group-expanded-placeholder {
        padding: 0 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        color: var(--color-blue-grey);
    }

    .overhaul-form-group-expanded-empty {
        margin: 0 20px 5px;
        padding: 20px;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        font-style: italic;

        color: var(--color-blue-grey);
        background: rgba(var(--color-white-rgb), 0.05);
    }
}