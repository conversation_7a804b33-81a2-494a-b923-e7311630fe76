<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="overhaul-add-object-modal">
        <template #header>
            <div class="overhaul-add-object-modal-header">
                <div class="overhaul-add-object-modal-header-title">
                    <h4 v-if="title" v-html="title"></h4>
                </div>

                <div class="overhaul-add-object-modal-header-actions">
                    <Button
                        tabindex="-1"
                        @click="handleModalClose"
                        variant="muted"
                    >
                        Cancel
                    </Button>

                    <Button
                        tabindex="-1"
                        v-text="addButtonText"
                        @click.prevent="handleConfirmSelection"
                    />
                </div>
            </div>
        </template>
        <div class="overhaul-add-object-modal-grid">            
            <div class="overhaul-add-object-modal-grid-item" v-for="elementType in selectableElements" :key="elementType.kind">
                <ElementsIconSelector
                    v-bind="elementType"
                    v-model="elementType.selected"
                    @popover-save="(options) => handleIconSelectorSave(elementType.name, options)"
                    />
            </div>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, onMounted } from 'vue';
import type { Ref } from 'vue';
import Button from '../elements/Button.vue';
import Modal from '../modals/Modal.vue';
import ModalHeaderRow from '../modals/ModalHeaderRow.vue';
import { capitalize } from '../../utils';
import { allAssetTypes, allExpenseTypes, allIncomeTypes, allInsuranceTypes, allLiabilityTypes, transformSituationTypesForCard } from '../../components/situations/data/intake-data';
import type { TEntryMode, TOverhaulCategory } from '../../stores/overhaul';
import { useOverhaulStore } from '../../stores/overhaul';

/** The modal for adding a situation after the intitial onboarding screen */
defineOptions();

export interface OverhaulAddObjectModalProps {
    category: TOverhaulCategory;
    isOpen: boolean;
    entryMode?: TEntryMode|null;
}
const props = defineProps<OverhaulAddObjectModalProps>();

const emit = defineEmits<{
    close: []
    open: []
    confirm: []
}>()

const overhaulStore = useOverhaulStore();

const situationTypesOfKind = computed(() => {
    switch(props.category) {
        case 'assets':
            return transformSituationTypesForCard(allAssetTypes);
        case 'liabilities':
            return transformSituationTypesForCard(allLiabilityTypes);
        case 'income':
            return transformSituationTypesForCard(allIncomeTypes);
        case 'expenses':
            return transformSituationTypesForCard(allExpenseTypes);
        case 'insurance':
            return transformSituationTypesForCard(allInsuranceTypes);
        default:
            return [];
    }
});

const allTypesOfKind = reactive(situationTypesOfKind.value);
const selectableElements = ref(situationTypesOfKind.value);

onMounted(() => {
    // initSync();
})

function handleIconSelectorSave(selector: string, options: string[]) {
    const situation = allTypesOfKind.find(o => o.name === selector);
    if (situation) {
        situation.selected = options.length > 0;
        situation.subOptions?.options.forEach((option) => {
            option.value = options.includes(option.name);
        });
    }
}

function handleConfirmSelection() {
    const selectedKinds = selectableElements.value?.filter(e => e.selected);

    // For each selected kind, add a new item of that kind
    selectedKinds?.forEach((kindData) => {
        const selectedSubOptions = kindData.subOptions?.options.filter(e => e.value);
        if (selectedSubOptions?.length) {
            selectedSubOptions.forEach(subOption => {
                overhaulStore.addItem(props.category, kindData.kind, 'Standard', subOption.name);
            });
        } else {
            overhaulStore.addItem(props.category, kindData.kind, 'Standard');
        }
    });

    // Reset all selected values
    selectableElements.value.forEach((element) => {
        element.selected = false;
        if (element.subOptions) {
            element.subOptions.options.forEach((option) => {
                option.value = false;
            });
        }
    });

    handleModalClose();
}

function handleModalClose() {
    emit('close');
}
function handleModalOpen() {
    emit('open');
}

const title = computed(() => 'Add ' + props.category);

const addButtonText = computed(() => 'Confirm & Add');
</script>

<style lang="scss">
@import "./OverhaulAddObjectModal";
</style>
