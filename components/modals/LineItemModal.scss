@import "/assets/css/mixins";

.line-item-modal {

    .modal-dialog-panel {
        height: 100%;
    }

    .modal-header {
        padding: 0;
        height: 260px;
    }

    .modal-content {
        padding-left: 20px;
        padding-right: 20px;
    }

    .line-item-modal-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        // background-color: #0D182A;
        // box-shadow: 3px 3px 10px 0px rgba(0, 0, 0, 0.5);
    }

    .line-item-modal-header {
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        justify-content: flex-end;
        align-content: flex-end;
        align-items: flex-start;
        padding: 20px;
        width: 600px;
        height: 200px;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.50) 100%);
        position: relative;

        &-icon {
            position: absolute;
            top: 20px;
            left: 10px;
            color: #57719C;
        }

        &-name {
            color: #E9EDF1;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
        }

        &-item {
            color: white;
            font-size: 24px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
            padding-top: 10px;
        }
    }

    .line-item-modal-body {
        display: flex;
        flex-direction: column;
        position: relative;

        .line-item-modal-body-content {
            display: flex;
            gap:3px;
        }

        &-nav {
            display: flex;
            flex-direction: row;
            padding: 0 20px;

            &-link {
                padding: 20px 20px;
                color: #57719C;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
                cursor: pointer;

                &:hover {
                    color: #8BA9D0;
                }

                &.active {
                    color: #E9EDF1; /* Active tab color */
                }
            }
        }

        &-right {
            display: flex;
            flex-direction: row;
            align-items: center;
            // position: absolute;
            // right: 10px;
            // top: 20px;
            margin-left: auto;
            color: #97ABCC;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 24px;
            word-wrap: break-word;
            cursor: pointer;
            gap: 10px;

            &:hover {
                color: #8BA9D0;
            }

            &-icon {
                position: absolute;
                right: 10px;
                color: #57719C;
            }
        }
        &-content {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        &-section {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        &-kpi {
            display: flex;
            flex-direction: row;
            gap: 10px;
            width: 100%;

            &-entity {
                display: flex;
                flex-direction: column;
                background-color: var(--color-dark-grey);
                border-radius: 4px;
                padding: 20px;
                width: 100%;

                &-name {
                    color: #57719C;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 20px;
                    letter-spacing: 1.2px;
                    word-wrap: break-word;
                    text-transform: uppercase;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    gap: 5px;
                }

                .line-item-modal-body-kpi-entity-color {
                    display: block;
                    width: 10px;
                    height: 10px;
                    border-radius: 2px;
                    background: var(--entity-color);
                    --entity-color: var(--color-blue);
                    &.entity-color-blue {
                        --entity-color: var(--color-blue);
                    }
                    &.entity-color-orange {
                        --entity-color: var(--color-orange);
                    }
                    &.entity-color-green {
                        --entity-color: var(--color-green);
                    }
                }

                &-value {
                    color: white;
                    font-size: 18px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;  
                    padding-top: 10px;
                }

                &-date {
                    color: #57719C;
                    font-size: 12px;
                    font-family: 'Oxygen', sans-serif;
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                    padding-top: 20px;
                }
            }
        }

        &-chart {
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            align-items: center;
            justify-content: center;
            width: 560px;
            height: 200px;
            padding: 10px 10px 0;
            background-color: var(--color-dark-grey);
            border-radius: 4px;
            margin-top: 10px;

            .line-chart {
                width: 100%;
                height: 100%;
            }

            // &-title {
            //     color: white;
            //     font-size: 12px;
            //     font-family: 'Oxygen', sans-serif;
            //     font-weight: 700;
            //     line-height: 20px;
            //     word-wrap: break-word;
            // }

            // &-description {
            //     color: white;
            //     font-size: 12px;
            //     font-family: 'Oxygen', sans-serif;
            //     font-weight: 400;
            //     line-height: 20px;
            //     word-wrap: break-word;
            // }
        }

        &-details {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: rgba(255, 255, 255, 0.05);
            cursor: pointer;

            &-editing {
                display: flex;
                flex-direction: column;
                padding: 20px;
                cursor: pointer;
                background-color: rgba(255, 255, 255, 0.10);
            }

            &-title {
                color: #97ABCC;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-description {
                color: #57719C;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-content {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                line-height: 20px;
                word-wrap: break-word;
            }
        }

        &-transactions {
            display: flex;
            flex-direction: column;
            padding: 20px;
            background-color: #19263C;

            &-title {
                color: #E9EDF1;
                font-size: 14px;
                font-family: 'Oxygen', sans-serif;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-list {
                list-style: none;
                padding: 0;
            }

            &-item {
                color: white;
                font-size: 12px;
                font-family: 'Oxygen', sans-serif;
                line-height: 20px;
                word-wrap: break-word;
                padding: 5px 0;
            }
        }
    }

    .line-item-modal-footer {
        display: flex;
        flex-direction: row;
        justify-content: center;
        padding: 20px;
        color: #97ABCC;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 24px;
        word-wrap: break-word;
        
        svg,span {
            cursor: pointer;

            &:hover {
                color: #8BA9D0;
            }
        }
    }

    .line-item-modal-body-details-edit {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .line-item-modal-body-details-input {
        color: white;
    }

    .line-item-modal-body-details-buttons {
        display: flex;
        flex-direction: row;
        gap: 10px;
        padding: 20px;
        border-top: 1px solid rgba(233, 237, 241, 0.10);
        background-color: rgba(255, 255, 255, 0.10);
        justify-content: space-between;
        align-items: center;

        &-actions {
            display: flex;
            gap: 10px;
        }

        &-time {
            color: #97ABCC;
            font-size: 12px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 12px;
            word-wrap: break-word;
        }
    }

    .line-item-modal-body-details-button {
        background-color: #57719C;
        border: none;
        color: white;
        padding: 10px;
        font-size: 14px;
        cursor: pointer;
        border-radius: 4px;
        text-align: center;
    }

    .line-item-modal-body-details-button:hover {
        background-color: #8BA9D0;
    }
}
