<template>
    <div class="invite-user-modal">
        <div class="invite-user-modal-header">
            <span>Invite user</span>
            <button class="popover-modal-close" @click.prevent="handleCancel">
                <SvgIcon class="popover-modal-close-icon" shape="close" width="20" height="20" />
            </button>
        </div>
        <div class="invite-user-modal-form">
            <div class="invite-user-modal-form-primary">
                <label class="invite-user-modal-form-label">Account Primary</label>
                <div class="invite-user-modal-form-separator">
                    <TextInput class="invite-user-modal-form-input-lg"  size="sm" label="First name" v-model="primary.firstName" />
                    <TextInput class="invite-user-modal-form-input-md" size="sm" label="Last name" v-model="primary.lastName" />
                </div>
                <div class="invite-user-modal-form-separator">
                    <TextInput class="invite-user-modal-form-input-xl"  size="sm" label="Email" v-model="primary.email" />
                    <TextInput class="invite-user-modal-form-input-sm"  size="sm" label="Phone (optional)" v-model="primary.phone" />
                </div>
            </div>
<!--            <div class="invite-user-modal-form-secondary">-->
<!--                <label class="invite-user-modal-form-label">Account Secondary (optional)</label>-->
<!--                <div class="invite-user-modal-form-separator">-->
<!--                    <TextInput class="invite-user-modal-form-input-lg"  size="sm" label="First name" v-model="secondary.firstName" />-->
<!--                    <TextInput class="invite-user-modal-form-input-md"  size="sm" label="Last name" v-model="secondary.lastName" />-->
<!--                </div>-->
<!--                <div class="invite-user-modal-form-separator">-->
<!--                    <TextInput class="invite-user-modal-form-input-xl"  size="sm" label="Email" v-model="secondary.email" />-->
<!--                    <TextInput class="invite-user-modal-form-input-sm"  size="sm" label="Phone (optional)" v-model="secondary.phone" />-->
<!--                </div>-->
<!--            </div>-->
            <div class="invite-user-modal-actions">
                <Button class="invite-user-modal-actions-button" variant="muted" @click="handleCancel">Cancel</Button>
                <Button class="invite-user-modal-actions-button" variant="default" @click="sendInvite">Send Invite</Button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref } from 'vue';
import Button from '../elements/Button.vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Ref } from 'vue';
import TextInput from './../forms/TextInput.vue';

/** A modal for advisors to invite new users to the system */

const primary = reactive({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
})

const secondary = reactive({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
})

export interface UserInvite {
    primary: {
        firstName: string,
        lastName: string,
        email: string,
        phone: string
    },
    secondary: {
        firstName: string,
        lastName: string,
        email: string,
        phone: string
    }
}

const emit = defineEmits<{
    close: [], 
    send: [UserInvite]
}>()

const handleCancel = () => {
    console.log('Cancel clicked');
    emit('close');
};

const sendInvite = () => {
    console.log('Invite sent:', { primary, secondary });
    emit('send', {
      primary: primary,
      secondary: secondary
    })
};

</script>

<style lang="scss">
.invite-user-modal {
    display: flex;
    flex-direction: column;
    padding: 20px;
    gap: 20px;
    border-radius: 10px;
}

.invite-user-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #ffffff;
    font-size: 18px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
}

.invite-user-modal-form-primary {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: #252f3f;
    padding: 20px;
    min-width: 520px;
}

.invite-user-modal-form-secondary {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: #192435;
    padding: 20px;
}

.invite-user-modal-form-label {
    color: #97ABCC;
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}


.invite-user-modal-form-separator {
    display: flex;
    flex-direction: row;
    gap: 10px;
}

.popover-modal-close-icon {
 fill: #7b828d;

}

.invite-user-modal-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
    padding-top: 20px;
}

.invite-user-modal-actions-button {
    width: 100%;
}

.invite-user-modal-form-input-xl {
    width: 100%;
}

.invite-user-modal-form-input-lg {
    flex-shrink: 1;
    width: 100%;
}

.invite-user-modal-form-input-md {
    flex-shrink: 1;
    width: 100%;
}
</style>
