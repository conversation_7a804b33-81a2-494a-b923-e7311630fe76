@import "/assets/css/mixins";

.dropdown-menu {
    .dropdown-menu-button {
        color: var(--color-grey);
    }

    .dropdown-menu-content {
        background: var(--color-dark-grey);
        border-radius: 4px;
        width: auto;
        min-width: 210px;

        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        padding: 10px 0;
        gap: 0;
    }

    .popover-modal-content {
        width: auto;
    }

    .popover-modal-dialog .popover-modal-outer-wrapper {
        transform-origin: right top;
        box-shadow: 0 0 12px 2px rgba(0, 0, 0, 0.15);
        left: auto;
        right: 0;
    }
}
