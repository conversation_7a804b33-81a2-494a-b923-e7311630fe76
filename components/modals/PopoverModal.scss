.popover-modal {
    position: relative;
    z-index: 1000;
}
.popover-modal-dialog {
    --popover-modal-panel-duration-in: .2s;
    --popover-modal-panel-duration-out: .15s;

    .popover-modal-overlay {
        position: fixed;
        inset: 0;

        background: rgba(var(--color-black-rgb), .5);
        backdrop-filter: blur(5px);

        --popover-modal-overlay-transition-duration: 0s;
        --popover-modal-overlay-transition-function: ease;
        transition: opacity var(--popover-modal-overlay-transition-duration) var(--popover-modal-overlay-transition-function);
        
        &.popover-modal-overlay-enter, &.popover-modal-overlay-enter-active {
            --popover-modal-overlay-transition-duration: var(--popover-modal-panel-duration-in);
            --popover-modal-overlay-transition-function: ease-out;
        }
        &.popover-modal-overlay-leave, &.popover-modal-overlay-leave-active {
            --popover-modal-overlay-transition-duration: var(--popover-modal-panel-duration-out);
            --popover-modal-overlay-transition-function: ease-in;
        }
        &.popover-modal-overlay-enter-from, &.popover-modal-overlay-leave-to {
            opacity: 0;
        }
        &.popover-modal-overlay-enter-to, &.popover-modal-overlay-leave-from {
            opacity: 100;
        }
    }

    .popover-modal-outer-wrapper {
        position:fixed;
        inset: 0;
        z-index: 10;
        width: 100dvw;
        overflow-y: auto;
        transform-origin: center center;
        // opacity: .5;

        --popover-modal-outer-wrapper-transition-duration: 0s;
        --popover-modal-outer-wrapper-transition-function: ease;
        transition-duration: var(--popover-modal-outer-wrapper-transition-duration);
        transition-timing-function: var(--popover-modal-outer-wrapper-transition-function);

        &.popover-modal-outer-wrapper-enter, &.popover-modal-outer-wrapper-enter-active {
            transition-property: opacity, visibility, transform;
            --popover-modal-outer-wrapper-transition-duration: var(--popover-modal-panel-duration-in);
            --popover-modal-outer-wrapper-transition-function: ease-out;
        }
        &.popover-modal-outer-wrapper-leave, &.popover-modal-outer-wrapper-leave-active {
            transition-property: opacity, visibility, transform;
            --popover-modal-outer-wrapper-transition-duration: var(--popover-modal-panel-duration-out);
            --popover-modal-outer-wrapper-transition-function: ease-in;
        }
        &.popover-modal-outer-wrapper-enter-from, &.popover-modal-outer-wrapper-leave-to {
            opacity:0;
            visibility: hidden;
            transform: scale(.95);
        }
        &.popover-modal-outer-wrapper-enter-to, &.popover-modal-outer-wrapper-leave-from {
            opacity: 1;
            visibility: visible;
            transform: scale(1);
        }
    }

    .popover-modal-inner-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100%;
    }

    .popover-modal-panel {
        position: relative;
        overflow:hidden;
        background: var(--color-dark-grey);
        color: var(--color-white);
        border-radius: 4px;
        max-height: 100dvh;
    }

    &.popover-modal-static {
        .popover-modal-outer-wrapper {
            position: absolute;
            z-index: 10;
            width: auto;
            inset: unset;
            top: 5px;
            left: 0;
        }
    }
}
