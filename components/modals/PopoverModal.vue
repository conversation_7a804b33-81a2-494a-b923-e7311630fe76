<template>
    <div class="popover-modal">
        <div class="popover-modal-dialog" @close="handleClose" :class="dialogClassObj">
            <Transition name="popover-modal-overlay">
                <div class="popover-modal-overlay" v-if="withOverlay && isOpen" />
            </Transition>

            <Transition name="popover-modal-outer-wrapper">
                <div class="popover-modal-outer-wrapper" v-if="isOpen">
                    <div class="popover-modal-inner-wrapper">
                        <div class="popover-modal-panel" ref="panel" :style="panelElementStyles">
                            <slot />
                        </div>
                    </div>
                </div>
            </Transition>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, ref, useTemplateRef, watch } from 'vue';
import type { Ref } from 'vue';
import { useFloating, autoUpdate, autoPlacement, flip, offset, shift, size } from '@floating-ui/vue';
import type { UseFloatingOptions } from '@floating-ui/vue';
import { onClickOutside, onKeyStroke } from '@vueuse/core'

/** A minimal popover modal that attaches to the element that triggered it */
defineOptions();

export interface PopoverModalProps {
    isOpen: boolean;
    triggerElement?: Ref<HTMLElement>|string;
    withOverlay?: boolean;
    dynamicPlacement?: boolean;
}
const props = withDefaults(defineProps<PopoverModalProps>(), {
    isOpen: true,
    withOverlay: true,
    dynamicPlacement: true
})

const panelElement = useTemplateRef('panel');
const triggerRef = ref();

const floatingOptions = computed<UseFloatingOptions<any>>(() => (props.dynamicPlacement) ? 
    {
        strategy: 'fixed',
        placement: 'right-end',
        transform: false,
        middleware: [
            autoPlacement(),
            size(),
            shift()
        ]
    }
    : {}
)
const { floatingStyles } = useFloating(
    triggerRef,
    panelElement,
    floatingOptions.value
);

const panelElementStyles = computed(() => (props.dynamicPlacement)
    ? floatingStyles.value
    : ''
);

onKeyStroke('Escape', (e) => { if(props.isOpen) handleClose() })
onClickOutside(panelElement, () => { handleClose() });

onMounted(() => {
    triggerRef.value = typeof props.triggerElement === 'string' ? document.getElementById(props.triggerElement) : props.triggerElement;
});

const dialogClassObj = computed(() => [
    `popover-modal-${props.dynamicPlacement ? 'dynamic' : 'static'}`
])

const emit = defineEmits<{
    close: []
}>()

function handleClose() {
    emit('close');
}
</script>

<style lang="scss">
@import './PopoverModal';
</style>

