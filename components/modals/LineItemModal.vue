<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="line-item-modal">
        <template #header>
            <div class="line-item-modal-header">
                <SvgIcon class="line-item-modal-header-icon" :shape="iconShape" width="100" height="100" />
                <span class="line-item-modal-header-name" v-text="typeLabel"></span>
                <span class="line-item-modal-header-item" v-text="nameLabel"></span>
            </div>
            <div class="line-item-modal-body-nav">
                <span 
                    class="line-item-modal-body-nav-link" 
                    :class="{ active: activeTab === 'overview' }" 
                    @click="setActiveTab('overview')"
                >
                    Overview
                </span>
                <span 
                    class="line-item-modal-body-nav-link" 
                    :class="{ active: activeTab === 'details' }" 
                    @click="setActiveTab('details')"
                >
                    Details
                </span>
                <!-- <div class="line-item-modal-body-right">
                    <SvgIcon class="line-item-modal-body-right-svg" :shape="'camera'" width="20" height="20" />
                    <span class="line-item-modal-body-right-text">Edit asset image</span>
                </div> -->
            </div>
        </template>

        <div class="line-item-modal-body">
            <div class="line-item-modal-body-section" v-if="activeTab === 'overview'">
                <div class="line-item-modal-body-kpi">
                    <div class="line-item-modal-body-kpi-entity" v-for="(kpi, index) in kpis">
                        <span class="line-item-modal-body-kpi-entity-name">
                            {{ kpi.label }}
                            <span class="line-item-modal-body-kpi-entity-color" :class="'entity-color-' + graphColors[index % 3]"></span>
                        </span>
                        <span class="line-item-modal-body-kpi-entity-value" v-text="abbreviateNumber(kpi.value, true)"></span>
                        <span class="line-item-modal-body-kpi-entity-date">as of Sep 01, 2024</span>
                    </div>
                </div>
                <div class="line-item-modal-body-chart">
                    <LineChart
                        v-bind="dummyChartData"
                    />
                </div>
            </div>

            <div class="line-item-modal-body-content" v-if="activeTab === 'details'">
                <slot />
            </div>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import TextInput from '../forms/TextInput.vue';
import Modal from './Modal.vue';
import SvgIcon from '../images/SvgIcon.vue';
import Button from '../elements/Button.vue';
import LineChart from '../dashboard/LineChart.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import { abbreviateNumber } from '../../utils';

export interface AddObjectModalProps {
    isOpen?: boolean;
    kpis?: { label: string; value: string }[];
    iconShape?: TBigIconShapeKey
    typeLabel: string;
    nameLabel: string;
}
const props = withDefaults(defineProps<AddObjectModalProps>(), {
    isOpen: false,
    iconShape: 'info'
});

const emit = defineEmits<{
    close: []
    open: []
    update: []
}>()

const graphColors = [
    'blue',
    'orange',
    'green'
]

// Manage active tab state
const activeTab = ref<'overview' | 'details' | 'transactions'>('overview');

// Manage details section
const items = ref(
    [
        { label: 'Description', description: 'Description of property/item', editDescription: 'Description of property/item', isEditing: false, required: true },
        { label: 'Address', description: 'The address of property', editDescription: 'The address of property', isEditing: false, required: true },
        { label: 'Estimated value', description: 'The estimated or appraised value of the item/property', editDescription: 'The estimated or appraised value of the item/property', isEditing: false, required: true },
        { label: 'Owner', description: 'The individual or entity that owns the property/item', editDescription: 'The individual or entity that owns the property/item', isEditing: false, required: false },
        { label: 'Income', description: 'All income generated by this property/item', editDescription: 'All income generated by this property/item', isEditing: false, required: false },
        { label: 'Liabilities', description: 'All debt associated with this property/item', editDescription: 'All debt associated with this property/item', isEditing: false, required: false },
        { label: 'Expenses', description: 'All expenses and taxes associated with this property/item', editDescription: 'All expenses and taxes associated with this property/item', isEditing: false, required: false }
    ]
);

const dummyChartData = computed(() => ({
    min: 0,
    max: 100,
    showPoints: true,
    yLabelFormat: 'currency',
    data: {
        labels: ['Sep \'23', 'Jan \'24', 'Apr \'24', 'Sep \'24'],
        datasets: [
            {
                data: [3546, 4431, 1326, 3213],
            },
            {
                data: [1254, 1654, 1284, 1234],
            },
            {
                data: [2292, 2777, 42, 1979],
            }
        ]
    }
}))

function setActiveTab(tab: 'overview' | 'details' | 'transactions') {
    activeTab.value = tab;
}

function toggleEditMode(index: number) {
    console.log('Toggling edit mode for index:', index);
    items.value[index].isEditing = !items.value[index].isEditing;
}

function saveEdit(index: number) {
    console.log('Saving edit for index:', index);
    items.value[index].description = items.value[index].editDescription;
    items.value[index].isEditing = false;
}

function cancelEdit(index: number) {
    console.log('Cancelling edit for index:', index);
    items.value[index].isEditing = false;
}

function handleModalClose() {
    emit('close');
}
function handleModalOpen() {
    emit('open');
}
function handleSave() {
    emit('update');
}
</script>

<style lang="scss">
@import "./LineItemModal.scss";
</style>
