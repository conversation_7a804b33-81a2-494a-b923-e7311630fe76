@import '/assets/css/mixins';

[id^='headlessui-dialog-panel'].modal-dialog-panel {
    border-radius: 20px;
    //overflow: hidden;
}

.post-onboarding-line-item-modal {
    display: flex;
    flex-direction: column;
    width: 453px;
    gap: 20px;

    .modal-header {
        padding: 20px;
        height: auto;
    }

    & .modal-content {
        display: flex;
        flex-direction: column;
        padding: 20px 0;
        padding-bottom: 0;
        width: 453px;
        background: linear-gradient(180deg, #1d293c 0%, #060b13 30%);
        border-radius: 20px;
    }

    &-header {
        display: flex;
        justify-content: space-between;

        &-left {
            display: flex;
            flex-direction: column;
            gap: 10px;

            &-top {
                color: #e9edf1;
                font-size: 14px;
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-bottom {
                color: #57719c;
                font-size: 12px;
                font-weight: 400;
                line-height: 14px;
                word-wrap: break-word;
            }
        }

        &-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;

            &-top {
                color: white;
                font-size: 20px;
                font-family: 'Oxygen';
                font-weight: 700;
                line-height: 1em;
                word-wrap: break-word;
            }

            &-bottom {
                color: #57719c;
                font-size: 12px;
                font-family: 'Oxygen';
                font-weight: 400;
                line-height: 14px;
                word-wrap: break-word;
            }
        }
    }

    &-background {
        padding: 0 20px;
        background-color: #060b13;
    }

    &-graph {
        height: 184px;
    }

    &-navigation {
        margin: 0 -20px;
        padding: 10px 20px;
        background-color: #060b13;

        * {
            outline: none !important;
            box-shadow: none !important;
        }

        button,
        [role='tab'] {
            &:focus,
            &:focus-visible {
                outline: none !important;
                box-shadow: none !important;
            }
        }
    }

    &-information {
        margin: 0 -20px;
        // border-radius: 20px;
        background-color: #19263c;
        border-bottom-left-radius: 20px;
        border-bottom-right-radius: 20px;
    }

    &-overview-deposits {
        display: flex;
        flex-direction: column;

        &-header {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 10px;

            &-label {
                color: var(--secondary-grey, #97abcc);
                font-size: 14px;
                font-family: 'Oxygen';
                font-weight: 700;
                line-height: 20px;
                word-wrap: break-word;
            }

            &-action {
                color: var(--secondary-grey, #97abcc);
                font-size: 14px;
                font-family: 'Oxygen';
                font-weight: 400;
                line-height: 24px;
                word-wrap: break-word;
                cursor: pointer;
                transition: opacity 0.2s ease;
                display: flex;
                align-items: center;
                gap: 4px;

                .deposits-action-icon {
                    width: 20px;
                    height: 20px;
                    color: #97abcc;
                }

                &:hover {
                    opacity: 0.8;
                }
            }
        }

        &-list {
            display: flex;
            flex-direction: column;
            padding: 0 10px;
            gap: 10px;

            &-row {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: center;

                &-date {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                &-amount {
                    color: var(--secondary-grey, #97abcc);
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                }
            }
        }
    }

    &-overview {
        display: flex;
        padding: 10px;
        flex-direction: column;

        &-deposit {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            background-color: #57719c;
            margin: 10 -10px;
            border-radius: 10px;
            padding: 10px;

            &-left {
                display: flex;
                flex-direction: column;
                gap: 10px;

                &-top {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                &-bottom {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                }
            }

            &-right {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 10px;

                &-top {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                &-bottom {
                    color: rgba(255, 255, 255, 0.5);
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                    cursor: pointer;
                }
            }
        }

        &-deposits {
            margin: 10px -10px 0;
            padding: 20px 10px;
            border-top: 1px solid rgba(233, 237, 241, 0.1);
        }
    }

    &-edit {
        display: flex;
        border-top-left-radius:20px;
        border-top-right-radius:20px;
        overflow: hidden;
        flex-direction: column;
    }

    &-history {
        display: flex;
        padding: 20px;
        flex-direction: column;
        gap: 20px;

        &-filter {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            cursor: pointer;

            .filter-icon {
                width: 20px;
                height: 20px;
            }

            &-label {
                color: #97abcc;
                font-size: 14px;
                font-family: 'Oxygen';
                font-weight: 400;
                line-height: 20px;
                word-wrap: break-word;
                display: flex;
                align-items: center;
            }
        }

        &-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-height: 300px;
            overflow-y: auto;
            padding-right: 5px;

            &::-webkit-scrollbar {
                width: 4px;
                margin-left: 5px;
            }

            &::-webkit-scrollbar-track {
                background: transparent;
                margin-left: 5px;
            }

            &::-webkit-scrollbar-thumb {
                background: #d9d9d9;
                border-radius: 5px;
                opacity: 0.3;
                margin-left: 5px;
            }

            &::-webkit-scrollbar-thumb:hover {
                background: #d9d9d9;
                opacity: 0.5;
                margin-left: 5px;
            }

            &-row {
                display: flex;
                flex-direction: row;
                justify-content: space-between;
                align-items: flex-start;
                padding: -10px -20px;
                background-color: #19263c;
                border-radius: 10px;
                cursor: pointer;

                &.is-editing {
                    background-color: #243047;
                    cursor: default;
                }

                &-edit {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    gap: 10px;

                    &-inputs {
                        display: flex;
                        flex-direction: row;
                        justify-content: space-between;
                        align-items: center;
                        width: 100%;
                        padding: 20px;
                    }

                    &-date {
                        color: white;
                        font-size: 14px;
                        font-family: 'Oxygen';
                        font-weight: 400;
                        line-height: 20px;
                    }

                    input {
                        background-color: transparent;
                        border: none;
                        color: white;
                        font-size: 14px;
                        font-family: 'Oxygen';
                        font-weight: 400;
                        line-height: 20px;
                        width: 100px;
                        text-align: right;
                        padding: 4px;
                        border-bottom: 1px solid #57719c;

                        &:focus {
                            outline: none;
                            border-bottom-color: #97abcc;
                        }
                    }

                    &-actions {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 20px;
                        border-top: 1px solid rgba(233, 237, 241, 0.1);

                        .trash-icon {
                            width: 20px;
                            height: 20px;
                            color: #57719c;
                            cursor: pointer;
                            transition: color 0.2s ease;

                            &:hover {
                                color: #97abcc;
                            }
                        }

                        .buttons {
                            display: flex;
                            gap: 10px;
                        }

                        button {
                            padding: 6px 12px;
                            font-size: 12px;
                            font-family: 'Oxygen';
                            font-weight: 700;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            transition: opacity 0.2s ease;

                            &:hover {
                                opacity: 0.8;
                            }
                        }

                        .cancel-button {
                            background-color: transparent;
                            color: #97abcc;
                            text-transform: uppercase;
                        }

                        .save-button {
                            background-color: #0066ff;
                            color: white;
                            text-transform: uppercase;
                        }
                    }
                }

                &-left,
                &-right {
                    transition: color 0.2s ease;
                }

                &:hover {
                    .post-onboarding-line-item-modal-history-list-row {
                        &-subtitle,
                        &-running-total {
                            color: #97abcc;
                        }
                    }
                }

                &-left {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                }

                &-right {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    gap: 4px;
                }

                &-date {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                &-subtitle {
                    color: #57719c;
                    font-size: 12px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 14px;
                    word-wrap: break-word;
                    transition: color 0.2s ease;
                }

                &-amount {
                    color: white;
                    font-size: 14px;
                    font-family: 'Oxygen';
                    font-weight: 700;
                    line-height: 20px;
                    word-wrap: break-word;
                }

                &-running-total {
                    color: #57719c;
                    font-size: 12px;
                    font-family: 'Oxygen';
                    font-weight: 400;
                    line-height: 14px;
                    word-wrap: break-word;
                    transition: color 0.2s ease;
                }
            }
        }
    }
}

.post-onboarding-line-item-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}
