import DropdownMenuRow from './DropdownMenuRow.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof DropdownMenuRow> = {
    component: DropdownMenuRow,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,
    
    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof DropdownMenuRow>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=10940-41674&t=Jks8p58ZZemCKHp4-4',
        },
    },
    args: {
        label: 'Edit liability',
        iconShape: 'edit', 
    }
};
