<template>
    <Modal class="post-onboarding-line-item-modal" :is-open="isOpen" @close="handleClose">
        <template #header>
            <div class="post-onboarding-line-item-modal-header">
                <div class="post-onboarding-line-item-modal-header-left">
                    <div class="post-onboarding-line-item-modal-header-left-top">{{ modalLabel }}</div>
                    <div class="post-onboarding-line-item-modal-header-left-bottom">{{ itemTypeLabel }}</div>
                </div>
                <div class="post-onboarding-line-item-modal-header-right">
                    <div class="post-onboarding-line-item-modal-header-right-top">{{ itemValueLabel }}</div>
                    <div
                        class="post-onboarding-line-item-modal-header-right-bottom"
                        v-if="leftBottomLabel || rightBottomLabel"
                    >
                        {{ rightBottomLabel }}
                    </div>
                </div>
            </div>
        </template>

        <!--
            Top Graph / KPI Section
            May 2025: Not being included in v1
        -->
        <div class="post-onboarding-line-item-container" v-if="false">
            <div class="post-onboarding-line-item-modal-graph">
                <svg width="100%" height="100%" viewBox="0 0 453 184" preserveAspectRatio="none">
                    <!-- Grid lines -->
                    <g class="grid" stroke="#2A3B59" stroke-width="1" opacity="0.5">
                        <line x1="0" y1="20" x2="453" y2="20" />
                        <line x1="0" y1="61" x2="453" y2="61" />
                        <line x1="0" y1="102" x2="453" y2="102" />
                        <line x1="0" y1="143" x2="453" y2="143" />
                    </g>

                    <!-- Y-axis labels -->
                    <g class="y-axis-labels" fill="#57719C" font-size="12">
                        <text x="10" y="25">$100</text>
                        <text x="10" y="66">$60</text>
                        <text x="10" y="107">$40</text>
                        <text x="10" y="148">$20</text>
                    </g>

                    <!-- X-axis labels -->
                    <g class="x-axis-labels" fill="#57719C" font-size="12">
                        <text x="40" y="170">2020</text>
                        <text x="138" y="170">2021</text>
                        <text x="236" y="170">2022</text>
                        <text x="334" y="170">2023</text>
                        <text x="433" y="170">2024</text>
                    </g>

                    <!-- Historical data line (solid) -->
                    <path d="M40,102 L138,98 L236,95 L334,92" fill="none" stroke="#E9EDF1" stroke-width="2" />

                    <!-- Projected data line (dotted) -->
                    <path d="M334,92 L433,85" fill="none" stroke="#E9EDF1" stroke-width="2" stroke-dasharray="4,4" />
                </svg>
            </div>
        </div>

        <!--
            May 2025: v1 only includes the "Edit" content, so there are no tabs
        -->
        <!-- <div class="post-onboarding-line-item-modal-navigation">-->
        <!--     <Tabs-->
        <!--         v-model="activeTab"-->
        <!--         :tabs="[-->
        <!--             { id: 'overview', label: 'Overview' },-->
        <!--             { id: 'edit', label: 'Edit' },-->
        <!--             { id: 'history', label: 'History' },-->
        <!--         ]"-->
        <!--     />-->
        <!-- </div>-->

        <div class="post-onboarding-line-item-modal-background">
            <div class="post-onboarding-line-item-modal-information">
                <!--
                    Edit section
                -->
                <div v-if="activeTab === 'edit' && populatedItem" class="post-onboarding-line-item-modal-edit">
                    <SituationInputGroup
                        v-for="field in populatedItem.fields.filter(f => !f.hiddenInModal)"
                        v-bind="field"
                        :key="populatedItem.id + field.name"
                        @change="payload => handleItemFieldChange(field.name, payload)"
                    />

                    <PaystubGroup
                        v-if="populatedItem.kind === 'IncomeEmployee' && populatedItem.entryMode !== 'Simple'"
                        :income-item="populatedItem"
                    />

                    <OverhaulAssociatedItems v-if="populatedItem?.entryMode !== 'Simple'" :item="populatedItem" />
                </div>

                <!--
                    Overview section
                -->
                <div v-if="activeTab === 'overview' && deposit" class="post-onboarding-line-item-modal-overview">
                    <!-- Overview content -->
                    <div class="post-onboarding-line-item-modal-overview-deposit">
                        <div class="post-onboarding-line-item-modal-overview-deposit-left">
                            <div class="post-onboarding-line-item-modal-overview-deposit-left-top">
                                {{ deposit.leftTop }}
                            </div>
                            <div class="post-onboarding-line-item-modal-overview-deposit-left-bottom">
                                {{ deposit.leftBottom }}
                            </div>
                        </div>
                        <div class="post-onboarding-line-item-modal-overview-deposit-right">
                            <div class="post-onboarding-line-item-modal-overview-deposit-right-top">
                                {{ deposit.rightTop }}
                            </div>
                            <div
                                v-if="deposit.rightBottom && deposit.isEditable"
                                class="post-onboarding-line-item-modal-overview-deposit-right-bottom"
                            >
                                {{ deposit.rightBottom }}
                            </div>
                        </div>
                    </div>
                    <div class="post-onboarding-line-item-modal-overview-deposits">
                        <div class="post-onboarding-line-item-modal-overview-deposits-header">
                            <div class="post-onboarding-line-item-modal-overview-deposits-header-label">
                                {{ depositsHeaderLabel }}
                            </div>
                            <div class="post-onboarding-line-item-modal-overview-deposits-header-action">
                                {{ depositsHeaderAction }}
                                <SvgIcon shape="keyboard-arrow-right" class="deposits-action-icon" />
                            </div>
                        </div>
                        <div class="post-onboarding-line-item-modal-overview-deposits-list">
                            <div
                                v-for="(deposit, index) in recentDeposits"
                                :key="index"
                                class="post-onboarding-line-item-modal-overview-deposits-list-row"
                            >
                                <div class="post-onboarding-line-item-modal-overview-deposits-list-row-date">
                                    {{ deposit.date }}
                                </div>
                                <div class="post-onboarding-line-item-modal-overview-deposits-list-row-amount">
                                    {{ formatAmount(deposit.amount) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--
                    History section
                -->
                <div v-if="activeTab === 'history'" class="post-onboarding-line-item-modal-history">
                    <div class="post-onboarding-line-item-modal-history-filter" @click="filtersModalIsOpen = true">
                        <SvgIcon shape="filter" class="filter-icon" />
                        <span class="post-onboarding-line-item-modal-history-filter-label">Filters</span>
                    </div>
                    <div class="post-onboarding-line-item-modal-history-list">
                        <div
                            v-for="(history, index) in filteredHistoryItems"
                            :key="index"
                            class="post-onboarding-line-item-modal-history-list-row"
                            :class="{ 'is-editing': editingIndex === index }"
                            @click="handleRowClick(index)"
                        >
                            <template v-if="editingIndex === index">
                                <div class="post-onboarding-line-item-modal-history-list-row-edit">
                                    <div class="post-onboarding-line-item-modal-history-list-row-edit-inputs">
                                        <div class="post-onboarding-line-item-modal-history-list-row-edit-date">
                                            {{ history.date }}
                                        </div>
                                        <input
                                            type="text"
                                            v-model="editingAmount"
                                            class="post-onboarding-line-item-modal-history-list-row-edit-amount"
                                        />
                                    </div>
                                    <div class="post-onboarding-line-item-modal-history-list-row-edit-actions">
                                        <SvgIcon shape="trash" class="trash-icon" @click.stop="handleDelete" />
                                        <div class="buttons">
                                            <button class="cancel-button" @click.stop="handleCancel">CANCEL</button>
                                            <button class="save-button" @click.stop="handleSave">SAVE</button>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <div class="post-onboarding-line-item-modal-history-list-row-left">
                                    <div class="post-onboarding-line-item-modal-history-list-row-date">
                                        {{ history.date }}
                                    </div>
                                    <div class="post-onboarding-line-item-modal-history-list-row-subtitle">
                                        {{ history.subtitle }}
                                    </div>
                                </div>
                                <div class="post-onboarding-line-item-modal-history-list-row-right">
                                    <div class="post-onboarding-line-item-modal-history-list-row-amount">
                                        {{ formatAmount(history.amount) }}
                                    </div>
                                    <div class="post-onboarding-line-item-modal-history-list-row-running-total">
                                        {{ formatAmount(history.runningTotal) }}
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <FiltersModal
            :is-open="filtersModalIsOpen"
            :filter-groups="filterGroups"
            @close="handleFiltersClose"
            @confirm="handleFiltersConfirm"
        />
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import type { Ref } from 'vue';
import Modal from './Modal.vue';
import Tabs from '../navigation/Tabs.vue';
import SvgIcon from '../images/SvgIcon.vue';
import FiltersModal from './FiltersModal.vue';
import type { FiltersModalFilterGroup } from './FiltersModal.vue';
import type { TPopulatedItem } from '~/stores/overhaul';
import SituationInputGroup from '~/components/situations/SituationInputGroup.vue';
import OverhaulAssociatedItems from '~/components/intake/OverhaulAssociatedItems.vue';
import { getCategoryVariants } from '~/stores/overhaul';
import { abbreviateNumber } from '~/utils';
import OverhaulFormGroup from '~/components/intake/OverhaulFormGroup.vue';
import PaystubGroup from '~/components/intake/PaystubGroup.vue';

const overhaulStore = useOverhaulStore();

/** The modal for viewing and editing a portfolio item post-onboarding */
defineOptions();

interface DepositProps {
    leftTop: string;
    leftBottom: string;
    rightTop: string;
    rightBottom?: string;
    date: string;
    amount: number;
    isEditable?: boolean;
}

interface RecentDepositProps {
    date: string;
    amount: number;
}

interface HistoryItemProps {
    date: string;
    amount: number;
    subtitle: string;
    runningTotal: number;
    type: string;
}

interface PostOnboardingLineItemModalProps {
    item: string | TPopulatedItem;
    isOpen?: boolean;
    deposit?: DepositProps;
    leftTopLabel?: string;
    leftBottomLabel?: string;
    rightTopLabel?: string;
    rightBottomLabel?: string;
    depositsHeaderLabel?: string;
    depositsHeaderAction?: string;
    recentDeposits?: RecentDepositProps[];
    historyItems?: HistoryItemProps[];
}

const props = withDefaults(defineProps<PostOnboardingLineItemModalProps>(), {
    depositsHeaderLabel: 'Recent deposits',
    depositsHeaderAction: 'View all deposits',
    recentDeposits: () => [],
    historyItems: () => [],
});

const emit = defineEmits<{
    change: [id: number];
    close: [];
    'update-history': [
        {
            index: number;
            date: string;
            amount: number;
        },
    ];
}>();

const activeTab = ref('edit');

const populatedItem = computed<TPopulatedItem | undefined>(() => {
    return typeof props.item === 'string' ? overhaulStore.getItemById(props.item) : props.item;
});

const modalLabel = computed(() => {
    return populatedItem.value
        ? (populatedItem.value.description ?? 'New ' + (populatedItem.value.unit ?? 'item'))
        : 'New item';
});

const itemTypeLabel = computed(() => {
    return populatedItem.value ? populatedItem.value.category + ' / ' + populatedItem.value.kindLabel : '';
});

const itemValueAmount = computed(() => {
    switch (populatedItem.value?.category) {
        case 'Asset':
            return populatedItem.value.stats?.find(stat => stat.kind === 'asset')?.value ?? null;
        case 'Liability':
            return populatedItem.value.stats?.find(stat => stat.kind === 'liability')?.value ?? null;
        case 'Income':
            return populatedItem.value.stats?.find(stat => stat.kind === 'income')?.value ?? null;
        case 'Expense':
            return populatedItem.value.stats?.find(stat => stat.kind === 'expense')?.value ?? null;
        default:
            return null;
    }
});

const itemValueLabel = computed(() => {
    return typeof itemValueAmount.value === 'number' ? abbreviateNumber(itemValueAmount.value) : null;
});

function handleItemFieldChange(fieldName: string, payload: any) {
    // console.log(selectedItem.value?.id, fieldName, payload)
    if (!populatedItem.value) return;
    overhaulStore.updateItemField(
        getCategoryVariants(populatedItem.value.category).lowerPlural,
        populatedItem.value.id,
        fieldName,
        payload,
    );
}

/**
 * History section logic
 */
const editingIndex = ref(-1);
const editingDate = ref('');
const editingAmount = ref('');

const filtersModalIsOpen = ref(false);

const filterGroups = ref<FiltersModalFilterGroup[]>([
    {
        title: 'Date Range',
        name: 'dateRange',
        description: 'Filter by date range',
        type: 'range',
        mode: 'year',
        data: {
            min: 2000,
            max: new Date().getFullYear(),
            bottom: 2000,
            top: new Date().getFullYear(),
        },
    },
    {
        title: 'Types',
        name: 'types',
        description: 'Filter by item type',
        type: 'checkboxes',
        data: [
            {
                label: 'Asset',
                name: 'asset',
                value: 'asset',
                checked: true,
            },
            {
                label: 'Liability',
                name: 'liability',
                value: 'liability',
                checked: true,
            },
            {
                label: 'Income',
                name: 'income',
                value: 'income',
                checked: true,
            },
            {
                label: 'Expense',
                name: 'expense',
                value: 'expense',
                checked: true,
            },
        ],
    },
]);

const activeFilters = ref({
    dateRange: {
        bottom: 2000,
        top: new Date().getFullYear(),
    },
    types: ['asset', 'liability', 'income', 'expense'],
});

const filteredHistoryItems = computed(() => {
    if (!props.historyItems) return [];

    let filtered = props.historyItems;

    // Apply date range filter
    const { bottom, top } = activeFilters.value.dateRange;
    filtered = filtered.filter(item => {
        const itemYear = new Date(item.date).getFullYear();
        return itemYear >= bottom && itemYear <= top;
    });

    // Apply type filters
    const checkedTypes = activeFilters.value.types;
    if (checkedTypes.length === 0) {
        return [];
    }

    // Filter items based on selected types
    filtered = filtered.filter(item => {
        return checkedTypes.includes(item.type.toLowerCase());
    });

    return filtered;
});
/**
 * End History section logic
 */

const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
    }).format(amount);
};

function handleRowClick(index: number) {
    editingIndex.value = index;
    editingDate.value = props.historyItems[index].date;
    editingAmount.value = props.historyItems[index].amount.toString();
}

function handleCancel() {
    editingIndex.value = -1;
}

function handleClose() {
    emit('close');
}

function handleSave() {
    // Emit event with updated values
    emit('update-history', {
        index: editingIndex.value,
        date: editingDate.value,
        amount: parseFloat(editingAmount.value),
    });
    editingIndex.value = -1;
}

function handleDelete() {
    // Implement delete logic
}

function handleFiltersClose() {
    filtersModalIsOpen.value = false;
}

function handleFiltersConfirm(filters: FiltersModalFilterGroup[]) {
    // Update the filter groups for the modal
    filterGroups.value = JSON.parse(JSON.stringify(filters));

    // Update the active filters
    filters.forEach(filter => {
        if (filter.type === 'range' && filter.name === 'dateRange') {
            const { bottom, top } = filter.data;
            if (typeof bottom === 'number' && typeof top === 'number') {
                activeFilters.value.dateRange = {
                    bottom,
                    top,
                };
            }
        } else if (filter.type === 'checkboxes' && filter.name === 'types') {
            activeFilters.value.types = (filter.data as any[])
                .filter(item => item.checked)
                .map(item => item.value ?? item.name);
        }
    });

    filtersModalIsOpen.value = false;
}
</script>

<style lang="scss">
@import './PostOnboardingLineItemModal';
</style>
