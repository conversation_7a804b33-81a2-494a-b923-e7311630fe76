@import "/assets/css/mixins";

.modal {
    position: relative;
    z-index: 1000;
    color: var(--color-white);
    
    --modal-border-radius: 4px;

    .modal-overlay {
        position: fixed;
        inset: 0;
        background: rgba(var(--color-black-rgb), .8);
        backdrop-filter: blur(20px);
        z-index:999;

        transition: opacity var(--modal-overlay-duration) var(--modal-overlay-function),
                    visibility var(--modal-overlay-duration) var(--modal-overlay-function);

        &.modal-overlay-enter {
            --modal-overlay-duration: .3s;
            --modal-overlay-function: ease-out;
        }
        &.modal-overlay-leave {
            --modal-overlay-duration: .2s;
            --modal-overlay-function: ease-in;
        }
        &.modal-overlay-leave-from, &.modal-overlay-enter-to {
            opacity: 100;
            visibility: visible;
        }
        &.modal-overlay-leave-to, &.modal-overlay-enter-from {
            opacity: 0;
            visibility: hidden;
        }
    }

    .modal-dialog-wrapper {
        position: fixed;
        inset: 0;
        width: 100dvw;
        overflow-y: auto;
        z-index: 1000;
        padding: 40px;
    }
    .modal-dialog {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        // height: calc(100dvh - 80px);
    }
    .modal-dialog-panel {
        position: relative;
        overflow: hidden;
        max-height: calc(100dvh - 80px);
        // min-height: 0px;
        border-radius: var(--modal-border-radius);
        overflow-y: auto;
        // max-height: 100%;
        background: var(--color-grey-section);

        transition: opacity var(--modal-dialog-duration) var(--modal-dialog-function),
                    visibility var(--modal-dialog-duration) var(--modal-dialog-function),
                    transform var(--modal-dialog-duration) var(--modal-dialog-function);

        &.modal-dialog-panel-enter {
            --modal-dialog-duration: .3s;
            --modal-dialog-function: ease-out;
        }
        &.modal-dialog-panel-leave-to, &.modal-dialog-panel-enter-from {
            opacity: 0;
            transform: scale(.95);
            visibility: visible;
        }
        &.modal-dialog-panel-leave-from, &.modal-dialog-panel-enter-to {
            opacity: 100;
            transform: scale(1);
            visibility: hidden;
        }
        &.modal-dialog-panel-leave {
            --modal-dialog-duration: .2s;
            --modal-dialog-function: ease-in;
        }
    }

    .modal-header {
        padding: 40px 40px 30px;
        height: 120px;
        position: sticky;
        z-index: 100;
        top: 0;
        left: 0;
        width: 100%;
        background: rgba(var(--color-blue-grey), 0.8);
        backdrop-filter: blur(20px);

        border-top-left-radius: var(--modal-border-radius);
        border-top-right-radius: var(--modal-border-radius);
    }

    .modal-content {
        padding: 40px;
        // overflow-y: scroll;
        // max-height: 100%;
    }

    .modal-header {
        & + .modal-content {
            padding-top: 0;
        }
    }
}
