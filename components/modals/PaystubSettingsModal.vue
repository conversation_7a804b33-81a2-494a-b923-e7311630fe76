<template>
    <Modal class="paystub-settings-modal" :is-open="isOpen" @close="handleClose">
        <!--<div :isOpen="true" class="paystub-settings-modal">-->
        <div class="paystub-settings-modal-header">
            <span>Edit Section</span>
            <button class="close-btn" @click="$emit('close')">
                <SvgIcon shape="close" width="24" height="24" />
            </button>
        </div>

        <div class="paystub-settings-modal-body">
            <div class="paystub-settings-modal-sections">
                <section class="paystub-settings-modal-section">
                    <label class="paystub-settings-modal-section-label" for="section-name">Section name</label>
                    <div class="paystub-settings-modal-section-content">
                        <TextInput id="section-name" type="text" v-model="sectionName" size="sm" placeholder="" />
                    </div>
                </section>
                <section class="paystub-settings-modal-section">
                    <label class="paystub-settings-modal-section-label" for="section-name">Type</label>
                    <div class="paystub-settings-modal-section-content">
                        <CheckboxInput
                            size="sm"
                            align="left"
                            type="radio"
                            value="income"
                            v-model="sectionType"
                            label="Income"
                        />
                        <CheckboxInput
                            size="sm"
                            align="left"
                            type="radio"
                            value="preTax"
                            v-model="sectionType"
                            label="Pre-tax Deduction"
                        />
                        <CheckboxInput
                            size="sm"
                            align="left"
                            type="radio"
                            value="postTax"
                            v-model="sectionType"
                            label="Post-tax Deduction"
                        />
                        <CheckboxInput
                            size="sm"
                            align="left"
                            type="radio"
                            value="tax"
                            v-model="sectionType"
                            label="Taxes"
                        />
                    </div>
                </section>
                <section class="paystub-settings-modal-section">
                    <label class="paystub-settings-modal-section-label" for="section-name">Columns</label>
                    <div class="paystub-settings-modal-section-content">
                        <CheckboxInput v-model="checkedHrsUnits" label="Hrs/Units" size="sm" align="left" />
                        <CheckboxInput v-model="checkedRate" label="Rate" size="sm" align="left" />
                        <CheckboxInput v-model="checkedCurrent" label="Current" size="sm" align="left" />
                        <CheckboxInput v-model="checkedFrequency" label="Frequency" size="sm" align="left" />
                        <CheckboxInput v-model="checkedYtd" label="YTD" size="sm" align="left" />
                    </div>
                </section>
            </div>
        </div>

        <div class="paystub-settings-modal-footer">
            <Button variant="muted" @click="$emit('close')">Cancel</Button>
            <Button :disabled="isSaveDisabled" @click="handleSave"> Save </Button>
        </div>

        <button class="delete-btn" v-if="canDeleteSection" @click="$emit('delete')">
            <SvgIcon shape="trash" /> Delete Section
        </button>
    </Modal>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import Modal from './Modal.vue';
import Button from '../elements/Button.vue';
import CheckboxInput from '../forms/CheckboxInput.vue';
import SvgIcon from '../images/SvgIcon.vue';
import TextInput from '../forms/TextInput.vue';
import type { PaystubFormColumnKind, PaystubFormSectionKind } from './AddPaystubManualModal.vue';

export interface PaystubSettingsModalSavePayload {
    name: string;
    type: PaystubFormSectionKind;
    columns: PaystubFormColumnKind[];
}
const emit = defineEmits<{
    save: [payload: PaystubSettingsModalSavePayload];
    close: [];
    delete: [];
}>();

export interface PaystubSettingsModalProps {
    isOpen?: boolean;
    section?: any;
}
const props = defineProps<PaystubSettingsModalProps>();

const sectionName = ref('');
const checkedCurrent = ref(false);
const checkedYtd = ref(false);
const checkedRate = ref(false);
const checkedHrsUnits = ref(false);
const checkedFrequency = ref(false);
const sectionType = ref<PaystubFormSectionKind>('income');

function setCheckedColumns(columns: string[] = []) {
    checkedCurrent.value = columns.includes('current') || columns.includes('Current');
    checkedYtd.value = columns.includes('ytd') || columns.includes('YTD');
    checkedRate.value = columns.includes('rate') || columns.includes('Rate');
    checkedHrsUnits.value = columns.includes('hours') || columns.includes('Hrs/Units');
    checkedFrequency.value = columns.includes('frequencyPerMonth') || columns.includes('Frequency');
}

const canDeleteSection = computed(() => true);
//
// onMounted(() => {
//     sectionName.value = props.name || '';
//     setCheckedColumns(props.columns as string[]);
//     sectionType.value = props.type || 'Income';
// });

watch(
    () => props.isOpen,
    (newValue, oldValue) => {
        if (newValue) {
            if (props.section) {
                sectionName.value = props.section.label;
                sectionType.value = props.section.kind;
                setCheckedColumns(props.section.cols);
            } else {
                sectionName.value = '';
                sectionType.value = 'income';
                setCheckedColumns([]);
            }
        }
    },
);

watch(
    () => props.name,
    newName => {
        sectionName.value = newName || '';
    },
);
watch(
    () => props.columns,
    newCols => {
        setCheckedColumns(newCols as string[]);
    },
);
watch(
    () => props.type,
    newType => {
        sectionType.value = newType || 'Income';
    },
);

const isSaveDisabled = computed(() => {
    const nameEmpty = !sectionName.value.trim();
    const noColumnsChecked = !(
        checkedCurrent.value ||
        checkedYtd.value ||
        checkedRate.value ||
        checkedHrsUnits.value ||
        checkedFrequency.value
    );
    return nameEmpty || noColumnsChecked;
});

function handleSave() {
    emit('save', {
        name: sectionName.value,
        columns: [
            checkedYtd.value ? 'ytd' : false,
            checkedCurrent.value ? 'current' : false,
            checkedRate.value ? 'rate' : false,
            checkedHrsUnits.value ? 'hours' : false,
            checkedFrequency.value ? 'frequencyPerMonth' : false,
        ].filter(Boolean),
        type: sectionType.value,
    });
}

function handleClose() {
    emit('close');
}
</script>

<style lang="scss">
@import './PaystubSettingsModal';

.save-btn.inactive {
    opacity: 0.5;
    cursor: not-allowed;
    background: #3a4a5a; // or your preferred inactive color
    color: #b0bed8;
}
</style>
