@import "/assets/css/mixins";

.modal-header-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 20px;
    color: var(--color-white);

    .modal-header-row-back {
        // width: 44px;
        height: 46px;
        .svg-icon {
            
        }
    }

    .modal-header-row-title {
        flex: 1 1 auto;

        h4 {
            font-size: 24px;
            font-weight: 700;
            line-height: 1.25em;
            .fade, &.fade {
                color: var(--color-blue-grey);
            }
        }
        h5 {
            font-size: 18px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.25em;
            .fade, &.fade {
                color: var(--color-blue-grey);
            }
        }
        h6 {
            color: var(--color-blue-grey);
            font-size: 14px;
            font-weight: 700;
            line-height: 1.125em;
            letter-spacing: 1.4px;
            text-transform: uppercase;
        }
    }

    .modal-header-row-actions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }
}


// <div class="modal-header-back" v-if="showBackButton">
//     <Button
//         @click="handleBack"
//         :icon-only="true"
//         variant="secondary"
//         icon="arrow-left"
//     >
//     </Button>
// </div>

// <div class="modal-header-row-title">
//     <h4 v-if="title" v-text="title"></h4>
//     <h6 v-if="subTitle" v-text="subTitle"></h6>
// </div>

// <div class="modal-header-row-actions">
//     <div>

//     </div>
    
//     <Button
//         v-if="showCloseButton"
//         @click="handleClose"
//         v-text="closeButtonText"
//     />
// </div>
