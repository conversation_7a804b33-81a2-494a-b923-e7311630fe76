@import '/assets/css/mixins';

.add-paystub-manual-modal {
    min-width: 720;
    height: auto;
    gap: 20px;
    display: flex;
    flex-direction: column;
    background-color: #19263c;
    border-radius: 4;
    margin: 0 auto;

    & .modal-content {
        padding: -60px !important;
        width: 90dvw;
        max-width: 900px;
    }

    &-content {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    &-kpis {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 10px;

        &-kpi,
        &-kpi-fixed {
            box-sizing: border-box;
        }

        &-kpi {
            display: flex;
            flex-direction: column;
            gap: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            width: 175px;
            padding: 20px;
        }

        &-kpi-input-muted {
            color: #b0b8c1 !important; // Muted text color
            background-color: rgba(255, 255, 255, 0.03) !important; // Muted background
        }

        &-kpi-fixed {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 175px;
            padding: 20px;
            outline: 1px solid var(--secondary-blue-grey, #57719c);
        }

        &-kpi-label {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            color: var(--secondary-grey, #97abcc);
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 20px;
            word-wrap: break-word;
        }

        &-kpi-option {
            color: var(--primary-white, #e9edf1);
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            line-height: 20px;
            word-wrap: break-word;
            &:has(input, select) {
                width: calc(100% + 20px);
            }
        }
    }

    &-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    &-header-left {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-header-right {
        display: flex;
        flex-direction: row;
        gap: 10px;
    }

    &-header-label {
        color: var(--secondary-grey, #97abcc);
        font-size: 18px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 30px;
        word-wrap: break-word;
    }

    &-header-break {
        color: var(--secondary-grey, #97abcc);
        font-size: 18px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 30px;
        word-wrap: break-word;
    }

    &-header-mode {
        color: var(--primary-white, #e9edf1);
        font-size: 18px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 30px;
        word-wrap: break-word;
    }

    &-sheets-pre-tax-deductions {
        color: white;
        font-size: 12px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
    }

    &-sheets {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 2px;
        font-size: 12px;
    }

    &-sheets-input {
        display: flex;
        flex-direction: column;
        width: 100%;

        &-line {
            width: 100%;
        }
    }

    &-sheets-input-header-label {
        width: 100%;
    }

    &-sheets-input-row {
        display: flex;
        flex-direction: row;
        width: 100%;
    }

    &-sheets-input-row-input {
        width: 100%;
    }

    &-sheets-item {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background-color: rgba(255, 255, 255, 0.05);
        cursor: pointer;
    }

    &-sheets-proceed {
        display: flex;
        gap: 10px;
        flex-direction: row;
        color: var(--secondary-blue-grey, #57719c);
        font-size: 12px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 20px;
        word-wrap: break-word;
        padding: 10px;
        align-items: center;
    }

    .row-action-menu {
        position: absolute;
        right: 0;
        top: 24px;
        background: #222b3a;
        border-radius: 4px;
        z-index: 10;
        width: 120px;
        button {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            padding: 8px 8px;
            gap: 4px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
            line-height: 1.25em;
        }
    }
}

.add-paystub-manual-modal-errors {
    max-width: 100%;

    color: var(--color-red);
    background: rgba(var(--color-red-rgb), 0.1);
    //margin-bottom: 4px;
    padding: 8px 10px;
    border-radius: 4px;
    border: 1px solid var(--color-red);
    p {
        white-space: wrap;
        font-size: 12px;
    }
}

.add-paystub-manual-modal-sheets-label {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.add-paystub-manual-modal-kpis {
    display: flex;
    gap: 16px;
}

.add-paystub-manual-modal-kpis-kpi,
.add-paystub-manual-modal-kpis-kpi-fixed {
    flex: 1 1 0;
    min-width: 0;
    box-sizing: border-box;
}

.add-paystub-manual-modal-kpis-kpi-option {
    input, select {
        color: #fff;
        //background-color: transparent;
        border: none;
        //border-radius: 4px;
        padding: 6px 10px;
        margin: -15px -10px;
        appearance: none;
        background: none transparent;
    }

    select {
        padding-left: 11px;
    }

    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }
}

input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
}

.add-paystub-manual-modal-kpis-kpi-option select option {
    background-color: #232f3e;
    color: #fff;
}

.kpi-input-muted {
    color: #57719c !important;
}

.add-paystub-manual-modal-kpis-kpi-asterisk-muted {
    color: #b0b8c1 !important;
}

.add-paystub-manual-modal-kpis-kpi-asterisk-active {
    color: #10bbb0 !important;
}

.kpi-input-muted::-webkit-calendar-picker-indicator {
    filter: invert(41%) sepia(13%) saturate(1042%) hue-rotate(176deg) brightness(92%) contrast(88%) !important;
    opacity: 1;
}

.kpi-input-muted::-ms-input-placeholder {
    color: #57719c;
}
.kpi-input-muted::placeholder {
    color: #57719c;
}

.add-paystub-manual-modal-kpis-kpi-option input,
.add-paystub-manual-modal-kpis-kpi-option select {
    width: 100%;
    box-sizing: border-box;
}

.add-paystub-manual-modal-sheets-inputs {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.add-paystub-manual-modal-sheets-inputs-line {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.add-paystub-manual-modal-sheet-cta-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex: 1 1 auto;
}

.add-paystub-manual-modal-sheets-inputs-row {
    width: 100%;
    display: flex;
    gap: 2px;
    position: relative;

    &:hover:not(:has(input:focus, select:focus), .row-focused) {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.add-paystub-manual-modal-sheets-inputs-row-input {
    flex: 1 1 0;
    min-width: 0;
    width: 100%;
}

.add-paystub-manual-modal-sheets-inputs-header-action {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
}

.add-paystub-manual-modal-sheets-inputs-row-action {
    display: flex;
    justify-content: center;
    align-items: center;
    min-width: 40px;
    background-color: rgba(255, 255, 255, 0.05);
}

.add-paystub-manual-modal-sheets-inputs-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.add-paystub-manual-modal-sheets-inputs-header-label {
    flex: 1 1 0;
    min-width: 0;
    width: 100%;
    text-align: right;
    padding: 10px;
    color: white;
    font-size: 10px;
    font-family: 'Oxygen';
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.2px;
    word-wrap: break-word;
    background-color: RGB 48 60 80;

    &:first-child {
        text-align: left;
        color: white;
        font-size: 12px;
        font-family: 'Oxygen';
        font-weight: 700;
        line-height: 20px;
        word-wrap: break-word;
    }
}

.add-paystub-manual-modal-sheets-inputs-row-input {
    background-color: rgba(255, 255, 255, 0.05);
    outline: none;
    padding: 0 10px;
}

.add-paystub-manual-modal-sheets-inputs-row-input:has(input:focus) {
    background: rgb(69, 80, 98);
    outline: none;
}

.add-paystub-manual-modal-sheets-inputs-row-add {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .add-paystub-manual-modal-sheets-inputs-row-add-col {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 0.5em 0.75em;
        font-size: 1em;
        font-weight: 400;
        // Only the first column shows text
        &:not(:first-child) {
            color: transparent;
            pointer-events: none;
        }
    }
}

.add-paystub-manual-modal-sheets-add-line-button {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.2);
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 10px;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.add-paystub-manual-modal-sheets-inputs-row.row-focused,
.add-paystub-manual-modal-sheets-inputs-row:has(input:focus) {
    box-shadow: 0 0 0 1px #57719c;
    z-index: 1;
}

.add-paystub-manual-modal-sheets-inputs-row-input.dollar-input {
    position: relative;
    padding-left: 20px !important;

    &::before {
        content: '$';
        color: rgba(151, 171, 204, 1) !important;
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        z-index: 1;
    }
}

.add-paystub-manual-modal-custom {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 20px;
    color: var(--secondary-grey, #97abcc);
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
    outline: 1px solid var(--secondary-blue-grey, #57719c);
    margin-top: -20px;
    cursor: pointer;

    &:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}
