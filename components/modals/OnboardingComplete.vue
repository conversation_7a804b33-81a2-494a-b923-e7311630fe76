<template>
    <div class="onboarding-complete">
        <div class="onboarding-complete-container">
            <div class="onboarding-complete-svg" :class="{ 'svg-hidden': !animationTriggered }">
                <SvgIcon shape="checkmark" color="#0D182A" width="52" height="40" />
            </div>
            <div class="onboarding-complete-text">
                <h3>Onboarding Complete!</h3>
            </div>
            <div class="onboarding-complete-subtext">
                <!-- Commented out timer prop -->
                <!-- <p>{{ props.timer }} MINS TO COMPLETE</p> -->
                <p></p>
            </div>
            <Button to="/client/dashboard" class="onboarding-complete-button" variant="primary">View Results</Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import Button from '../elements/Button.vue';
import type { Ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Props as SvgIconProps } from '../images/SvgIcon.vue';

/** The panel that shows when the user finishes the intake process */
defineOptions();

const animationTriggered = ref(false);
onMounted(async () =>
    setTimeout(() => {
        animationTriggered.value = true;
    }, 150),
);
</script>

// Inline CSS
<style lang="scss">
.onboarding-complete {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #19263c;
    padding: 40px;
    width: 100vw;
    height: 100vh;
}

.onboarding-complete-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #19263c;
    padding: 40px;
    min-width: 100%;
    min-height: 100%;
}

.onboarding-complete-svg {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1be2f9;
    width: 100px; /* Ensure the width and height are the same */
    height: 100px; /* Ensure the width and height are the same */
    border-radius: 50%; /* Make it fully rounded */
    overflow: hidden; /* Hide any overflow content */
    margin-bottom: 40px;

    transform-origin: center bottom;
    animation: svgSlideIn 0.6s 1s forwards;
}

.onboarding-complete svg {
    height: 60px; /* Scale the SVG to fit the container */
}

.onboarding-complete-text {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #e9edf1;
    font-size: 24px;
    font-weight: 700;
    line-height: 30px;
    margin-bottom: 40px;
}

.onboarding-complete-subtext {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #57719c;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    max-width: 400px;
    word-break: break-word;
    margin-bottom: 20px;
}

.onboarding-complete-button {
    min-width: 400px;
}

@keyframes svgSlideIn {
    0% {
        transform: scale(0) translateY(40px);
    }
    100% {
        transform: scale(1) translateY(0);
    }
}
</style>
