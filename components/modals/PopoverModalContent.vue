<template>
    <div class="popover-modal-content">
        <div class="popover-modal-content-header" v-if="$slots.header">
            <slot name="header" />
        </div>

        <slot />

        <div class="popover-modal-content-footer" v-if="$slots.footer">
            <slot name="footer" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';

/** A wrapper for standard styling of content in PopoverModals */
defineOptions();
</script>

<style lang="scss">
@import "./PopoverModalContent";
</style>
