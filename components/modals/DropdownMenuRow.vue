<template>
    <button class="dropdown-menu-row">
        <div class="dropdown-menu-row-icon" v-if="iconShape">
            <SvgIcon
                :shape="iconShape"
                :width="22"
                :height="22"
            />
        </div>

        <div class="dropdown-menu-row-label">
            <slot v-if="$slots.default"></slot>
            <span v-if="label" v-text="label"></span>
        </div>
    </button>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import type { TSvgIconShapeKey } from '../images/SvgIconShapes';
import SvgIcon from '../images/SvgIcon.vue';

/** An individual row for use inside the DropdownMenu component */
defineOptions();

export interface DropdownMenuRowProps {
    label?: string;
    iconShape?: TSvgIconShapeKey,
    isActive?: boolean;
}
const props = withDefaults(defineProps<DropdownMenuRowProps>(), {
    // foo: 'bar'
})
</script>

<style lang="scss">
@import "./DropdownMenuRow";
</style>

