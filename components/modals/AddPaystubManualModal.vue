<template>
    <Modal class="add-paystub-manual-modal" :is-open="isOpen" @close="handleModalClose">
        <div class="add-paystub-manual-modal">
            <div class="add-paystub-manual-modal-header">
                <span class="add-paystub-manual-modal-header-left">
                    <span class="add-paystub-manual-modal-header-label">Add Paystub</span>
                    <span class="add-paystub-manual-modal-header-break">/</span>
                    <span class="add-paystub-manual-modal-header-mode">{{ headerMode }}</span>
                </span>
                <span class="add-paystub-manual-modal-header-right">
                    <Button variant="muted" @click.prevent="handleModalClose">Cancel</Button>
                    <Button
                        :disabled="isAddDisabled"
                        :class="{ inactive: isAddDisabled }"
                        @click.prevent="handleSubmit"
                    >
                        <span v-text="existingPaystubId ? 'Save' : 'Add'"></span> Paystub
                    </Button>
                </span>
            </div>

            <div class="add-paystub-manual-modal-content">
                <div class="add-paystub-manual-modal-errors" v-if="serverErrors">
                    <p>
                        There was an error with your paystub. Please ensure all required fields are filled, including
                        pay date and pay frequency. Furthermore, each row must have a description.
                    </p>
                </div>
                <div class="add-paystub-manual-modal-kpis">
                    <div class="add-paystub-manual-modal-kpis-kpi">
                        <div class="add-paystub-manual-modal-kpis-kpi-label">
                            Pay Date
                            <span
                                class="add-paystub-manual-modal-kpis-kpi-asterisk"
                                :class="{
                                    'add-paystub-manual-modal-kpis-kpi-asterisk-muted': !localPayDate,
                                    'add-paystub-manual-modal-kpis-kpi-asterisk-active': !!localPayDate,
                                }"
                                >*</span
                            >
                        </div>
                        <div class="add-paystub-manual-modal-kpis-kpi-option">
                            <input
                                type="date"
                                class="bg-red-500"
                                v-model="localPayDate"
                                :class="{ 'kpi-input-muted': !localPayDate }"
                            />
                        </div>
                    </div>

                    <div class="add-paystub-manual-modal-kpis-kpi">
                        <div class="add-paystub-manual-modal-kpis-kpi-label">
                            Pay Frequency
                            <span
                                class="add-paystub-manual-modal-kpis-kpi-asterisk"
                                :class="{
                                    'add-paystub-manual-modal-kpis-kpi-asterisk-muted': localPayFrequency === 'Select',
                                    'add-paystub-manual-modal-kpis-kpi-asterisk-active': localPayFrequency !== 'Select',
                                }"
                                >*</span
                            >
                        </div>
                        <div class="add-paystub-manual-modal-kpis-kpi-option">
                            <select v-model="localPayFrequency" :class="{ 'kpi-input-muted': !localPayFrequency }">
                                <option :value="null" disabled>Select</option>
                                <option
                                    v-for="option in frequencyOptions"
                                    :key="option.value"
                                    :value="option.value"
                                    v-text="option.label"
                                ></option>
                            </select>
                        </div>
                    </div>

                    <div class="add-paystub-manual-modal-kpis-kpi-fixed">
                        <div class="add-paystub-manual-modal-kpis-kpi-label">Today's Pay Rate</div>
                        <div
                            class="add-paystub-manual-modal-kpis-kpi-option"
                            v-text="calculatedPayRate ? numberToUSD(calculatedPayRate) : '-'"
                        ></div>
                    </div>

                    <div class="add-paystub-manual-modal-kpis-kpi-fixed">
                        <div class="add-paystub-manual-modal-kpis-kpi-label">Projected Annual</div>
                        <div
                            class="add-paystub-manual-modal-kpis-kpi-option"
                            v-text="calculatedAnnual ? numberToUSD(calculatedAnnual) : '-'"
                        ></div>
                    </div>
                </div>
                <!-- End KPIS -->

                <!-- Table -->
                <div class="add-paystub-manual-modal-sheets">
                    <div v-for="section in sections" :key="section.label" class="add-paystub-manual-modal-sheets-item">
                        <template v-if="section.rows?.length">
                            <div class="add-paystub-manual-modal-sheets-inputs">
                                <div class="add-paystub-manual-modal-sheets-inputs-line">
                                    <div class="add-paystub-manual-modal-sheets-inputs-header">
                                        <span
                                            v-text="section.label"
                                            class="add-paystub-manual-modal-sheets-inputs-header-label"
                                        ></span>
                                        <span
                                            v-for="col in section.cols"
                                            :key="col"
                                            class="add-paystub-manual-modal-sheets-inputs-header-label"
                                            v-text="columnLabelMap[col] ?? col"
                                        ></span>

                                        <span class="add-paystub-manual-modal-sheets-inputs-header-action"></span>

                                        <!--<span-->
                                        <!--    class="add-paystub-manual-modal-sheets-inputs-header-action"-->
                                        <!--    @click.prevent="handleShowSectionSettingsModal(section.id)"-->
                                        <!--&gt;-->
                                        <!--    <SvgIcon shape="settings" />-->
                                        <!--</span>-->
                                    </div>

                                    <div
                                        class="add-paystub-manual-modal-sheets-inputs-row"
                                        v-for="row in section.rows"
                                        :key="row.id"
                                    >
                                        <!-- Description input field -->
                                        <TextInput
                                            class="add-paystub-manual-modal-sheets-inputs-row-input"
                                            :name="row.id + ':description'"
                                            v-model="row['description']"
                                            @keydown="e => handleInputKeydown(e, section, row.id, 'description')"
                                            placeholder="Description"
                                            size="xs"
                                            :with-border="false"
                                        />

                                        <!-- All other input field -->
                                        <TextInput
                                            v-for="col in section.cols"
                                            :key="col"
                                            class="add-paystub-manual-modal-sheets-inputs-row-input"
                                            v-model="row[col]"
                                            :name="row.id + ':' + col"
                                            size="xs"
                                            :with-border="false"
                                            text-align="right"
                                            :floating-dollar="!['hours', 'frequencyPerMonth'].includes(col)"
                                            :number-formatting="
                                                ['hours', 'frequencyPerMonth'].includes(col)
                                                    ? true
                                                    : { precision: { min: 0, max: 2 } }
                                            "
                                            @keydown="e => handleInputKeydown(e, section, row.id, col)"
                                        />

                                        <span
                                            class="add-paystub-manual-modal-sheets-inputs-row-action"
                                            @click.stop="openRowActionMenu(section.id, row.id)"
                                            style="position: relative"
                                        >
                                            <SvgIcon shape="dot-menu" />
                                            <div
                                                v-if="
                                                    openRowMenu &&
                                                    openRowMenu.sectionId === section.id &&
                                                    openRowMenu.rowId === row.id
                                                "
                                                class="row-action-menu"
                                            >
                                                <button @click="deleteLineItem(section.id, row.id)">
                                                    <SvgIcon shape="trash" width="20" height="20" />
                                                    <span>Remove row</span>
                                                </button>
                                            </div>
                                        </span>
                                    </div>
                                </div>

                                <button
                                    class="add-paystub-manual-modal-sheets-add-line-button"
                                    @click="addLineItem(section)"
                                    @keydown="e => handleAddLineButtonKeydown(e, section)"
                                    tabindex="0"
                                >
                                    + New line item
                                </button>
                            </div>
                        </template>

                        <!-- Default "Get Started" State -->
                        <button
                            v-else
                            @click.prevent="handleCtaRowClick(section)"
                            @keydown="e => handleCtaKeydown(e, section)"
                            class="add-paystub-manual-modal-sheet-cta-row"
                            tabindex="0"
                        >
                            <!-- if !section.rows.length -->
                            <div class="add-paystub-manual-modal-sheets-label" v-text="section.label"></div>
                            <div class="add-paystub-manual-modal-sheets-proceed">
                                Get started <SvgIcon shape="long-arrow"></SvgIcon>
                            </div>
                        </button>
                    </div>
                </div>

                <div class="add-paystub-manual-modal-add"></div>
            </div>

            <!-- TODO Add Custom Sections Back-->
            <!--<div class="add-paystub-manual-modal-custom" @click.prevent="handleClickAddCustomSection">-->
            <!--    <SvgIcon shape="plus"></SvgIcon> Add Custom Section-->
            <!--</div>-->
        </div>

        <PaystubSettingsModal
            :is-open="!!settingsModalVisibleForSectionId"
            :section="selectedSectionForSettingsModal"
            @close="handleCloseSectionSettingsModal"
            @save="handleSaveSectionSettingsModal"
            @delete="handleDeleteCustomSection"
        />
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch, type ComponentPublicInstance, onBeforeUnmount, nextTick } from 'vue';
import { useId } from 'vue';
import Button from '../elements/Button.vue';
import SvgIcon from '../images/SvgIcon.vue';
import Modal from './Modal.vue';
import TextInput from '../forms/TextInput.vue';
import PaystubSettingsModal from './PaystubSettingsModal.vue';
import type { PaystubSettingsModalSavePayload } from './PaystubSettingsModal.vue';
import { nanoid } from 'nanoid';
import type { Paystub, PaystubLine } from '~/types/paystub';

import { numberToUSD, frequencyToInt, isValidDate, formatDate } from '~/utils';

import { useOverhaulStore } from '~/stores/overhaul';
const overhaulStore = useOverhaulStore();

export interface AddPaystubManualModalProps {
    incomeEmployeeId?: string;
    isOpen?: boolean;
    headerMode?: string;
    // payRate?: string;
    existingPaystub?: Paystub;
}

const props = withDefaults(defineProps<AddPaystubManualModalProps>(), {
    headerMode: 'Enter Manually',
});

export type PaystubFormColumnKind = 'frequencyPerMonth' | 'rate' | 'hours' | 'current' | 'ytd';
export type PaystubFormSectionKind = 'income' | 'preTax' | 'postTax' | 'tax';
export interface PaystubFormSection {
    id: string;
    label: string;
    kind: PaystubFormSectionKind;
    cols: Array<PaystubFormColumnKind>;
    rows: Array<PaystubLine & { id: string }>;
}

const columnLabelMap: { [k in keyof PaystubLine]: string } = {
    _id: '',
    description: 'Description',
    frequencyPerMonth: 'Freq/Mo',
    rate: 'Rate',
    hours: 'Hrs/Units',
    current: 'Current',
    ytd: 'YTD',
};

const paystubObj = reactive<Paystub>({});
// const sections = computed<PaystubFormSection[]>(() => {
const sections = reactive<PaystubFormSection[]>([
    {
        id: nanoid(),
        label: 'Earnings',
        kind: 'income',
        cols: ['hours', 'rate', 'current', 'ytd'],
        rows: [],
    },
    {
        id: nanoid(),
        label: 'Pre-tax Deductions',
        kind: 'preTax',
        cols: ['hours', 'rate', 'current', 'ytd'],
        rows: [],
    },
    {
        id: nanoid(),
        label: 'Post-tax Deductions',
        kind: 'postTax',
        cols: ['hours', 'rate', 'current', 'ytd'],
        rows: [],
    },
    {
        id: nanoid(),
        label: 'Taxes',
        kind: 'tax',
        cols: ['hours', 'rate', 'current', 'ytd'],
        rows: [],
    },
]);

const payload = computed<Paystub>(() => {
    const obj = {
        ...props.existingPaystub,
        name: '',
        payDate: localPayDate.value,
        frequency: localPayFrequency.value ?? '',
        incomeItems: [],
        preTaxItems: [],
        postTaxItems: [],
        taxesItems: [],
        customItems: [],
    };

    sections.forEach(section => {
        const objKeyForSection = getPaystubItemKeyByKind(section.kind);
        section.rows.forEach(row => {
            obj[objKeyForSection].push(row);
        });
    });

    return obj;
});

function getPaystubItemKeyByKind(kind: string) {
    switch (kind) {
        case 'income':
            return 'incomeItems';
        case 'preTax':
            return 'preTaxItems';
        case 'postTax':
            return 'postTaxItems';
        case 'tax':
            return 'taxesItems';
        default:
            return '';
    }
}

const frequencyOptions = [
    // {label: 'Daily', value: 'Daily'},
    { label: 'Weekly', value: 'Weekly' },
    { label: 'Every two weeks', value: 'Biweekly' },
    { label: 'Twice a month', value: 'Semimonthly' },
    { label: 'Monthly', value: 'Monthly' },
    // {label: 'Bimonthly', value: 'Bimonthly'},
    // {label: 'Trimesterly', value: 'Trimesterly'},
    // {label: 'Quarterly', value: 'Quarterly'},
    // {label: 'SemiAnnually', value: 'Semiannually'},
    { label: 'Annually', value: 'Yearly' },
];

const emit = defineEmits<{
    update: [paystub: Paystub];
    new: [paystub: Paystub];
    close: [];
}>();

// The total *gross* pay that this paystub indicates
const calculatedPayRate = computed(() => {
    return sections.reduce((acc: number, section) => {
        // If this section doesn't have any rows, skip it
        if (!section.rows?.length) return acc;

        // "income" kind sections are the only ones that contribute to payRate
        if (section.kind !== 'income') return acc;

        // Sum up all of the rows in this section
        // The "current" column is the only one that we care about right now
        const rowTotals = section.rows.reduce((innerAcc: number, row) => {
            return innerAcc + (row.current ?? 0);
        }, 0);

        return rowTotals + acc;
    }, 0);
});

const calculatedAnnual = computed(() => {
    if (!calculatedPayRate.value || !localPayFrequency.value) return 0;

    const key = localPayFrequency.value;
    const paymentsPerYear = key ? frequencyToInt[key as keyof typeof frequencyToInt] : undefined;
    if (!paymentsPerYear) return 0;

    return calculatedPayRate.value * paymentsPerYear;
});

const localPayDate = ref();
const localPayFrequency = ref('Semimonthly');

const sectionOrder = ['earnings', 'preTax', 'postTax', 'taxes'] as const;

const EMPTY_LINE_ITEM: Omit<PaystubLine, 'id'> = {
    description: '',
    frequencyPerMonth: null,
    rate: null,
    hours: null,
    current: null,
    ytd: null,
    // customLineOptions: null,
};

async function handleCtaRowClick(section: any) {
    addLineItem(section);
}

const savePending = ref(false);
const isAddDisabled = computed(() => !localPayDate.value || localPayFrequency.value === 'Select' || savePending.value);

async function addLineItem(section: PaystubFormSection) {
    const newRow = {
        ...EMPTY_LINE_ITEM,
        id: nanoid(),
    };
    section.rows.push(newRow);
    await nextTick();

    // Focus the first input of the newly added row
    const input = document.querySelector(`input[name="${newRow.id}:description"]`) as HTMLInputElement;
    if (input) {
        input.focus();
    }
}

const settingsModalVisibleForSectionId = ref<string | null>(null);
const selectedSectionForSettingsModal = computed(() => {
    if (!settingsModalVisibleForSectionId.value) return;
    return sections.find(s => s.id === settingsModalVisibleForSectionId.value);
});
function handleShowSectionSettingsModal(sectionId?: string) {
    settingsModalVisibleForSectionId.value = sectionId ?? null;
}

// The order that we always want the columns to be stored in
const columnOrder: PaystubFormColumnKind[] = ['frequencyPerMonth', 'hours', 'rate', 'current', 'ytd'];

async function handleSaveSectionSettingsModal(payload: PaystubSettingsModalSavePayload) {
    const section = selectedSectionForSettingsModal.value;
    if (section) {
        section.label = payload.name;
        section.kind = payload.type;
        section.cols = payload.columns.sort((a, b) => columnOrder.indexOf(a) - columnOrder.indexOf(b));
    } else {
        const newSection = {
            id: nanoid(),
            label: payload.name,
            kind: payload.type,
            cols: payload.columns.sort((a, b) => columnOrder.indexOf(a) - columnOrder.indexOf(b)),
            rows: [],
        };
        sections.push(newSection);
        await nextTick();
        await addLineItem(newSection);
    }
    handleCloseSectionSettingsModal();
}

function handleCloseSectionSettingsModal() {
    settingsModalVisibleForSectionId.value = null;
}

function handleDeleteCustomSection() {
    if (!settingsModalVisibleForSectionId.value) return;

    const index = sections.findIndex(s => s.id === settingsModalVisibleForSectionId.value);
    if (index !== -1) {
        sections.splice(index, 1);
    }
    settingsModalVisibleForSectionId.value = null;
}

function handleClickAddCustomSection() {
    // Yes, this is a hack; but it will open the modal to an empty state, and when that form is submitted
    // It will trigger the add section process in handleSaveSectionSettingsModal
    settingsModalVisibleForSectionId.value = 'new';
}

// Helper function to focus an input or interactive element
const focusElement = (selector: string, event?: KeyboardEvent) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
        if (event) event.preventDefault();
        element.focus();
        return true;
    }
    return false;
};

// Helper function to focus an input in a specific section, row, and column
const focusInput = (sectionIdx: number, rowIdx: number, colIdx: number, event?: KeyboardEvent) => {
    const targetSection = sections[sectionIdx];
    if (!targetSection?.rows?.[rowIdx]) {
        // If the target row doesn't exist, check if there's a CTA row or add line button to focus
        if (targetSection && targetSection.rows.length === 0) {
            // Try to focus the CTA row
            const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
            return focusElement(ctaSelector, event);
        } else if (targetSection) {
            // Try to focus the add line button
            const addLineButtonSelector = `.add-paystub-manual-modal-sheets-add-line-button[tabindex="0"]`;
            return focusElement(addLineButtonSelector, event);
        }
        return false;
    }

    const targetRow = targetSection.rows[rowIdx];
    const cols = ['description', ...targetSection.cols];

    // If the target column doesn't exist in the destination row, focus the first input
    if (colIdx >= cols.length) {
        colIdx = 0;
    }

    const targetCol = cols[colIdx];
    if (!targetCol) return false;

    const selector = `input[name="${targetRow.id}:${targetCol}"]`;
    return focusElement(selector, event);
};

function handleInputKeydown(e: KeyboardEvent, section: PaystubFormSection, rowId: string, col: string) {
    const input = e.target as HTMLInputElement;
    const isAtStart = input.selectionStart === 0;
    const isAtEnd = input.selectionStart === input.value.length;

    // Find current section index and row index
    const sectionIndex = sections.findIndex(s => s.id === section.id);
    const rowIndex = section.rows.findIndex(r => r.id === rowId);
    const colIndex = ['description', ...section.cols].indexOf(col);

    switch (e.key) {
        case 'Tab':
            if (!e.shiftKey) {
                // Forward tab navigation
                if (colIndex < ['description', ...section.cols].length - 1) {
                    // Go to next column in same row
                    focusInput(sectionIndex, rowIndex, colIndex + 1, e);
                } else if (rowIndex < section.rows.length - 1) {
                    // Go to first column of next row in same section
                    focusInput(sectionIndex, rowIndex + 1, 0, e);
                } else {
                    // Try to focus the add line button for this section
                    const addLineButtonSelector = `.add-paystub-manual-modal-sheets-add-line-button[tabindex="0"]`;
                    if (!focusElement(addLineButtonSelector, e) && sectionIndex < sections.length - 1) {
                        // If no add line button or couldn't focus it, go to the next section
                        const nextSection = sections[sectionIndex + 1];
                        if (nextSection.rows.length > 0) {
                            // Go to first column of first row in next section
                            focusInput(sectionIndex + 1, 0, 0, e);
                        } else {
                            // Focus the CTA row of the next section
                            const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
                            focusElement(ctaSelector, e);
                        }
                    }
                }
            } else {
                // Backward tab navigation
                if (colIndex > 0) {
                    // Go to previous column in same row
                    focusInput(sectionIndex, rowIndex, colIndex - 1, e);
                } else if (rowIndex > 0) {
                    // Go to last column of previous row in same section
                    focusInput(sectionIndex, rowIndex - 1, ['description', ...section.cols].length - 1, e);
                } else if (sectionIndex > 0) {
                    // Go to last column of last row in previous section
                    const prevSection = sections[sectionIndex - 1];
                    if (prevSection.rows.length > 0) {
                        focusInput(
                            sectionIndex - 1,
                            prevSection.rows.length - 1,
                            ['description', ...prevSection.cols].length - 1,
                            e,
                        );
                    } else {
                        // Focus the CTA row of the previous section
                        const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
                        focusElement(ctaSelector, e);
                    }
                }
            }
            break;

        case 'ArrowLeft':
            if (isAtStart && colIndex > 0) {
                focusInput(sectionIndex, rowIndex, colIndex - 1, e);
            }
            break;

        case 'ArrowRight':
            if (isAtEnd && colIndex < ['description', ...section.cols].length - 1) {
                focusInput(sectionIndex, rowIndex, colIndex + 1, e);
            }
            break;

        case 'ArrowDown':
            if (rowIndex < section.rows.length - 1) {
                // Go to same column in next row of same section
                focusInput(sectionIndex, rowIndex + 1, colIndex, e);
            } else {
                // Try to focus the add line button for this section
                // Find all sections
                const allSections = document.querySelectorAll('.add-paystub-manual-modal-sheets-item');
                // Get the current section element
                const currentSectionElement = allSections[sectionIndex];

                if (currentSectionElement) {
                    // Try to find the add line button within this section
                    const addLineButton = currentSectionElement.querySelector(
                        '.add-paystub-manual-modal-sheets-add-line-button[tabindex="0"]',
                    );
                    if (addLineButton) {
                        e.preventDefault();
                        (addLineButton as HTMLElement).focus();
                        return;
                    }
                }

                // If we couldn't find or focus the add line button, and there's a next section, go to it
                if (sectionIndex < sections.length - 1) {
                    const nextSection = sections[sectionIndex + 1];
                    if (nextSection.rows.length > 0) {
                        // Go to same column in first row of next section if it exists
                        focusInput(sectionIndex + 1, 0, colIndex, e);
                    } else {
                        // Focus the CTA row of the next section
                        const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
                        focusElement(ctaSelector, e);
                    }
                }
            }
            break;

        case 'ArrowUp':
            if (rowIndex > 0) {
                // Go to same column in previous row of same section
                focusInput(sectionIndex, rowIndex - 1, colIndex, e);
            } else if (sectionIndex > 0) {
                // Go to same column in last row of previous section
                const prevSection = sections[sectionIndex - 1];
                if (prevSection.rows.length > 0) {
                    focusInput(sectionIndex - 1, prevSection.rows.length - 1, colIndex, e);
                } else {
                    // Focus the CTA row of the previous section
                    const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
                    focusElement(ctaSelector, e);
                }
            }
            break;
    }
}

// Handle keydown events on the CTA row
function handleCtaKeydown(e: KeyboardEvent, section: PaystubFormSection) {
    const sectionIndex = sections.findIndex(s => s.id === section.id);

    switch (e.key) {
        case 'Enter':
        case ' ':
            // Simulate click on Enter or Space
            e.preventDefault();
            handleCtaRowClick(section);
            break;

        case 'Tab':
            // Tab navigation is handled by the browser
            break;

        case 'ArrowDown':
            e.preventDefault();
            // Get all CTA rows
            const allCtaRows = Array.from(
                document.querySelectorAll('.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]'),
            );
            const currentIndex = allCtaRows.findIndex(row => row === e.target);

            // If there are more CTA rows below this one, focus the next one
            if (currentIndex < allCtaRows.length - 1) {
                (allCtaRows[currentIndex + 1] as HTMLElement).focus();
                return;
            }

            // Otherwise, try to focus the next section's CTA row or first input
            if (sectionIndex < sections.length - 1) {
                const nextSection = sections[sectionIndex + 1];
                if (nextSection.rows.length > 0) {
                    // Focus the first input of the first row
                    focusInput(sectionIndex + 1, 0, 0, e);
                }
            }
            break;

        case 'ArrowUp':
            e.preventDefault();
            // Get all CTA rows
            const allCtaRowsUp = Array.from(
                document.querySelectorAll('.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]'),
            );
            const currentIndexUp = allCtaRowsUp.findIndex(row => row === e.target);

            // If there are more CTA rows above this one, focus the previous one
            if (currentIndexUp > 0) {
                (allCtaRowsUp[currentIndexUp - 1] as HTMLElement).focus();
                return;
            }

            // Otherwise, try to focus the previous section's last input or CTA row
            if (sectionIndex > 0) {
                const prevSection = sections[sectionIndex - 1];
                if (prevSection.rows.length > 0) {
                    // Focus the last input of the last row
                    const lastRowIndex = prevSection.rows.length - 1;
                    const lastColIndex = ['description', ...prevSection.cols].length - 1;
                    focusInput(sectionIndex - 1, lastRowIndex, lastColIndex, e);
                }
            }
            break;
    }
}

// Handle keydown events on the add line button
function handleAddLineButtonKeydown(e: KeyboardEvent, section: PaystubFormSection) {
    const sectionIndex = sections.findIndex(s => s.id === section.id);

    switch (e.key) {
        case 'Enter':
        case ' ':
            // Simulate click on Enter or Space
            e.preventDefault();
            addLineItem(section);
            break;

        case 'Tab':
            // Tab navigation is handled by the browser
            break;

        case 'ArrowDown':
            e.preventDefault();
            // Try to focus the next section's first input or CTA row
            if (sectionIndex < sections.length - 1) {
                const nextSection = sections[sectionIndex + 1];
                if (nextSection.rows.length > 0) {
                    // Focus the first input of the first row
                    focusInput(sectionIndex + 1, 0, 0, e);
                } else {
                    // Focus the CTA row
                    const ctaSelector = `.add-paystub-manual-modal-sheet-cta-row[tabindex="0"]`;
                    focusElement(ctaSelector, e);
                }
            }
            break;

        case 'ArrowUp':
            e.preventDefault();
            // Try to focus the last input of the current section
            if (section.rows.length > 0) {
                const lastRowIndex = section.rows.length - 1;
                const lastColIndex = ['description', ...section.cols].length - 1;
                focusInput(sectionIndex, lastRowIndex, lastColIndex, e);
            }
            break;
    }
}

const openRowMenu = ref<{ sectionId: string | number; rowId: string } | null>(null);

function openRowActionMenu(sectionId: string | number, rowId: string) {
    openRowMenu.value = { sectionId, rowId };
}

function closeRowActionMenu() {
    openRowMenu.value = null;
}

function deleteLineItem(sectionId: string, rowId: string) {
    const section = sections.find(e => e.id === sectionId);
    if (section) {
        section.rows = section.rows.filter(e => e.id !== rowId);
    }

    closeRowActionMenu();
}

function handleModalClose() {
    emit('close');
}

const serverErrors = ref();

const existingPaystubId = computed(() => (props.existingPaystub?._id ? props.existingPaystub._id : null));

function handleSubmit() {
    // isAddDisabled is false if savePending is true, so don't need to check that
    if (isAddDisabled.value) return;
    savePending.value = true;

    if (existingPaystubId.value) {
        overhaulStore
            .updatePaystub(existingPaystubId.value, payload.value, props.incomeEmployeeId)
            .then(response => {
                emit('update', payload.value);
                savePending.value = false;
            })
            .catch(err => {
                console.log('error', err);
                savePending.value = false;
                serverErrors.value = ['bad!'];
            });
    } else {
        overhaulStore
            .saveNewPaystub(payload.value, props.incomeEmployeeId)
            .then(response => {
                emit('new', response);
                savePending.value = false;
            })
            .catch(err => {
                console.log('error', err);
                savePending.value = false;
                serverErrors.value = ['bad!'];
            });
    }
}

function hydrateDataFromExistingPaystub(existingPaystub: Paystub) {
    if (existingPaystub.payDate) {
        // Doing this to remove timezone conversions on the pay date
        const payDate = new Date(existingPaystub.payDate?.replace(/Z$/, ''));
        localPayDate.value = payDate ? formatDate(payDate, 'yyyy-MM-dd') : null;
    }

    localPayFrequency.value = existingPaystub.frequency;
    sections
        .find(sec => sec.label === 'Earnings')
        ?.rows.push(
            ...(existingPaystub.incomeItems ?? []).map(item => ({ ...EMPTY_LINE_ITEM, id: nanoid(), ...item })),
        );
    sections
        .find(sec => sec.label === 'Pre-tax Deductions')
        ?.rows.push(
            ...(existingPaystub.preTaxItems ?? []).map(item => ({ ...EMPTY_LINE_ITEM, id: nanoid(), ...item })),
        );
    sections
        .find(sec => sec.label === 'Post-tax Deductions')
        ?.rows.push(
            ...(existingPaystub.postTaxItems ?? []).map(item => ({ ...EMPTY_LINE_ITEM, id: nanoid(), ...item })),
        );
    sections
        .find(sec => sec.label === 'Taxes')
        ?.rows.push(...(existingPaystub.taxesItems ?? []).map(item => ({ ...EMPTY_LINE_ITEM, id: nanoid(), ...item })));
}

function resetForm() {
    savePending.value = false;
    localPayDate.value = null;
    localPayFrequency.value = null;

    // TODO: Account for a previous state of the modal that removed default rows, and add them back in
    const labels = ['Earnings', 'Pre-tax Deductions', 'Post-tax Deductions', 'Taxes'];
    labels.forEach(label => {
        const section = sections.find(sec => sec.label === label);
        if (section) {
            section.rows = [];
        }
    });
}

watch(
    () => props.isOpen,
    newValue => {
        if (newValue) {
            resetForm();
            if (props.existingPaystub) {
                hydrateDataFromExistingPaystub(props.existingPaystub);
            }
        }
    },
);
</script>

<style lang="scss">
@import './AddPaystubManualModal';

.modal-disabled {
    pointer-events: none;
    user-select: none;
    opacity: 0.5; // Optional: visually indicate it's disabled
}

.headlessui-portal-root {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.row-action-menu {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
