import PostOnboardingLineItemModal from './PostOnboardingLineItemModal.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

interface DepositProps {
    leftTop: string;
    leftBottom: string;
    rightTop: string;
    rightBottom?: string;
    date: string;
    amount: number;
    isEditable?: boolean;
}

interface RecentDepositProps {
    date: string;
    amount: number;
}

interface HistoryItemProps {
    date: string;
    amount: number;
    subtitle: string;
    runningTotal: number;
}

const meta = {
    title: 'Modals/PostOnboardingLineItemModal',
    component: PostOnboardingLineItemModal,
    tags: ['autodocs'],
    argTypes: {
        deposit: {
            control: 'object',
            description: 'Deposit information object',
        },
        recentDeposits: {
            control: 'object',
            description: 'Array of recent deposits',
        },
        historyItems: {
            control: 'object',
            description: 'Array of history items with running totals',
        },
    },
    args: {
        isOpen: true,
    },
} satisfies Meta<typeof PostOnboardingLineItemModal>;

export default meta;
type Story = StoryObj<typeof meta>;
export const Default: Story = {
    args: {
        leftTopLabel: 'Emergency fund',
        leftBottomLabel: 'Bank of America ****1234',
        rightTopLabel: '$28.5K',
        rightBottomLabel: 'Total as of April 1st, 2025',
        depositsHeaderLabel: 'Recent deposits',
        depositsHeaderAction: 'View all',
        deposit: {
            leftTop: 'Next deposit scheduled',
            leftBottom: 'April 15th, 2025',
            rightTop: '$800',
            rightBottom: 'edit deposit',
            date: '2025-04-15',
            amount: 800,
            isEditable: true,
        } as DepositProps,
        recentDeposits: [
            { date: 'March 15th, 2025', amount: 800 },
            { date: 'February 15th, 2025', amount: 800 },
            { date: 'January 15th, 2025', amount: 800 },
        ] as RecentDepositProps[],
        historyItems: [
            {
                date: 'March 15th, 2025',
                amount: 800,
                subtitle: 'Automatically assigned',
                runningTotal: 41300,
            },
            {
                date: 'February 15th, 2025',
                amount: 800,
                subtitle: 'Automatically assigned',
                runningTotal: 40500,
            },
            {
                date: 'January 15th, 2025',
                amount: 800,
                subtitle: 'Automatically assigned',
                runningTotal: 39700,
            },
            {
                date: 'December 15th, 2024',
                amount: 800,
                subtitle: 'Automatically assigned',
                runningTotal: 38900,
            },
        ] as HistoryItemProps[],
    },
};
