import ModalHeaderRow from './ModalHeaderRow.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof ModalHeaderRow> = {
    component: ModalHeaderRow,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        showCloseButton: {
            control: { type: 'boolean' },
            description: ''
        },
        showBackButton: {
            control: { type: 'boolean' },
            description: ''
        },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof ModalHeaderRow>;

export const Default: Story = {
    args: {
        title: 'Commercial Real Estate',
        subTitle: 'Standard Entry',
        showCloseButton: true,
        closeButtonText: 'Close',
        showBackButton: true
    }
};
