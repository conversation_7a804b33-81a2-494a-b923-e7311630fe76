<template>
    <div class="dropdown-menu">
        <button class="dropdown-menu-button" @click="handleToggle">
            <SvgIcon
                :shape="props.iconShape"
                :width="iconSize"
                :height="iconSize"
            />
        </button>
        <PopoverModal
            :dynamic-placement="false"
            :with-overlay="false"
            :is-open="isOpen"
            @close="handleToggle"
        >
            <PopoverModalContent>
                <div class="dropdown-menu-content">
                    <slot></slot>
                </div>
            </PopoverModalContent>
        </PopoverModal>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';

import PopoverModal from './PopoverModal.vue';
import PopoverModalContent from './PopoverModalContent.vue';
import type { TSvgIconShapeKey } from '../images/SvgIconShapes';
import SvgIcon from '../images/SvgIcon.vue';

/** A small dropdown menu for quick actions */
defineOptions();

export interface DropdownMenuProps {
    iconShape?: TSvgIconShapeKey
    iconSize?: number | string
}
const props = withDefaults(defineProps<DropdownMenuProps>(), {
    iconShape: 'dot-menu',
    iconSize: 32
})

const isOpen = ref(false);

const emit = defineEmits<{
    change: [id: number]
}>()

function handleToggle() {
    isOpen.value = !isOpen.value;
}
</script>

<style lang="scss">
@import "./DropdownMenu";
</style>
