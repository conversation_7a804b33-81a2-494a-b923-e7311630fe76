<template>
    <div class="modal-header-row">
        <div class="modal-header-row-back" v-if="showBackButton">
            <Button
                @click="handleBack"
                :icon-only="true"
                variant="muted"
                svg-icon="keyboard-arrow-left"
            >
            </Button>
        </div>

        <div class="modal-header-row-title">
            <component :is="subTitle ? 'h4' : 'h5'" v-if="title" v-html="title"></component>
            <h6 v-if="subTitle" v-text="subTitle"></h6>
        </div>

        <div class="modal-header-row-actions">
            <div v-if="activeMode && showModeSwitcher">
                <EntryModeSwitcher
                    :active="activeMode"
                    :label="modeTypeLabel"
                    @change="handleEntryModeChange"
                />
            </div>

            <!-- <div class="modal-header-row-delete" v-if="canDelete">
                <Button
                    class="modal-header-row-delete-button"
                    tabindex="-1"
                    variant="muted"
                    :icon-only="true"
                    :svg-icon="{
                        shape: 'trash'
                    }"
                    @click="handlePopoverToggle"
                />
                <PopoverModal
                    :dynamic-placement="false"
                    :with-overlay="false"
                    :is-open="deletePopoverIsOpen"
                    @close="handlePopoverClose"
                >
                    <PopoverModalContent>
                        <template #header>
                            <h5>
                                <em>Are you sure you want to delete</em> <span v-text="modeTypeLabel"></span> <em>?</em>
                            </h5>
                            <button class="popover-modal-close" @click.prevent="handlePopoverClose">
                                <SvgIcon shape="close" />
                            </button>
                        </template>

                        <p class="modal-header-row-delete-confirmation">This will permenantly remove all of your <strong v-text="modeTypeLabel"></strong> items and data.</p>
                        
                        <template #footer>
                            <Button size="sm" variant="muted" class="entry-mode-switcher-popover-button" @click.prevent="handleConfirmDelete">Yes, Delete</Button>
                            <Button size="sm" class="entry-mode-switcher-popover-button" @click.prevent="handlePopoverClose">Cancel</Button>
                        </template>
                    </PopoverModalContent>
                </PopoverModal>
            </div> -->

            <DeleteButtonWithConfirmation
                v-if="canDelete"
                :label="modeTypeLabel"
                @confirm="handleConfirmDelete"
                />
            
            <Button
                tabindex="-1"
                v-if="showCloseButton"
                @click="handleClose"
                v-text="closeButtonText"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import Button from '../elements/Button.vue';
import EntryModeSwitcher from '../situations/EntryModeSwitcher.vue';
import type { SituationEntryMode } from '../situations/EntryModeSwitcher.vue';
import type { Props as ButtonProps } from '../elements/Button.vue';
import type { Ref } from 'vue';
import DeleteButtonWithConfirmation from '../elements/DeleteButtonWithConfirmation.vue';

/** The header row for modals and popovers */
defineOptions();

export type ModalHeaderButton = ButtonProps & {
    onClick: () => void
};

export interface ModalHeaderRowProps {
    title?: string;
    subTitle?: string;
    showCloseButton?: boolean;
    closeButtonText?: string;
    showBackButton?: boolean;

    activeMode?: 'simple' | 'standard' | null;
    showModeSwitcher?: boolean;
    modeTypeLabel?: string | null;

    canDelete?: boolean;
    // otherButtons?: ModalHeaderButton[];
}

const props = withDefaults(defineProps<ModalHeaderRowProps>(), {
    showCloseButton: true,
    showModeSwitcher: true,
    closeButtonText: 'Done'
})

const emit = defineEmits<{
    back: [],
    close: [],
    delete: [],
    entryModeChange: [SituationEntryMode, boolean]
}>()

const deletePopoverIsOpen = ref(false);
function handlePopoverToggle() {
    deletePopoverIsOpen.value = !deletePopoverIsOpen.value;
}
function handlePopoverShow() {
    deletePopoverIsOpen.value = true;
}

function handlePopoverClose() {
    deletePopoverIsOpen.value = false;
}

function handleConfirmDelete() {
    emit('delete');
}

function handleBack() {
    emit('back')
}

function handleClose() {
    emit('close')
}

function handleEntryModeChange(newMode: SituationEntryMode, useAsDefault: boolean) {
    emit('entryModeChange', newMode, useAsDefault);
}

</script>

<style lang="scss">
@import "./ModalHeaderRow";
</style>
