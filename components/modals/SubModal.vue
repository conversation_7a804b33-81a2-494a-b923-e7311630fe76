<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="sub-modal">
        <div class="sub-modal-header">
        <div class="sub-modal-header-title-container">
            <span class="sub-modal-header-title-container-title" v-html="title"></span>
        </div>
        <div class="sub-modal-header-buttons">
            <!-- <SvgIcon :shape="search" width="18" height="18" /> -->
            <SvgIcon :shape="close" width="18" height="18" @click.prevent="handleModalClose" />
        </div>
        </div>

        <div class="sub-modal-content">
            <slot />
        </div>

        <div v-if="showFooter" class="sub-modal-footer">
            <Button variant="muted" @click.prevent="handleCancel">Cancel</Button>
            <Button variant="primary" @click.prevent="handleConfirm" v-text="confirmText"></Button>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from "vue";
import type { Ref } from "vue";
import type { TSvgIconShapeKey } from "../images/SvgIconShapes";
import Modal from "../modals/Modal.vue";

const search: TSvgIconShapeKey = "search";
const close: TSvgIconShapeKey = "close";
import SvgIcon from "../images/SvgIcon.vue";
import Button from "../elements/Button.vue";

/** TODO: Description of this component */
defineOptions();

export interface SubModalProps {
    isOpen: boolean;
    title: string;
    showFooter?: boolean;
    confirmText?: string;
}
const props = withDefaults(defineProps<SubModalProps>(), {
    showFooter: true,
    confirmText: "Confirm"
});

const emit = defineEmits<{
    close: [];
    confirm: [];
}>();

function handleModalClose() {
    emit("close");
}
function handleCancel() {
    emit("close");
}
function handleConfirm() {
    emit("confirm");
}
</script>

<style lang="scss">
@import "./SubModal";
</style>
