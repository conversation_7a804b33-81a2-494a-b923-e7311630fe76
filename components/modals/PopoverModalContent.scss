@import "/assets/css/mixins";

.popover-modal-content {
    width: 400px;
    max-height: 100dvh;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: flex-start;
    .popover-modal-content-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex: none;
        padding: 20px;
        gap: 20px;

        border-bottom: 1px solid rgba(var(--color-blue-grey-rgb), .2);

        .big-icon {
            flex:none;
            color: var(--color-blue-grey);
        }

        h5 {
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 1.45em;

            color: var(--color-white);

            em {
                color: var(--color-grey);
                font-style: normal;
            }
        }

        .popover-modal-close {
            height: 24px;
            width: 24px;
            flex: none;
            margin-left: auto;
        }
    }

    .checkbox-group {
        flex: 1 1 auto;
        overflow: scroll;
    }

    .popover-modal-content-footer {
        flex: none;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        gap: 20px;
        
        border-top: 1px solid rgba(var(--color-blue-grey-rgb), .2);

        > .button {
            flex: 1 1 50%;
        }
    }
    
}
