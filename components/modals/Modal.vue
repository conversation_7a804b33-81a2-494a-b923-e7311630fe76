<template>
    <TransitionRoot as="template" :show="isOpen ?? false">
        <Dialog class="modal" @close="handleClose">
            <TransitionChild
                as="template"
                enter="modal-overlay-enter"
                enter-from="modal-overlay-enter-from"
                enter-to="modal-overlay-enter-to"
                leave="modal-overlay-leave"
                leave-from="modal-overlay-leave-from"
                leave-to="modal-overlay-leave-to"
            >
                <div class="modal-overlay" />
            </TransitionChild>

            <div class="modal-dialog-wrapper">
                <div class="modal-dialog">
                    <TransitionChild
                        as="template"
                        enter="modal-dialog-panel-enter"
                        enter-from="modal-dialog-panel-enter-from"
                        enter-to="modal-dialog-panel-enter-to"
                        leave="modal-dialog-panel-leave"
                        leave-from="modal-dialog-panel-leave-from"
                        leave-to="modal-dialog-panel-leave-to"
                    >
                        <DialogPanel class="modal-dialog-panel">
                            <div class="modal-header" v-if="$slots.header">
                                <slot name="header" />
                            </div>

                            <div class="modal-content">
                                <slot />
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>

<script setup lang="ts">
import { Dialog, DialogPanel, TransitionRoot, TransitionChild } from '@headlessui/vue';

/** A simple modal component */
defineOptions();

defineProps<{
    isOpen?: boolean | null;
}>();

function handleClose() {
    emit('close');
}

const emit = defineEmits<{
    close: [];
}>();
</script>

<style lang="scss">
@import './Modal';
</style>
