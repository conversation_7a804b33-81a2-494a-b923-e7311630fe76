@import '/assets/css/mixins';
.paystub-settings-modal {
    background-color: rgba(13, 24, 42, 1);
    border-radius: 4px;
    padding: 24px;
    width: 340px;
    color: #fff;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.32);
    z-index: 10001;

    .modal-content {
        padding: 20px;
    }

    .modal-content {
        width: 400px;
    }

    .paystub-settings-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 1.25em;
    }

    .close-btn {
        background: none;
        border: none;
        color: var(--color-grey);
        font-size: 24px;
        cursor: pointer;
        line-height: 1;
        &:hover {
            color: var(--color-white);
        }

    }

    .section-label {
        display: block;
        background: RGB(25 36 53);
        padding-left: 12px;
        color: white;
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 24px;
        word-wrap: break-word;
    }

    .section-input {
        width: 100%;
        padding: 8px 12px;
        outline: none;
        border-bottom: 1px solid #fff;
        background: RGB(25 36 53);
        color: #fff;
        margin-bottom: 8px;
        font-size: 15px;
    }

    .paystub-settings-modal-footer {
        display: flex;
        justify-content: space-between;
        gap: 12px;
        margin-bottom: 8px;
        padding-top: 20px;

        > .button {
            flex: 1;
        }

        .cancel-btn {
            flex: 1;
            background: #162447;
            color: #b0bed8;
            border: none;
            border-radius: 6px;
            padding: 10px 0;
            font-weight: 600;
            cursor: pointer;
            font-size: 15px;
        }

        .save-btn {
            flex: 1;
            background: #3a7afe;
            color: #fff;
            border: none;
            border-radius: 6px;
            padding: 10px 0;
            font-weight: 600;
            cursor: pointer;
            font-size: 15px;
        }
    }

    .delete-btn {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        width: 100%;
        background: none;
        border: none;
        color: #b0bed8;
        font-size: 14px;
        margin-top: 15px;
        opacity: 0.5;

        &:hover {
            opacity: 0.8;
        }
    }
}

.headlessui-portal-root,
.modal-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
    z-index: 10000;
    background: rgba(0, 0, 0, 0.3);
}

.checkbox-input checkbox-input-s-sm {
    z-index: 10000;
}

.section-type-group {
    display: flex;
    flex-direction: column;
    background-color: rgba(255, 255, 255, 0.05);
    margin-bottom: 4px;
    color: var(--primary-white, #e9edf1);
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;

    &-type {
        display: flex;
        flex-direction: column;
        gap: 8px;
        padding: 20px;
        padding-top: 10px;
    }
}

.section-type-group label {
    display: flex;
    align-items: center;
}

.section-type-group-label {
    padding-top: 20px;
    padding-left: 20px;
}

.section-type-group label input[type='radio'] {
    margin-right: 10px;
}

.checkbox-input-option-label {
    color: var(--primary-white, #e9edf1);
    font-size: 14px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
}

.paystub-settings-modal-sections {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    gap: 2px;
}

.paystub-settings-modal-section {
    background: rgba(255,255,255,.05);
    padding: 20px;
    .paystub-settings-modal-section-label {
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 1.45em;
        color: var(--color-white);
    }

    .paystub-settings-modal-section-content {
        padding-top: 10px;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;
        gap: 20px;
        > .checkbox-input:first-child {
            margin-top: 10px;
        }
    }
}