@import "/assets/css/mixins";

.filters-modal {
    .modal-dialog-panel {
        width: 600px;
    }
    .modal-content {
        padding: 0 24px;
    }
    .filters-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
        h3 {
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
        }
        .filters-modal-header-close {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-blue-grey);
            transition: color 0.2s ease-in-out;
            margin-right: -16px;
            position: relative;
            &:hover {
                color: var(--color-white);
            }
        }
    }

    .filters-modal-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0 16px;
        .filters-modal-clear {
            font-weight: 400;
            font-size: 14px;
            line-height: 18px;
            color: var(--color-grey);
            transition: color 0.2s ease-in-out;
            &:hover {
                color: var(--color-white);
            }
        }

        .filters-modal-confirm {

        }
    }

    .filters-modal-content {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }
    .filters-modal-filter-group {
        padding: 20px;
        background: rgba(var(--color-blue-grey-rgb), 0.2);

        h5 {
            font-weight: 700;
            font-size: 14px;
            line-height: 1.45em;
            color: var(--color-grey);
            margin-bottom: 5px;
        }
        
        .filters-modal-filter-group-description {
            font-weight: 400;
            font-size: 14px;
            line-height: 1.45em;
            color: var(--color-blue-grey);
        }
    }

    .filters-modal-filter-group-checkboxes {
        padding-top: 24px;
        display: flex;
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
        padding-left: 5px;
    }

    .filters-modal-filter-group-slider {
        margin-top:10px;
    }
}
