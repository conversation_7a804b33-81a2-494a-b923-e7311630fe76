@import "/assets/css/mixins";

.sub-modal {
    display: flex;
    flex-direction: column;
    // background-color: var(--color-primary-black);
    // border-radius: 10px;
    // padding: 20px;
    // box-shadow: 4px 4px 10px 0px rgba(0, 0, 0, 0.2);
    // width: 100%;
    // height: 100%;
    // gap: 20px;

    .modal-dialog-panel {
        background-color: var(--color-primary-black);
        border-radius: 10px;
        width: 100%;
        max-width: 600px;
    }

    .modal-content {
        display: flex;
        flex-direction: column;
        padding: 20px;
        box-shadow: 4px 4px 10px 0px rgba(0, 0, 0, 0.2);
        width: 100%;
        height: 100%;
        gap: 20px;        
    }

  &-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 10px;
    align-items: center;
  }

  &-header-buttons {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;
    color: #57719c;

    svg {
      cursor: pointer;
    }
  }

  &-header-title-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    min-width: 200px;
    gap: 5px;
  }
  &-header-title-container-title {
    color: #57719c;
    font-size: 14px;
    font-family: "Oxygen", sans-serif;
    font-weight: 700;
    line-height: 24px;
    word-wrap: break-word;
    strong {
        color: #e9edf1;    
        font-weight: 700;
    }
  }

  &-content {
    display: flex;
    flex-direction: column;
  }

  &-footer {
    display: flex;
    flex-direction: row;
    gap: 10px;
    align-items: center;

    button {
      width: 100%;
    }
  }
}
