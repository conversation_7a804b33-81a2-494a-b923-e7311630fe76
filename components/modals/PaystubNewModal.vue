<template>
    <Modal class="paystub-new-modal" :is-open="isOpen" @close="handleModalClose">
        <ModalHeaderRow title="Add Paystub" close-button-text="Cancel" @close="handleModalClose" />

        <div class="paystub-new-modal-errors" v-if="serverError">
            <p v-html="serverError"></p>
        </div>

        <div class="paystub-new-modal-options" v-if="!showUploadForm">
            <button class="paystub-new-modal-option" @click.prevent="handleSelectUpload">
                <span class="paystub-new-modal-option-icon">
                    <SvgIcon shape="upload" width="45" height="45" />
                </span>
                <span class="paystub-new-modal-option-label">
                    <strong>Upload paystub</strong>
                    <span>Autofill your income using a recent paystub</span>
                </span>
                <span class="paystub-new-modal-option-arrow">
                    <SvgIcon shape="long-arrow" width="24" height="24" />
                </span>
            </button>

            <button class="paystub-new-modal-option" @click.prevent="handleSelectManual">
                <span class="paystub-new-modal-option-icon">
                    <SvgIcon shape="form" width="45" height="45" />
                </span>
                <span class="paystub-new-modal-option-label">
                    <strong>I only have a printed copy</strong>
                    <span>Add each field in your paystub manually</span>
                </span>
                <span class="paystub-new-modal-option-arrow">
                    <SvgIcon shape="long-arrow" width="24" height="24" />
                </span>
            </button>
        </div>

        <div class="paystub-new-modal-dropbox-wrapper" v-if="showUploadForm">
            <div class="paystub-new-modal-loading" :class="{ 'paystub-new-modal-loading-visible': uploadIsProcessing }">
                <h4>Processing your paystub</h4>
                <p>Please do not close this window.</p>
                <div class="paystub-new-modal-loading-animation-wrapper">
                    <LoadingAnimation size="fill" />
                </div>
            </div>
            <FileUpload
                name="file"
                :allow-multiple="false"
                @complete="handleOcrComplete"
                @uploadcomplete="handleUploadComplete"
                @error="handleUploadError"
            />
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import Modal from '~/components/modals/Modal.vue';
import FileUpload from '~/components/forms/FileUpload.vue';
import { type Paystub, isPaystub } from '~/types/paystub';
import LoadingAnimation from '~/components/animation/LoadingAnimation.vue';
import ModalHeaderRow from './ModalHeaderRow.vue';
import SvgIcon from '../images/SvgIcon.vue';

/** A modal for uploading a paystub to processed through OCR */
defineOptions();

export interface PaystubNewModalProps {
    isOpen?: boolean;
}
const props = withDefaults(defineProps<PaystubNewModalProps>(), {});

const emit = defineEmits<{
    new: [paystub?: Paystub];
    close: [];
}>();

const showUploadForm = ref(false);
const uploadIsProcessing = ref(false);
const uploadIsComplete = ref(false);
const serverError = ref();
function handleModalClose() {
    emit('close');
    setTimeout(() => {
        showUploadForm.value = false;
        uploadIsProcessing.value = false;
        uploadIsComplete.value = false;
    }, 500);
}

function handleSelectUpload() {
    console.log('upload');
    showUploadForm.value = true;
}

function handleSelectManual() {
    console.log('manual');
    emit('new');
}

function handleOcrComplete(response: { paystub: unknown }) {
    const paystub = response.paystub;
    if (!isPaystub(paystub)) return;

    uploadIsProcessing.value = false;
    uploadIsComplete.value = true;
    emit('new', paystub);
}

function handleUploadComplete() {
    uploadIsProcessing.value = true;
}

function handleUploadError(error: unknown) {
    console.log('handleUploadError', error);
    uploadIsProcessing.value = false;
    showUploadForm.value = false;
    uploadIsComplete.value = false;

    serverError.value = 'There was a problem with your upload.<br />Please try again, or add it manually.';
}
</script>

<style lang="scss">
@import './PaystubNewModal';
</style>
