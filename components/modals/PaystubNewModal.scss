@import "/assets/css/mixins";

.paystub-new-modal {
    .modal-dialog-panel {
        width: 80dvw;
        max-width: 450px;
    }
    .paystub-new-modal-dropbox-wrapper {
        position: relative;
    }

    .modal-header-row {
        margin-bottom: 30px;
    }

    .paystub-new-modal-options {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
    }

    .paystub-new-modal-option {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
        gap: 20px;
        padding: 20px;

        border-radius: 4px;

        background: var(--color-grey);
        transition: background .2s ease-in-out;

        &:hover {
            background: var(--color-white);
            --paystub-new-modal-option-arrow-transform: 5px;
        }
    }

    .paystub-new-modal-option-icon {
        width: 60px;
        height: 60px;
        border-radius: 50px;
        background: var(--color-grey-section);
        display: flex;
        align-items: center;
        justify-content: center;
        flex: none;
    }

    .paystub-new-modal-option-label {
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.45em;

        flex: 1 1 auto;

        color: var(--color-dark-grey);

        text-align: left;


        strong {
            font-weight: 700;
            display: block;
        }
    }

    .paystub-new-modal-option-arrow {
        color: var(--color-black);
        width: 24px;
        height: 24px;
        flex:none;
        transition: transform .2s ease-in-out;
        transform: translateX(var(--paystub-new-modal-option-arrow-transform, 0));
    }

    --paystub-loading-animation-transition: .2s ease-in-out;

    .paystub-new-modal-loading {
        transition: opacity var(--paystub-loading-animation-transition),
                    visibility var(--paystub-loading-animation-transition);

        background: #263550;
        position: absolute;
        z-index: 10;
        top: 2px;
        left: 2px;
        width: calc(100% - 4px);
        height: calc(100% - 4px);
        border-radius: 8px;

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 20px;

        opacity: 0;
        visibility: hidden;

        h4 {
            font-size: 18px;
            font-weight: 700;
            line-height: 1.25em;
            text-align: center;
            color: var(--color-white);
        }

        p {
            font-size: 12px;
            line-height: 1.25em;
            text-align: center;
            font-weight: 400;
            color: var(--color-white);
            margin-top: -15px;
        }

        .paystub-new-modal-loading-animation-wrapper {
            width: auto;
            max-width: 100px;

        }
    }

    &.paystub-new-modal-processing, .paystub-new-modal-loading.paystub-new-modal-loading-visible {
        opacity: 1;
        visibility: visible;
    }

    .paystub-new-modal-errors {
        max-width: 100%;

        color: var(--color-red);
        background: rgba(var(--color-red-rgb), 0.1);
        //margin-bottom: 4px;
        padding: 8px 10px;
        border-radius: 4px;
        margin-bottom: 10px;
        border: 1px solid var(--color-red);
        p {
            white-space: wrap;
            font-size: 12px;
        }
    }
}
