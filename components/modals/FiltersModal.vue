<!--suppress RequiredAttributes -->
<template>
    <Modal :is-open="isOpen" @close="handleClose" class="filters-modal">
        <div class="filters-modal-header" ref="abcdefg">
            <h3 class="filters-modal-header-title">Filters</h3>
            <button class="filters-modal-header-close" @click.prevent="handleClose">
                <SvgIcon shape="close" width="24" height="24" />
            </button>
        </div>
        <div class="filters-modal-content">
            <div class="filters-modal-filter-group" v-for="group in groups" :key="group.title">
                <h5 v-text="group.title"></h5>
                <p class="filters-modal-filter-group-description" v-text="group.description"></p>
                <template v-if="group.type === 'range'">
                    <RangeSlider
                        class="filters-modal-filter-group-slider"
                        v-bind="group.data"
                        :mode="group.mode"
                        @change="payload => handleSliderChange(group, payload)"
                    />
                </template>

                <template v-else>
                    <div class="filters-modal-filter-group-checkboxes">
                        <CheckboxInput
                            size="xs"
                            v-for="checkItem in group.data"
                            :label="checkItem.label"
                            :name="checkItem.name"
                            v-model="checkItem.checked"
                        />
                    </div>
                </template>
            </div>
        </div>
        <div class="filters-modal-footer">
            <button class="filters-modal-clear" @click.prevent="handleClearAll">Clear all</button>
            <Button class="filters-modal-confirm" @click.prevent="handleConfirm" :disabled="!hasFiltersChanged">
                Apply filters
            </Button>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import Modal from '../modals/Modal.vue';
import Button from '../elements/Button.vue';
import SvgIcon from '../images/SvgIcon.vue';
import CheckboxInput from '../forms/CheckboxInput.vue';
import RangeSlider from '../forms/RangeSlider.vue';
import type { RangeSliderProps } from '../forms/RangeSlider.vue';

/** The modal for adjusting filters */
defineOptions();

export interface BaseFiltersModalFilterGroup {
    name: string;
    title: string;
    description: string;
    type: 'range' | 'checkboxes';
    data:
        | RangeSliderProps
        | {
              label: string;
              name: string;
              checked: boolean;
          }[];
}

type RangeFilterGroup = BaseFiltersModalFilterGroup & {
    type: 'range';
    data: RangeSliderProps;
    mode?: 'year' | 'currency' | null;
};

type CheckboxesFilterGroup = BaseFiltersModalFilterGroup & {
    type: 'checkboxes';
    data: {
        label: string;
        name: string;
        checked: boolean;
    }[];
};

export interface FiltersModalProps {
    isOpen: boolean;
    filterGroups: FiltersModalFilterGroup[];
}

export type FiltersModalFilterGroup = RangeFilterGroup | CheckboxesFilterGroup;

const props = defineProps<FiltersModalProps>();

const groups = ref<FiltersModalFilterGroup[]>([]);
const initialGroups = ref<FiltersModalFilterGroup[]>([]);

const hasFiltersChanged = computed(() => {
    if (!initialGroups.value.length) return false;

    return groups.value.some((group, groupIndex) => {
        const initialGroup = initialGroups.value[groupIndex];

        if (group.type === 'range' && initialGroup.type === 'range') {
            return group.data.bottom !== initialGroup.data.bottom || group.data.top !== initialGroup.data.top;
        } else if (group.type === 'checkboxes' && initialGroup.type === 'checkboxes') {
            return group.data.some((item, itemIndex) => item.checked !== initialGroup.data[itemIndex].checked);
        }
        return false;
    });
});

const emit = defineEmits<{
    close: [];
    confirm: [filters: FiltersModalFilterGroup[]];
}>();

function handleSliderChange(group: FiltersModalFilterGroup, payload: { bottom: number; top: number }) {
    if (group.type !== 'range') return;
    group.data.bottom = payload.bottom;
    group.data.top = payload.top;
}

function handleClose() {
    emit('close');
}
function handleConfirm() {
    emit('confirm', groups.value);
}

function handleClearAll() {
    groups.value.forEach(group => {
        if (group.type === 'range') {
            group.data.bottom = group.data.min;
            group.data.top = group.data.max;
        } else if (group.type === 'checkboxes') {
            group.data.forEach(item => {
                item.checked = true;
            });
        }
    });
    emit('confirm', groups.value);
}

watch(
    () => props.filterGroups,
    newVal => {
        console.log('newVal', newVal);
        groups.value = JSON.parse(JSON.stringify(props.filterGroups));
        initialGroups.value = JSON.parse(JSON.stringify(props.filterGroups));
    },
);

watch(
    () => props.isOpen,
    newVal => {
        if (newVal) {
            console.log(props.filterGroups);
            groups.value = JSON.parse(JSON.stringify(props.filterGroups));
            initialGroups.value = JSON.parse(JSON.stringify(props.filterGroups));
        }
    },
);
</script>

<style lang="scss">
@import './FiltersModal';
</style>
