import PopoverModal from './PopoverModal.vue';
import Button from '../elements/Button.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof PopoverModal> = {
    component: PopoverModal,
    tags: ['autodocs'],

    argTypes: {
        default: {},
        isOpen: {
            control: { type: 'boolean' },
            description: 'Whether the popover is open or not',
        },
    },

    //👇 Emitted events and default args
    args: {
        onClose: fn(),
        default: `<div style="width:300px;height:200px;display:flex;align-items:center;justify-content:center">
            I'm a modal, howdy, howdy, howdy
        </div>`,
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots)
    render: (args) => ({
        components: { PopoverModal, Button },
        setup() {
            const isOpen = ref(false);
            const toggleButton = ref();
            return { args, isOpen, toggleButton };
        },
        template: `
            <Button @click="isOpen = !isOpen" ref="toggleButton" id="sb-toggle-button">Show Modal</Button>
            <PopoverModal v-bind="args" trigger-element="sb-toggle-button" :is-open="isOpen" @close="isOpen = false">
                ${args.default}
            </PopoverModal>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof PopoverModal>;

export const Default: Story = {};
