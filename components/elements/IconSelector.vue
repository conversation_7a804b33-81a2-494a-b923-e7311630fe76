<template>
    <div class="icon-selector" :class="{ 'icon-selector-selected': selectedState, 'icon-selector-popover-open': popoverIsOpen, 'icon-selector-popover-read-only': readOnly }">
        <label type="button" :id="buttonId" class="icon-selector-button" :for="elementId" @click="handleClick"
            ref="selectorButton">
            <span class="icon-selector-icon">
                <span class="icon-selector-check">
                    <svg width="23" height="18" viewBox="0 0 23 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M7.29493 13.8069L1.85309 8.36503L0 10.2051L7.29493 17.5L22.9549 1.84004L21.1148 0L7.29493 13.8069Z"
                            fill="currentColor" />
                    </svg>
                </span>
                <BigIcon :shape="props.iconShape" />
            </span>

            <span class="icon-selector-label" v-text="label"></span>

            <div class="icon-selector-dot-nav">
                <span class="icon-selector-dot" v-for="item in dotNavItems"
                    :class="{'icon-selector-dot-active': item}"></span>
            </div>

            <input type="checkbox" class="icon-selector-checkbox" :id="elementId" v-model="model" />
        </label>

        <IconSelectorPopover
            :trigger-element="buttonId"
            :is-open="popoverIsOpen"
            :options="subOptions"
            :heading-icon-shape="iconShape"
            @save="handlePopoverSave"
            @close="handlePopoverClose"
            v-if="subOptions" 
        />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, onMounted } from 'vue';
import type { Ref } from 'vue';
import BigIcon from '../images/BigIcon.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import type { CheckboxGroupOption } from '../forms/CheckboxGroup.vue';
import IconSelectorPopover from './IconSelectorPopover.vue';

const model = defineModel()
// const selectedOptions = defineModel<string[]>('selected-options');

/** A toggleable block with a large icon and optional name/description. Can optionally trigger a popover with additional fields */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'icon-selector-' + componentId);
const buttonId = computed(() => 'icon-selector-button-' + componentId);

const emit = defineEmits<{
    toggle: [],
    // popoverSave: [options: CheckboxGroupOption[]]
    popoverSave: [options: string[]]
}>()

export interface IconSelectorSubOptionProp {
    heading?: string;
    headingIconShape?: TBigIconShapeKey;
    options: CheckboxGroupOption[];
}

export interface IconSelectorProps {
    iconShape: TBigIconShapeKey;
    label: string;
    
    readOnly?: boolean;
    readOnlySelected?: boolean;

    subOptions?: IconSelectorSubOptionProp | null;
}

const props = defineProps<IconSelectorProps>();

const isSelected = computed(() => model.value || props.subOptions?.options.some((o) => o.value));
const selectedState = computed(() => isSelected.value || (props.readOnly && props.readOnlySelected));

const selectorButton = ref<HTMLDivElement>()

const popoverIsOpen = ref(false);

const dotNavItems = computed(() => 
    props.subOptions?.options?.map((o) => o.value).sort().reverse()
);

function handlePopoverClose() {
    popoverIsOpen.value = false;
}

// function handlePopoverSave(options: CheckboxGroupOption[]) {
function handlePopoverSave(options: string[]) {
    // const selected = options.filter((o) => o.value).map((o) => o.name) ?? [];
    emit('popoverSave', options)
    handlePopoverClose();
}

function handleClick(e: MouseEvent) {
    if (props.subOptions) {
        e.preventDefault();
        e.stopPropagation();

        popoverIsOpen.value = !popoverIsOpen.value;
    }
}

// onMounted(() => {
//     if (props.subOptions?.options?.length) {
//         selectedOptions.value = props.subOptions.options.filter((o) => o.value).map((o) => o.name);
//         model.value = (selectedOptions.value?.length) ? true : false;
//     }
// })

</script>

<style lang="scss">
@import "./IconSelector";
</style>
