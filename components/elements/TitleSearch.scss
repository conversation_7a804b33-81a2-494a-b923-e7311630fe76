@import '/assets/css/mixins';

.title-search {
    display: flex;
    flex-direction: row;
    align-items: center;
    border: 2px solid transparent;
    border-radius: 4px;
    transition: border-color 0.2s;
    width: 100%;
    color: var(--primary-white, #e9edf1);
    font-size: 18px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 30px;
    word-wrap: break-word;
    padding: 0 10px;

    &.hover {
        cursor: pointer;
        color: var(--primary-white, #e9edf1);
        background-color: rgb(36 49 70);
        padding: 0 10px;
    }
    &.active {
        background-color: rgb(36 49 70);
        font-size: 18px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 30px;
        word-wrap: break-word;
        padding: 0 10px;

        & > svg {
            fill: #fff;
        }

        &::placeholder {
            color: rgb(85, 95, 112);
            opacity: 0.5;
        }
    }

    .title {
        font-weight: 700;
        color: #e9edf1;
        margin-right: 12px;
    }
    .icon {
        font-size: 1.2em;
        cursor: pointer;
        user-select: none;
    }
    .search-input {
        flex: 1;
        background: transparent;
        border: none;
        color: #e9edf1;
        font-size: 1em;
        outline: none;
        margin-right: 8px;
    }

    .icon.search-icon {
        margin-left: auto;
    }
}
