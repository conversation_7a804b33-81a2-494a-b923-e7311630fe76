import SparkKpi from './SparkKpi.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof SparkKpi> = {
    component: SparkKpi,
    tags: ['autodocs'],

    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SparkKpi>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        label: 'Net Worth',
        value: 15138393
    }
};
