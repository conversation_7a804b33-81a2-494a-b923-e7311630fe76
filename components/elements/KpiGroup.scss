@import "/assets/css/mixins";

.kpi-group {
    display: flex;
    padding: 20px;
    align-items: stretch;
    justify-content: flex-start;
    gap: 20px;

    border-radius: 4px;
    background: var(--color-grey-section);

    .kpi-group-meta {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        gap: 15px;
        flex: none;
    }

    .kpi-group-label {
        color: var(--color-grey);
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 1.125em;
        letter-spacing: 1px;
        text-transform: uppercase;
    }

    .kpi-group-kpis {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
        flex: none;
        dl {
            dd {
                color: var(--color-white);
                font-size: 18px;
                font-weight: 700;
                line-height: 1.125em;
            }
            dt {
                color: var(--color-blue-grey, #57719C);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 1.25em;
            }
            dd + dt {
                margin-top: 6px;
            }

            &:not(:first-child) {
                position: relative;
                padding-left: 42px;
                &:before {
                    content: "";
                    display: block;
                    width: 22px;
                    height: 46px;
                    background-image: url("data:image/svg+xml,%3Csvg width='22' height='46' viewBox='0 0 22 46' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath opacity='0.5' d='M21 1L0.999998 45' stroke='%2357719C'/%3E%3C/svg%3E%0A");
                    background-size:contain;
                    background-repeat: no-repeat;
                    background-position: center;
                    position: absolute;
                    top: 50%;
                    margin-top: -23px;
                    left:0;
                }
            }
        }
    }

    .kpi-group-disclaimer {
        color: var(--color-grey);
        font-size: 12px;
        font-weight: 400;
        line-height: 1.25em;
        margin-top: auto;
        opacity: .5;
    }

    .kpi-group-slot {
        flex: auto;
        height: 100%;
        position: relative;
        width: 100%;
        
        min-height: 20px;
        .line-chart {
            margin-bottom: -18px;
        }
        .tree-chart {
            max-height: 200px;
        }

        .kpi-group-placeholder {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            min-height: 115px;
            height: 100%;
            padding: 10px;
            gap: 10px;
            color: var(--color-blue-grey);
            background: rgba(4, 7, 20, 0.20);

            text-align: center;
            font-size: 12px;
            font-weight: 400;
            line-height: 14px;
            letter-spacing: -0.25px;
        }
    }

    &.kpi-group-clickable {
        cursor: pointer;
    }
}
