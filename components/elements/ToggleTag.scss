@import "/assets/css/mixins";

.toggle-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex-direction: row;
  border-radius: 4px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  font-size: 12px;
  font-weight: 700;
  line-height: 14px;
  word-wrap: break-word;
  color: rgba(255, 255, 255, 0.7);
  gap: 10px;
  cursor: pointer;

  &-count {
    color: #88b3ff;
  }

  // Default state
  & {
    transition: background-color 0.3s ease, border 0.3s ease;
  }

  // Hover state
  &:hover {
    background-color: rgba(255, 255, 255, 0.15);
  }

  // Selected state
  &.selected {
    background-color: #57719c;
    color: #ffffff;

    .toggle-tag-value {
      color: #88b3ff;
    }
  }
}
