import IconSelector from './IconSelector.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof IconSelector> = {
    component: IconSelector,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        
    },

    //👇 Emitted events that we want to listen for 
    args: {
        onToggle: fn(),
        onPopoverSave: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof IconSelector>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3372-25862&mode=dev',
        },
    },
    args: {
        iconShape: 'bungalow',
        label: 'Real Estate',
        modelValue: false,
    }
};

export const WithPopover: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3372-25862&mode=dev',
        },
    },
    args: {
        iconShape: 'bungalow',
        label: 'Real Estate',
        modelValue: false,
        subOptions: {
            heading: 'What type of <em>real estate</em> do you own?',
            options: [
                {
                    label: 'Residential',
                    description: "Single-Family, Duplex, Triplex, Condos, Townhomes, Apartments, etc.",
                    name: 'residential',
                    value: false,
                },
                {
                    label: 'Commercial',
                    description: "Office Buildings, Retail Spaces, Warehouses, Industrial, etc.",
                    name: 'commercial',
                    value: true,
                },
                {
                    label: 'Special purpose',
                    description: "Hospitals, Churches, Government, etc.",
                    name: 'special-purpose',
                    value: false,
                },
                {
                    label: 'Land',
                    description: "Agriculture, Undeveloped land, Developed land",
                    name: 'land',
                    value: false,
                }
            ]
        }
    }
};
