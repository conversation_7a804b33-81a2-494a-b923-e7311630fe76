<template>
  <div
    class="toggle-tag"
    :class="{ selected: isSelected }"
  >
    <span v-text="label"></span>
    <span
        class="toggle-tag-count"
        v-text="count"
        v-if="count"
    ></span>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// Props for the component
export interface ToggleTagProps {
    label: string;
    count?: string|number|null;
    isSelected: boolean;
}

// Default props
const props = withDefaults(defineProps<ToggleTagProps>(), {
    label: "Default Tag",
    count: "0",
});
</script>

// Inline CSS
<style lang="scss">
.toggle-tag {
}
</style>
// Imported CSS
<style lang="scss">
@import "./ToggleTag";
</style>
