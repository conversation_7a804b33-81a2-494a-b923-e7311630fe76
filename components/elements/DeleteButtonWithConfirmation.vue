<template>
    <div class="delete-button-with-confirmation">
        <Button
            class="delete-button-with-confirmation-button"
            tabindex="-1"
            variant="muted"
            :icon-only="true"
            :svg-icon="{
                shape: 'trash'
            }"
            @click="handlePopoverToggle"
        />
        <PopoverModal
            :dynamic-placement="false"
            :with-overlay="false"
            :is-open="popoverIsOpen"
            @close="handlePopoverClose"
        >
            <PopoverModalContent>
                <template #header>
                    <h5>
                        <em>Are you sure you want to delete</em> <span v-text="label"></span> <em>?</em>
                    </h5>
                    <button class="popover-modal-close" @click.prevent="handlePopoverClose">
                        <SvgIcon shape="close" />
                    </button>
                </template>

                <p class="delete-button-with-confirmation-message">This will permenantly remove all of your <strong v-text="label"></strong> items and data.</p>
                
                <template #footer>
                    <Button size="sm" variant="muted" class="entry-mode-switcher-popover-button" @click.prevent="handleConfirmDelete">Yes, Delete</Button>
                    <Button size="sm" class="entry-mode-switcher-popover-button" @click.prevent="handlePopoverClose">Cancel</Button>
                </template>
            </PopoverModalContent>
        </PopoverModal>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import Button from '../elements/Button.vue';
import PopoverModal from '../modals/PopoverModal.vue';
import PopoverModalContent from '../modals/PopoverModalContent.vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Ref } from 'vue';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'delete-button-with-confirmation-' + componentId);

export interface DeleteButtonWithConfirmationProps {
    label: string;
}
const props = defineProps<DeleteButtonWithConfirmationProps>();

const emit = defineEmits<{
    confirm: [];
}>()

const popoverIsOpen = ref(false);
function handlePopoverToggle() {
    popoverIsOpen.value = !popoverIsOpen.value;
}
function handlePopoverShow() {
    popoverIsOpen.value = true;
}

function handlePopoverClose() {
    popoverIsOpen.value = false;
}

function handleConfirmDelete() {
    emit('confirm');
}
</script>

<style lang="scss">
.delete-button-with-confirmation {
    // .modal-header-row-delete-popover-content {
    //     min-width:120px;
    //     display: flex;
    //     align-items: stretch;
    //     justify-content: center;
    //     flex-direction: column;
    //     gap: 1px;
    //     .modal-header-row-delete-popover-button {
    //         display: flex;
    //         align-items: center;
    //         justify-content: center;
    //         text-align: center;
    //         padding: 10px 20px;
    //         font-size: 14px;
    //         background: rgba(var(--color-blue-grey-rgb), .5);
    //         transition: background .3s ease;
    //         white-space: nowrap;
    //         &:hover {
    //             background: rgba(var(--color-blue-grey-rgb), .8);
    //         }
    //     }
    // }

    .delete-button-with-confirmation-button {
        height: 44px;
        width: 44px;
    }
    .popover-modal-dialog .popover-modal-outer-wrapper {
        transform-origin: right top;
        box-shadow: 0 0 12px 2px rgba(0, 0, 0, 0.15);
        left: auto;
        right: 0;
    }
    .delete-button-with-confirmation-message {
        padding: 20px;
        font-size: 14px;
        line-height: 1.45em;
        color: var(--color-white);
    }
    .popover-modal-content {
        // width: auto;

        
    }
}
</style>

