<template>
    <div class="bulk-action" :class="{ 'bulk-action--active': selected }" :id="elementId">
        <div class="bulk-action-label">{{ label }}</div>
        <div class="bulk-action-description">{{ description }}</div>
        <div class="bulk-action-action">{{ action }} <SvgIcon shape="long-arrow" /></div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';

/** Bulk action component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'bulk-action-' + componentId);

export interface BulkActionProps {
    foo: string;
    label: string;
    description: string;
    action: string;
    selected?: boolean;
}
const props = withDefaults(defineProps<BulkActionProps>(), {
    foo: 'bar',
    label: 'Change category',
    description: 'Move the selected expenses from their current category into a new category',
    action: 'Select new category',
    selected: false,
});

const emit = defineEmits<{
    change: [id: number];
}>();
</script>

<style lang="scss">
@import './BulkAction';
</style>
