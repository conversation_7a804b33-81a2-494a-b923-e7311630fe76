<template>
  <div class="sort-selector">
    <span class="sort-selector-label">Sort by</span>
    <span
      class="sort-selector-select"
      :class="{ active: isDropdownOpen }"
      @click="toggleDropdown"
    >
      {{ selectedOption }}
    </span>
    <div class="sort-selector-dropdown" v-if="isDropdownOpen">
      <div
        v-for="option in options"
        :key="option"
        class="sort-selector-option"
        @click="selectOption(option)"
      >
        {{ option }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";

// State for the dropdown
const isDropdownOpen = ref(false);

// Options for the dropdown
const options = ref(["Date added", "Net Worth", "Liabilities", "Expenses"]);

// Currently selected option
const selectedOption = ref("Date");

// Toggle dropdown visibility
function toggleDropdown() {
  isDropdownOpen.value = !isDropdownOpen.value;
}

// Handle option selection
function selectOption(option: string) {
  selectedOption.value = option;
  isDropdownOpen.value = false; // Close the dropdown
}
</script>

// Inline CSS
<style lang="scss">
.sort-selector {
}
</style>
// Imported CSS
<style lang="scss">
@import "./SortSelector";
</style>
