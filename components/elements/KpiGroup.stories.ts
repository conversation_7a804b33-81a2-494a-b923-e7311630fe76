import KpiGroup from './KpiGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';

const meta: Meta<typeof KpiGroup> = {
    component: KpiGroup,
    tags: ['autodocs'],

    render: (args) => ({
        components: { KpiGroup },
        setup() {
            return { args };
        },
        template: `
            <template v-if="args.default">
                <KpiGroup v-bind="args">
                    ${args.default}
                </KpiGroup>
            </template>
            <template v-else>
                <KpiGroup v-bind="args" />
            </template>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof KpiGroup>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=7170-32326&t=IYzeOGULwqDw4NcU-4',
        },
    },
    args: {
        label: 'Situations',
        kpis: [
            {
                label: 'TOTAL',
                value: 17
            },
            {
                label: 'Standard mode',
                value: 15,
            },
            {
                label: 'Simple mode',
                value: 2,
            },
        ]
    }
};

export const Currency: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=7170-32326&t=IYzeOGULwqDw4NcU-4',
        },
    },
    args: {
        label: 'Net Worth',
        kpis: [
            {
                label: 'Annually',
                value: 1354654,
                formatting: 'currency'
            },
            {
                label: 'Standard mode',
                value: 15,
                formatting: 'currency'
            },
            {
                label: 'Simple mode',
                value: 2,
                formatting: true
            },
        ]
    }
};

export const WithSlot: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=7170-32326&t=IYzeOGULwqDw4NcU-4',
        },
    },
    args: {
        label: 'Net Worth',
        kpis: [
            {
                label: 'Annually',
                value: 1354654,
                formatting: 'currency'
            },
            {
                label: 'Standard mode',
                value: 15,
                formatting: 'currency'
            },
            {
                label: 'Simple mode',
                value: 2,
                formatting: true
            },
        ],
        disclaimer: 'As of Jan 1, 2024',
        default: `
            <div class="kpi-group-placeholder">
                <span style="font-weight: light; font-size: 12px; color: rgba(255,255,255,.3)">Look at this graph</span>
            </div>
        `
    }
};
