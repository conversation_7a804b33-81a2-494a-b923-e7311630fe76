@import "/assets/css/mixins";

.icon-selector {
    width: 160px;
    height: 122px;

    --icon-selector-duration: .15s;
    --icon-selector-icon-color: var(--color-blue-grey);
    
    background: rgba(var(--color-white-rgb), 0.05);
    border-radius: 10px;
    color: var(--color-grey);

    transition: background var(--icon-selector-duration),
                color var(--icon-selector-duration);

    &:hover,&:not(.icon-selector-read-only) {
        background: rgba(var(--color-white-rgb), 0.1);
        color: var(--color-white);
        --icon-selector-icon-color: var(--color-grey);
    }

    .icon-selector-checkbox {
        display: none;
    }

    .icon-selector-button {
        width:100%;
        height:100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 10px;
        gap: 10px;
        cursor: pointer;
    }

    .icon-selector-icon {
        position: relative;
        
        .big-icon {
            color: var(--icon-selector-icon-color);
            transition: opacity var(--icon-selector-duration),
                        color var(--icon-selector-duration);
        }
    }

    .icon-selector-check {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 15px;
        color: var(--color-white);
        z-index: 5;
        opacity: 0;
        visibility: hidden;
        transform-origin: center bottom;
        transform: scale(.75) translateY(2px);
        transition: opacity var(--icon-selector-duration),
                    visibility var(--icon-selector-duration),
                    transform var(--icon-selector-duration);
        // background:rgba(255,0,0,.1);
    }

    .icon-selector-label {
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        text-align: center;

        user-select: none;
        
        // color: #97ABCC;
    }

    &.icon-selector-selected // , &.icon-selector-popover-open 
    {
        background: var(--color-blue-grey);
        color: var(--color-white);
        --icon-selector-icon-color: var(--color-white);
    }
    &.icon-selector-selected {
        .icon-selector-check {
            opacity: 1;
            visibility: visible;
            transform: scale(1) translateY(0);
        }
        .icon-selector-icon {
            .big-icon {
                opacity: .3;
            }
        }   
    }

    .icon-selector-dot-nav {
        display:flex;
        align-items:center;
        justify-content:center;
        gap: 4px;
        transition: opacity var(--icon-selector-duration);
        height: 4px;
        margin-top: -4px;
        margin-bottom: -8px;
        .icon-selector-dot {
            width: 4px;
            height: 4px;
            background: var(--color-white);
            opacity: 0.2;
            border-radius:50%;
            &.icon-selector-dot-active {
                opacity: 1;
            }
        }
    }

    &.icon-selector-popover-read-only {
        .icon-selector-button {
            cursor: not-allowed;
        }
    }
}

.icon-selector-popover {
    max-width: 400px;
    .icon-selector-popover-header {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 20px;
        gap: 20px;

        border-bottom: 1px solid rgba(var(--color-blue-grey-rgb), .2);

        .big-icon {
            flex:none;
            color: var(--color-blue-grey);
        }

        h5 {
            font-style: normal;
            font-weight: 700;
            font-size: 14px;
            line-height: 1.45em;

            color: var(--color-white);

            em {
                color: var(--color-grey);
                font-style: normal;
            }

        }
    }

    .icon-selector-popover-footer {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        gap: 20px;
        
        border-top: 1px solid rgba(var(--color-blue-grey-rgb), .2);

        > .button {
            flex: 1 1 50%;
        }
    }
}
