<template>
    <div class="spark-kpi">
        <div class="spark-kpi-graph"></div>
        <dl class="spark-kpi-text">
            <dt v-text="label"></dt>
            <dd v-html="displayValue"></dd>
        </dl>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import { abbreviateNumber } from '../../utils';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'spark-kpi-' + componentId);

export interface SparkKpiProps {
    label: string;
    value: string | number;
}
const props = withDefaults(defineProps<SparkKpiProps>(), {
    numberFormat: {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    },
});

const compNumberFormat = computed(() => {
    return props.numberFormat === true
        ? {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
          }
        : props.numberFormat;
});

const displayValue = computed(() => {
    const value = typeof props.value === 'string' ? parseInt(props.value) : props.value;
    const num = abbreviateNumber(Math.abs(value), false);
    return `<span class="sign">${value < 0 ? '-' : ''}$</span>${num}`;

    // return Intl.NumberFormat('en-US', compNumberFormat.value)
    //     .format(value)
    //     .replace("$", "");
});
</script>

<style lang="scss">
.spark-kpi {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20px;
    gap: 10px;

    height: 90px;

    background: var(--color-grey-section);
    border-radius: 10px;

    .spark-kpi-graph {
        width: 100px;
        flex: none;
    }

    .spark-kpi-text {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
        flex: 1 1 auto;

        dt {
            font-weight: 700;
            font-size: 12px;
            line-height: 1em;

            color: var(--color-white);
        }

        dd {
            font-style: normal;
            font-weight: 700;
            font-size: 24px;
            line-height: 0.8333333333em;

            color: var(--color-grey);
            .sign {
                opacity: 0.5;
                font-weight: 400;
                margin-right: 4px;
            }
        }
    }
}
</style>
