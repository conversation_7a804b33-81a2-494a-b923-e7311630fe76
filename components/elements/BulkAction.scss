@import '/assets/css/mixins';

.bulk-action {
    display: flex;
    flex-direction: column;
    padding: 20px;
    width: 257px;
    box-sizing: content-box;
    gap: 10px;
    background-color: white; // default = unselected
    border-radius: 4px;

    .bulk-action-label {
        color: var(--primary-black, #040714);
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 700;
        line-height: 24px;
        word-wrap: break-word;
    }

    .bulk-action-description {
        color: var(--primary-black, #040714);
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 24px;
        word-wrap: break-word;
    }

    .bulk-action-action {
        display: flex;
        flex-direction: row;
        gap: 10px;
        color: var(--secondary-blue-grey, #57719c);
        font-size: 14px;
        font-family: 'Oxygen', sans-serif;
        font-weight: 400;
        line-height: 20px;
        word-wrap: break-word;

        &:hover {
            color: rgba(87, 113, 156, 0.5);
        }
    }

    &--active {
        background-color: rgba(87, 113, 156, 1); // selected = blue

        .bulk-action-label {
            width: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 700;
            line-height: 24px;
            word-wrap: break-word;
        }
        .bulk-action-description {
            color: white;
            opacity: 0.7;
        }

        .bulk-action-action {
            color: rgba(187, 202, 232, 1);
            display: flex;
            flex-direction: row;
            gap: 10px;

            &:hover {
                color: white;
            }
        }
    }
}
