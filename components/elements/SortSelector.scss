@import "/assets/css/mixins";

.sort-selector {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;

  &-label {
    color: #57719c;
    font-size: 12px;
    font-family: "Oxygen";
    font-weight: 700;
    word-wrap: break-word;
  }

  &-select {
    color: #97abcc;
    font-size: 12px;
    font-family: "Oxygen";
    font-weight: 700;
    word-wrap: break-word;
    cursor: pointer;
    padding: 4px 10px;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: background-color 0.3s ease, color 0.3s ease;
    margin-left: auto;

    &:hover {
      color: #57719c;
    }

    &.active {
      background-color: #e9edf1;
      color: #040714;
      margin-left: 10px;
    }
  }

  &-dropdown {
    position: absolute;
    top: 100%; // Position below the select
    right: 0; // Align the dropdown to the right side
    border-radius: 4px;
    margin-top: 4px;
    z-index: 10;
    width: 150px;
    background-color: rgba(255, 255, 255, 0.05);

    .sort-selector-option {
      padding: 10px;
      color: #97abcc;
      font-size: 14px;
      font-weight: 700;
      cursor: pointer;
      border-radius: 4px;
      word-wrap: break-word;

      &:hover {
        background-color: #e9edf1;
        color: #040714;
      }
    }
  }
}
