import Button from './Button.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { within } from '@storybook/testing-library';
import { expect } from '@storybook/jest';
// import { action } from '@storybook/addon-actions';

const meta: Meta<typeof Button> = {
    component: Button,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        default: {},

        color: {
            control: {type: 'select'},
            options: [],
            description: 'color'
        },
        size: {
            control: {type: 'select'},
            options: ['sm','md'],
            description: 'size'
        },
        variant: {
            control: {type: 'select'},
            options: ['default', 'outline', 'secondary'],
            description: 'variant'
        },
        disabled: {
            control: {type: 'boolean'},
            description: 'disabled'
        },
        icon: {
            control: {type: 'select'},
            options: ['Coming Soon'],
            description: 'icon'
        },
        href: {
            description: '`href` property; if set, button will be rendered as an anchor tag (<a>)'
        },
        type: {
            description: '`type` property for a button; if `href` is set, this will be ignored'
        },
        svgIcon: {
            description: 'A custom SVG icon to render'
        },
        loading: {
            control: {type: 'boolean'},
        }
    },

    args: {
        default: 'Click me',
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        slots: {
            default: "Button label"
        },
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    render: (args) => ({
        components: { Button },
        setup() {
            return { args };
        },
        template: `
            <Button v-bind="args">
                ${args.default}
            </Button>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27489&mode=dev',
        },
    },
    args: {
        default: "Get Started"
    },
};

Default.play = async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await expect(button.innerText).toBe('GET STARTED');
    await expect(button).toBeEnabled();
}

export const WithIcon: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27481&mode=dev',
        },
    },
    args: {
        svgIcon: {
            shape: 'lock',
        }
    }
};

export const Secondary: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27529&mode=dev',
        },
    },
    args: {
        variant: 'secondary',
        svgIcon: {
            shape: 'lock',
            position: 'right'
        },
    }
};

export const CustomColor: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=791-8136&mode=design&t=e3bNfwR9p9na4zaa-4',
        },
    },
    args: {
        color: 'purple',
        svgIcon: {
            shape: 'lock',
        }
    }
};

export const WithIconColor: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27521&mode=dev',
        },
    },
    args: {
        svgIcon: {
            shape: 'lock',
            color: 'yellow',
        },
        variant: 'secondary'
    }
};

export const Small: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6342-32030&t=nnAcHNkmjCuZFyL3-4',
        },
    },
    args: {
        svgIcon: {
            shape: 'lock',
        },
        size: 'sm'
    }
};

export const SmallIconOnly: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6342-32146&t=nnAcHNkmjCuZFyL3-4',
        },
    },
    args: {
        svgIcon: {
            shape: 'lock',
        },
        iconOnly: true,
        variant: 'muted',
        size: 'sm'
    }
};

export const Outline: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27510&mode=dev',
        },
    },
    args: {
        variant: 'outline'
    }
};

export const Muted: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-27470&mode=dev',
        },
    },
    args: {
        variant: 'muted'
    }
};

export const Disabled: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-37232&mode=dev',
        },
    },
    args: {
        disabled: true
    }
};

Disabled.play = async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await expect(button).toBeDisabled();
}

export const OutlineDisabled: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-37281&mode=dev',
        },
    },
    args: {
        disabled: true,
        variant: 'outline'
    }
};

OutlineDisabled.play = async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await expect(button).toBeDisabled();
}

export const IconOnly: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3412-36983&mode=dev',
        },
    },
    args: {
        iconOnly: true,
        svgIcon: {
            shape: 'lock'
        },
    }
}
IconOnly.play = async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await expect(button).toHaveClass('button button-icon-only');
}

export const Loading: Story = {
    parameters: {},
    args: {
        loading: true
    }
};

Loading.play = async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);
    const button = await canvas.getByRole('button');
    await expect(button).toHaveClass('button button-loading');
}
