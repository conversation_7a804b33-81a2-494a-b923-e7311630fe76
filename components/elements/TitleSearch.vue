<template>
    <div
        class="title-search"
        :class="{ active: isActive, hover: isHovered }"
        @mouseenter="isHovered = true"
        @mouseleave="isHovered = false"
        @click="activateSearch"
    >
        <template v-if="!isActive">
            <span class="title">{{ title }}</span>
            <span class="icon search-icon" @click.stop="activateSearch">
                <SvgIcon shape="search" :color="isHovered || isActive ? '#fff' : 'rgb(146, 152, 162)'" />
            </span>
        </template>
        <template v-else>
            <input v-model="search" class="search-input" :placeholder="`Filter ${title.toLowerCase()}`" @click.stop />
            <span class="icon close-icon" @click.stop="deactivateSearch">
                <SvgIcon shape="close" color="rgb(146, 152, 162)" />
            </span>
        </template>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import { withDefaults } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'title-search-' + componentId);

const props = withDefaults(defineProps<{ title?: string }>(), {
    title: 'Expenses',
});

const isActive = ref(false);
const isHovered = ref(false);
const search = ref('');

function activateSearch() {
    isActive.value = true;
}

function deactivateSearch() {
    isActive.value = false;
    search.value = '';
}
</script>

<style lang="scss">
@import './TitleSearch.scss';
</style>
