@import "/assets/css/mixins";

.button {
    appearance: none;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    text-align:center;
    padding: 5px 20px;
    gap: 10px;
    min-height: 44px;
    cursor: pointer;
    user-select: none;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 2px;

    font-family: var(--font-stack), sans-serif;
    font-weight: 700;
    font-size: 14px;
    line-height: 1.25em;

    --button-border-radius: 4px;
    --button-text-color: var(--color-white);
    --button-color: var(--color-blue);
    --button-border-color: var(--button-color);
    color: var(--button-text-color);
    border-radius: var(--button-border-radius);
    background: var(--button-color);
    border: 1px solid var(--button-border-color);
    
    --button-transition-duration: .2s;
    transition: opacity var(--button-transition-duration),
                background var(--button-transition-duration),
                color var(--button-transition-duration),
                border-color var(--button-transition-duration);

    &:hover {
        &:not(.button-c-custom, .button-v-outline, .button-v-secondary, .button-v-muted, .button-disabled, :disabled) {
            --button-color: var(--color-white);
            --button-text-color: var(--color-blue);
            --button-icon-color: var(--color-blue);
        }
        &.button-c-custom {
            opacity: .9;
        }
    }

    @each $name, $property in $variantColors {
        &.button-c-#{$name} {
            --button-color: var(#{$property});
        }
    }

    // Wrapped to account for upcoming SCSS breaking changes
    & {
        --button-icon-color: var(--button-text-color);
    }
    .button-icon {
        transition: color var(--button-transition-duration),
                    opacity var(--button-transition-duration);
        color: var(--button-icon-color);
        line-height:0;
        font-size:0;
        margin-left: -6px;
        &.button-icon-right {
            order:1000;
            margin-left: 0;
            margin-right: -6px;
        }
        @each $name, $property in $variantColors {
            &.button-icon-c-#{$name} {
                --button-icon-color: var(#{$property});
            }
        }
    }
    
    .svg-icon {
        width: 20px;
        height: 20px;
    }

    &.button-not-uppercase {
        text-transform: none;
        letter-spacing: 0;
        &.button-v-muted {
            --button-text-color: var(--color-grey);
        }
    }

    &.button-icon-only {
        padding-left: 9px;
        padding-right: 9px;
        .button-icon, .button-icon.button-icon-right {
            margin:0;
        }
        .svg-icon {
            width: 24px;
            height: 24px;
        }
    }

    &.button-s-sm {
        font-size: 12px;
        letter-spacing: 1.2px;
        padding: 5px 10px;
        min-height: 34px;
        gap: 4px;

        &.button-icon-only {
            padding-left: 9px;
            padding-right: 9px;
        }

        .button-icon {
            margin-left: 0;
            &.button-icon-right {
                margin-right: 0;
            }
        }

        .svg-icon {
            width: 14px;
            height: 14px;
        }
    }

    &.button-v-outline {
        background: transparent;

        --button-outline-color: var(--color-blue-grey);
        --button-border-color: var(--button-outline-color);
        --button-text-color: var(--button-outline-color);
        &:hover {
            --button-outline-color: var(--color-grey);
        }
    }

    &.button-v-secondary {
        background: transparent;
        --button-text-color: var(--color-white);
        --button-border-color: var(--color-blue);
        &:hover {
            --button-border-color: var(--color-white);
        }
    }

    &.button-v-muted {
        --button-border-color: transparent;
        --button-color: rgba(var(--color-blue-grey-rgb),.2);
        --button-text-color: var(--color-blue-grey);

        &:hover {
            --button-color: rgba(var(--color-blue-grey-rgb),.5);
            --button-text-color: var(--color-grey);
        }
    }

    &.button-disabled, &:disabled {
        // &:not(.button-loading) {
            --button-color: rgba(var(--color-white-rgb), 0.05);
            --button-text-color: rgba(var(--color-white-rgb), 0.2);
            --button-border-color: transparent;
            cursor: not-allowed;
            &:hover {
                opacity:1;
            }

            &.button-v-outline, &.button-v-secondary {
                --button-color: rgba(var(--color-white-rgb), .2);
                --button-border-color: rgba(var(--color-white-rgb), .2);
                &:hover {
                    opacity: 1;
                    color: var(--button-color);
                    background: transparent;
                }
            }
        // }
    }

    &.button-v-transparent {
        background: transparent;
        --button-border-color: transparent;
    }

    .button-label {
        transition: opacity var(--button-transition-duration);
    }

    &.button-loading {
        cursor: wait;
        .button-icon, .button-label {
            opacity:0.1;
        }
        .button-loading-animation {
            opacity: 1;
            visibility: visible;
        }
    }

    .button-loading-animation {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        opacity:0;
        visibility: hidden;
        transition: opacity .3s, visibility .3s;
        &:before {
            content: "";
            display:block;
            width: 24px;
            padding: 5px;
            aspect-ratio: 1;
            border-radius: 50%;
            background: var(--button-text-color);
            --button-animation-mask: 
                conic-gradient(#0000 10%,#000),
                linear-gradient(#0000,#000) content-box;
            -webkit-mask: var(--button-animation-mask);
                    mask: var(--button-animation-mask);
            -webkit-mask-composite: source-out;
                    mask-composite: subtract;
            animation: l3 .75s infinite linear;
        }
        @keyframes l3 {to{transform: rotate(1turn)}}
    }
}
