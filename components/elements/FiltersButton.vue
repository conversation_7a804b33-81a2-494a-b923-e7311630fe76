<template>
    <div class="filters-button">
        <Button
            svg-icon="filter"
            :variant="activeFiltersCount < 1 ? 'muted' : null"
            :uppercase="false"
            @click.prevent="handleShowModal"
        >
            Filters 
            <span class="filters-button-count" v-if="activeFiltersCount > 0" v-text="activeFiltersCount"></span>
        </Button>

        <FiltersModal
            :is-open="modalIsOpen"
            :filter-groups="props.data"
            @confirm="handleModalConfirm"
            @close="handleHideModal"
        />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import Button from './Button.vue';
import FiltersModal from '../modals/FiltersModal.vue';
import type { FiltersModalFilterGroup } from '../modals/FiltersModal.vue';

/** TODO: Description of this component */
defineOptions();

export type FiltersButtonFilterValue = string | number | boolean | string[] | number[] | boolean[];

export interface FiltersButtonFilter {
    name: string;
    value: FiltersButtonFilterValue;
}

export interface FiltersButtonProps {
    data: FiltersModalFilterGroup[];
}

export interface ActiveFilter {
    name: FiltersModalFilterGroup['name'];
    type: FiltersModalFilterGroup['type'];
    data: FiltersModalFilterGroup['data'];
}

const props = defineProps<FiltersButtonProps>()

const emit = defineEmits<{
    change: [filters: FiltersModalFilterGroup[]]
}>()

function handleModalConfirm(filters: FiltersModalFilterGroup[]) {
    emit('change', filters);
    modalIsOpen.value = false;
}

const activeFiltersCount = computed(() => {
    return props.data.reduce((acc, filter) => {
        if (filter.type === 'checkboxes') {
            return acc + (filter.data.some((item) => item.checked) && !filter.data.every((item) => item.checked) ? 1 : 0);
        }
        if (filter.type === 'range') {
            return acc + (filter.data.bottom !== filter.data.min || filter.data.top !== filter.data.max ? 1 : 0);
        }
        return acc;
    }, 0);
});

const modalIsOpen = ref(false);
function handleShowModal() {
    modalIsOpen.value = true;
}
function handleHideModal() {
    modalIsOpen.value = false;
}
</script>

<style lang="scss">
.filters-button {
    .button-label {
        display: flex;
        align-items: center;
        gap: 6px;
    }
    .filters-button-count {
        background: rgba(var(--color-white-rgb), .2);
        border-radius: 9px;
        padding: 0 2px;
        height: 18px;
        min-width: 18px;
        font-size: 14px;
        font-weight: 900;
        line-height: 1em;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    &:hover {
        .filters-button-count {
            background: rgba(var(--color-blue-rgb), .25);
        }
    }
}
</style>
