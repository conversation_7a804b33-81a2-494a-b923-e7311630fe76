<!-- 
    This is only used as an import for IconSelector
-->
<template>
    <PopoverModal :trigger-element="triggerElement" :is-open="isOpen" @close="handleClose">
        <PopoverModalContent>
            <template #header>
                <BigIcon v-if="iconShape" :shape="iconShape" :width="20" :height="20" />
                <h5 v-html="options.heading"></h5>
            </template>

            <CheckboxGroup
                v-model:options="optionsRef"
                v-model:selected="selectedRef"
                size="sm"
                :mode="mode"
                />
            
            <template #footer>
                <Button variant="muted" @click="handleClose">Cancel</Button>
                <Button @click.prevent="handleSubmit">Done</Button>
            </template>
        </PopoverModalContent>
    </PopoverModal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref, onMounted } from 'vue';
import type { Ref } from 'vue';
import BigIcon from '../images/BigIcon.vue';
import type { TBigIconShapeKey } from '../images/BigIconShapes';
import type { IconSelectorSubOptionProp } from './IconSelector.vue';
import PopoverModalContent from '../modals/PopoverModalContent.vue';
import CheckboxGroup from '../forms/CheckboxGroup.vue';
import type { CheckboxGroupOption, CheckboxGroupProps } from '../forms/CheckboxGroup.vue';
import Button from './Button.vue';

import PopoverModal from '../modals/PopoverModal.vue';

/** The multiselect popover that shows when multi-option situation items are selected  */
defineOptions();

export interface PopoverModalProps {
    triggerElement: Ref<HTMLElement> | string;
    isOpen: boolean;
    options: IconSelectorSubOptionProp,
    headingIconShape?: TBigIconShapeKey|undefined,
    mode?: CheckboxGroupProps['mode'];
}
const props = withDefaults(defineProps<PopoverModalProps>(), {
    isOpen: true,
    mode: 'checkbox'
})

const models = reactive({
    ...props.options.options.reduce((acc, option) => {
        acc[option.name] = option.value ?? false;
        return acc;
    }, {} as Record<string, boolean>)
})

const optionsRef = ref<CheckboxGroupOption[]>(JSON.parse(JSON.stringify(props.options.options)));
const selectedRef = ref(props.options.options.filter(option => option.value).map(option => option.name));

const iconShape = computed(() => props.headingIconShape || props.options.headingIconShape);

const emit = defineEmits<{
    close: [],
    // save: [options: PopoverModalProps['options']['options']]
    save: [options: string[]]
}>()

function handleClose() {
    optionsRef.value = JSON.parse(JSON.stringify(props.options.options));
    emit('close');
}

function handleSubmit() {
    emit('save', optionsRef.value.filter(o => o.value).map(o => o.name));
}
</script>
