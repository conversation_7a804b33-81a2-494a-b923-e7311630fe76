<template>
    <template v-if="to">
        <NuxtLink :class="classObj" :disabled="disabled || loading" :to="to">
            <div class="button-loading-animation"></div>

            <span v-if="iconProps" class="button-icon" :class="buttonIconClassObj">
                <SvgIcon v-bind="iconProps" />
            </span>

            <span class="button-label" v-if="!iconOnly">
                <slot />
            </span>
        </NuxtLink>
    </template>

    <button v-else :class="classObj" :disabled="disabled || loading" :type="type">
        <div class="button-loading-animation"></div>
        <span v-if="iconProps" class="button-icon" :class="buttonIconClassObj">
            <SvgIcon v-bind="iconProps" />
        </span>

        <span class="button-label" v-if="!iconOnly">
            <slot />
        </span>
    </button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ButtonHTMLAttributes } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Props as SvgIconProps } from '../images/SvgIcon.vue'
import type { TAccentColor } from '~/composables/types'
import type { TSvgIconShapeKey } from '../images/SvgIconShapes';

/** A clickable button, rendered as a `<button>` or an `<a>`  */
defineOptions();

export type ButtonSvgIconProps = SvgIconProps & {
    position?: 'left'|'right'|null,
    color?: TAccentColor|null
};

export interface Props {
    color?: TAccentColor|null;
    size?: |'sm'|'md'|null;
    variant?: 'default'|'outline'|'primary'|'secondary'|'muted'|'transparent'|null;
    disabled?: ButtonHTMLAttributes['disabled'];
    iconOnly?: boolean|null;
    to?: string|null;
    type?: ButtonHTMLAttributes['type']

    svgIcon?: Partial<ButtonSvgIconProps> | TSvgIconShapeKey | null;
    loading?: boolean
    uppercase?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    color: null,
    variant: null,
    disabled: false,
    icon: null,
    to: null,
    type: 'submit',
    svgIcon: null,
    loading: false,
    uppercase: true
})

const classObj = computed(() => [
    'button',
    props.color ? `button-c-custom button-c-${props.color}` : '',
    props.variant ? `button-v-${props.variant}` : '',
    props.size ? `button-s-${props.size}` : '',
    props.loading ? `button-loading` : '',
    props.iconOnly ? `button-icon-only` : '',
    props.uppercase ? '' : 'button-not-uppercase'
]);

const iconProps = computed(() => {
    if (!props.svgIcon) return null;
    if (typeof props.svgIcon === 'string') {
        return { shape: props.svgIcon };
    } else {
        const { color, position, ...rest } = props.svgIcon;
        return rest;
    }
})

const iconPosition = computed(() => (isSvgIconProps(props.svgIcon) && props.svgIcon?.position) || 'left')

const buttonIconClassObj = computed(() => [
    (iconPosition.value === 'right') ? `button-icon-right` : '',
    (isSvgIconProps(props.svgIcon) && props.svgIcon?.color) ? `button-icon-c-${props.svgIcon?.color}` : ''
]);

function isSvgIconProps(svgIcon: Partial<ButtonSvgIconProps> | TSvgIconShapeKey | null): svgIcon is Partial<ButtonSvgIconProps> {
    if (!svgIcon) {
        return false;
    }

    return svgIcon.color !== undefined
        || svgIcon.shape !== undefined
        || svgIcon.position !== undefined;
}
</script>

<style lang="scss">
    @import './Button';
</style>
