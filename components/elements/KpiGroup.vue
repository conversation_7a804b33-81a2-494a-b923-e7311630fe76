<template>
    <div class="kpi-group" @click.prevent="handleClick" :class="{ 'kpi-group-clickable': !!props.clickDest }">
        <div class="kpi-group-meta">
            <h5 class="kpi-group-label" v-text="label"></h5>
            <div class="kpi-group-kpis">
                <dl v-for="kpi in kpiValues">
                    <dd v-text="formatValue(kpi)"></dd>
                    <dt v-text="kpi?.label"></dt>
                </dl>
            </div>

            <span class="kpi-group-disclaimer" v-text="disclaimer" v-if="disclaimer"></span>
        </div>

        <div class="kpi-group-slot" v-if="$slots.default">
            <slot></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { abbreviateNumber } from '../../utils';

/** A box that contains one or more KPI values in a row; built for the intake form review screen */
defineOptions();

const router = useRouter();

export interface KpiGroupKpi {
    label?: string;
    value: number;
    formatting?: boolean | 'currency' | 'number';
    succinct?: boolean;
}

export interface KpiGroupProps {
    label: string;
    kpis: KpiGroupKpi | KpiGroupKpi[];
    disclaimer?: string;
    clickDest?: string;
}
const props = defineProps<KpiGroupProps>();

const kpiValues = computed(() => {
    return Array.isArray(props.kpis) ? props.kpis : [props.kpis];
});

function handleClick() {
    if (props.clickDest) {
        router.push(props.clickDest);
    }
}

function formatValue(kpi?: KpiGroupKpi) {
    if (!kpi) return '';
    if (kpi.formatting === false) return kpi.value;

    if (kpi.succinct) {
        return abbreviateNumber(kpi.value, kpi.formatting === 'currency');
    }

    return kpi.value.toLocaleString('en-US', {
        style: kpi.formatting === 'currency' ? 'currency' : 'decimal',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: kpi.formatting === 'currency' ? 2 : 0,
    });

    // return kpi.value.toLocaleString();
}
</script>

<style lang="scss">
@import './KpiGroup';
</style>
