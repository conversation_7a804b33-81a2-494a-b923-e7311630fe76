import TopKpi from './TopKpi.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof TopKpi> = {
    component: TopKpi,
    tags: ['autodocs'],

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TopKpi>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-26626&mode=dev',
        },
    },
    args: {
        label: 'Assets',
        value: 654321,
        numberFormat: true
    }
};
