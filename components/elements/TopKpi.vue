<template>
    <dl class="top-kpi">
        <dt class="top-kpi-dt" v-text="props.label"></dt>
        <dd class="top-kpi-dd">
            <span v-if="showUsdPrefix" class="top-kpi-dd-prefix">$</span>
            <span v-text="displayValue"></span>
        </dd>
    </dl>
</template>

<script setup lang="ts">
import { computed } from 'vue';

/** A single KPI value to show along the top of multi-step forms */
defineOptions();

export interface TopKpiProps {
    label: string;
    value: string | number;
    numberFormat?: boolean | Intl.NumberFormatOptions;
}
const props = withDefaults(defineProps<TopKpiProps>(), {
    numberFormat: {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    }
})

const compNumberFormat = computed(() => {
    return (props.numberFormat === true) 
        ? {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
    } : props.numberFormat;
})

const displayValue = computed (() => {
    if (!compNumberFormat.value) return;
    const value = typeof props.value === 'string' ? parseInt(props.value) : props.value;
    return Intl.NumberFormat('en-US', compNumberFormat.value)
        .format(value)
        .replace("$", "");
})

const showUsdPrefix = computed(() => 
    compNumberFormat.value && compNumberFormat.value.currency === 'USD'
)
</script>

<style lang="scss">
.top-kpi {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    flex-direction: column;
    min-width: 120px;
    min-height: 62px;
    padding: 5px 20px;
    gap: 5px;
    border-radius: 10px;
    overflow: hidden;
    position: relative;

    background: var(--color-grey-section);

    &:after {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        background: var(--color-white);

        visibility: hidden;
        opacity: 0;
        transition: opacity 0.2s,
                    visibility 0.2s;
    }

    &:hover {
        &:after {
            visibility: visible;
            opacity: .1;
        }
    }

    .top-kpi-dt {
        color: var(--color-white);
        text-align: right;
        font-size: 12px;
        font-weight: 700;
        line-height: 1.125em;
        position: relative;
        z-index: 5;
    }

    .top-kpi-dd {
        color: var(--color-grey);
        text-align: right;
        font-size: 16px;
        font-weight: 700;
        line-height: 1.25em;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 4px;
        position: relative;
        z-index: 5;

        .top-kpi-dd-prefix {
            font-size: 12px;
            line-height:1.125em;
            padding-top:2px;
        }
    }
}
</style>

