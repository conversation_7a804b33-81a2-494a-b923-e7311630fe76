﻿<script setup lang="ts" generic="T">
defineProps<{ items: T | T[] }>();
</script>

<template>
    <slot name="empty" v-if="!items || (Array.isArray(items) && !items.length)"></slot>
    <template v-if="Array.isArray(items)">
        <template v-for="item in items">
            <slot name="item" v-bind="item"></slot>
        </template>
    </template>
    <slot name="item" v-else v-bind="<T>items"></slot>
</template>

<style scoped lang="scss"></style>
