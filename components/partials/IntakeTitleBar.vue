<template>
    <div class="intake-layout-title">
        <h3 v-html="title"></h3>

        <div class="intake-layout-title-actions">
            <ElementsButton 
                @click.prevent="handlePrevClick"
                svg-icon="keyboard-arrow-left"
                :icon-only="true"
                variant="muted"
                class="intake-layout-action"
            />
            <ElementsButton
                @click.prevent="handleNextClick"
                :svg-icon="(nextArrow) ? { position: 'right', shape: 'keyboard-arrow-right' } : null"
                class="intake-layout-action"
                :disabled="nextDisabled"
            >
                {{ nextText }}
            </ElementsButton>
        </div>
    </div>
</template>

<script setup lang="ts">

export interface IntakeTitleBarProps {
    title: string;
    nextText?: string;
    nextDisabled?: boolean;
    nextArrow?: boolean;
}

const props = withDefaults(defineProps<IntakeTitleBarProps>(), {
    nextText: 'Continue',
    nextDisabled: false,
    nextArrow: true,
});

const emit = defineEmits<{
    prevClicked: []
    nextClicked: []
}>();

function handleNextClick() {
    emit('nextClicked');
}

function handlePrevClick() {
    emit('prevClicked');
}

</script>
