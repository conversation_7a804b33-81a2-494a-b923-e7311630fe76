import LoadingAnimation from './LoadingAnimation.vue';
import type { Meta, StoryObj } from '@storybook/vue3';

const meta: Meta<typeof LoadingAnimation> = {
    component: LoadingAnimation,
    tags: ['autodocs'],

    argTypes: {
        type: {
            control: { type: 'select' },
            options: ['spinner', 'ellipsis'],
            description: 'The type of loading animation to display',
        },
        size: {
            control: { type: 'select' },
            options: ['small', 'medium', 'large', 'fill'],
            description: 'Predefined size for the animation',
        },
        color: {
            control: { type: 'color' },
            description: 'Custom color for the animation',
        },
        customSize: {
            control: { type: 'text' },
            description: 'Custom size override (e.g., "50px", "3rem")',
        },
    },

    args: {
        type: 'spinner',
        size: 'medium',
        color: null,
        customSize: null,
    },

    decorators: [
        () => ({
            template:
                '<div style="margin: 3em; display: flex; align-items: center; justify-content: center; min-height: 100px;"><story/></div>',
        }),
    ],
};

export default meta;
type Story = StoryObj<typeof LoadingAnimation>;

export const SpinnerDefault: Story = {
    args: {
        type: 'spinner',
        size: 'medium',
    },
};

export const SpinnerSmall: Story = {
    args: {
        type: 'spinner',
        size: 'small',
    },
};

export const SpinnerLarge: Story = {
    args: {
        type: 'spinner',
        size: 'large',
    },
};

export const SpinnerCustomColor: Story = {
    args: {
        type: 'spinner',
        size: 'medium',
        color: '#3b82f6',
    },
};

export const SpinnerCustomSize: Story = {
    args: {
        type: 'spinner',
        customSize: '60px',
        color: '#ef4444',
    },
};

export const EllipsisDefault: Story = {
    args: {
        type: 'ellipsis',
        size: 'medium',
    },
};

export const EllipsisSmall: Story = {
    args: {
        type: 'ellipsis',
        size: 'small',
    },
};

export const EllipsisLarge: Story = {
    args: {
        type: 'ellipsis',
        size: 'large',
    },
};

export const EllipsisCustomColor: Story = {
    args: {
        type: 'ellipsis',
        size: 'medium',
        color: '#10b981',
    },
};

export const EllipsisCustomSize: Story = {
    args: {
        type: 'ellipsis',
        customSize: '80px',
        color: '#8b5cf6',
    },
};

export const FillContainer: Story = {
    args: {
        type: 'spinner',
        size: 'fill',
    },
    decorators: [
        () => ({
            template:
                '<div style="margin: 3em; width: 200px; height: 200px; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center;"><story/></div>',
        }),
    ],
};

export const ColorVariations: Story = {
    render: args => ({
        components: { LoadingAnimation },
        setup() {
            return { args };
        },
        template: `
            <div style="display: flex; gap: 2rem; align-items: center; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" size="medium" color="#3b82f6" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Blue Spinner</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" size="medium" color="#ef4444" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Red Spinner</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="ellipsis" size="medium" color="#10b981" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Green Ellipsis</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="ellipsis" size="medium" color="#8b5cf6" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Purple Ellipsis</p>
                </div>
            </div>
        `,
    }),
};

export const SizeComparison: Story = {
    render: args => ({
        components: { LoadingAnimation },
        setup() {
            return { args };
        },
        template: `
            <div style="display: flex; gap: 2rem; align-items: center; flex-wrap: wrap;">
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" size="small" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Small</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" size="medium" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Medium</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" size="large" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Large</p>
                </div>
                <div style="text-align: center;">
                    <LoadingAnimation type="spinner" custom-size="4rem" />
                    <p style="margin-top: 0.5rem; font-size: 0.875rem; color: #6b7280;">Custom (4rem)</p>
                </div>
            </div>
        `,
    }),
};
