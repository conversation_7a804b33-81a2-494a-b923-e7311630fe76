<template>
    <div class="loading-animation" :class="[`loading-animation--${type}`, sizeClass]" :style="styleObj">
        <div v-if="type === 'spinner'" class="loading-animation__spinner">
            <svg
                class="loading-animation__spinner-svg"
                viewBox="0 0 50 50"
                :width="computedSize"
                :height="computedSize"
            >
                <circle class="loading-animation__spinner-circle" cx="25" cy="25" r="20" fill="none" stroke-width="4" />
            </svg>
        </div>

        <div v-else-if="type === 'ellipsis'" class="loading-animation__ellipsis">
            <div class="loading-animation__ellipsis-dot"></div>
            <div class="loading-animation__ellipsis-dot"></div>
            <div class="loading-animation__ellipsis-dot"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

export type LoadingAnimationType = 'spinner' | 'ellipsis';
export type LoadingAnimationSize = 'small' | 'medium' | 'large' | 'fill';

export interface Props {
    type?: LoadingAnimationType;
    size?: LoadingAnimationSize;
    color?: string | null;
    customSize?: number | string | null;
}

const props = withDefaults(defineProps<Props>(), {
    type: 'spinner',
    size: 'medium',
    color: null,
    customSize: null,
});

const sizeClass = computed(() => `loading-animation--${props.size}`);

const computedSize = computed(() => {
    if (props.customSize) {
        return typeof props.customSize === 'number' ? `${props.customSize}px` : props.customSize;
    }
    return '100%';
});

const styleObj = computed(() => {
    const styles: Record<string, string> = {};

    if (props.color) {
        styles['--loading-animation-color'] = props.color;
    }

    if (props.customSize) {
        const sizeValue = typeof props.customSize === 'number' ? `${props.customSize}px` : props.customSize;
        styles['--loading-animation-custom-size'] = sizeValue;
    }

    return styles;
});
</script>

<style lang="scss">
@import './LoadingAnimation.scss';
</style>
