<template>
    <Transition name="transition-slide-in">
        <div class="transition-slide-in" v-if="isVisible">
            <div class="transition-slide-in-content">
                <slot></slot>
            </div>
        </div>
    </Transition>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { Ref } from 'vue';

/** A wrapper to add slide in-out transitions to children */
defineOptions();

const props = defineProps<{
    /** Whether the content is visible; use this in lieu of v-if */
    isVisible?: boolean|null
}>();

</script>

<style lang="scss">
.transition-slide-in {
    display: grid;
    --transition-slide-in-duration: 0.15s;
    --transition-slide-out-duration: var(--transition-slide-in-duration);

    // If this element is in a layout with a gap (eg display:flex; gap:10px)
    // this offset will prevent a jump when the element is added or removed
    --bottom-offset: 10px;
    padding-bottom: var(--bottom-offset);
    margin-bottom: calc(-1 * var(--bottom-offset));

    .transition-slide-in-content {
        overflow: hidden;
    }

    &.transition-slide-in-enter-active {
        transition: grid-template-rows var(--transition-slide-in-duration) ease-in,
                    padding var(--transition-slide-in-duration) ease-out;
    }

    &.transition-slide-in-leave-active {
        transition: grid-template-rows var(--transition-slide-out-duration) ease-out, 
                    padding var(--transition-slide-in-duration) ease-in;
    }

    &.transition-slide-in-enter-from, &.transition-slide-in-leave-to {
        grid-template-rows: 0fr;
        padding-bottom:0;
    }

    &.transition-slide-in-enter-to, &.transition-slide-in-leave-from {
        grid-template-rows: 1fr;
        padding-bottom: var(--bottom-offset);
    }
}
</style>

