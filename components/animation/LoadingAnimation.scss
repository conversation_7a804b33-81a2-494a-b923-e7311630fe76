.loading-animation {
    // CSS custom properties for easy customization
    --loading-animation-color: currentColor;
    --loading-animation-duration: 1s;
    --loading-animation-custom-size: auto;
    
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    // Size variants
    &--small {
        width: 1rem;
        height: 1rem;
    }
    
    &--medium {
        width: 2rem;
        height: 2rem;
    }
    
    &--large {
        width: 3rem;
        height: 3rem;
    }
    
    &--fill {
        width: 100%;
        height: 100%;
    }
    
    // Custom size override
    &:has([style*="--loading-animation-custom-size"]) {
        width: var(--loading-animation-custom-size);
        height: var(--loading-animation-custom-size);
    }
    
    // Spinner animation
    &__spinner {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &-svg {
            width: 100%;
            height: 100%;
            animation: loading-spin var(--loading-animation-duration) linear infinite;
        }
        
        &-circle {
            stroke: var(--loading-animation-color);
            stroke-linecap: round;
            stroke-dasharray: 80, 200;
            stroke-dashoffset: 0;
            animation: loading-dash calc(var(--loading-animation-duration) * 1.5) ease-in-out infinite;
        }
    }
    
    // Ellipsis animation
    &__ellipsis {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.2em;
        width: 100%;
        height: 100%;
        
        &-dot {
            width: 0.25em;
            height: 0.25em;
            background-color: var(--loading-animation-color);
            border-radius: 50%;
            animation: loading-ellipsis calc(var(--loading-animation-duration) * 1.4) ease-in-out infinite;
            
            &:nth-child(1) {
                animation-delay: 0s;
            }
            
            &:nth-child(2) {
                animation-delay: calc(var(--loading-animation-duration) * 0.2);
            }
            
            &:nth-child(3) {
                animation-delay: calc(var(--loading-animation-duration) * 0.4);
            }
        }
    }
}

// Keyframe animations
@keyframes loading-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes loading-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 90, 200;
        stroke-dashoffset: -35;
    }
    100% {
        stroke-dasharray: 90, 200;
        stroke-dashoffset: -125;
    }
}

@keyframes loading-ellipsis {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}