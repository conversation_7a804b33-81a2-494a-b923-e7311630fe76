<template>
    <Transition name="transition-slide-down">
        <div class="transition-slide-down" v-if="isVisible">
            <slot></slot>
        </div>
    </Transition>
</template>

<script setup lang="ts">
/** A wrapper to add slide in-out transitions to children */
defineOptions();

const props = defineProps<{
    /** Whether the content is visible; use this in lieu of v-if */
    isVisible?: boolean|null
}>();
</script>

<style lang="scss">
.transition-slide-down {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow:hidden;
    --transition-slide-down-duration: .15s;

    &.transition-slide-down-enter-active {
        pointer-events: none;
        transition: height var(--transition-slide-down-duration) ease-in,
                    visibility var(--transition-slide-down-duration) ease-in,
                    opacity var(--transition-slide-down-duration) ease-in,
                    transform var(--transition-slide-down-duration) ease-in;
    }

    &.transition-slide-down-leave-active {
        pointer-events: none;
        transition: height var(--transition-slide-down-duration) ease-out,
                    visibility var(--transition-slide-down-duration) ease-out,
                    opacity var(--transition-slide-down-duration) ease-out,
                    transform var(--transition-slide-down-duration) ease-out;
    }

    &.transition-slide-down-enter-from, &.transition-slide-down-leave-to {
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
    }

    &.transition-slide-down-enter-to, &.transition-slide-down-leave-from {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }
}
</style>

