import SelectInput from './SelectInput.vue';
import type { Meta, StoryObj } from '@storybook/vue3';

const meta: Meta<typeof SelectInput> = {
    component: SelectInput,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        placeholder: {
            control: { type: 'text' },
        },

        options: {
            control: {type: 'object'},
        },

        label: {
            control: {type: 'object'},
        },
        
        size: {
            control: {type: 'select'},
            options: ['sm', 'lg'],
        },

        alignment: {
            control: {type: 'select'},
            options: ['left', 'right'],
        },

        disabled: {
            control: {type: 'boolean'},
        },

        validationState: {
            control: {type: 'select'},
            options: ['none', 'error'],
        },
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // This component is just the input; this rule will be enforced in a parent component
                // rules: [{ id: 'select-name', enabled: false }],
            },
        }
    },

    args: { ariaLabel: 'Which came first?' },

    render: (args) => ({
        components: { SelectInput },
        setup() {
            return { args };
        },
        template: `
            <SelectInput v-bind="args">
                ${args.default}
            </SelectInput>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SelectInput>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3526-38282&mode=design&t=vtmTrqRRSjQmgafo-4',
        },
    },
    args: {
        placeholder: 'Select an option',
        options: [
            { value: 'chicken', label: 'Chicken' },
            { value: 'egg', label: 'Egg' },
        ],
        modelValue : null,
    }
};

export const Small: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3526-38282&mode=design&t=vtmTrqRRSjQmgafo-4',
        },
    },
    args: {
        size: 'sm',
        placeholder: 'Select an option',
        options: [
            { value: 'chicken', label: 'Chicken' },
            { value: 'egg', label: 'Egg' },
        ],
        modelValue : null,
    }
};

export const FloatingLabel: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3526-38282&mode=design&t=vtmTrqRRSjQmgafo-4',
        },
    },
    args: {
        label: 'Which came first?',
        placeholder: 'Select an option',
        options: [
            { value: 'chicken', label: 'Chicken' },
            { value: 'egg', label: 'Egg' },
        ],
        modelValue : null,
    }
};

export const RightAligned: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3526-38282&mode=design&t=vtmTrqRRSjQmgafo-4',
        },
    },
    args: {
        alignment: 'right',
        label: 'Which came first?',
        placeholder: 'Select an option',
        options: [
            { value: 'chicken', label: 'Chicken' },
            { value: 'egg', label: 'Egg' },
        ],
        modelValue : null,
    }
};

export const Error: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3526-38282&mode=design&t=vtmTrqRRSjQmgafo-4',
        },
    },
    args: {
        validationState: 'error',
        options: [
            { value: 'chicken', label: 'Chicken' },
            { value: 'egg', label: 'Egg' },
        ],
        modelValue : 'chicken',
    }
};
