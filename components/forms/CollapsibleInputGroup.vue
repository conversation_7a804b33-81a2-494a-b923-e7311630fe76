<template>
    <div class="collapsible-input-group" :class="topLevelClass" ref="rootRef" :tabindex="rootTabIndex">
        <div class="collapsible-input-group-tooltip-target" ref="tooltipTarget"></div>

        <div class="collapsible-input-group-pulse" v-if="isPulsing"></div>

        <div class="collapsible-input-group-edit-icon" v-if="showEditIcon">
            <SvgIcon shape="edit" />
        </div>

        <div class="collapsible-input-group-view-content">
            <div class="collapsible-input-group-overlay" @click.prevent="handleExpand"></div>

            <label class="collapsible-input-group-label">
                <span v-html="label"></span>
                <span class="collapsible-input-group-indicator" v-if="validationState" :class="indicatorClass">
                    <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M5 0L5.82864 3.2793L8.90916 1.88255L6.86195 4.57502L9.87464 6.1126L6.49317 6.19076L7.16942 9.50484L5 6.90983L2.83058 9.50484L3.50683 6.19076L0.12536 6.1126L3.13805 4.57502L1.09084 1.88255L4.17136 3.2793L5 0Z"
                            fill="currentColor"
                        />
                    </svg>
                </span>
            </label>
            <span
                class="collapsible-input-group-description"
                v-if="formattedValueHtml || descriptionHtml"
                v-html="formattedValueHtml ? formattedValueHtml : descriptionHtml"
            ></span>
        </div>

        <Transition name="collapsible-input-group-edit">
            <div
                class="collapsible-input-group-edit"
                v-if="isExpanded || expandMode !== 'absolute'"
                ref="editBlockRef"
                :style="popoverStyles"
            >
                <div class="collapsible-input-group-edit-title" v-if="expandMode === 'absolute'">
                    <button @click.prevent="handleCollapse" class="collapsible-input-group-edit-close">
                        <SvgIcon shape="close" />
                    </button>
                    <label class="collapsible-input-group-label" v-html="label"></label>
                    <span
                        class="collapsible-input-group-description numero-2"
                        v-if="formattedValueHtml || descriptionHtml"
                        v-html="formattedValueHtml ?? descriptionHtml"
                    ></span>
                </div>

                <span
                    class="collapsible-input-group-edit-description"
                    v-if="showDescriptionOnEdit && descriptionHtml"
                    v-html="descriptionHtml"
                ></span>

                <div class="collapsible-input-group-fields" ref="fieldsRef">
                    <slot />
                </div>

                <div class="collapsible-input-group-footer" v-if="footerButtons || expandMode === 'absolute'">
                    <template v-if="expandMode === 'absolute'">
                        <Button @click.prevent="handleCancel" variant="muted">Cancel</Button>
                        <Button @click.prevent="handleDone" v-text="confirmButtonText"></Button>
                    </template>

                    <template v-else>
                        <span
                            class="collapsible-input-group-footer-text"
                            v-if="footerDisclaimer"
                            v-html="footerDisclaimer"
                        ></span>
                        <Button @click.prevent="handleCancel" variant="muted" size="sm" tabindex="-1">Cancel</Button>
                        <Button
                            @click.prevent="handleDone"
                            size="sm"
                            tabindex="-1"
                            :svg-icon="confirmButtonIcon ? { shape: confirmButtonIcon, position: 'right' } : null"
                        >
                            {{ confirmButtonText }}
                        </Button>
                    </template>
                </div>
            </div>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, onMounted, nextTick } from 'vue';
import useElementBlur from '../../composables/useElementBlur';
import { onClickOutside, onKeyStroke } from '@vueuse/core';
import { useFloating, autoUpdate, autoPlacement, flip, offset } from '@floating-ui/vue';
import type { TSvgIconShapeKey } from '../images/SvgIconShapes';
import SvgIcon from '../images/SvgIcon.vue';
import Button from '../elements/Button.vue';

/** Read / edit states for one or more input fiels that can be focused or clicked into */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'collapsible-input-group-' + componentId);

const rootRef = ref<HTMLTextAreaElement | null>();
onKeyStroke(['Escape', 'Enter'], e => {
    if (isExpanded.value) handleCollapse();
});
onClickOutside(rootRef, handleClickOutside);
useElementBlur(rootRef, handleBlur);

const isExpanded = ref(false);
const rootTabIndex = computed(() => (isExpanded.value ? -1 : 0));

export interface CollapsibleInputGroupProps {
    /** The persisent primary label at the very top of the block */
    label: string;

    /** A short description of the field; used as a placeholder when there is no formattedValue */
    description?: string | string[];

    /** The "value" to show when a filled input group is collapsed (e.g. a formatted number, list of selected values, etc) */
    formattedValue?: string | string[] | null;

    /** The validation state of the input group */
    validationState?: 'required' | 'bad' | 'good' | null;

    /** Whether the field should focus immediately when it is added to the DOM */
    autoFocus?: boolean;

    /** How the component should expand; "absolute" will make it expand to a popover modal */
    expandMode?: 'absolute' | 'inline';

    /** Whether the block should toggle to the collapsed state when the user blurs from the component or any child elements */
    collapseOnBlur?: boolean;

    /** Whether the block should toggle to the collapsed state when the user clicks outside the component */
    collapseOnClickOutside?: boolean;

    /** Whether the description should be shown when the block is in the edit state */
    showDescriptionOnEdit?: boolean;

    /** Whether the block should pulse to draw attention to itself */
    isPulsing?: boolean;

    /** Whether to include a footer with buttons while editing */
    footerButtons?: boolean;

    /** Small text to include on the left side of the edit mode footer */
    footerDisclaimer?: string;

    /** The text of the confirmation button, if `footerButtons` is true */
    confirmButtonText?: string | null;

    /** An icon to include in the confirm button */
    confirmButtonIcon?: TSvgIconShapeKey | null;
}
const props = withDefaults(defineProps<CollapsibleInputGroupProps>(), {
    collapseOnBlur: true,
    collapseOnClickOutside: true,
    footerButtons: true,
    confirmButtonText: 'Done',
    expandMode: 'inline',
});

const topLevelClass = computed(() => [
    isExpanded.value ? 'collapsible-input-group-expanded' : '',
    props.expandMode ? 'collapsible-input-group-expand-' + props.expandMode : '',
    formattedValueHtml.value ? 'collapsible-input-group-filled' : '',
]);

// The HTML class(es) for the validation state indicator (astrisk)
const indicatorClass = computed(() => [
    props.validationState ? 'collapsible-input-group-indicator-' + props.validationState : '',
]);

// Whether the edit icon should be shown
const showEditIcon = computed(() => {
    return !isExpanded.value && formattedValueHtml.value;
});

const descriptionHtml = computed(() => {
    return Array.isArray(props.description) && props.description.length
        ? '<div class="collapsible-input-group-pills">' +
              props.description.map(desc => `<span class="collapsible-input-group-pill">${desc}</span>`).join('') +
              '</div>'
        : props.description;
});

const formattedValueHtml = computed(() => {
    return Array.isArray(props.formattedValue)
        ? props.formattedValue.length
            ? '<div class="collapsible-input-group-pills">' +
              props.formattedValue.map(value => `<span class="collapsible-input-group-pill">${value}</span>`).join('') +
              '</div>'
            : null
        : props.formattedValue;
});

const emit = defineEmits<{
    collapse: [];
    blur: [];
    abort: [];
    done: [];
}>();

// The HTML wrapper around the slot content
const fieldsRef = ref();
async function handleExpand() {
    if (isExpanded.value) return;
    isExpanded.value = true;
    await nextTick();
    // Focus the first input element in the slotted content
    fieldsRef.value.querySelector('input, select')?.focus();
}

async function handleCollapse() {
    emit('collapse');
    if (!isExpanded.value) return;
    isExpanded.value = false;
}

function handleClickOutside() {
    if (isExpanded.value) {
        emit('blur');
    }
    if (props.collapseOnClickOutside) handleCollapse();
}

function handleBlur() {
    emit('blur');
    if (props.collapseOnBlur) handleCollapse();
}

function handleCancel() {
    emit('abort');
    handleCollapse();
}

function handleDone() {
    emit('done');
    handleCollapse();
}

// All of the functionality for "absolute" mode, where the expandeed/edit block is a popover
const tooltipTarget = ref();
const editBlockRef = ref();
const { floatingStyles } = useFloating(tooltipTarget, editBlockRef, {
    whileElementsMounted: autoUpdate,
    strategy: 'fixed',
    transform: false,
    middleware: [autoPlacement()],
});
// The HTML Rect object of the root component element
const elRect = computed(() => rootRef.value?.getBoundingClientRect());
const popoverStyles = computed(() => {
    if (props.expandMode !== 'absolute') return;
    return {
        width: elRect.value ? elRect.value.width + 'px' : 'inherit',
        ...floatingStyles.value,
    };
});

async function handleRootFocus() {
    if (isExpanded.value) return;
    await handleExpand();
}
onMounted(async () => {
    await nextTick();
    rootRef.value?.addEventListener('focus', handleRootFocus);
    if (props.autoFocus) await handleExpand();
});
</script>

<style lang="scss">
@import './CollapsibleInputGroup';
</style>
