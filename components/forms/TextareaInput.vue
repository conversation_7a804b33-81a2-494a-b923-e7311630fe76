<template>
    <div class="textarea-input" :class="topLevelClassObj">
        <label class="textarea-input-input-wrapper" :for="elementId">
            <label
                v-if="label"
                class="textarea-input-label"
                :for="elementId"
                v-text="label"
            ></label>

            <textarea
                class="textarea-input-input"
                :placeholder="compPlaceholder"
                :id="elementId"
                :disabled="disabled"
                v-model="model"
            ></textarea>

            <!-- This is what is actually used to resize the textarea vertically -->
            <div class="textarea-input-spacer" aria-hidden="true" v-text="model"></div>
        </label>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';
import type { InputHTMLAttributes } from 'vue';

const model = defineModel<string|null>()

/** A form `<input>` field */
defineOptions();

export interface TextareaInputProps {
    /** Specific border styling for form validation states */
    validationState?: 'none' | 'error',

    /** Placeholder text for an empty input */
    placeholder?: string,

    /** Label for the input */
    label?: string,

    /** The size of the input */
    size?: 'sm' | 'lg' | null;

    /** The "tabindex" HTML attribute for the input */
    tabindex?: InputHTMLAttributes['tabindex'];

    /** The "disabled" HTML attribute for the input */
    disabled?: boolean;
}

const props = defineProps<TextareaInputProps>()

// Generate a unique ID for the input element
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'textarea-input-' + componentId);

// Generate the top-level class object for the input based on props and "filled" state
const topLevelClassObj = computed(() => [
    'textarea-input-v-' + props.validationState,
    (model.value?.length) ? ' textarea-input-filled' : '',
    (props.size) ? 'textarea-input-s-' + props.size : ''
]);

// Don't show the placeholder if there's a label
const compPlaceholder = computed(() => (props.label) ? undefined : props.placeholder)

</script>

<style lang="scss">
@import './TextareaInput';
</style>
