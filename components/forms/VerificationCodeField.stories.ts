import Verification<PERSON>odeField from './VerificationCodeField.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof VerificationCodeField> = {
    component: VerificationCodeField,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        count: {
            control: { type: 'text' },
        },
        type: {
            control: { type: 'text' },
        },
        autofocus: {
            control: { type: 'boolean' },
        }
    },

    //👇 Emitted events that we want to listen for 
    args: {
        onFilled: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof VerificationCodeField>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2777-28502&mode=design&t=I0TPLf9cRwgV7eHV-4',
        }
    }
};

export const Autofocus: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2777-28502&mode=design&t=I0TPLf9cRwgV7eHV-4',
        },
    },
    args: {
        autofocus: true,
        count: 8
    }
};
