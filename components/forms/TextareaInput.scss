.textarea-input {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    min-height: 44px;
    position: relative;
    color: var(--color-white);
    
    --textarea-input-font-size: 1rem;

    --textarea-input-label-inline-top: 10px;
    --textarea-input-label-floating-top: -2px;
    --textarea-input-label-floating-size: .75rem;

    font-size: var(--textarea-input-font-size);

    --textarea-input-hover-transition: .15s;
    transition: color var(--textarea-input-hover-transition),
                border-color var(--textarea-input-hover-transition);

    --textarea-input-accent-color: var(--color-blue-grey);
    border-bottom: 1px solid var(--textarea-input-accent-color);

    &:hover, &.textarea-input-hovered {
        --textarea-input-accent-color: var(--color-grey);
    }

    &.textarea-input-v-error {
        --textarea-input-accent-color: var(--color-red) !important;
    }

    &:focus-within, &.textarea-input-focused {
        --textarea-input-accent-color: var(--color-grey);
        .textarea-input-suffix-wrapper {
            color: var(--color-grey);
        }
    }

    .textarea-input-input-wrapper {
        height:100%;
        flex:auto;
        width: 100%;
        position: relative;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
    }

    .textarea-input-input, .textarea-input-spacer {
        appearance:none;
        background:transparent;
        outline: none;
        padding: 13px 0 6px;
        height:100%;
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--textarea-input-font-size);
        line-height: 1.5em;
        color: var(--color-white);
        flex: 1 1 auto;
    }

    .textarea-input-input {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;

        transition: color var(--textarea-input-hover-transition);
        &::placeholder {
            color: var(--color-blue-grey);
        }

        // Overwrite autofill styling in Safari
        &:-webkit-autofill, &:-webkit-autofill:focus {
            // TODO: Light mode
            -webkit-box-shadow: 0 0 0 1000px #182334 inset;
            -webkit-text-fill-color: var(--color-white);
        }
    }

    .textarea-input-label {
        position: absolute;
        top: var(--textarea-input-label-inline-top);
        left:0;
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--textarea-input-font-size);
        line-height: 1.5em;
        width:100%;
        pointer-events: none;
        color: var(--textarea-input-accent-color);

        transition: top var(--textarea-input-hover-transition),
                    font-size var(--textarea-input-hover-transition),
                    line-height var(--textarea-input-hover-transition),
                    color var(--textarea-input-hover-transition),
                    opacity var(--textarea-input-hover-transition);
    }

    .textarea-input-spacer {
        width: 100%;
        opacity: 0;
        visibility: hidden;
    }

    &.textarea-input-filled {
        &:not(:focus-within, .textarea-input-focused) {
            .textarea-input-input {
                color: var(--color-grey);
            }
        }
    }

    &:focus-within, &.textarea-input-focused, &.textarea-input-filled {
        .textarea-input-label {
            top: var(--textarea-input-label-floating-top);
            font-size: var(--textarea-input-label-floating-size);
        }
    }

    &.textarea-input-s-sm {
        min-height: 40px;
        --textarea-input-font-size: 0.875rem;
        --textarea-input-label-floating-size: 0.7142857143em;

        .textarea-input-label {
            --textarea-input-label-inline-top: 9px; 
        }
    }
}
