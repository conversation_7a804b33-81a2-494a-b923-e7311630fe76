import CheckboxInput from './CheckboxInput.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof CheckboxInput> = {
    component: CheckboxInput,
    tags: ['autodocs'],

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof CheckboxInput>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6510-32650&t=YE9koqNb59ZNfQ4c-4',
        },
    },
    args: {
        label: 'Use as default'
    }
};

export const Small: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/design/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?node-id=6510-32650&t=YE9koqNb59ZNfQ4c-4',
        },
    },
    args: {
        label: 'Use as default',
        size: 'sm'
    }
};
