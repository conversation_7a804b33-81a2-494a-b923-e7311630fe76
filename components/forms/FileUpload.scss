@import "filepond/dist/filepond.min.css";
// https://pqina.nl/filepond/docs/api/style/

.file-upload {
    display: flex;
    flex-direction: column;

    --file-upload-hover-transition: .35s;
    --file-upload-icon-color: rgba(var(--color-white-rgb), .2);
    --file-upload-transform: translateY(0) scale(1);

    transition: color var(--file-upload-hover-transition),
                border-color var(--file-upload-hover-transition);
    
    &:hover, &.file-upload-hovered {
        --file-upload-icon-color: rgba(var(--color-white-rgb), 1);
        --file-upload-transform: translateY(-2px) scale(1.02);
    }
    
    &.file-upload-v-error {
        //
    }

    .file-upload-container {
        width: 100%;
    }
    
    &.file-upload-s-sm, &.file-upload-s-xs {
        --file-upload-font-size: 0.875rem;
        
        .file-upload-label {
            margin-bottom: 6px;
        }
    }
    
    &.file-upload-s-xs {
        --file-upload-font-size: 0.75rem;
    }
    
    &.file-upload-disabled {
        --file-upload-accent-color: rgba(var(--color-blue-grey-rgb), .35);
        opacity: 0.7;
        cursor: not-allowed;
        
        .filepond--root {
            pointer-events: none;
        }
    }

    .filepond--root {
        //background-color: rgba(var(--color-blue-grey-rgb), .2);
        background-color: #263550;
        border-radius: 8px;
        margin:0;
        //background-image: url("data:image/svg+xml,%3csvg width='100%' height='100%' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%' height='100%' fill='none' stroke='%23CECECEFF' stroke-width='4' stroke-dasharray='4 11' stroke-linecap='square'/%3e%3c/svg%3e");
        border: 1px dashed var(--color-blue-grey);
    }

    .filepond--panel-root {
        background:transparent;
    }
    .filepond--root {
        min-height:300px;
        .filepond--drop-label {
            height: 300px;
        }
    }
    .filepond--slim-dropzone {
        .filepond--root {
            min-height:120px;
            .filepond--drop-label {
                height: 120px;
            }
        }
    }

    .filepond--drop-label label {
        cursor: pointer;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: column;

        color: var(--color-grey);

        .file-upload-icon {
            color: var(--file-upload-icon-color);
            transform: var(--file-upload-transform);
            margin-bottom: 30px;
            transition: color var(--file-upload-hover-transition),
                        transform var(--file-upload-hover-transition);
        }

        h3 {
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            text-align: center;

            color: var(--color-white);
        }

        p {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
            max-width: 260px;

            color: var(--color-grey);
            & + p {
                margin-top: 20px;
            }
        }

    }

    .filepond--credits {
        display:none;
    }

}
//
///* FilePond Custom Styling */
//.filepond--root {
//    font-family: var(--font-stack), sans-serif;
//    margin-bottom: 0;
//}
//
//.filepond--panel-root {
//    background-color: var(--color-dark-blue);
//    border: 1px dashed var(--color-blue-grey);
//}
//
//.filepond--drop-label {
//    color: var(--color-blue-grey);
//}
//
//.filepond--label-action {
//    text-decoration-color: var(--color-blue-grey);
//    color: var(--color-blue);
//}
//
//.filepond--file {
//    color: var(--color-white);
//}
//
//.filepond--file-action-button {
//    background-color: rgba(var(--color-dark-blue-rgb), 0.5);
//    color: var(--color-white);
//}
//
//.filepond--file-info {
//    color: var(--color-white);
//}
//
//.filepond--file-status {
//    color: var(--color-blue-grey);
//}
//
//.filepond--item {
//    margin-top: 4px;
//}
//
//.filepond--drip-blob {
//    background-color: var(--color-blue);
//}
//
//.filepond--file-action-button:hover {
//    background-color: var(--color-blue);
//}
//
//.file-upload-v-error .filepond--panel-root {
//    border-color: var(--color-red);
//}
//
//.file-upload-v-error .filepond--drop-label {
//    color: var(--color-red);
//}
//
///* Size variants */
//.file-upload-filepond-s-sm .filepond--drop-label {
//    font-size: 0.875rem;
//}
//
//.file-upload-filepond-s-xs .filepond--drop-label {
//    font-size: 0.75rem;
//}