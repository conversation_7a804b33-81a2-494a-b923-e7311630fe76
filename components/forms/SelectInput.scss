@import "/assets/css/mixins";

.select-input {
    min-width:200px;
    position: relative;

    --select-input-height: 44px;
    --select-input-font-size: 1rem;
    --select-input-label-inline-top: 10px;
    --select-input-label-floating-top: -2px;
    --select-input-label-floating-size: .75rem;
    --select-input-hover-transition: .15s;
    --select-input-text-color: var(--color-blue-grey);
    --select-input-accent-color: var(--color-blue-grey);
    --select-input-padding-right: 28px;

    transition: color var(--select-input-hover-transition),
                border-color var(--select-input-hover-transition);

    border-bottom: 1px solid var(--select-input-accent-color);

    .select-input-select {
        width:100%;
        appearance: none;
        border: none;
        background: transparent none;
        outline: none;
        height: var(--select-input-height);
        color: var(--select-input-text-color);
        
        font-size: var(--select-input-font-size);

        transition: color var(--select-input-hover-transition);

        padding-right: var(--select-input-padding-right);


        // Dropdown caret
        // background-image: url("data:image/svg+xml,%3Csvg width='12' height='8' viewBox='0 0 12 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M6.00167 8.00167L6.00334 8.00333L7.41755 6.58912L7.41588 6.58746L12.0033 2L10.5891 0.58579L6.00167 5.17324L1.41421 0.585785L0 2L4.58746 6.58746L4.58579 6.58913L6 8.00334L6.00167 8.00167Z' fill='%23CCCCCC'/%3E%3C/svg%3E%0A");
        // background-position: right 0.5rem center;
        // background-repeat: no-repeat;
        // background-size: 12px 8px;

        option {
            background: var(--color-dark-grey);
        }
    }

    .select-input-caret {
        position: absolute;
        bottom: 1px;
        right: 5px;
        width: 12px;
        color: var(--select-input-accent-color);
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        pointer-events: none;
    }

    &.select-input-empty {
        .select-input-label + .select-input-select:not(:focus) {
            opacity: 0;
        }
    }

    .select-input-label {
        position: absolute;
        top: var(--select-input-label-inline-top);
        left:0;
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--select-input-font-size);
        line-height: 1.5em;
        width:100%;
        pointer-events: none;
        color: var(--select-input-accent-color);

        transition: top var(--select-input-hover-transition),
                    font-size var(--select-input-hover-transition),
                    line-height var(--select-input-hover-transition),
                    color var(--select-input-hover-transition),
                    opacity var(--select-input-hover-transition);
    }

    &.select-input-filled {
        --select-input-text-color: var(--color-grey);
        // --select-input-accent-color: var(--color-blue-grey);
        .select-input-label {
            top: var(--select-input-label-floating-top);
            font-size: var(--select-input-label-floating-size);
        }
        &.select-input-placeholder {
            --select-input-accent-color: var(--color-white);
        }
    }

    &:focus-within, &.select-input-focused {
        --select-input-accent-color: var(--color-white);
        --select-input-text-color: var(--color-white);
        .select-input-label {
            top: var(--select-input-label-floating-top);
            font-size: var(--select-input-label-floating-size);
        }
        &.select-input-placeholder {
            --select-input-accent-color: var(--color-white);
        }
    }

    // When the select value is empty, fade out the colors
    &.select-input-placeholder {
        .select-input-select {
            color: rgba(var(--color-white-rgb), .5);
        }
    }

    &.select-input-v-error {
        --select-input-accent-color: var(--color-red) !important;
    }

    &.select-input-s-sm {
        --select-input-font-size: 0.875rem;
        --select-input-label-floating-size: 0.7142857143em;
        --select-input-padding-right: 24px;
        --select-input-height: 39px;
        .select-input-select {
            background-size: 10px 7px;
        }

        .select-input-label {
            --select-input-label-inline-top: 9px; 
        }

        .select-input-caret {
            width: 10px;
        }
    }

    &.select-input-align-right {
        .select-input-select {
            text-align: right;
            text-align-last: right;
            // direction: rtl;
            // option {
            //     direction: ltr;
            // }
        }

        .select-input-label {
            text-align: right;
            left: auto;
            right: var(--select-input-padding-right);
        }
    }
}
