<template>
    <div class="tabs-radio-group">
        <div class="tabs-radio-group-tabs">
            <CheckboxGroup mode="radio" v-model:options="options" v-model:selected="activeTab" />
        </div>

        <div class="tabs-radio-group-content" ref="contentRef">
            <template v-for="slotName in slotNames">
                <div class="tabs-radio-group-slot" v-if="activeTab === slotName">
                    <slot :name="slotName" />
                </div>
            </template>

            <div v-if="$slots.default && !activeTab" class="tabs-radio-group-default" :style="defaultSlotCss">
                <slot />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch, nextTick } from 'vue';
import type { Ref } from 'vue';
import Button from '../elements/Button.vue';
import CheckboxGroup from './CheckboxGroup.vue';
import type { CheckboxGroupOption } from './CheckboxGroup.vue';

/** A two-column layout with radio buttons on the left that changes the right-side content */
defineOptions();

// const selected = defineModel('selected');

export interface TabsRadioGroupProps {
    list: CheckboxGroupOption[];
    default?: string | null;
    defaultSlotMinHeight?: number | string | null;
}
const props = withDefaults(defineProps<TabsRadioGroupProps>(), { default: '' });

const activeTab = ref(props.default ? props.default : '');
const options = ref(props.list);

const slotNames = computed(() => props.list.map(option => option.name));

const emit = defineEmits<{
    change: [tab: string];
}>();

const defaultSlotCss = computed(() => {
    if (props.defaultSlotMinHeight) {
        const height = typeof (props.defaultSlotMinHeight === 'string')
            ? props.defaultSlotMinHeight
            : `${props.defaultSlotMinHeight}px`;
        return `min-height: ${height}`;
    }
});

const contentRef = ref<HTMLElement | null>(null);
watch(activeTab, async newVal => {
    emit('change', newVal);
    await nextTick();
    await nextTick();
    // setTimeout(() => {
    const focusableChildren = contentRef.value?.querySelector(`
            input:not([disabled]):not([type="hidden"]),
            select:not([disabled]),
            textarea:not([disabled]),
            button:not([disabled]),
            [tabindex]
        `) as HTMLElement;
    focusableChildren?.focus();
    // }, 50);
});
</script>

<style lang="scss">
@import './TabsRadioGroup';
</style>
