import { ref } from 'vue';
import InputGroupBlock from './InputGroupBlock.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import CheckboxGroup from './CheckboxGroup.vue';
import TextInput from './TextInput.vue';
import { fn } from '@storybook/test';

const meta: Meta<typeof InputGroupBlock> = {
    component: InputGroupBlock,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        title: {
            control: { type: 'text' }
        },
        description: {
            control: { type: 'text' }
        },
        layout: {
            control: { type: 'select' },
            options: ['horizontal', 'vertical'],
        },
        validationState: {
            control: { type: 'select' },
            options: ['none', 'error'],
        },
    },

    //👇 Emitted events and default args
    args: {
        // onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    render: (args) => ({
        components: { InputGroupBlock, CheckboxGroup, TextInput },
        setup() {
            const options = ref([
                {
                    label: 'Single',
                    name: 'single',
                    value: false,
                },
                {
                    label: 'Married',
                    name: 'married',
                    value: true,
                },
                {
                    label: 'Widow',
                    name: 'widow',
                    value: false,
                },
                {
                    label: 'Separated',
                    name: 'separated',
                    value: false,
                },
                {
                    label: 'Divorced',
                    name: 'divorced',
                    value: false,
                }
            ]);
            const selected = ref([]);
            return { args, options, selected };
        },
        template: `
            <InputGroupBlock v-bind="args">
                ${args.default}
            </InputGroupBlock>
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;width: 100%"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof InputGroupBlock>;

export const Name: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35074&mode=dev',
        },
    },
    args: {
        title: 'Name',
        description: 'If you need a description to help you understand your name, this may not be the right app for you.',
        default: `
            <TextInput label="First" />
            <TextInput label="Middle" />
            <TextInput label="Last" />
        `,
    }
};

export const Birthday: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35074&mode=dev',
        },
    },
    args: {
        title: 'Birthday',
        description: 'The day of your birth.',
        default: `
            <TextInput label="Birthday" type="date" :svg-icon="{shape: 'calendar'}" />
        `,
    }
};

export const RadioButtons: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35205&mode=design&t=IbzH5ePFUn0QBf7p-4',
        },
    },
    args: {
        title: 'Current marital status',
        description: 'Please only provide your current marital status. Your past life is not important to anybody.',
        default: `
            <CheckboxGroup
                variant="buttons"
                mode="radio"
                v-model:options="options"
                v-model:selected="selected"
            />`
    }
};
