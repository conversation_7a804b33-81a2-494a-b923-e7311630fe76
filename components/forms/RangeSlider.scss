@import "/assets/css/mixins";
@import 'nouislider/dist/nouislider.css';

.range-slider {

    .range-slider-values {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;

        label {
            font-weight: 400;
            font-size: 14px;
            line-height: 1.25em;
            color: var(--color-blue-grey);
            flex: none;
        }

        .range-slider-value-input {
            flex: auto;
        }
    }

    .range-slider-range {
        margin:0 18px;
    }

    .noUi-horizontal {
        .noUi-handle {
            // 34 x 28
            background: transparent !important;
            // background: rgba(255,255,255,.1) !important;
            border: none;
            box-shadow: none;
            top: 1px;
            &:before {
                width: 8px;
                height: 20px;
                border-radius: 4px;
                left: 13px;
                top: 4px;
                background: var(--color-white);
            }
            &:after {
                top: 9px;
                left: 16px;
                height: 10px;
                width: 2px;
                background: var(--color-grey-section);
                border-radius: 1px;
            }
        }
    }
    
    .noUi-connect {
        background: blue !important;
    }
    
    .noUi-target {
        background:transparent;
        border: none;
        box-shadow: none;
        margin-bottom: 40px;
        height: 30px;
    }
    
    .noUi-connect {
        background: var(--color-blue) !important;
    }
    
    .noUi-pips {
        top: 50%;
        .noUi-marker {
            &.noUi-marker-horizontal {
                top: -5px;
                height: 10px;
            }
            &.noUi-marker-normal {
                opacity: .2;
            }
        }
    
        .noUi-value {
            font-size: 12px;
            line-height: 1.25em;
            letter-spacing: 0.1em;
            font-weight: 700;
        
            &.noUi-value-large {
                color: var(--color-white);
            }
        }
    }    
}
