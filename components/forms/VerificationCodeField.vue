<template>
    <div class="verification-code-field">
        <input v-model="model" autocomplete="one-time-code" type="text"
            :inputmode="(type != 'alpha') ? 'numeric' : 'text'" :pattern="pattern" class="verification-code-field-input"
            :autofocus="autofocus" ref="verificationField"
            @keyup="handleKeyup" />
            <!-- @keyup.left="onPressLeft"
            @keyup.right="onPressRight" -->
        <div v-for="(character, index) in characterBlocks" :key="index"
            @click.prevent="() => handleCharacterClick(index)" class="verification-code-field-character"
            :class="{filled: character || character === 0, active: activeCharacterIndex === index}">
            <span>{{ character ? character : '0' }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { Ref } from 'vue';

/** Form field for inputting a OTC or verification code */
defineOptions();

const model = defineModel()
const verificationField = ref();

export type VerificationCodeFieldCharType = 'digit' | 'alpha';

export interface Props {
    count?: number;
    type?: VerificationCodeFieldCharType | Array<VerificationCodeFieldCharType>;
    autofocus?: boolean;
}
const props = withDefaults(defineProps<Props>(), {
    count: 6,
    type: 'digit',
    autofocus: false
})

const pattern = computed(() => {
    if (props.type === 'digit') {
        return `[0-9]{${props.count}}`
    }
    if (props.type === 'alpha') {
        return `[A-Za-z]{${props.count}}`
    }
    return `[0-9A-Za-z]{${props.count}}`
});

const characters = computed(() => model.value?.split("")) 

const characterBlocks = computed(() =>
    Array.from({ length: props.count }, (_, index) => 
        characters.value?.[index] || ''
    )
)

const arrowKeyPressCount = ref('');

const activeCharacterIndex = computed(() => {
    arrowKeyPressCount.value; // this is a hack to trigger a recalculation
    return Math.min(verificationField.value?.selectionStart, (characters.value?.length ?? 0));
})

const emit = defineEmits<{
    filled: [value: unknown],
}>()

const compModel = computed(() => {
    return model.value
});

watch(model, (value) => {
    // if the value is longer than the count, truncate it
    if (value.length > props.count) {
        model.value = value.slice(0, props.count);
        return;
    }

    handleCursorMove();

    // if the value is exactly the count, emit the filled event
    if (value.length >= props.count) {
        emit('filled', model.value)
    }
})

function handleKeyup(event: KeyboardEvent) {
    switch(event.key) {
        case 'ArrowLeft':
            onPressLeft();
            break;
        case 'ArrowRight':
            onPressRight();
            break;
        case 'ArrowUp':
            onPressUp();
            break;
        case 'ArrowDown':
            onPressDown();
            break;
        case 'Backspace':
            onPressBackspace();
            break;
        default:
            break;
    }
}

function handleCursorMove() {
    const location = (verificationField.value?.selectionStart >= props.count)
        ? props.count - 1
        : verificationField.value?.selectionStart;
    handleCharacterClick(location)
}

function handleCharacterClick(index: number) {
    if (verificationField.value.setSelectionRange) {
        verificationField.value.focus();
        verificationField.value.setSelectionRange(index, index + 1);
    }
}

function onPressLeft() {
    if (verificationField.value.setSelectionRange && verificationField.value?.selectionStart) {
        const start = (activeCharacterIndex.value >= model.value?.length)
            ? model.value?.length - 1
            : verificationField.value?.selectionStart - 1;
        verificationField.value.focus();
        verificationField.value.setSelectionRange(start, start + 1);
        arrowKeyPressCount.value += 1;
    }
}

function onPressRight() {
    if (verificationField.value.setSelectionRange && verificationField.value?.selectionStart) {
        const start = (model.value?.length >= props.count)
            ? model.value?.length - 1
            : verificationField.value?.selectionStart;
        verificationField.value.focus();
        verificationField.value.setSelectionRange(start, start + 1);
    }
    arrowKeyPressCount.value += 1;
}

function onPressUp() {
    if (verificationField.value.setSelectionRange) {
        verificationField.value.focus();
        verificationField.value.setSelectionRange(0, 1);
    }
    arrowKeyPressCount.value += 1;
}

function onPressDown() {
    if (verificationField.value.setSelectionRange && verificationField.value?.selectionStart) {
        verificationField.value.focus();
        const len = model.value?.length ?? 0;
        const start = (len < props.count) ? len + 1 : props.count - 1;
        verificationField.value.setSelectionRange(start, start + 1);
    }
    arrowKeyPressCount.value += 1;
}

function onPressBackspace() {
    if (verificationField.value.setSelectionRange && verificationField.value?.selectionStart) {
        verificationField.value.focus();
        verificationField.value.setSelectionRange(verificationField.value?.selectionStart - 1, verificationField.value?.selectionStart);
    }
    arrowKeyPressCount.value += 1;
}

</script>

<style lang="scss">
.verification-code-field {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
    .verification-code-field-input {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity:0;

        // opacity: 1;
        // top:100%;
        // color: var(--color-black);
    }
    .verification-code-field-character {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align:center;
        
        background: #040714;
        border: 2px solid transparent;

        width: 50px;
        height: 55px;

        border-radius: 4px;

        background: var(--color-black);

        color: rgba(var(--color-white-rgb), .2);

        // --verification-code-transition-duration: .15s;
        // transition: color var(--verification-code-transition-duration),
        //             background var(--verification-code-transition-duration),
        //             border-color var(--verification-code-transition-duration);

        span {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width:20px;
            height: 32px;
            font-size: 1rem;
            line-height: 1.5em;
            border:2px solid transparent;
        }

        &.filled {
            color: var(--color-white);
            border-color: var(--color-blue);
        }
    }
    &:focus-within {
        .verification-code-field-character {
            &.active {
                background: linear-gradient(180deg, rgba(5, 94, 250, 0.8) 0%, rgba(5, 94, 250, 1) 100%), rgb(5, 94, 250);
                border-top-color: transparent;
                span {
                    border-bottom: 2px solid #fff;
                }
            }
        }
    }
}
</style>

