<template>
    <div class="form-message" :class="classObj">
        <span v-html="message" />
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

/** A message box for errors or other feedback at the top of a form */
defineOptions();

export interface Props {
    message?: string|null;
    type?: 'error' | 'success' | 'warning' | 'info' | null;
}

const props = withDefaults(defineProps<Props>(), {
    message: null,
    type: 'info'
})

const classObj = computed(() => [
    `form-message-${props.type}`,
]);
</script>

<style lang="scss">
.form-message {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
    padding: 8px 20px;
    min-height: 60px;
    backdrop-filter: blur(10px);
    border-radius: 4px;
    
    background: var(--color-blue);
    color: var(--color-white);

    font-weight: 700;
    font-size: 14px;
    line-height: 1.45em;

    &.form-message-error {
        background: var(--color-pink);
    }
    
    &.form-message-success {
        background: var(--color-green);
    }

    &.form-message-warning {
        background: var(--color-yellow);
    }

    &.form-message-anim-enter-active, &.form-message-anim-leave-active {
        transition: all 0.3s;
        overflow:hidden;
    }

    &.form-message-anim-enter-from, &.form-message-anim-leave-to {
        height:0;
        opacity:0;
        visibility: hidden;
    }

    &.form-message-anim-enter-to , &.form-message-anim-leave-from {
        min-height:60px;
        opacity: 1;
        visibility: visible;
    }
}
</style>

