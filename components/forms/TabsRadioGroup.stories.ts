import { ref } from 'vue'; 
import TabsRadioGroup from './TabsRadioGroup.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof TabsRadioGroup> = {
    component: TabsRadioGroup,
    tags: ['autodocs'],

    excludeStories: /.*Data$/,

    parameters: {
        slots: {
            default: ''
        },
    },

    render: (args) => ({
        components: { TabsRadioGroup },
        setup() {
            return { args };
        },
        template: `
            <TabsRadioGroup
                v-bind="args"
            >
                ${args.default}
            </TabsRadioGroup>
        `,
    }),

    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TabsRadioGroup>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-27142&mode=design&t=gfNdIL3kAafgc35w-4',
        },
    },
    args: {
        list: [
            { "label": "Dolor sit amet", "name": "dolorem", "description": " Lorem ipsum dolor sit amet, consectetur adipiscing elit." },
            { "label": "Consectetur adipiscing", "name": "adipisci", "description": "Nam libero tempore, cum soluta nobis est eligendi optio." },
            { "label": "Sed do eiusmod tempor", "name": "seddo", "description": "Ut enim ad minima veniam, quis nostrum exercitationem ullam." },
            { "label": "Incididunt ut labore", "name": "labore", "description": "Sed ut perspiciatis unde omnis iste natus error sit voluptatem." },
            { "label": "Et dolore magna aliqua", "name": "magne", "description": "At vero eos et accusamus et iusto odio dignissimos ducimus." }
        ],
        default: `
            <template #dolorem><span style="color: var(--color-white)">Dolor sit amet</span></template>
            <template #adipisci><span style="color: var(--color-white)">Consectetur adipiscing</span></template>
            <template #seddo><span style="color: var(--color-white)">Sed do eiusmod tempor</span></template>
            <template #labore><span style="color: var(--color-white)">Incididunt ut labore</span></template>
            <template #magne><span style="color: var(--color-white)">Et dolore magna aliqua</span></template>
        `
    }
};
