<template>
    <div class="expense-input-row">
        <SelectInput
            size="sm"
            label="Owner"
            v-model="owner"
            :options="ownerOptions"
            placeholder="Select one"
            class="expense-input-owner"
        />
        
        <TextInput 
            v-model="description"
            placeholder="Expense description"
            size="sm"
            class="expense-input-description"
            autocomplete="off"
            :readonly="descriptionReadonly"
        />
    
        <TextInput 
            v-model="amount"
            size="sm"
            :number-formatting="true"
            min="0"
            pattern="^[0-9]*$"
            placeholder="0"
            inputmode="decimal"
            :svg-icon="{shape: 'dollar'}"
            class="expense-input-amount"
            autocomplete="off"
        />

        <SelectInput
            size="sm"
            v-model="frequency"
            :options="frequencyOptions"
            placeholder="Select one"
            class="expense-input-frequency"
            tabindex="-1"
        />

        <button class="expense-input-remove" @click="handleRemove">
            <SvgIcon shape="trash" width="20" height="20" />
        </button>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import TextInput from '../forms/TextInput.vue';
import SelectInput from '../forms/SelectInput.vue';

export interface ExpenseInputRowProps {
    id: string;
    ownerOptions: { value: string; label: string }[];
    owner: string|null;
    description: string|null;
    descriptionReadonly?: boolean;
    amount?: number|null;
    frequency?: string|null;
}

const props = defineProps<ExpenseInputRowProps>();

const owner = ref(props.owner);
const description = ref(props.description);
const amount = ref(props.amount);
const frequency = ref(props.frequency ? props.frequency : 'Monthly');

const frequencyOptions = [
    { label: 'Daily', value: 'Daily' },
    { label: 'Weekly', value: 'Weekly' },
    { label: 'Biweekly', value: 'Biweekly' },
    { label: 'Semimonthly', value: 'Semimonthly' },
    { label: 'Monthly', value: 'Monthly' },
    { label: 'Quarterly', value: 'Quarterly' },
    { label: 'Yearly', value: 'Yearly' }
]

watch(owner, (newValue) => {
    emit('change', props.id, 'owner', newValue);
});

watch(description, (newValue) => {
    emit('change', props.id, 'description', newValue);
});

watch(amount, (newValue) => {
    emit('change', props.id, 'amount', newValue);
});

watch(frequency, (newValue) => {
    emit('change', props.id, 'frequency', newValue);
});

const emit = defineEmits<{
    'change': [itemId: string, fieldName: string, newValue: string|number];
    'remove': [itemId: string];
    // 'update:owner': (value: string) => void;
    // 'update:description': (value: string) => void;
    // 'update:amount': (value: number) => void;
    // 'update:frequency': (value: string) => void;
}>();

function handleRemove() {
    emit('remove', props.id)
}

onMounted(() => {
    if (props.ownerOptions.length === 1) {
        owner.value = props.ownerOptions[0].value;
    }
})

</script>

<style scoped lang="scss">
.expense-input-row {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 5px 10px 10px;
    color: white;
    width: 100%;
    flex: 1 1 auto;
    position: relative;

    &:focus-within {
        background-color: rgba(255, 255, 255, 0.05);
    }

    &:has(.expense-input-remove:focus) {
        background-color: rgba(var(--color-red-rgb), 0.25);
    }
}

.expense-input-owner {
    width: 120px;
    min-width: 120px;
}

.expense-input-description {
    flex: auto;
    width: auto;
}

.expense-input-amount {
    width: 130px;
    min-width: 130px;
}

.expense-input-frequency {
    width: 140px;
    min-width: 140px;
}

.expense-input-remove {
    position: absolute;
    top:0;
    width: 25px;
    right: -20px;
    height: 40px;
    color: var(--color-blue-grey);
    transition: color .2s ease;
    display:none;
    &:hover {
        color: var(--color-grey);
    }
}

.expense-input-row {
    &:hover {
        .expense-input-remove {
            display: block;
        }
    }
}
</style>
