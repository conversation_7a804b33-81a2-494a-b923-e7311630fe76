import type { Meta, StoryObj } from '@storybook/vue3';
import FileUpload from './FileUpload.vue';

const meta = {
    title: 'Forms/FileUpload',
    component: FileUpload,
    tags: ['autodocs'],
    argTypes: {
        label: { control: 'text' },
        allowMultiple: { control: 'boolean' },
        acceptedFileTypes: { control: 'text' },
        maxFileSize: { control: 'text' },
        size: { control: 'select', options: [null, 'sm', 'lg'] },
        disabled: { control: 'boolean' },
        validationState: { control: 'select', options: ['none', 'error'] },
        labelIdle: { control: 'text' },
    },
    decorators: [() => ({ template: '<div style="margin: 3em;width: 80dvw;"><story/></div>' })],
} satisfies Meta<typeof FileUpload>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        label: 'Upload Files',
    },
};

export const WithAcceptedTypes: Story = {
    args: {
        label: 'Upload Images',
        acceptedFileTypes: 'image/jpeg, image/png, image/gif',
    },
};

export const WithMaxFileSize: Story = {
    args: {
        label: 'Upload Files (Max 5MB)',
        maxFileSize: '5MB',
    },
};

export const MultipleFiles: Story = {
    args: {
        label: 'Upload Multiple Files',
        allowMultiple: true,
    },
};

export const SmallSize: Story = {
    args: {
        label: 'Small Upload',
        size: 'sm',
    },
};

export const LargeSize: Story = {
    args: {
        label: 'Large Upload',
        size: 'lg',
    },
};

export const Disabled: Story = {
    args: {
        label: 'Disabled Upload',
        disabled: true,
    },
};

export const Error: Story = {
    args: {
        label: 'Error State',
        validationState: 'error',
    },
};

export const CustomLabel: Story = {
    args: {
        label: 'Custom Idle Text',
        labelIdle: 'Click here to upload or drag and drop files',
    },
};
