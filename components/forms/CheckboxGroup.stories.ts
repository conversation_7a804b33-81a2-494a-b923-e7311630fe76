import CheckboxGroup from './CheckboxGroup.vue';
import { ref } from 'vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof CheckboxGroup> = {
    component: CheckboxGroup,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        size: {
            control: { type: 'select' },
            options: ['lg', 'sm'],
            description: '',
        },

        mode: {
            control: { type: 'select' },
            options: ['radio', 'checkbox'],
            description: '',
        }
    },

    //👇 Emitted events and default args
    args: {
        selected: [],
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

     //👇 Only necessary if you need to customize the render template (e.g. slots)
    render: (args) => ({
        components: { CheckboxGroup },
        setup() {
            const { options: optionsArg, selected: selectedArg, ...rest } = args;
            const options = ref(optionsArg || []);
            const selected = ref(selectedArg || []);
            return { rest, options, selected };
        },
        template: `
            <CheckboxGroup 
                v-bind="rest"
                v-model:options="options"
                v-model:selected="selected"
            />
        `,
    }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof CheckboxGroup>;

export const MultiSelect: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-27215&mode=dev',
        },
    },
    args: {
        mode: 'checkbox',
        options: [
            {
                label: 'Residential',
                description: "Single-Family, Duplex, Triplex, Condos, Townhomes, Apartments, etc.",
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                description: "Office Buildings, Retail Spaces, Warehouses, Industrial, etc.",
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                description: "Hospitals, Churches, Government, etc.",
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                description: "Agriculture, Undeveloped land, Developed land",
                name: 'land',
                value: false,
            }
        ]
    }
};

export const Radio: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-27215&mode=dev',
        },
    },
    args: {
        mode: 'radio',
        selected: ['commercial'],
        options: [
            {
                label: 'Residential',
                description: "Single-Family, Duplex, Triplex, Condos, Townhomes, Apartments, etc.",
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                description: "Office Buildings, Retail Spaces, Warehouses, Industrial, etc.",
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                description: "Hospitals, Churches, Government, etc.",
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                description: "Agriculture, Undeveloped land, Developed land",
                name: 'land',
                value: false,
            }
        ]
    }
};

export const Small: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-27215&mode=dev',
        },
    },
    args: {
        size: 'sm',
        mode: 'checkbox',
        options: [
            {
                label: 'Residential',
                description: "Single-Family, Duplex, Triplex, Condos, Townhomes, Apartments, etc.",
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                description: "Office Buildings, Retail Spaces, Warehouses, Industrial, etc.",
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                description: "Hospitals, Churches, Government, etc.",
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                description: "Agriculture, Undeveloped land, Developed land",
                name: 'land',
                value: false,
            }
        ]
    }
};


export const ButtonsRadio: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35209&mode=design&t=IbzH5ePFUn0QBf7p-4',
        },
    },
    args: {
        variant: 'buttons',
        mode: 'radio',
        options: [
            {
                label: 'Residential',
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                name: 'land',
                value: false,
            }
        ]
    }
};

export const ButtonsMulti: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35209&mode=design&t=IbzH5ePFUn0QBf7p-4',
        },
    },
    args: {
        variant: 'buttons',
        mode: 'checkbox',
        options: [
            {
                label: 'Residential',
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                name: 'land',
                value: false,
            }
        ]
    }
};

export const ButtonsMultiWithSearch: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=4163-35209&mode=design&t=IbzH5ePFUn0QBf7p-4',
        },
    },
    args: {
        variant: 'list',
        mode: 'checkbox',
        filterable: true,
        options: [
            {
                label: 'Residential',
                description: "Single-Family, Duplex, Triplex, Condos, Townhomes, Apartments, etc.",
                name: 'residential',
                value: false,
            },
            {
                label: 'Commercial',
                description: "Office Buildings, Retail Spaces, Warehouses, Industrial, etc.",
                name: 'commercial',
                value: true,
            },
            {
                label: 'Special purpose',
                description: "Hospitals, Churches, Government, etc.",
                name: 'special-purpose',
                value: false,
            },
            {
                label: 'Land',
                description: "Agriculture, Undeveloped land, Developed land",
                name: 'land',
                value: false,
            }
        ]
    }
};
