<template>
    <div class="collapsible-input-group-story">
        <CollapsibleInputGroup
            v-if="version === 'checkboxes'"
            :description="description"
            :footer-buttons="true"
            confirm-button-text="Next"
            confirm-button-icon="keyboard-arrow-right"
            label="Additional pay"
        >
            <CheckboxGroup mode="checkbox" variant="buttons" :options="additionalPayOptions" />
        </CollapsibleInputGroup>

        <CollapsibleInputGroup v-else-if="version === 'checklist'"
            :description="description"
            :formatted-value="formattedValue"
            :collapse-on-blur="false"
            label="Your favorite foods"
            validation-state="good"
            expand-mode="absolute">
            <CheckboxGroup
                mode="checkbox"
                size="sm"
                v-model:options="favoriteFoodOptions"
                v-model:selected="selectedFavoriteFoods"
                :filterable="true"
                />
        </CollapsibleInputGroup>

        <CollapsibleInputGroup v-else :description="description" label="Address" validation-state="required">
            <div class="input-group-row">
                <TextInput size="sm" label="Street" v-model="fields.address1" />
            </div>
            <div class="input-group-row input-group-row-address">
                <TextInput size="sm" label="City" v-model="fields.city" />
                <TextInput size="sm" label="State" v-model="fields.state" />
                <TextInput size="sm" label="Zip" v-model="fields.zip" />
            </div>
        </CollapsibleInputGroup>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref } from 'vue';
import CollapsibleInputGroup from './CollapsibleInputGroup.vue'
import CheckboxGroup from './CheckboxGroup.vue'
import TextInput from './TextInput.vue'

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'collapsible-input-group-' + componentId);

const isExpanded = ref(false);

const props = defineProps<{
    version?: 'address'|'checkboxes'|'checklist';
}>();

const fields = reactive({
    address1: '',
    city: '',
    state: '',
    zip: '',
})

const addressString = computed(() => 
    [
        fields.address1,
        fields.city,
        [fields.state, fields.zip].filter(Boolean).join(' ')
    ].filter(Boolean).join(', ')
)

const additionalPayOptions = ref([
    {
        label: 'Commissions',
        name: 'commissions',
        value: false,
    },
    {
        label: 'Bonus',
        name: 'bonus',
        value: false,
    },
    {
        label: 'Distribution',
        name: 'distribution',
        value: false,
    },
    {
        label: 'Stock options',
        name: 'stock-options',
        value: false,
    },
    {
        label: 'Royalties',
        name: 'royalties',
        value: false,
    }
])

const selectedAdditionalPayOptions = computed(() => 
    additionalPayOptions.value
        .filter((o) => o.value)
        .map((o) => o.label)
)

const selectedFavoriteFoods = ref([]);
const favoriteFoodsDesc = computed(() => {
    const first = selectedFavoriteFoods.value[0];
    let str = favoriteFoodOptions.value.find(e => e.name === first)?.label ?? first;
    str = (str) ? `<strong>${str}</strong>` : str;
    if (selectedFavoriteFoods.value?.length > 1) {
        str += ` <em>+ ${selectedFavoriteFoods.value.length - 1} more</em>`
    }
    return str;
})
const favoriteFoodOptions = ref([
    { 
        label: 'Apples',
        name: 'apples',
        value: false
    },
    { 
        label: 'Bananas',
        name: 'bananas',
        value: false
    },
    { 
        label: 'Pineapples',
        name: 'pineapples',
        value: false
    },
    { 
        label: 'Pizza',
        name: 'pizza',
        value: false
    },
    { 
        label: 'Sushi',
        name: 'sushi',
        value: false
    },
    { 
        label: 'Tacos',
        name: 'tacos',
        value: false
    },
    { 
        label: 'Burgers',
        name: 'burgers',
        value: false
    },
    { 
        label: 'Ice Cream',
        name: 'iceCream',
        value: false
    },
    { 
        label: 'Chicken Wings',
        name: 'wings',
        value: false
    },
    { 
        label: 'Mac and Cheese',
        name: 'macncheese',
        value: false
    },
    { 
        label: 'Steak',
        name: 'steak',
        value: false
    },
    { 
        label: 'Fries',
        name: 'fries',
        value: false
    },
    { 
        label: 'Chocolate Cake',
        name: 'cake',
        value: false
    },
    { 
        label: 'Donuts',
        name: 'donuts',
        value: false
    },
    { 
        label: "S'mores",
        name: 'smores',
        value: false
    },
    { 
        label: 'BBQ Ribs',
        name: 'bbqRibs',
        value: false
    },
    { 
        label: 'Grilled Cheese',
        name: 'grilled-cheese',
        value: false
    },
    { 
        label: 'Fruit Salad',
        name: 'fruit-salad',
        value: false
    },
    { 
        label: 'Yogurt Parfait',
        name: 'yogurt-parfait',
        value: false
    }
])

const description = computed(() => {
    switch (props.version) {
        case 'checkboxes':
            return 'Commission, bonus, options, distributions, etc.'
        case 'checklist':
            return 'Select from a list of your favorite foods.'
        default:
            return 'The property address'
    }
})

const formattedValue = computed(() => {
    switch (props.version) {
        case 'checkboxes':
            return (selectedAdditionalPayOptions.value?.length)
                ? selectedAdditionalPayOptions.value
                : undefined
        case 'checklist':
            return (favoriteFoodsDesc.value)
                ? favoriteFoodsDesc.value
                : undefined
        default:
            return (addressString.value)
                ? addressString.value
                : undefined
    }
})

const defaultDescription = computed(() => {
    switch(props.version) {
        case 'checkboxes':
            return 'The property address';
            break;
        case 'checklist': 
            return 'Additional pay';
            break;
        default:
            return 'Your favorite foods';
    }
})

const emit = defineEmits<{
    change: [id: number]
}>()
</script>

<style lang="scss">
.collapsible-input-group-story {
    width: 800px;
    max-width: calc(100% - 40px);
    > * {
        width:100%;
    }
}
</style>
