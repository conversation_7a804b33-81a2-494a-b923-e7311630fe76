@import "/assets/css/mixins";

.input-group-block {
    border-radius: 4px;
    background: rgba(var(--color-white-rgb), 0.05);
    width: 100%;
    gap: 10px;
    color: var(--color-white);

    display: flex;
    align-items: center;
    justify-content: flex-start;

    .input-group-block-label {
        width: 300px;
        flex: none;
        padding: 20px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
        
        label {
            font-size: 14px;
            font-weight: 700;
            line-height: 1.45em;
        }
        p {
            color: var(--color-blue-grey);
            font-size: 14px;
            font-weight: 400;
            line-height: 1.45em;
        }
    }

    .input-group-block-fields {
        flex: 1 1 auto;
        display: grid;
        padding: 20px;
        gap: 4px;
        @include grid-columns();
    }

    .input-group-block-fields-multiple {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        padding: 20px;
        gap: 4px;
        @include grid-columns();
    }

    &.input-group-block-l-vertical {
        flex-direction: column;
        align-items: stretch;
        justify-content: flex-start;
        padding: 20px;
        gap: 20px;
        .input-group-block-label {
            width: auto;
            padding: 0;
            label {
                font-size: 18px;
                font-weight: 700;
                line-height: 1.15em;
            } 
            p {
                font-size: 14px;
                font-weight: 400;
                line-height: 1.45em;
            }
        }
        .input-group-block-fields {
            padding:0;
        }
    }
}
