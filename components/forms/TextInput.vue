<template>
    <div class="text-input" :class="topLevelClassObj">
        <label v-if="iconProps" class="text-input-icon" :class="inputIconClassObj" :for="elementId">
            <SvgIcon v-bind="iconProps" />
        </label>

        <label class="text-input-input-wrapper" :for="elementId">
            <label v-if="label" class="text-input-label" :for="elementId" v-text="label"></label>

            <span v-if="floatingDollar" class="text-input-floating-dollar-wrapper">
                <span class="text-input-floating-dollar-wrapper-sign">$</span>
                <span class="text-input-floating-dollar-wrapper-ghost" v-text="formattedValue"></span>
            </span>

            <input
                v-if="numberFormatting"
                class="text-input-input"
                :placeholder="compPlaceholder"
                :type="inputType"
                :inputmode="inputmode"
                :id="elementId"
                :disabled="disabledComp"
                :min="min"
                :max="max"
                :autocomplete="autocomplete"
                :readonly="readonly"
                :tabindex="tabIndexComp"
                :name="name"
                :pattern="pattern"
                ref="inputRef"
                v-model="formattedValue"
            />

            <input
                v-else
                class="text-input-input"
                :placeholder="compPlaceholder"
                :type="inputType"
                :inputmode="inputmode"
                :id="elementId"
                :disabled="disabledComp"
                :min="min"
                :max="max"
                :autocomplete="autocomplete"
                :readonly="readonly"
                :tabindex="tabIndexComp"
                :name="name"
                v-model="model"
            />

            <div class="text-input-suffix-wrapper" v-if="suffix">
                <span class="text-input-suffix-placeholder" v-text="suffixPlaceholderContent"></span>
                <label v-if="suffix" class="text-input-suffix" :for="elementId" v-text="suffix"></label>
            </div>
        </label>

        <button
            class="text-input-password-toggle"
            v-if="type === 'password'"
            type="button"
            @click="passwordVisible = !passwordVisible"
        >
            <SvgIcon :shape="passwordVisible ? 'hide' : 'show'" />
        </button>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, getCurrentInstance, watch } from 'vue';
// import { computed, ref, getCurrentInstance, watch } from '#imports';
import type { InputHTMLAttributes } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Props as SvgIconProps } from '../images/SvgIcon.vue';
import { useCurrencyInput } from 'vue-currency-input';
import type { CurrencyInputOptions } from 'vue-currency-input';

const model = defineModel<string | number | null>();

/** A form `<input>` field */
defineOptions();

export type TextInputSvgIconProps = SvgIconProps & {
    position?: 'left' | 'right' | null;
};

export interface TextInputProps {
    /** Specific border styling for form validation states */
    validationState?: 'none' | 'error';

    /** Placeholder text for an empty input */
    placeholder?: string;

    /** Label for the input */
    label?: string;

    /** The "type" of the HTML input for semantics and in-context virtual keyboards */
    type?: InputHTMLAttributes['type'];

    /** The size of the input */
    size?: 'xs' | 'sm' | 'lg' | null;

    /** The "autocomplete" HTML attribute for the input */
    autocomplete?: InputHTMLAttributes['autocomplete'];

    /** The "inputmode" HTML attribute for the input */
    inputmode?: InputHTMLAttributes['inputmode'];

    /** The "tabindex" HTML attribute for the input */
    tabindex?: InputHTMLAttributes['tabindex'];

    /** The "tabindex" HTML attribute for the input */
    name?: InputHTMLAttributes['name'];

    /** The "disabled" HTML attribute for the input */
    disabled?: boolean;

    /** Optionally include an icon before or after the input field */
    svgIcon?: Partial<TextInputSvgIconProps> | null;

    /** A suffix that floats to the left of the input (eg "/hour") */
    suffix?: string;

    /** Whether to apply number formatting; see https://dm4t2.github.io/vue-currency-input/config.html for all options */
    numberFormatting?: boolean | Partial<CurrencyInputOptions>;

    /** The "min" HTML attribute for the input */
    min?: string | number;

    /** The "max" HTML attribute for the input */
    max?: string | number;

    /** Sets the "readonly" property on the input */
    pattern?: InputHTMLAttributes['pattern'];

    /** Sets the "readonly" property on the input */
    readonly?: InputHTMLAttributes['readonly'];

    /** Whether to show the bottom border */
    withBorder?: boolean;

    textAlign?: 'left' | 'right';

    floatingDollar?: boolean;

    showBorderOnFocus?: boolean;
}

const props = withDefaults(defineProps<TextInputProps>(), {
    validationState: 'none',
    type: 'text',
    inputmode: 'none',
    svgIcon: null,
    numberFormatting: false,
    min: '',
    max: '',
    withBorder: true,
    textAlign: 'left',
    showBorderOnFocus: false,
});

// When type is "password", this controls whether the input is a "password" or "text" type
const passwordVisible = ref(false);

// Generate a unique ID for the input element
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'text-input-' + componentId);

// Determine the input type, based on the "type" prop and whether the password is visible
const inputType = computed(() => {
    if (props.type === 'password') {
        return passwordVisible.value ? 'text' : 'password';
    }
    return props.type;
});

const tabIndexComp = computed(() => ((props.tabindex ?? (props.readonly || props.disabled)) ? -1 : null));
const disabledComp = computed(() => props.disabled || props.readonly);

// Generate the top-level class object for the input based on props and "filled" state
const topLevelClassObj = computed(() => [
    'text-input-v-' + props.validationState,
    model.value?.length || (numberValue.value ?? -1 >= 0) ? ' text-input-filled' : '',
    props.size ? 'text-input-s-' + props.size : '',
    props.readonly ? 'text-input-readonly' : '',
    props.withBorder ? 'text-input-with-border' : '',
    props.floatingDollar ? 'text-input-floating-dollar' : '',
    props.textAlign ? 'text-input-align-' + props.textAlign : '',
    props.showBorderOnFocus ? 'text-input-border-on-focus' : '',
]);

// Generate the props object to be passed to the SvgIcon component, based on this component's "svgIcon" prop
const iconProps = computed(() => {
    if (!props.svgIcon) return null;

    // Remove color and position from the svgIcon prop
    const { color, position, ...rest } = props.svgIcon;

    // Specify the icon size, depending on the input size; 20px for small, 24px otherwise
    const size = props.size === 'sm' ? 20 : 24;

    return {
        ...rest,
        width: size,
        height: size,
    };
});

// The suffix is positioned based on an invisble span with the same content as the input or placeholder
// This generates the content for that invisible span
const suffixPlaceholderContent = computed(() => {
    if (model.value?.length || formattedValue.value?.length) {
        return props.numberFormatting ? formattedValue.value : model.value;
    } else {
        return compPlaceholder.value ?? '';
    }
});

// Generate the class object for the icon label, based on the icon position
const inputIconClassObj = computed(() => [props.svgIcon?.position === 'right' ? `text-input-icon-right` : '']);

// Don't show the placeholder if there's a label
const compPlaceholder = computed(() => (props.label ? undefined : props.placeholder));

// useCurrencyInput emits change events
const emit = defineEmits<{
    change: [];
}>();

//   _   _                 _                  __                            _   _   _
//  | \ | |_   _ _ __ ___ | |__   ___ _ __   / _| ___  _ __ _ __ ___   __ _| |_| |_(_)_ __   __ _
//  |  \| | | | | '_ ` _ \| '_ \ / _ \ '__| | |_ / _ \| '__| '_ ` _ \ / _` | __| __| | '_ \ / _` |
//  | |\  | |_| | | | | | | |_) |  __/ |    |  _| (_) | |  | | | | | | (_| | |_| |_| | | | | (_| |
//  |_| \_|\__,_|_| |_| |_|_.__/ \___|_|    |_|  \___/|_|  |_| |_| |_|\__,_|\__|\__|_|_| |_|\__, |
//                                                                                          |___/
const currencyInputOptions = computed<CurrencyInputOptions>(() => {
    const customOptions = typeof props.numberFormatting === 'object' ? props.numberFormatting : {};
    return {
        currency: 'USD',
        currencyDisplay: 'hidden',
        hideGroupingSeparatorOnFocus: false,
        precision: 0,
        hideCurrencySymbolOnFocus: false,
        hideNegligibleDecimalDigitsOnFocus: false,
        autoDecimalDigits: false,
        useGrouping: true,
        accountingSign: false,
        ...customOptions,
    } as CurrencyInputOptions;
});
const { inputRef, numberValue, formattedValue, setValue } = useCurrencyInput(currencyInputOptions.value);

watch(
    () => model.value,
    value => {
        setValue(value);
    },
);
</script>

<style lang="scss">
@import './TextInput';
.text-input-border-on-focus {
    border-bottom: none !important;
    transition: border-bottom 0.2s;
    &:focus-within {
        border-bottom: 2px solid var(--primary, #57719c) !important;
    }
}
</style>
