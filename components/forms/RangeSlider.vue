<template>
    <div class="range-slider">
        <div class="range-slider-range" ref="range"></div>
        <div class="range-slider-values">
            <label class="range-slider-value-label">From</label>
            <TextInput :number-formatting="mode === 'currency'" :svg-icon="mode === 'currency' ? {shape: 'dollar'} : null" size="sm" class="range-slider-value-input" inputmode="numeric" :min="min" :max="max" v-model="bottomRef" />
            <label class="range-slider-value-label">To</label>
            <TextInput :number-formatting="mode === 'currency'" :svg-icon="mode === 'currency' ? {shape: 'dollar'} : null" size="sm" class="range-slider-value-input" inputmode="numeric" :min="min" :max="max" v-model="topRef" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref, useTemplateRef, onMounted, nextTick, watch } from 'vue';
import type { Ref } from 'vue';
import noUiSlider, { PipsType } from 'nouislider';
import TextInput from './TextInput.vue';

/** TODO: A slider with a range of values */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'range-slider-' + componentId);

export interface RangeSliderProps {
    min: number;
    max: number;
    top?: number;
    bottom?: number;
    // step?: number;
    mode?: 'year'|'currency'|null;
}
const props = defineProps<RangeSliderProps>();

const emit = defineEmits<{
    change: [payload: {bottom: number, top: number}];
}>()

const rangeEl = useTemplateRef('range');
const bottomRef = ref(props.bottom ?? props.min);
const topRef = ref(props.top ?? props.max);

const data = reactive({
    bottom: props.bottom ?? props.min,
    top: props.top ?? props.max,
})

const step = computed(() => {
    if (props.mode === 'year') return 1;
    if (props.mode === 'currency') {
        const range = props.max - props.min;
        return range / 10;
    }
    return 1;
})

const slider = ref<any>();

function handleSliderChange(values: (string | number)[], handle: number, unencoded: number[], tap: boolean, positions: number[], noUiSlider: any) {
    return;
    // bottomRef.value = unencoded[0];
    // topRef.value = unencoded[1];
}

function handleSliderUpdate(values: (string | number)[], handle: number, unencoded: number[], tap: boolean, positions: number[], noUiSlider: any) {
    bottomRef.value = Math.round(unencoded[0]);
    topRef.value = Math.round(unencoded[1]);

    emit('change', {bottom: unencoded[0], top: unencoded[1]});
}

const pips = computed(() => {
    if (props.mode === 'year') {
        return {
            mode: 'steps',
            values: [0, 25, 50, 75, 100],
            density: 5,
            filter: (value: number, type: PipsType) => (type === 1 || value % 5 === 0) ? 1 : 0,
        };
    } else if (props.mode === 'currency') {
        return {
            mode: 'positions',
            values: [0, 25, 50, 75, 100],
            density: 5,
            stepped: true,
            format: {
                to: function(value: number) {
                    return Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: 'USD',
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                        })
                        .format(value);
                },
                from: function(value: string) {
                    return parseInt(value.replace("$", ""));
                }
            }
        }
    } else {
        return {
            mode: 'positions',
            values: [0, 25, 50, 75, 100],
            density: 5,
            stepped: true
        }
    }
})

onMounted(async () => {
    await nextTick();
    if (rangeEl.value) {
        slider.value = noUiSlider.create(rangeEl.value, {
            start: [props.bottom ?? props.min, props.top ?? props.max],
            step: 1,
            range: {
                'min': [props.min],
                'max': [props.max]
            },
            connect: [false, true, false],
            pips: pips.value
        })
        slider.value.on('change', handleSliderChange);
        slider.value.on('update', handleSliderUpdate);
    }
})

watch(bottomRef, (newVal) => {
    if (Math.floor(slider.value?.get()?.[0] ?? 0) == newVal) return;
    slider.value.set([newVal, topRef.value]);
})

watch(topRef, (newVal) => {
    if (Math.floor(slider.value?.get()?.[1] ?? 0) == newVal) return;
    slider.value?.set([bottomRef.value, newVal]);
})

</script>

<style lang="scss">
@import "./RangeSlider";
</style>
