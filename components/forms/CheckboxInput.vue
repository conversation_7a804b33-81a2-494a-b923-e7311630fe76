<!--suppress HtmlUnknownTag -->
<template>
    <div class="checkbox-input" :class="topLevelClassObj">
        <label :for="elementId" :class="{ 'no-label': !showLabel }">
            <input v-model="model" v-bind="valueProps" :id="elementId" :name="inputName" :type="type" />
            <div class="checkbox-input-faux-input"></div>

            <div v-if="showLabel" class="checkbox-input-label">
                <span class="checkbox-input-option-label" v-text="label"></span>
            </div>
        </label>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';

/** A simple inline checkbox with an optional label */
defineOptions();

const model = defineModel();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => props.id ?? 'checkbox-input-' + componentId);

const inputName = computed(() => props.name ?? '');

export interface CheckboxInputProps {
    name?: string;
    label?: string;
    id?: string;
    size?: 'xs' | 'sm' | 'lg' | null;
    type?: 'radio' | 'checkbox';
    value?: unknown;
    align?: 'left' | 'right' | 'center' | 'space-between';
    showLabel?: boolean;
}

const props = withDefaults(defineProps<CheckboxInputProps>(), {
    type: 'checkbox',
    showLabel: true,
});

const valueProps = computed(() => {
    return props.value ? { value: props.value } : null;
});

const topLevelClassObj = computed(() => [
    props.size ? `checkbox-input-s-${props.size}` : '',
    props.type === 'radio' ? 'checkbox-input-t-radio' : '',
    props.align ? 'checkbox-input-align-' + props.align : '',
]);
</script>

<style lang="scss">
.checkbox-input {
    --checkbox-group-duration: 0.15s;
    --checkbox-group-input-size: 24px;
    --checkbox-group-svg-size: 100px 100px;

    --checkbox-input-align: center;

    &.checkbox-input-s-sm {
        --checkbox-group-input-size: 20px;
        --checkbox-group-svg-size: 67px 67px;
    }

    &.checkbox-input-s-xs {
        --checkbox-group-input-size: 14px;
        --checkbox-group-svg-size: 50px 50px;
        > label {
            gap: 10px;
        }
        .checkbox-input-label {
            font-weight: 700;
        }
    }

    &.checkbox-input-t-radio {
        .checkbox-input-faux-input {
            border-radius: 9999em;
        }
    }

    &.checkbox-input-align-left {
        --checkbox-input-align: flex-start;
    }
    &.checkbox-input-align-right {
        --checkbox-input-align: flex-end;
    }
    &.checkbox-input-align-center {
        --checkbox-input-align: center;
    }
    &.checkbox-input-align-space-between {
        --checkbox-input-align: space-between;
    }

    > label {
        display: flex;
        align-items: center;
        justify-content: var(--checkbox-input-align, center);
        gap: 20px;

        &.no-label {
            justify-content: center;
            gap: 0;
        }
    }
    .checkbox-input-faux-input {
        width: var(--checkbox-group-input-size);
        height: var(--checkbox-group-input-size);
        flex: none;
        border: 2px solid rgba(var(--color-white-rgb), 0.1);

        border-radius: 4px;
        overflow: hidden;
        position: relative;

        transition:
            border-color var(--checkbox-group-duration) ease-in-out,
            opacity var(--checkbox-group-duration) ease-in-out;

        &.checkbox-faux-radio {
            border-radius: 9999em;
        }

        &:before {
            width: 100px;
            height: 100px;
            // TODO: Light mode
            //content: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M100 0H0V100H100V0ZM42.5338 49.692L47.0381 54.0455L58.477 43L60 44.472L47.0381 57L41 51.1641L42.5338 49.692Z' fill='%23E9EDF1'/%3E%3C/svg%3E%0A");
            background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M100 0H0V100H100V0ZM42.5338 49.692L47.0381 54.0455L58.477 43L60 44.472L47.0381 57L41 51.1641L42.5338 49.692Z' fill='%23E9EDF1'/%3E%3C/svg%3E%0A");
            background-size: var(--checkbox-group-svg-size);
            background-position: center;
            background-repeat: no-repeat;
            position: absolute;
            top: calc(50% - 50px);
            left: calc(50% - 50px);
            content: '';

            opacity: 0;
            transform: scale(0.5);

            transition:
                opacity var(--checkbox-group-duration) ease-in-out,
                transform var(--checkbox-group-duration) ease-in-out;
        }
    }

    input {
        display: none;
    }

    input:checked + .checkbox-input-faux-input {
        border-color: var(--color-white);
        &:before {
            opacity: 1;
            transform: scale(1);
        }
    }

    .checkbox-input-label {
        color: var(--color-blue-grey);
        font-size: 14px;
        font-weight: 400;
        line-height: 1.25em;
        letter-spacing: -0.28px;
        user-select: none;
        transition: color var(--checkbox-group-duration) ease-in-out;
    }

    input:checked ~ .checkbox-input-label {
        color: var(--color-grey);
    }

    &:hover {
        .checkbox-input-faux-input {
            border-color: rgba(var(--color-blue-grey-rgb), 0.65);
        }
        .checkbox-input-label {
            // color: rgba(var(--color-grey-rgb),.8);
        }
        input:checked ~ .checkbox-input-label {
            color: var(--color-grey);
        }
    }
}
</style>
