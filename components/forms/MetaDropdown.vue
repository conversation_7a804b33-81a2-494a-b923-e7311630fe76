<template>
    <div class="meta-dropdown">
        <select
            :id="elementId"
            class="meta-dropdown-select"
            v-model="selectedValue"
            @change="handleChange"
            ref="selectRef"
        >
            <option v-for="(option, index) in options" :key="index" :value="option">
                {{ option }}
            </option>
        </select>
        <SvgIcon class="meta-dropdown-arrow" shape="keyboard-arrow-down" />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';

const componentId = getCurrentInstance()?.uid || Math.random().toString(36).substr(2, 9);
const elementId = computed(() => 'meta-dropdown-' + componentId);

// Props
export interface MetaDropdownProps {
    options: string[];
    modelValue: string;
}

const props = withDefaults(defineProps<MetaDropdownProps>(), {
    options: () => ['Entertainment', 'Recurring Entertainment', 'Discretionary'],
    modelValue: '',
});

const emit = defineEmits<{
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string): void;
}>();

const selectedValue = ref(props.modelValue);
const selectRef = ref<HTMLSelectElement>();

// Keep internal value in sync with parent
watch(
    () => props.modelValue,
    newVal => {
        if (newVal !== selectedValue.value) selectedValue.value = newVal;
    },
);

function handleChange() {
    emit('update:modelValue', selectedValue.value);
    emit('change', selectedValue.value);

    // Blur the select element to remove focus/active state
    if (selectRef.value) {
        selectRef.value.blur();
    }
}
</script>

<style lang="scss">
.meta-dropdown {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .meta-dropdown-select {
        background: transparent;
        border: none;
        border-bottom: 1px solid rgb(48 60 80);
        color: #fff;
        padding: 10px 32px 10px 8px;
        width: 100%;
        font-family: Oxygen, sans-serif;
        font-size: 14px;
        appearance: none;
        outline: none;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;

        &:focus,
        &:hover {
            border-bottom-color: #b9c9e5;
        }
    }

    .meta-dropdown-arrow {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        width: 20px;
        height: 20px;
        color: #b9c9e5;
        display: none;
    }

    .meta-dropdown-select:focus + .meta-dropdown-arrow,
    .meta-dropdown-select:hover + .meta-dropdown-arrow {
        display: block;
    }
}
</style>
