import FormMessage from './FormMessage.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
// import { action } from '@storybook/addon-actions';

const meta: Meta<typeof FormMessage> = {
    component: FormMessage,
    tags: ['autodocs'],

    //👇 Our args will be mapped in Storybook UI
    argTypes: {
        message: {
            description: 'The message to display',
        },
        type: {
            control: { type: 'select' },
            options: ['error', 'warning', 'info', 'success'],
            description: 'The type/color of message',
        }
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof FormMessage>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2827-34555&mode=design&t=m3nGxUhAW7TbcP17-4',
        // },
    },
    args: {
        message: "Don't forget to contact your senator"
    }
};

export const Error: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2827-34555&mode=design&t=m3nGxUhAW7TbcP17-4',
        },
    },
    args: {
        message: 'Your password is incorrect',
        type: 'error'
    }
};
