@import "/assets/css/mixins";

.collapsible-input-group {
    position: relative;
    background: rgba(var(--color-white-rgb), 0.05);
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
    gap: 10px;
    transition: background .2s ease-in-out;
    cursor: pointer;

    & + .collapsible-input-group {
        margin-top: 1px;
    }

    .collapsible-input-group-view-content {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        gap: 10px;
    }

    .collapsible-input-group-tooltip-target {
        position: absolute;
        top: 0;
        left: 0;
    }

    .collapsible-input-group-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10;
        display: block;
    }

    .collapsible-input-group-edit-icon {
        position: absolute;
        top: 18px;
        right: 18px;
        color: var(--color-blue-grey);
        transition: color .2s;
    }

    .collapsible-input-group-pulse {
        height: 100%;
        position: absolute;
        top: 0;
        overflow-y: hidden;

        background: var(--color-grey);
        
        width: 10px;
        left: -5px;
        background: transparent;
        
        &:before {
            position: absolute;
            top:0;
            left:5px;
            width:1px;
            height: 100%;
            content: '';
            display: block;
            background: var(--color-grey);
            box-shadow: 0 0 0 0 rgba(0, 0, 0, 1);
            animation: pulse 3.5s infinite ease-out;
            // animation-delay: 0.6666667s;
            // animation: pulse-2 0.8s ease-out infinite;

            // overflow:hidden;
            @keyframes pulse {
                0%{
                    box-shadow: 0 0 0 0 rgba(var(--color-teal-rgb), 0.7);
                }

                10% {
                    background: var(--color-white);
                    box-shadow: 0 0 3px 2px rgba(var(--color-teal-rgb), 0.3);
                    // box-shadow: 0 0 0 4px red;
                }

                40%, 100% {
                    background: var(--color-grey);
                    box-shadow: 0 0 0 0 rgba(var(--color-teal-rgb), 0);
                }
            }
        }

        // &:before {
        //     content: '';
        //     display: block;
        //     top:0;
        //     left:0;
        //     position:absolute;
        //     background: var(--color-blue-grey);
        //     height: 100%;
        //     width: 3px;
        //     transform: translateX(-4px);
        //     animation: shoot-before 2.2s infinite ease-out;
        //     @keyframes shoot-before {
        //         0% {
        //             opacity: 1;
        //             transform: translateX(-1px);
        //         }
        //         100% {
        //             opacity: 0;
        //             transform: translateX(-7px);
        //         }
        //     }
        // }

        // Shooting gradient
        // &:after {
        //     content: '';
        //     display: block;
        //     top:0;
        //     left:0;
        //     position:absolute;
        //     background: var(--color-blue-grey);
        //     background: linear-gradient(180deg,
        //         rgba(var(--color-teal-rgb), 0) 0%,
        //         rgba(var(--color-teal-rgb), .4) 40%,
        //         rgba(var(--color-teal-rgb), 1) 50%,
        //         rgba(var(--color-teal-rgb), .4) 60%,
        //         rgba(var(--color-teal-rgb), 0) 100%
        //     );
        //     height: 100%;
        //     width: 1px;
        //     transform: translateY(100%);
        //     animation: shoot-after 2.2s infinite ease-in-out;
        //     @keyframes shoot-after {
        //         0% {
        //             // opacity: 1;
        //             transform: translateY(100%);
        //         }
        //         100% {
        //             // opacity: 0;
        //             transform: translateY(-100%);
        //         }
        //     }
        // }


        // @keyframes pulse-2 {
        //     0% {
        //         box-shadow: 0;
        //     }
        //     70% {
        //         box-shadow: 0 0 0 1px #aaa, 0 0 0 1px rgba(255, 255, 255, 0.75);
        //     }
        //     100% {
        //         box-shadow: 0 0 0 3px #aaa, 0 0 0 2px rgba(255, 255, 255, 0);
        //     }
        // }

        

        // &:before {
        //     height:50px;
        //     width:100%;
        //     background: var(--color-blue-grey);
        //     // background gradient from white to transparent
        //     background: linear-gradient(180deg, rgba(var(--color-blue-rgb), .4) 0%, rgba(var(--color-teal-rgb), 0) 100%);
        //     content: '';
        //     display: block;
        //     position: absolute;
        //     top: calc(100% + 50px);
        //     // box-shadow: 0 4px 0 4px rgba(var(--color-blue-grey-rgb), 0.75);

        //     animation: shoot 2.2s infinite ease-in-out;

        //     @keyframes shoot {
        //         0% {
        //             top: calc(100% + 50px);
        //         }
        //         // 50% {
        //         //     transform: translateY(-10px);
        //         // }
        //         100% {
        //             top: -50px;
        //         }
        //     }
        // }
        
    }

    .collapsible-input-group-indicator {
        color: rgba(var(--color-white-rgb),.5);
        &.collapsible-input-group-indicator-good {
            color: var(--color-green);
        }
        &.collapsible-input-group-indicator-bad {
            color: var(--color-red);
        }
    }

    .collapsible-input-group-label {
        padding: 20px 20px 0;
        font-size: 14px;
        font-weight: 700;
        line-height: 1.45em;
        color: var(--color-grey);
        transition: color .2s ease-in-out;
        user-select: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 2px;
        &:last-child {
            padding-bottom: 20px;
        }
        .collapsible-input-group-indicator {
            margin-bottom:10px;
        }
    }

    .collapsible-input-group-description, .collapsible-input-group-edit-description {
        padding: 0 20px 20px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.45em;
        color: var(--color-blue-grey);
        transition: color .2s ease-in-out;
        user-select: none;

        strong {
            color: var(--color-white);
        }

        em {
            color: var(--color-grey);
            font-style: normal;
        }

        .collapsible-input-group-pills {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 4px;
            flex-wrap: wrap;
        }

        .collapsible-input-group-pill {
            display: flex;
            padding: 2px 4px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            white-space: nowrap;
            flex-wrap: nowrap;
            color: var(--color-white);
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 1.45em;
            height: 24px;

            border-radius: 4px;
            background: rgba(var(--color-white-rgb), 0.2);

            &:after {
                // TODO: Light mode
                background-image: url("data:image/svg+xml,%3Csvg width='23' height='18' viewBox='0 0 23 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.29493 13.8069L1.85309 8.36503L0 10.2051L7.29493 17.5L22.9549 1.84004L21.1148 0L7.29493 13.8069Z' fill='%23FFFFFF'/%3E%3C/svg%3E%0A");
                background-size: contain;
                background-position: center;
                background-repeat: no-repeat;
                content: "";

                width: 11px; 
                height: 11px;
                margin:0 2px;
            }
        }
    }

    .collapsible-input-group-edit-description {
        padding-bottom:10px;
    }

    &.collapsible-input-group-expanded {
        cursor: default;
        .collapsible-input-group-pulse {
            display:none;
        }
        .collapsible-input-group-overlay {
            display:none;
        }
        .collapsible-input-group-label {
            &:last-child {
                padding-bottom: 0;
            }
        }
    }

    &:hover, &.collapsible-input-group-expanded {
        background: rgba(var(--color-white-rgb), 0.10);
        .collapsible-input-group-label {
            color: var(--color-white);
        }
        .collapsible-input-group-description {
            color: var(--color-grey);
            strong {
                color: var(--color-white);
            }
        }
        .collapsible-input-group-edit-icon {
            color: var(--color-grey);
        }
    }

    &.collapsible-input-group-filled {
        &:not(.collapsible-input-group-expanded) {
            .collapsible-input-group-indicator {
                &.collapsible-input-group-indicator-required, &.collapsible-input-group-indicator-good {
                    display: none;
                }
            }
        }
        &.collapsible-input-group-expanded {
            .collapsible-input-group-indicator {
                &.collapsible-input-group-indicator-required {
                    color: var(--color-green);
                }
            }
        }
    }

    .collapsible-input-group-edit {
        display: none;
        width: 100%;
        align-items: stretch;
        justify-content: flex-start;
        flex-direction: column;

        .collapsible-input-group-edit-title {
            display: flex;
            align-items: flex-start;
            justify-content: center;
            flex-direction: column;
            gap: 10px;
            // min-height:90px;
            flex: none;
            border-bottom: 1px solid rgba(var(--color-blue-grey-rgb), .2);
            padding: 20px;
            // background: rgba(244,244,0,.1);
            .collapsible-input-group-label {
                padding: 0;
            }
            .collapsible-input-group-description {
                padding: 0;
            }
        }
    }

    .collapsible-input-group-fields {
        padding: 0 20px 20px;
        align-items: stretch;
        justify-content: flex-start;
        gap: 10px;
        flex-direction: column;
        width: 100%;

        .input-group-row {
            flex: 1 1 auto;
            display: grid;
            width: 100%;
            gap: 10px;
            @for $i from 2 through 6 {
                &:has(> :last-child:nth-child(#{$i})) {
                    grid-template-columns: repeat(#{$i}, 1fr);
                }
            }
            & + .input-group-row {
                margin-top: 10px;
            }
        }
    }

    &.collapsible-input-group-expanded {
        .collapsible-input-group-edit {
            display:flex;
        }
        &:not(.collapsible-input-group-expand-absolute) {
            .collapsible-input-group-description {
                display:none;
            }
        }
        &.collapsible-input-group-expand-absolute {
            .collapsible-input-group-view-content {
                display:none;
            }
        }
    }

    &.collapsible-input-group-expand-absolute {
        .collapsible-input-group-fields {
            padding:0;
        }

        .collapsible-input-group-edit-close {
            position: absolute;
            top: 20px;
            right: 20px;
            color: var(--color-grey);
            transition: color .2s;
            &:hover {
                color: var(--color-white);
            }
        }
        
        .collapsible-input-group-edit {
            position:absolute;
            top:0;
            left:0;
            width: 100%;
            // max-height: calc(100dvh - 40px);
            height: calc(100dvh - 40px);
            // height:00px;
            background: #2F3C50;
            overflow: hidden;

            transform-origin: center center;
            // opacity: .5;

            --collapsible-input-group-edit-transition-duration: 0s;
            --collapsible-input-group-edit-transition-function: ease;
            transition-duration: var(--collapsible-input-group-edit-transition-duration);
            transition-timing-function: var(--collapsible-input-group-edit-transition-function);

            &.collapsible-input-group-edit-enter, &.collapsible-input-group-edit-enter-active {
                transition-property: opacity, visibility, transform;
                --collapsible-input-group-edit-transition-duration: .2s;
                --collapsible-input-group-edit-transition-function: ease-out;
            }
            &.collapsible-input-group-edit-leave, &.collapsible-input-group-edit-leave-active {
                transition-property: opacity, visibility, transform;
                --collapsible-input-group-edit-transition-duration: .2s;
                --collapsible-input-group-edit-transition-function: ease-in;
            }
            &.collapsible-input-group-edit-enter-from, &.collapsible-input-group-edit-leave-to {
                opacity:0;
                visibility: hidden;
                transform: scale(.95);
            }
            &.collapsible-input-group-edit-enter-to, &.collapsible-input-group-edit-leave-from {
                opacity: 1;
                visibility: visible;
                transform: scale(1);
            }
        }

        .collapsible-input-group-fields {
            flex: 1 1 auto;
        }

        .checkbox-group {
            height: 100%;
            position: relative;
        }
        .checkbox-group-filter-bar {
            background: transparent;
            width: 100%;
            border-bottom: 1px solid rgba(var(--color-blue-grey-rgb), .2);
        }
        .checkbox-group-options-wrapper {
            flex: 1 1 auto;
            height: 100%;
            position: relative;
            width: 100%;
            background: rgba(13, 24, 42, 0.20);
        }
        .checkbox-group-options{
            position: absolute;
            overflow-y: scroll;
            top:0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }

    .collapsible-input-group-footer {
        padding: 20px;
        gap: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr;
    }

    &.collapsible-input-group-expand-inline {
        .collapsible-input-group-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            border-top: 1px solid rgba(var(--color-white-rgb), .1);
            .collapsible-input-group-footer-text {
                margin-right: auto;
                color: var(--color-grey);
                font-size: 12px;
                font-weight: 400;
                line-height: 1.125em;
            }
        }
    }
}
