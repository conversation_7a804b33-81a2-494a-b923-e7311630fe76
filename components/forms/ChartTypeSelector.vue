<template>
    <div class="chart-type-selector">
        <label
            v-for="option in options"
            :key="option.name"
            class="chart-type-selector-item"
            :class="{ 'chart-type-selector-item-selected': option.name === model }"
        >
            <input type="radio" name="name" :value="option.name" v-model="model" />
            <span class="chart-type-selector-image" v-html="option.icon"></span>
        </label>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';

/** A radio-style selector of a chart type, using predefined svg graphics */
defineOptions();

const model = defineModel();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'chart-type-selector-' + componentId);

export interface ChartTypeSelectorProps {
    name?: string;
}
const props = defineProps<ChartTypeSelectorProps>();

const options = [
    {
        name: 'bar',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="14" y="26" width="9" height="16" fill="#E9EDF1"/>
            <rect x="14" y="44" width="9" height="9" fill="#57719C"/>
            <rect x="25" y="25" width="9" height="17" fill="#E9EDF1"/>
            <rect x="25" y="44" width="9" height="11" fill="#57719C"/>
            <rect x="36" y="23" width="9" height="19" fill="#E9EDF1"/>
            <rect x="36" y="44" width="9" height="7" fill="#57719C"/>
            <rect x="47" y="27" width="9" height="15" fill="#E9EDF1"/>
            <rect x="47" y="44" width="9" height="13" fill="#57719C"/>
            <rect x="58" y="26" width="9" height="16" fill="#E9EDF1"/>
            <rect x="58" y="44" width="9" height="11" fill="#57719C"/>
        </svg>`,
    },

    {
        name: 'line',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M66.0013 33L65.7684 33.121C64.0628 34.0072 62.2628 34.6985 60.4024 35.1818V35.1818L60.3125 35.2052C58.5135 35.6726 56.6623 35.9091 54.8036 35.9091V35.9091H54.5861C52.9049 35.9091 51.2637 36.4221 49.8817 37.3795V37.3795C49.4315 37.6914 49.0134 38.0473 48.6336 38.4419L46.4053 40.7576L45.0826 41.903C44.1301 42.7278 42.9123 43.1818 41.6523 43.1818V43.1818" stroke="#57719C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M15 48.9999H17.8605H20.721H20.9614C22.6609 48.9999 24.3133 48.4417 25.6646 47.411V47.411C26.1808 47.0173 26.7443 46.69 27.342 46.4367L27.5373 46.354C28.7021 45.8604 29.9543 45.606 31.2194 45.606H32.1629V45.606C34.0506 45.606 35.919 45.2265 37.6571 44.49L37.8838 44.3939L40.7443 43.1818" stroke="#E9EDF1" stroke-width="2"/>
            <rect x="6" y="18" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="25" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="32" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="39" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="46" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="53" width="4" height="1" fill="#57719C"/>
            <rect x="6" y="60" width="4" height="1" fill="#57719C"/>
            <rect x="13" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="20.8555" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="28.7148" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="36.5703" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="44.4297" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="52.2852" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="60.1445" y="65" width="1" height="4" fill="#57719C"/>
            <rect x="68" y="65" width="1" height="4" fill="#57719C"/>
        </svg>`,
    },

    // // Savings goal progress
    // `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    //     <rect x="0.5" y="0.5" width="79" height="79" rx="3.5" stroke="#57719C"/>
    //     <g clip-path="url(#clip0_2249_25952)">
    //     <ellipse cx="40.0006" cy="54.3529" rx="31.7037" ry="31.4706" stroke="#57719C" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1 3"/>
    //     <path d="M9.4374 46.5118C9.09546 46.4332 8.88181 46.0915 8.96846 45.7515C10.1705 41.0352 12.4587 36.6568 15.6562 32.9623C18.993 29.1071 23.2208 26.1114 27.978 24.2316C32.7353 22.3517 37.8804 21.6436 42.9735 22.1678C47.8553 22.6703 52.55 24.2907 56.6893 26.8978C56.9856 27.0844 57.065 27.4787 56.8706 27.7698C56.678 28.0581 56.2892 28.1364 55.9957 27.9517C52.0254 25.4541 47.5237 23.9017 42.8427 23.4199C37.9534 22.9167 33.014 23.5965 28.447 25.4011C23.8801 27.2058 19.8214 30.0816 16.6181 33.7826C13.5531 37.3239 11.3586 41.5199 10.2031 46.0396C10.1166 46.378 9.77777 46.5901 9.4374 46.5118Z" fill="#E9EDF1"/>
    //     </g>
    //     <rect x="29" y="47" width="22" height="4" fill="#E9EDF1"/>
    //     <rect x="29" y="53" width="22" height="4" fill="#57719C"/>
    //     <defs>
    //     <clipPath id="clip0_2249_25952">
    //     <rect width="64" height="25" fill="white" transform="translate(8 22)"/>
    //     </clipPath>
    //     </defs>
    // </svg>`
];

const emit = defineEmits<{
    change: [id: number];
}>();
</script>

<style lang="scss">
.chart-type-selector {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    input {
        opacity: 0;
        width: 0;
        height: 0;
    }
}
.chart-type-selector-item {
    border: 2px solid var(--color-blue-grey);
    border-radius: 10px;
    width: 80px;
    height: 80px;
    flex: none;
    position: relative;
    transition: border-color 0.2s;
    cursor: pointer;
    &.chart-type-selector-item-selected {
        border-color: var(--color-white);
    }
}
.chart-type-selector-image {
    width: 80px;
    height: 80px;
    position: absolute;
    top: -2px;
    left: -2px;
}
</style>
