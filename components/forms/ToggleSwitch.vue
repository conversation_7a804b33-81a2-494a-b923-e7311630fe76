<template>
    <label class="toggle-switch">
        <input type="checkbox" v-model="model" />
        <span class="slider"></span>
    </label>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';

/** A simple iOS-styled toggle switch  */
defineOptions();

const model = defineModel();
</script>

<style lang="scss">
@import './ToggleSwitch';
</style>
