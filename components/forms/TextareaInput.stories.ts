import TextareaInput from './TextareaInput.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from "vue";
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof TextareaInput> = {
    component: TextareaInput,
    tags: ['autodocs'],

    //👇 Our events will be mapped in Storybook UI
    argTypes: {
        validationState: {
            control: {type: 'select'},
            options: ['none', 'error'],
            // description: 'The type of validationState should be applied to this input',
        },
        placeholder: {
            control: {type: 'text'},
        },
        label: {
            control: {type: 'text'},
        },
        size: {
            control: {type: 'select'},
            options: ['sm', 'lg'],
        }
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // This component is just the input; this rule will be enforced in a parent component
                rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    decorators: [() => ({ template: '<div style="margin: 3em; display: flex; align-items: stretch; justify-content: center; flex-direction:column; width: 400px;"><story /></div>' })]
};

export default meta;
type Story = StoryObj<typeof TextareaInput>;

/** The default one ok? */
export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const Placeholder: Story = {
    args: {
        placeholder: 'Enter your name',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const FloatingPlaceholder: Story = {
    args: {
        label: 'Value',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const Error = {
    args: {
        validationState: 'error',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const Small = {
    args: {
        svgIcon: {
            shape: 'search',
        },
        size: 'sm'
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};
