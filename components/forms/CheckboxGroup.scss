@import '/assets/css/mixins';

.checkbox-group {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;

    label {
        display: flex;
        align-items: flex-start;
        cursor: pointer;

        transition:
            color var(--checkbox-group-duration) ease-in-out,
            background var(--checkbox-group-duration) ease-in-out;
    }

    .checkbox-group-filter-bar {
        flex: none;
        width: 100%;
        background: var(--color-grey-section);
        padding: 20px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 20px;
        position: relative;
        .checkbox-group-filter-icon {
            color: var(--color-grey);
            position: absolute;
            top: 50%;
            left: 20px;
            width: 24px;
            height: 24px;
            margin-top: -12px;
            pointer-events: none;
        }
        .checkbox-group-filter-input {
            appearance: none;
            border: none;
            outline: none;
            background: none transparent;
            padding-left: 32px;

            // background: rgba(255, 0, 0, .2);
            flex: 1 1 auto;
            width: 100%;
            height: 30px;

            color: var(--color-white);
            @include placeholder {
                color: rgba(var(--color-grey-rgb), 0.5);
                // color: red;
            }
        }
        .checkbox-group-filter-clear {
            border-radius: 4px;
            background: var(--color-blue-grey);
            color: var(--color-white);
            user-select: none;

            font-size: 16px;
            font-weight: 400;
            line-height: 1.125em;
            letter-spacing: -0.32px;
            height: 30px;
            padding: 2px 10px;
        }
    }

    .checkbox-group-option {
        flex: 1 1 auto;
    }

    &.checkbox-group-list {
        --checkbox-group-duration: 0.15s;

        --checkbox-group-label-color: var(--color-grey);
        --checkbox-group-label-size: 18px;

        --checkbox-group-description-color: var(--color-blue-grey);

        --checkbox-group-input-size: 30px;
        --checkbox-group-svg-size: 100px 100px;

        &.checkbox-group-s-sm {
            --checkbox-group-input-size: 20px;
            --checkbox-group-label-size: 14px;
            --checkbox-group-svg-size: 67px 67px;
        }

        .checkbox-group-option {
            label {
                gap: 20px;
                padding: 20px;

                &:hover {
                    &:not(.checkbox-group-item-selected) {
                        background: rgba(var(--color-white-rgb), 0.1);
                        --checkbox-group-label-color: var(--color-white);
                        --checkbox-group-description-color: var(--color-grey);
                    }
                }

                &.checkbox-group-item-selected {
                    background: var(--color-blue-grey);
                    color: var(--color-white);
                    --checkbox-group-label-color: var(--color-white);
                    --checkbox-group-description-color: var(--color-white);
                }
            }
            .checkbox-group-option-input {
                flex: none;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                position: relative;
                width: var(--checkbox-group-input-size);
                height: var(--checkbox-group-input-size);

                .checkbox-group-faux-input {
                    width: 100%;
                    height: 100%;
                    border: 2px solid rgba(var(--color-white-rgb), 0.1);

                    border-radius: 4px;
                    overflow: hidden;
                    position: relative;

                    transition:
                        border-color var(--checkbox-group-duration) ease-in-out,
                        opacity var(--checkbox-group-duration) ease-in-out;

                    &.checkbox-group-faux-radio {
                        border-radius: 9999em;
                    }

                    &:before {
                        width: 100px;
                        height: 100px;
                        // TODO: Light mode
                        //content: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M100 0H0V100H100V0ZM42.5338 49.692L47.0381 54.0455L58.477 43L60 44.472L47.0381 57L41 51.1641L42.5338 49.692Z' fill='%23E9EDF1'/%3E%3C/svg%3E%0A");
                        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M100 0H0V100H100V0ZM42.5338 49.692L47.0381 54.0455L58.477 43L60 44.472L47.0381 57L41 51.1641L42.5338 49.692Z' fill='%23E9EDF1'/%3E%3C/svg%3E%0A");
                        background-size: var(--checkbox-group-svg-size);
                        background-position: center;
                        background-repeat: no-repeat;
                        position: absolute;
                        top: calc(50% - 50px);
                        left: calc(50% - 50px);
                        content: '';

                        opacity: 0;
                        transform: scale(0.5);

                        transition:
                            opacity var(--checkbox-group-duration) ease-in-out,
                            transform var(--checkbox-group-duration) ease-in-out;
                    }
                }

                input {
                    display: none;
                }

                input:checked + .checkbox-group-faux-input {
                    border-color: var(--color-white);
                    &:before {
                        opacity: 1;
                        transform: scale(1);
                    }
                }
            }

            .checkbox-group-option-text {
                display: flex;
                align-items: flex-start;
                justify-content: flex-start;
                gap: 10px;
                flex-direction: column;
                user-select: none;
                &:not(:has(.checkbox-group-option-description)) {
                    align-self: center;
                }
            }

            .checkbox-group-option-label {
                color: var(--checkbox-group-label-color);
                font-weight: 700;
                font-size: var(--checkbox-group-label-size);
                line-height: 1.1em; /* 111.111% */
                letter-spacing: -0.36px;
                transition: color var(--checkbox-group-duration) ease-in-out;
            }

            .checkbox-group-option-description {
                color: var(--checkbox-group-description-color);
                font-weight: 400;
                font-size: 14px;
                line-height: 1.4em;
                letter-spacing: -0.28px;
                transition: color var(--checkbox-group-duration) ease-in-out;
            }
        }
    }

    &.checkbox-group-list {
        &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        &::-webkit-scrollbar-thumb {
            background-color: #4a4f5c;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-corner {
            background-color: rgba(13, 24, 42, 1);
        }
    }

    &.checkbox-group-buttons {
        .checkbox-group-options,
        .checkbox-group-options-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 4px;
        }

        .checkbox-group-option {
            label {
                appearance: none;
                display: inline-flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                padding: 5px 20px;
                min-height: 40px;
                cursor: pointer;
                user-select: none;
                position: relative;

                font-family: var(--font-stack), sans-serif;
                font-weight: 700;
                font-size: 14px;
                line-height: 1.25em;

                --checkbox-group-option-border-radius: 4px;
                // --checkbox-group-option-text-color: var(--color-white);
                --checkbox-group-option-outline-color: var(--color-blue-grey);
                --checkbox-group-option-border-color: var(--checkbox-group-option-outline-color);
                --checkbox-group-option-text-color: var(--checkbox-group-option-outline-color);
                --checkbox-group-option-background: transparent;

                color: var(--checkbox-group-option-text-color);
                border-radius: var(--checkbox-group-option-border-radius);
                border: 1px solid var(--checkbox-group-option-border-color);
                background: var(--checkbox-group-option-background);

                --checkbox-group-option-duration: 0.2s;
                transition:
                    opacity var(--checkbox-group-option-duration),
                    background var(--checkbox-group-option-duration),
                    color var(--checkbox-group-option-duration),
                    border-color var(--checkbox-group-option-duration),
                    padding var(--checkbox-group-option-duration);

                &:hover {
                    --checkbox-group-option-outline-color: var(--color-grey);
                }

                .checkbox-group-option-input {
                    order: 10;
                    input {
                        display: none;
                    }
                }

                .checkbox-group-option-description {
                    display: none;
                }

                .checkbox-group-faux-input {
                    position: absolute;
                    right: 10px;
                    top: 50%;
                    margin-top: -4px;
                    width: 12px;
                    height: 8px;

                    overflow: hidden;
                    // position: relative;

                    // transition: border-color var(--checkbox-group-transition-duration) ease-in-out,
                    //             opacity var(--checkbox-group-transition-duration) ease-in-out;

                    // &:before {
                    //     width: 100%;
                    //     height: 100%;
                    // TODO: Light mode
                    // content: url("data:image/svg+xml,%3Csvg width='23' height='18' viewBox='0 0 23 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.29493 13.8069L1.85309 8.36503L0 10.2051L7.29493 17.5L22.9549 1.84004L21.1148 0L7.29493 13.8069Z' fill='%23FFFFFF'/%3E%3C/svg%3E%0A");;
                    background-image: url("data:image/svg+xml,%3Csvg width='23' height='18' viewBox='0 0 23 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.29493 13.8069L1.85309 8.36503L0 10.2051L7.29493 17.5L22.9549 1.84004L21.1148 0L7.29493 13.8069Z' fill='%23FFFFFF'/%3E%3C/svg%3E%0A");
                    background-size: contain;
                    background-position: center;
                    background-repeat: no-repeat;
                    content: '';

                    opacity: 0;
                    transform: scale(0.6) translateX(-5px);
                    // margin-right: -20px;
                    // transition: opacity var(--checkbox-group-option-duration) ease-in-out;

                    transition:
                        opacity var(--checkbox-group-option-duration) ease-in-out,
                        transform var(--checkbox-group-option-duration) ease-out;
                    // }
                }

                &.checkbox-group-item-selected {
                    --checkbox-group-option-background: var(--color-blue-grey);
                    --checkbox-group-option-text-color: var(--color-white);
                    --checkbox-group-option-outline-color: var(--color-blue-grey);
                    --checkbox-group-option-border-color: var(--checkbox-group-option-outline-color);
                    padding: 5px 30px 5px 10px;

                    .checkbox-group-faux-input {
                        // &:before {
                        opacity: 1;
                        transform: scale(1) translateX(0px);
                        // margin-right:0;
                        // }
                    }
                }
            }
        }
    }
}

// <div v-if="filterable" class="checkbox-group-filter-bar">
//     <SvgIcon shape="search" class="checkbox-group-filter-icon" />
//     <input type="text" v-model="filter" class="checkbox-group-filter-input">
//     <button type="button" class="checkbox-group-filter-clear">clear</button>
// </div>
