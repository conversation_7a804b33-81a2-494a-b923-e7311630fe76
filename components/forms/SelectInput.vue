<template>
    <div class="select-input" :class="topLevelClassObj">
        <label v-if="label" class="select-input-label" :for="elementId" v-text="label"></label>

        <select class="select-input-select" :id="elementId" v-model="model" :aria-labelledby="ariaLabelledby" :aria-label="ariaLabel" :readonly="readonly">
            <option v-if="placeholder" :value="null" disabled selected v-text="placeholder"></option>
            <option v-for="option in props.options" :value="option.value" v-text="option.label"></option>
        </select>

        <span class="select-input-caret">
            <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M6.00167 8.00167L6.00334 8.00333L7.41755 6.58912L7.41588 6.58746L12.0033 2L10.5891 0.58579L6.00167 5.17324L1.41421 0.585785L0 2L4.58746 6.58746L4.58579 6.58913L6 8.00334L6.00167 8.00167Z"
                    fill="currentColor" />
            </svg>
        </span>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';

const model = defineModel()

/** A styled <select> box for selecting from a predefined list of options  */
defineOptions();

// Generate a unique ID for the input element
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'select-input-' + componentId);

export interface SelectInputOption {
    label: string,
    value: any,
}

export interface SelectInputProps {
    /** The options that can be selected in the dropdown */
    options: SelectInputOption[];

    /** Placeholder text for the default (disabled) option */
    placeholder?: string;

    /** A label for the select; it will serve as a placeholder, then float above selected values */
    label?: string,

    /** The size of the select */
    size?: 'sm' | 'lg' | null;

    /** The alignment of the text within the input */
    alignment?: 'left' | 'right';
    
    /** Whether the select should be disabled */
    disabled?: boolean;

    /** The HTML "id" property of the select */
    id?: string

    /** Specific border styling for form validation states */
    validationState?: 'none' | 'error',

    /** aria-labeledby for the select */
    ariaLabelledby?: string;

    /** aria-labeled for the select; should be provided if there are not external labels (or a label prop) */
    ariaLabel?: string;

    /** Sets the "readonly" property on the input */
    readonly?: InputHTMLAttributes['readonly'];
}

const props = defineProps<SelectInputProps>();

const topLevelClassObj = computed(() => [
    (props.validationState) ? 'select-input-v-' + props.validationState : '',
    (props.size) ? 'select-input-s-' + props.size : '',
    (model.value?.length) ? 'select-input-filled' : 'select-input-empty',
    (props.alignment === 'right') ? 'select-input-align-right' : '',
    (model.value || model.value === 0) ? '' : 'select-input-placeholder',
    (props.readonly) ? 'text-input-readonly' : '',
]);

</script>

<style lang="scss">
@import "./SelectInput";
</style>
