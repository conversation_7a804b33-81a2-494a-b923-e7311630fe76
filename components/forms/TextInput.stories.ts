import TextInput from './TextInput.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { ref } from "vue";
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof TextInput> = {
    component: TextInput,
    tags: ['autodocs'],

    //👇 Our events will be mapped in Storybook UI
    argTypes: {
        validationState: {
            control: {type: 'select'},
            options: ['none', 'error'],
            // description: 'The type of validationState should be applied to this input',
        },
        placeholder: {
            control: {type: 'text'},
        },
        label: {
            control: {type: 'text'},
        },
        type: {
            control: {type: 'select'},
            options: ['text', 'password', 'email', 'number', 'tel', 'url', 'search']
        },
        size: {
            control: {type: 'select'},
            options: ['sm', 'lg'],
        },
        inputmode: {
            control: {type: 'select'},
            options: ['none', 'text', 'decimal', 'numeric', 'tel', 'search', 'email', 'url'],
        },
        suffix: {
            control: {type: 'text'},
        },
        numberFormatting: {
            control: {type: 'boolean'},
        },
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // This component is just the input; this rule will be enforced in a parent component
                rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TextInput>;

/** The default one ok? */
export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const Placeholder: Story = {
    args: {
        placeholder: 'Enter your name',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const FloatingPlaceholder: Story = {
    args: {
        label: 'Value',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};

export const NumberFormatting = {
    args: {
        numberFormatting: true
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const Suffix = {
    args: {
        suffix: '/hr',
        placeholder: "0",
        numberFormatting: true
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const Password = {
    args: {
        type: 'password',
        placeholder: "Password"
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=791-8125&mode=design&t=m3nGxUhAW7TbcP17-4',
        },
    },
};

export const Icon = {
    args: {
        svgIcon: {
            shape: 'dollar',
        }
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const IconRight = {
    args: {
        svgIcon: {
            shape: 'dollar',
            position: 'right',
        }
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const Error = {
    args: {
        validationState: 'error',
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const KitchenSink: Story = {
    args: {
        label: 'Hourly Rate',
        validationState: 'error',
        placeholder: "0",
        suffix: '/hr',
        numberFormatting: true,
        svgIcon: {
            shape: 'dollar',
            position: 'left',
        }
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};


export const Small = {
    args: {
        svgIcon: {
            shape: 'search',
        },
        size: 'sm'
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-34336&mode=design&t=v8iPSZbmvtFBCGTp-4',
        },
    },
};

export const SmallKitchenSink: Story = {
    args: {
        size: 'sm',
        label: 'Hourly Rate',
        placeholder: "0",
        suffix: '/hr',
        numberFormatting: true,
        svgIcon: {
            shape: 'dollar',
            position: 'left',
        }
    },
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=2780-29484&mode=design&t=kC6IoB62yOE6SViC-4',
        },
    }
};
