.text-input {
    display: flex;
    align-items: stretch;
    justify-content: flex-start;
    height: 44px;
    color: var(--color-white);
    
    --text-input-font-size: 1rem;

    --text-input-label-inline-top: 10px;
    --text-input-label-floating-top: -2px;
    --text-input-label-floating-size: .75rem;
    
    font-size: var(--text-input-font-size);

    --text-input-hover-transition: .15s;
    transition: color var(--text-input-hover-transition),
                border-color var(--text-input-hover-transition);

    --text-input-accent-color: var(--color-blue-grey);

    &:hover, &.text-input-hovered {
        --text-input-accent-color: var(--color-grey);
    }

    &.text-input-v-error {
        --text-input-accent-color: var(--color-red) !important;
    }

    &:focus-within, &.text-input-focused {
        --text-input-accent-color: var(--color-grey);
        .text-input-suffix-wrapper {
            color: var(--color-grey);
        }
    }

    &.text-input-with-border {
        border-bottom: 1px solid var(--text-input-accent-color);
    }

    .text-input-input-wrapper {
        height:100%;
        flex:auto;
        position: relative;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
    }

    .text-input-input {
        appearance:none;
        background:transparent;
        outline: none;
        padding-left: 0;
        padding-right: 0;
        height:100%;
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--text-input-font-size);
        line-height: 1.5em;
        color: var(--color-white);
        flex: 1 1 auto;

        transition: color var(--text-input-hover-transition);
        &::placeholder {
            color: var(--color-blue-grey);
        }

        &[type="date"]::-webkit-calendar-picker-indicator {
            display: none;
            -webkit-appearance: none;
        }

        // Overwrite autofill styling in Safari
        &:-webkit-autofill, &:-webkit-autofill:focus {
            // TODO: Light mode
            -webkit-box-shadow: 0 0 0 1000px #182334 inset;
            -webkit-text-fill-color: var(--color-white);
        }
    }

    &:not(:focus-within, .text-input-focused, .text-input-filled) {
        .text-input-input[type="date"] {
            opacity: 0;
        }
        .text-input-floating-dollar-wrapper {
            display: none;
        }
    }

    .text-input-suffix-wrapper {
        position: absolute;
        left:0;
        height:100%;
        width:100%;
        pointer-events: none;
        display:flex;
        align-items:center;
        justify-content:flex-start;
        gap: 2px;

        color: rgba(var(--color-white-rgb), .2);

        opacity: 0;
        visibility: hidden;

        transition: color var(--text-input-hover-transition);
    }

    &.text-input-filled {
        .text-input-suffix-wrapper {
            opacity: 1;
            visibility: visible;
        }
        &:not(:focus-within, .text-input-focused) {
            .text-input-suffix-wrapper {
                color: var(--color-blue-grey);
            }
        }
    }

    .text-input-suffix-placeholder {
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--text-input-font-size);
        line-height: 1.5em;
        flex:none;

        opacity: 0;
        visibility: hidden;
    }

    .text-input-suffix {
        flex: auto;
    }

    .text-input-label {
        position: absolute;
        top: var(--text-input-label-inline-top);
        left:0;
        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--text-input-font-size);
        line-height: 1.5em;
        width:100%;
        pointer-events: none;
        color: var(--text-input-accent-color);

        transition: top var(--text-input-hover-transition),
                    font-size var(--text-input-hover-transition),
                    line-height var(--text-input-hover-transition),
                    color var(--text-input-hover-transition),
                    opacity var(--text-input-hover-transition);
    }

    &.text-input-filled {
        &:not(:focus-within, .text-input-focused, .text-input-readonly) {
            .text-input-input {
                color: var(--color-grey);
            }
        }
    }

    &:focus-within, &.text-input-focused, &.text-input-filled {
        .text-input-suffix-wrapper {
            opacity: 1;
            visibility: visible;
        }
        .text-input-label {
            top: var(--text-input-label-floating-top);
            font-size: var(--text-input-label-floating-size);
        }
    }

    .text-input-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        transition:color var(--text-input-hover-transition);
        color: var(--text-input-accent-color);
        line-height:0;
        font-size:0;
        cursor: text;
        padding-top: 1px;
        &.text-input-icon-right {
            margin-right:0;
            margin-left:4px;
            order:1000;
        }
    }

    &.text-input-s-sm, &.text-input-s-xs {
        height: 40px;
        --text-input-font-size: 0.875rem;
        --text-input-label-floating-size: 0.7142857143em;

        .text-input-label {
            --text-input-label-inline-top: 9px; 
        }
    }

    &.text-input-s-xs {
        --text-input-font-size: 0.75rem;
    }

    &.text-input-readonly {
        --text-input-accent-color: rgba(var(--color-blue-grey-rgb), .35);
        .text-input-input {
            color: var(--color-white);
            // color: rgba(var(--color-white-rgb), .2);
            cursor: not-allowed;
        }
        
        &:focus-within, &.text-input-focused {
            --text-input-accent-color: rgba(var(--color-white-rgb), .2);
            .text-input-suffix-wrapper {
                color: var(--color-grey);
            }
        }

        &:hover, &.text-input-hovered {
            --text-input-accent-color: var(--color-blue-grey);
        }
    }

    &.text-input-floating-dollar {
        .text-input-input {
            text-align: right;
            color: var(--color-white);
        }
    }

    &.text-input-align-right {
        .text-input-input {
            text-align: right;
        }
    }

    .text-input-floating-dollar-wrapper {
        position: absolute;
        top:0;
        left:0;
        height: 100%;
        width: 100%;
        //background: rgba(255,255,0,.1);
        display: flex;
        align-items: center;
        justify-content: flex-end;

        font-family: var(--font-stack), sans-serif;
        font-weight: 400;
        font-size: var(--text-input-font-size);
        line-height: 1.5em;
        padding-top: 1px;
        color: var(--color-grey);

        .text-input-floating-dollar-wrapper-ghost {
            opacity: 0;
            visibility: hidden;
        }
    }
}
