@import "/assets/css/mixins";

.tabs-radio-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;

    .tabs-radio-group-slot {
        display: flex;
        align-items: stretch;
        flex-direction: column;
        gap: 4px;
    }

    .tabs-radio-group-default {
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        flex-direction: column;
        border-radius: 6px;

        color: var(--color-blue-grey);

        padding: 40px 20px;

        border: 1px dashed rgba(var(--color-blue-grey-rgb), 0.5);
    }
}
