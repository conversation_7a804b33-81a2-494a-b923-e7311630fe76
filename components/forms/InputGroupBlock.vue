<template>
    <div class="input-group-block" :class="topLevelClassObj">
        <div class="input-group-block-label">
            <label :for="props.labelFor" v-html="props.title"></label>
            <p v-if="props.description" class="input-group-block-description" v-html="props.description"></p>
        </div>
        <div :class="fieldClass">
            <slot />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue';
import type { Ref } from 'vue';

/** A panel that includes a label, description, and one or more form fields */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'input-group-block-' + componentId);

export interface InputGroupBlockProps {
    title: string;
    description?: string;

    layout?: 'horizontal' | 'vertical';

    /** Specific border styling for form validation states */
    validationState?: 'none' | 'error';

    labelFor?: string;

    /** Determines whether the input fields should be displayed in multiple rows */
    multiRow?: boolean;
}
const props = withDefaults(defineProps<InputGroupBlockProps>(), {
    layout: 'horizontal',
    multiRow: false
})

const emit = defineEmits<{
    change: [id: number]
}>()

const topLevelClassObj = computed(() => [
    (props.validationState) ? 'input-group-block-v-' + props.validationState : '',
    (props.layout) ? 'input-group-block-l-' + props.layout : '',
]);

const fieldClass = computed(() => [
    'input-group-block-fields',
    { 'input-group-block-fields-multiple': props.multiRow }
]);
</script>

<style lang="scss">
@import "./InputGroupBlock";

.input-group-block-fields-multiple {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* Adjust the spacing as needed */
}
</style>
