<template>
    <div class="checkbox-group" :class="topLevelClassObj">
        <div v-if="filterable" class="checkbox-group-filter-bar">
            <SvgIcon shape="search" class="checkbox-group-filter-icon" />
            <input type="text" v-model="filter" class="checkbox-group-filter-input" placeholder="Filter">
            <button type="button" class="checkbox-group-filter-clear" @keydown.escape="clearFilter"
                @click.prevent="clearFilter">clear</button>
        </div>
        <div class="checkbox-group-options-wrapper">
            <div class="checkbox-group-options">
                <template v-if="mode === 'radio'">
                    <div class="checkbox-group-option" v-for="option in filteredOptions" :key="option.name">
                        <label :for="elementId + '-' + option.name"
                            :class="{ 'checkbox-group-item-selected': selectedOptions.includes(option.name)}">
                            <div class="checkbox-group-option-input">
                                <input type="radio" :id="elementId + '-' + option.name" v-model="selected"
                                    :name="elementId+'-radio'" :value="option.name" />
                                <div class="checkbox-group-faux-input checkbox-group-faux-radio"></div>
                            </div>
                            <div class="checkbox-group-option-text">
                                <!-- :class="'checkbox-group-option-text-'+(option.description) ? 'with-desc' : 'no-desc'"> -->
                                <span class="checkbox-group-option-label" v-text="option.label"></span>
                                <span class="checkbox-group-option-description" v-if="option.description"
                                    v-text="option.description"></span>
                            </div>
                        </label>
                    </div>
                </template>

                <template v-else>
                    <div class="checkbox-group-option" v-for="option in filteredOptions" :key="option.name">
                        <label :for="elementId + '-' + option.name"
                            :class="{ 'checkbox-group-item-selected': selectedOptions.includes(option.name)}">
                            <div class="checkbox-group-option-input">
                                <input type="checkbox" :id="elementId + '-' + option.name" v-model="option.value"
                                    :value="option.name" :name="elementId" />
                                <div class="checkbox-group-faux-input checkbox-group-faux-checkbox"></div>
                            </div>
                            <div class="checkbox-group-option-text">
                                <span class="checkbox-group-option-label" v-text="option.label"></span>
                                <span class="checkbox-group-option-description" v-if="option.description"
                                    v-text="option.description"></span>
                            </div>
                        </label>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref, watch } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';

const options = defineModel<CheckboxGroupOption[]>('options');
const selected = defineModel<string[] | string>('selected');

/** A set of checkbox or radio buttons; can be implemented as a list of blocks or inline buttons */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'checkbox-group-' + componentId);

export interface CheckboxGroupOption {
    label: string;
    description?: string | null;
    name: string;
    value?: boolean;
}

export interface CheckboxGroupProps {
    size?: 'sm'|'lg'|undefined;
    mode: 'checkbox' | 'radio' | undefined;
    variant?: 'buttons'|'list'|null; 
    filterable?: boolean;
    // options?: CheckboxGroupOption[] | undefined;
}

const props = withDefaults(defineProps<CheckboxGroupProps>(), {
    size: 'lg',
    mode: 'checkbox',
    variant: 'list'
})

const models = reactive({
    ...options.value?.reduce((acc, option) => {
        acc[option.name] = option.value;
        return acc;
    }, {} as Record<string, boolean>)
})

// Generate the top-level class object for the input based on props and "filled" state
const topLevelClassObj = computed(() => [
    (props.size) ? 'checkbox-group-s-' + props.size : '',
    (props.variant === 'buttons') ? 'checkbox-group-buttons' : 'checkbox-group-list'
]);

const selectedOptions = computed(() =>
    (props.mode === 'radio' && selected.value)
        ? selected.value ?? []
        : options.value?.filter((option) => option.value).map((option) => option.name) ?? []
);

const selectedRadio = ref(options.value?.find((option) => option.value)?.name ?? undefined);
function optionIsChecked(name: string) {
    return (props.mode === 'radio') ? selectedRadio.value === name : (models[name] ?? false);
}

const filter = ref('');
const filteredOptions = computed(() => 
    (filter.value)
        ? options.value?.filter((o) => (o.label + (o.description ?? '')).toLowerCase().indexOf(filter.value.toLowerCase()) !== -1)
        : options.value
)
function clearFilter() {
    filter.value = '';
}

const compOptions = computed(() => options.value);
// watch(options.value, async (newVal: CheckboxGroupOption[] | null) => {
watch(options.value, async (newVal) => {
    if (props.mode === 'checkbox' && newVal) {
        if (!Array.isArray(selected.value)) {
            selected.value = selected.value ? [selected.value] : [];
        }
        const mappedNew = newVal.filter(e => e.value).map(e => e.name);
        const newOptions = mappedNew.filter(e => !selected.value?.includes(e));
        const removedOptions = selected.value?.filter(e => !mappedNew?.includes(e));
        if (newOptions?.length) {
            selected.value = selected.value.concat(newOptions);
        }
        if (removedOptions?.length) {
            selected.value = selected.value.filter(e => !removedOptions.includes(e));
        }
    }
})
</script>

<style lang="scss">
@import "./CheckboxGroup";
</style>
