<template>
    <div class="file-upload" :class="topLevelClassObj">
        <div class="file-upload-container">
            <FilePond
                ref="filepondRef"
                :name="name"
                :id="elementId"
                :class="filepondClassObj"
                :disabled="disabled"
                :allow-multiple="allowMultiple"
                :accepted-file-types="acceptedFileTypes"
                :max-file-size="maxFileSize"
                :files="files"
                :label-idle="idleUploadMarkup"
                @processfileprogress="handleFileProcessProgress"
                @error="handleError"
            />

            <!--@processfile="handleFileProcessed"-->
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, ref, getCurrentInstance, watch } from 'vue';
import vueFilePond from 'vue-filepond';
import { setOptions } from 'filepond';

const serverMessage: Ref<any> = ref({});

import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
import type { Paystub } from '~/types/paystub';
const FilePond = vueFilePond(FilePondPluginFileValidateType, FilePondPluginFileValidateSize);

setOptions({
    server: {
        process: async (fieldName, file, metadata, load, error, progress, abort) => {
            const controller = new AbortController();

            try {
                // Step 1: get SAS upload URL — send filename + mimetype
                const { uploadUrl, blobName } = await $fetch('/api/paystub/upload-url');
                console.log(uploadUrl);

                const monitoredBlob = file;
                progress(true, file.size, file.size); // file is already a blob, mark as fully uploaded

                // Step 2: PUT to Azure Blob
                const response = await fetch(uploadUrl, {
                    method: 'PUT',
                    headers: {
                        'x-ms-blob-type': 'BlockBlob',
                    },
                    body: monitoredBlob,
                    signal: controller.signal,
                    ...({ duplex: 'half' } as any),
                });

                if (!response.ok) {
                    throw new Error(`Azure upload failed: ${response.statusText}`);
                }

                // Step 3: trigger OCR
                const ocrResult = await $fetch('/api/paystub/ocr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Accept: 'application/json',
                    },
                    body: { blobName },
                });

                emit('complete', ocrResult);
                load(blobName);
            } catch (err: any) {
                error(err.message || 'Upload error');
            }

            return {
                abort: () => controller.abort(),
            };
        },
    },
});

// Define the model to allow two-way binding
const model = defineModel<File[] | null>();

const filepondRef = ref(null);

/**
 * A file upload component using FilePond
 *
 * For enhanced functionality, consider installing these FilePond plugins:
 * - filepond-plugin-file-validate-type: For better file type validation
 * - filepond-plugin-file-validate-size: For better file size validation
 * - filepond-plugin-image-preview: For image preview functionality
 * - filepond-plugin-image-exif-orientation: For correcting image orientation
 *
 * Example usage with plugins:
 * ```
 * // Import plugins
 * import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
 * import FilePondPluginFileValidateSize from 'filepond-plugin-file-validate-size';
 * import FilePondPluginImagePreview from 'filepond-plugin-image-preview';
 *
 * // Register plugins
 * registerPlugin(
 *   FilePondPluginFileValidateType,
 *   FilePondPluginFileValidateSize,
 *   FilePondPluginImagePreview
 * );
 * ```
 */
defineOptions();

export interface FileUploadProps {
    /** The name attribute for the file input */
    name?: string;

    /** Whether to allow multiple file uploads */
    allowMultiple?: boolean;

    /** Whether the file upload is disabled */
    disabled?: boolean;

    /** Validation state */
    validationState?: 'none' | 'error';

    /** Custom server configuration for file uploads */
    server?: Record<string, any>;
}

const props = withDefaults(defineProps<FileUploadProps>(), {
    allowMultiple: false,
    disabled: false,
    validationState: 'none',
    server: () => ({}),
});

const emit = defineEmits<{
    // addfile: [File];
    // removefile: [File];
    complete: [{ paystub: Paystub; ocr?: unknown }];
    error: [Error];
    uploadcomplete: [];
}>();

// Generate a unique ID for the input element
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'file-upload-' + componentId);

// Files array for FilePond
const files = ref([]);

// Watch for changes in the model and update files
watch(
    () => model.value,
    newValue => {
        if (newValue) {
            files.value = newValue;
        } else {
            files.value = [];
        }
    },
    { immediate: true },
);

const acceptedFileTypes = 'image/jpeg, image/png, application/pdf, application/rtf';
const maxFileSize = '20MB';

const idleUploadMarkup = computed(() =>
    true
        ? `<span class="file-upload-icon">
                <svg class="file-upload-idle-markup-icon" width="41" height="51" viewBox="0 0 41 51" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.75 44.7436H40.75V50.6665H0.75V44.7436ZM12.1594 38.8208V21.1899H0.75L20.75 0.666504L40.75 21.1899H29.3406V38.8208H12.1594Z" fill="currentColor"/>
                </svg>
            </span>
            <h3>Upload</h3>
            <p>Drag paystub into this box or click to select a file from your computer</p>
            <p>PDF, Word, RTF, JPG, PNG</p>`
        : `<h3>Drag files here</h3>
            <h4>or</h4>
            <span class="btn btn-stroked filepond--label-action">Browse Files</span>`,
);

// Generate the top-level class object
const topLevelClassObj = computed(() => [
    'file-upload-v-' + props.validationState,
    props.disabled ? 'file-upload-disabled' : '',
]);

// Generate the FilePond class object
const filepondClassObj = computed(() => ['file-upload-filepond']);

// Server options for FilePond
// const serverOptions = computed(() => {
//     return props.server;
// });

function handleFileProcessProgress(_event, progress) {
    console.log('progress', progress, progress >= 0.98);

    if (progress && progress >= 0.98) {
        console.log('progress 100: upload complete');
        emit('uploadcomplete');
    }
}
//
// function handleFileProcessed(error, data) {
//     console.log('processfile', error, data, data.file, data.file?.paystub)
//
//     if (error) {
//         emit('error', error);
//         return;
//     }
//
//     console.log('request response received; emitting complete')
//
//     emit('complete', data.file);
// }

function handleError(error) {
    filepondRef.value.removeFiles();
    emit('error', error);
}
</script>

<style lang="scss">
@import './FileUpload';
</style>
