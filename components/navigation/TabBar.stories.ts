import TabBar from './TabBar.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof TabBar> = {
    component: TabBar,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClicked: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="padding: 3em; max-width:100%; width: 800px;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TabBar>;

export const Default: Story = {
    parameters: {
        design: {
            type: 'figma',
            url: 'https://www.figma.com/file/jM0ON6DwH8FcTB5Q3ICvs1/Budget-Register-%3A%3A-Phase-01?type=design&node-id=3379-26167&mode=dev',
        },
    },
    args: {
        tabs: [
            {
                label: 'Tab 1',
                name: 'tab-1',
                completion: 100,
            },
            {
                label: 'Second Tab',
                name: 2,
                completion: 0,
                active: true
            },
            {
                label: 'Here it is: The Third Tab™',
                name: '3',
                iconShape: 'lock',
                completion: 20
            }
        ]
    }
};
