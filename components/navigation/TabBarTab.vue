<template>
    <button class="tab-bar-tab" :class="{'tab-bar-tab-active': active}" @click.prevent="handleClick">
        <span class="tab-bar-tab-icon" v-if="props.iconShape">
            <SvgIcon :shape="props.iconShape" :width="20" :height="20" />
        </span>
        <span class="tab-bar-tab-label" v-text="props.label"></span>

        <div class="tab-bar-progress">
            <div class="tab-bar-progress-bar" :style="{ width: props.completion + '%' }"></div>
        </div>
    </button>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance} from 'vue';
import type { TSvgIconShapeKey } from '../images/SvgIconShapes';
import SvgIcon from '../images/SvgIcon.vue';

/** An individual tab within the TabBar component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'tab-bar-tab-' + componentId);

export interface TabBarTabProps {
    label: string;
    name: string | number;
    completion: number;
    iconShape?: TSvgIconShapeKey;
    active?: boolean;
}
const props = defineProps<TabBarTabProps>();

const emit = defineEmits<{
    clicked: [name: TabBarTabProps['name']]
}>()

function handleClick() {
    emit('clicked', props.name);
}
</script>
