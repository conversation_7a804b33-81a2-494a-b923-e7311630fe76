@import "/assets/css/mixins";

.sidebar-help {
    height: 100dvh;
    color: var(--color-white);
    position: relative;

    .sidebar-help-nav {
        display: flex;
        justify-content: flex-start;
        padding: 40px 20px 20px;
        gap: 20px;
        position: absolute;
        top: 0;
        background: rgba(var(--color-sidebar-rgb), .8);
        width: 100%;
        backdrop-filter: blur(5px);
    }

    .sidebar-help-content {
        overflow: scroll;
        height: 100%;
        padding: 84px 20px 40px;
        display: flex;
        align-items: stretch;
        justify-content: flex-start;
        gap: 20px;
        flex-direction: column;

        div[data-content-id] {
            display: flex;
            align-items: stretch;
            justify-content: flex-start;
            gap: 20px;
            flex-direction: column;
        }

        h4, h5 {
            font-size: 14px;
            font-weight: 700;
            line-height: 1.25em;
        }

        p {
            font-size: 14px;
            font-weight: 400;
            line-height: 1.45em;
            color: rgba(var(--color-white-rgb), .5);
        }
    }    
}
