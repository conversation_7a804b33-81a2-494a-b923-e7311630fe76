import SidebarNav from './SidebarNav.vue';
import type { Meta, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';

const meta: Meta<typeof SidebarNav> = {
    component: SidebarNav,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        mode: {
            control: { type: 'select' },
            options: ['advisory', 'non-advisory'],
            description: 'Mode of the sidebar (advisory or non-advisory)',
        },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { SidebarNav },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, bar };
    //     },
    //     template: `
    //         <SidebarNav v-bind="args" :foo="foo">
    //             ${args.default}
    //         </SidebarNav>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof SidebarNav>;

export const AdvisoryMode: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        mode: 'advisory',
        activeLink: 'Assets',
        user: {
            _id: '1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
        }
    },
};

export const NonAdvisoryMode: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        mode: 'non-advisory',
        activeLink: 'Assets',
        user: {
            _id: '1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
        }
    },
};
