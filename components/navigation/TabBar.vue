<template>
    <div class="tab-bar">
        <TabBarTab
            v-for="tab in compTabs"
            @clicked="handleTabClicked"
            :key="tab.name"
            v-bind="tab"
        />
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import TabBarTab from './TabBarTab.vue';
import type { TabBarTabProps } from './TabBarTab.vue';

/** A list of tabs stretching horizontally */
defineOptions();

export interface TabBarProps {
    tabs: TabBarTabProps[];
    activeTab?: TabBarTabProps['name']|null;
}
const props = defineProps<TabBarProps>();

const compTabs = computed(() => (props.activeTab)
    ? props.tabs.map(tab => ({
        ...tab,
        active: tab.name === props.activeTab
    }))
    : props.tabs
);

const emit = defineEmits<{
    clicked: [name: TabBarTabProps['name']]
}>()

function handleTabClicked(e: TabBarTabProps['name']) {
    emit('clicked', e);
}
</script>

<style lang="scss">
@import './TabBar';
</style>
