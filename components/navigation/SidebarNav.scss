@import '/assets/css/mixins';

.sidebar-nav {
    width: var(--sidebar-width);
    height: 100vh;
    position: relative;
    transition: width var(--sidebar-nav-expand-transition);
    flex: none;

    --sidebar-nav-background: #00010c;

    --sidebar-nav-expand-transition: 0.5s ease;
    --sidebar-nav-text-color: var(--color-blue-grey);
    --sidebar-nav-text-hover-color: var(--color-grey);
    --sidebar-nav-active-bg-color: transparent;
    --sidebar-nav-text-active-color: #ffffff;
    --sidebar-extended-width: 210px;
    --sidebar-collapsed-width: 80px;
    --sidebar-width: var(--sidebar-collapsed-width);

    .sidebar-nav-block {
        position: fixed;
        top: 0;
        left: 0;
        height: 100dvh;
        width: var(--sidebar-width);
        transition: width var(--sidebar-nav-expand-transition);
        border-top-right-radius: 40px;
        padding: 40px 20px;
        display: flex;
        justify-content: flex-start;
        flex-direction: column;
        align-items: center;
        color: var(--color-white);
        gap: 40px;
        background-color: var(--sidebar-nav-background);
    }

    .logo {
        flex: none;
        margin-left: 6px;
        align-self: flex-start;
    }

    nav {
        display: flex;
        flex-direction: column;

        .sidebar-nav-item {
            text-decoration: none; // Remove link underline
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            color: var(--sidebar-nav-text-color);
            transition: color 0.15s width var(--sidebar-nav-expand-transition);
            width: 40px;
            overflow-x: hidden;

            .sidebar-nav-item-icon {
                flex: none;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: inherit;
            }

            &:hover {
                color: var(--sidebar-nav-text-hover-color);
            }

            &.sidebar-nav-item-active {
                color: #ffffff !important;
                background: var(--sidebar-nav-active-bg-color) !important;

                .sidebar-nav-item-icon {
                    color: #ffffff !important;
                    svg {
                        color: #ffffff !important;
                        fill: currentColor !important;
                    }
                }

                .sidebar-nav-item-label {
                    color: #ffffff !important;
                }
            }

            .sidebar-nav-item-label {
                display: flex;
                flex-direction: row;
                gap: 10px;
                padding-right: 0px;
                opacity: 0;
                // visibility: hidden;
                transition:
                    opacity var(--sidebar-nav-expand-transition),
                    visiblity var(--sidebar-nav-expand-transition);
                margin-left: -1px;
                font-size: 14px;
            }

            svg {
                display: block;
            }
        }

        .sidebar-nav-advisor-items {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 0px;

            width: auto;

            background: var(--color-grey-section);
            border-radius: 20px;
        }
    }

    .sidebar-nav-advisor-items {
        .sidebar-nav-item.sidebar-nav-item-active {
            color: rgba(233, 237, 241, 1) !important;
            .sidebar-nav-item-icon {
                color: rgba(233, 237, 241, 1) !important;
            }
            .sidebar-nav-item-label {
                color: rgba(233, 237, 241, 1) !important;
            }
        }
    }

    &.sidebar-nav-advisor {
        --sidebar-nav-background: #055efa;
        --sidebar-nav-text-color: rgba(var(--color-white-rgb), 0.9);
        --sidebar-nav-text-hover-color: rgba(var(--color-white-rgb), 1);
        --sidebar-nav-text-active-color: rgba(5, 94, 250, 1);
        --sidebar-nav-active-bg-color: #fff;
        nav .sidebar-nav-item {
            border-radius: 20px;

            &.sidebar-nav-item-active {
                color: rgba(5, 94, 250, 1) !important;
                background: var(--sidebar-nav-active-bg-color) !important;

                .sidebar-nav-item-icon {
                    color: rgba(5, 94, 250, 1) !important;
                    svg {
                        color: rgba(5, 94, 250, 1) !important;
                        fill: currentColor !important;
                    }
                }
                .sidebar-nav-item-label {
                    color: rgba(5, 94, 250, 1) !important;
                }
            }
        }
    }

    &.sidebar-expanded {
        --sidebar-width: var(--sidebar-extended-width);
        display: flex;
        align-items: flex-start;

        nav {
            .sidebar-nav-item {
                width: calc(var(--sidebar-extended-width) - 40px);
                justify-content: flex-start;
                .sidebar-nav-item-label {
                    opacity: 1;
                    visibility: visible;
                }

                // Ensure icons are visible when expanded
                svg {
                    display: block;
                }
            }
        }

        &.sidebar-nav-advisor {
            nav .sidebar-nav-item {
                &.sidebar-nav-item-active {
                    padding-right: 15px;
                    width: max-content;
                    max-width: calc(var(--sidebar-extended-width) - 40px);
                }
            }
        }
    }

    .expand-button {
        position: absolute;
        top: 40px;
        right: -15px;
        width: 24px;
        height: 24px;
        background-color: #19263c;
        border-radius: 3px;
        color: #57719c;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition:
            opacity 0.2s ease,
            visibility 0.2s ease,
            background-color 0.2s ease,
            color 0.2s ease;

        opacity: 0;

        &:hover {
            background-color: #57719c;
            color: #ffffff;
        }

        &.button-visible {
            opacity: 1;
        }
    }

    .onboarding-status {
        display: flex;
        padding-left: 5px;

        .onboarding-status-label {
            opacity: 0;
            transition: opacity 0.3s ease;
            color: #57719c;
            font-size: 14px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            word-wrap: break-word;
            margin-left: 15px;
        }

        .onboarding-status-percent {
            color: #57719c;
            padding-top: 2px;
        }

        .onboarding-status-number {
            color: #97abcc;
            font-size: 12px;
            font-family: 'Oxygen', sans-serif;
            font-weight: 400;
            word-wrap: break-word;
        }
    }

    .sidebar-nav-advisory-exit {
        width: 40px;
        height: 40px;
        background: #fff;
        border-radius: 20px;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-blue);
        margin-top: auto;
    }

    .user-initials {
        background-color: #57719c;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: bold;
        margin-top: auto;
        margin-bottom: 20px;
        cursor: pointer;
        border: 1px solid var(--color-blue-grey);
        flex: none;

        span {
            text-transform: uppercase;
        }

        &:hover {
            background-color: rgba(var(--color-white-rgb), 1);
            color: #57719c;
        }
    }
}
