<template>
    <div
        class="sidebar-nav"
        :class="[{ 'sidebar-expanded': isExpanded, 'sidebar-nav-advisor': isAdvisoryMode }]"
        @mouseover="isHovered = true"
        @mouseleave="isHovered = false"
    >
        <div class="sidebar-nav-block">
            <template v-if="isAdvisoryMode">
                <div class="sidebar-nav-advisory-user" v-if="user?.userData">
                    <NuxtLink to="/client/settings/Profile">
                        <UserAvatar :size="isExpanded ? 60 : 30" mode="initials" />
                    </NuxtLink>
                </div>
            </template>
            <template v-else>
                <Logo variant="brandmark" width="28" height="25" />
            </template>

            <nav>
                <NuxtLink
                    v-for="item in filteredItems"
                    :key="item.label"
                    :to="item.route"
                    class="sidebar-nav-item"
                    :class="{
                        'sidebar-nav-item-active':
                            item.route === '/client/dashboard'
                                ? route.path === item.route
                                : route.path.startsWith(item.route),
                        'advisory-mode': isAdvisoryMode,
                    }"
                >
                    <span class="sidebar-nav-item-icon">
                        <SvgIcon :shape="item.icon" width="24" height="24" />
                    </span>
                    <span v-show="isExpanded" class="sidebar-nav-item-label">
                        {{ item.label }}
                    </span>
                </NuxtLink>

                <div class="sidebar-nav-advisor-items" v-if="userIsAdvisor && !user?.isImpersonated">
                    <NuxtLink
                        v-for="item in advisorItems"
                        :key="item.label"
                        :to="item.route"
                        class="sidebar-nav-item"
                        :class="{
                            'sidebar-nav-item-active': route.path === item.route,
                            'advisory-mode': isAdvisoryMode,
                        }"
                    >
                        <span class="sidebar-nav-item-icon">
                            <SvgIcon :shape="item.icon" width="24" height="24" />
                        </span>
                        <span v-show="isExpanded" class="sidebar-nav-item-label">
                            {{ item.label }}
                        </span>
                    </NuxtLink>
                </div>
            </nav>

            <button class="expand-button" :class="{ 'button-visible': expandButtonIsVisible }" @click="toggleSidebar">
                <SvgIcon :shape="isExpanded ? 'keyboard-arrow-left' : 'keyboard-arrow-right'" width="16" height="16" />
            </button>

            <div class="onboarding-status" v-if="completionPercentage">
                <span class="onboarding-status-number">
                    {{ displayedCompletionPercentage }}
                </span>
                <span class="onboarding-status-percent">
                    <SvgIcon :shape="'percent'" width="15" height="15" />
                </span>
                <span v-show="isExpanded" class="onboarding-status-label"> Complete </span>
            </div>

            <template v-if="isAdvisoryMode">
                <button
                    v-if="user?.isImpersonated"
                    class="sidebar-nav-advisory-exit"
                    @click.prevent="handleStopImpersonate"
                >
                    <SvgIcon shape="close" width="24" height="24" />
                </button>
                <NuxtLink v-else to="/advisor/UserManagement" class="sidebar-nav-advisory-exit">
                    <SvgIcon shape="list" width="24" height="24" />
                </NuxtLink>
            </template>
            <template v-else>
                <NuxtLink to="/client/settings/Profile" class="user-initials">
                    <UserAvatar :size="isExpanded ? 60 : 30" mode="initials" />
                </NuxtLink>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import Logo from '../images/Logo.vue';
import SvgIcon from '../images/SvgIcon.vue';
import UserAvatar from '../images/UserAvatar.vue';
import type { UserManagementUser } from '~/server/api/advisors/users.get';

const props = defineProps<{
    // mode: 'advisory' | 'non-advisory';
    completionPercentage?: number;
    // user?: UserData
    activeLink?: string;
    // onboardingComplete?: boolean;
}>();

const expandButtonIsVisible = computed(() => {
    return isHovered.value;
});

const displayedCompletionPercentage = computed(() => {
    return props.completionPercentage ?? 0;
});

const items = [
    // Temporarily switched out to be able to get back to intake easily
    // { icon: 'financial-profile', label: 'Overview', route: '/dashboard' },
    { icon: 'financial-profile', label: 'Overview', route: '/client/dashboard' },
    { icon: 'timeline', label: 'Reports', route: '/client/reports/cash-flow' },
    // { icon: 'grid-small', label: 'Situations', route: '/client/intake' },
    { icon: 'assets', label: 'Assets', route: '/client/dashboard/assets' },
    { icon: 'debt', label: 'Liabilities', route: '/client/dashboard/liabilities' },
    { icon: 'budget', label: 'Income', route: '/client/dashboard/income' },
    { icon: 'payments', label: 'Expenses', route: '/client/dashboard/expenses' },
    { icon: 'settings', label: 'Settings', route: '/client/settings/Profile' },
];

const advisorItems = computed(() => [
    { icon: 'people', label: 'User Management', route: '/advisor/UserManagement' },
    { icon: 'dashboard', label: 'Profile', route: '/dashboards/edit' },
]);

const onboardingItem = { icon: 'grid', label: 'Onboarding', route: '/client/intake' };

const isHovered = ref(false);
const isExpanded = ref(false);
const showLabels = ref(false);

const { user, refresh: userRefresh } = useOidcAuth();
const route = useRoute();
const router = useRouter();

const userIsAdvisor = computed(() => isInRole(user, ['Advisor']));

const isAdvisoryMode = computed(() => {
    // Only show advisor mode when actually impersonating a client
    return userIsAdvisor.value && user.value?.isImpersonated;
});

// Temporary fix to reference intake data rather than the userData which is not being updated
const overhaulStore = useOverhaulStore();
const intakeCompleted = computed(() => {
    return user.value?.userData?.intakeCompleted || overhaulStore.data.intakeCompleted;
});

const filteredItems = computed(() => {
    return intakeCompleted.value ? [...items] : [onboardingItem];
});

function toggleSidebar() {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
        setTimeout(() => {
            showLabels.value = true;
        }, 300); // Adjust the delay as needed
    } else {
        showLabels.value = false;
    }
}

async function handleStopImpersonate() {
    try {
        console.log('Stop impersonating user');
        await $fetch('/api/advisors/impersonate', { method: 'delete' });
        await userRefresh();
        await router.push('/advisor/UserManagement');
    } catch (e) {
        console.log('Error in handleStopImpersonate()');
        console.log(`${e}`);
    }
}
</script>

<style lang="scss">
@import './SidebarNav';

.advisor-racetrack {
    position: relative;
    margin: 8px 0;
    padding: 4px 0;
    background-color: #19263c;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;

    .sidebar-nav-item {
        margin: 0;
        padding: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .sidebar-nav-item-icon {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .sidebar-nav-item-label {
            font-size: 12px;
            margin-top: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        &:hover .sidebar-nav-item-label {
            opacity: 1;
        }

        &.sidebar-nav-item-active {
            .sidebar-nav-item-icon {
                color: #e9edf1;
            }
        }
    }
}
</style>
