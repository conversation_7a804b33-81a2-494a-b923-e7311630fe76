@import "/assets/css/mixins";

.tab-bar {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    width: 100%;
}

.tab-bar-tab {
    display: flex;
    align-items: center;
    justify-content: flex-start;   
    gap: 10px;
    
    position: relative;
    height: 34px;
    padding-bottom: 14px;

    overflow: hidden;

    color: var(--color-blue-grey);
    text-overflow: ellipsis;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 1.6666667em;

    flex: 1;

    --tab-bar-progress-bg: rgba(255, 255, 255, 0.1);

    transition: color .2s;

    .tab-bar-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--tab-bar-progress-bg);

        transition: background-color .2s;

        .tab-bar-progress-bar {
            height: 4px;
            background: var(--color-blue);
            width: 0;
            transition: width .2s;
        }
    }

    &:hover {
        --tab-bar-progress-bg: rgba(255, 255, 255, 0.2);
        color: var(--color-grey);
    }

    &.tab-bar-tab-active {
        --tab-bar-progress-bg: rgba(255, 255, 255, 0.2);
        color: var(--color-white);
    }

    // <div class="tab-bar-tab-progress">
    //     <div class="tab-bar-tab-progress-bar" :style="{ width: props.progress + '%' }"></div>
    // </div>
}
