<template>
    <div class="sidebar-help">
        <div class="sidebar-help-nav">
            <SvgIcon shape="info" />
            <SvgIcon shape="search" />
        </div>

        <div class="sidebar-help-content">
            <ContentDoc
                v-if="topics?.length"
                v-for="topic in topics"
                :path="'/'+topic"
            >
                <template #not-found> 
                    <!-- Empty -->
                </template>
            </ContentDoc>

            <div v-else class="text-xs flex items-center justify-center text-white/50 py-10 px-2 bg-black/10">
                No help for this page
            </div>
            <!-- <h4>Income</h4>
            <p>In ad amet proident cillum pariatur sit nulla sunt ullamco. Esse et nulla amet aliqua nisi officia cillum ipsum tempor magna reprehenderit ullamco esse sunt adipisicing. In sit non cillum magna sit ex anim anim duis voluptate cupidatat sit nulla. Incididunt labore magna commodo occaecat qui eiusmod aliquip mollit duis laborum veniam nostrud. Ad aute sunt reprehenderit exercitation. Non ipsum velit deserunt dolor veniam amet dolore exercitation qui ipsum eu mollit commodo qui ea. Pariatur duis ad fugiat nisi nisi ex consequat. Qui ad cillum labore in eiusmod eu adipisicing esse ea eiusmod Lorem veniam.</p>
            <p>Id irure in commodo laborum incididunt irure deserunt. Labore minim ad est non fugiat culpa. Id ad sit ad cupidatat labore non consectetur reprehenderit duis cillum pariatur pariatur anim. Adipisicing ipsum id commodo eu voluptate magna veniam adipisicing incididunt aliqua voluptate nostrud in. Id sunt fugiat fugiat fugiat proident incididunt incididunt incididunt dolor consequat eu duis sint.</p>
            <p>Culpa elit aliqua dolore labore ullamco et. Proident duis nulla cillum ullamco amet qui. Aliqua qui Lorem laborum in. Anim irure irure officia.</p>
            <p>Ipsum veniam deserunt dolor quis ullamco consectetur. Cillum ex occaecat cupidatat ad cupidatat minim anim est voluptate labore esse. Lorem et pariatur labore ea exercitation sit. Culpa adipisicing nisi amet aliquip non duis consectetur id fugiat esse culpa laborum aute nisi.</p> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';
import type { Ref } from 'vue';

const helpStore = useHelpStore();
const { topics } = storeToRefs(helpStore);

/** The right sidebar help panel */
defineOptions();
</script>

<style lang="scss">
@import "./SidebarHelp";
</style>
