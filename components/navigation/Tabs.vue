<template>
    <div class="tabs">
        <button
            v-for="tab in tabs"
            :key="tab.id"
            :class="['tab', { active: activeTab === tab.id }]"
            @click="handleTabClick(tab.id)"
            ref="tabRefs"
        >
            {{ tab.label }}
        </button>
        <div class="active-indicator" :style="activeIndicatorStyle"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';

interface Tab {
    id: string;
    label: string;
}

interface TabsProps {
    tabs: Tab[];
    modelValue: string;
}

const props = defineProps<TabsProps>();
const emit = defineEmits<{
    'update:modelValue': [value: string];
}>();

const tabRefs = ref<HTMLButtonElement[]>([]);
const activeIndicatorStyle = ref({
    width: '0px',
    transform: 'translateX(0)',
});

const activeTab = computed(() => props.modelValue);

const updateActiveIndicator = () => {
    const activeTabIndex = props.tabs.findIndex(tab => tab.id === props.modelValue);
    if (activeTabIndex === -1 || !tabRefs.value[activeTabIndex]) return;

    const activeTabElement = tabRefs.value[activeTabIndex];
    activeIndicatorStyle.value = {
        width: `${activeTabElement.offsetWidth}px`,
        transform: `translateX(${activeTabElement.offsetLeft}px)`,
    };
};

watch(() => props.modelValue, updateActiveIndicator, { immediate: true });

onMounted(() => {
    updateActiveIndicator();
});

const handleTabClick = (tabId: string) => {
    emit('update:modelValue', tabId);
};
</script>

<style lang="scss" scoped>
.tabs {
    display: inline-flex;
    background: var(--secondary-grey-section, #19263c);
    border-radius: 10px;
    position: relative;
    overflow: hidden;

    .tab {
        border: none;
        background: transparent;
        border-radius: 10px;
        cursor: pointer;
        font-size: 14px;
        font-family: 'Oxygen';
        font-weight: 700;
        line-height: 20px;
        color: var(--primary-white, #e9edf1);
        z-index: 1;
        transition: all 0.2s ease;
        position: relative;
        padding: 10px 20px;

        &:hover:not(.active) {
            color: rgba(233, 237, 241, 0.8);
        }
    }

    .active-indicator {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background: var(--primary-blue, #055efa);
        border-radius: 0;
        transition: all 0.3s ease;
        z-index: 0;
    }
}
</style>
