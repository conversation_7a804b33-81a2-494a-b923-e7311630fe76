import DashboardWidget from './DashboardWidget.vue';
import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof DashboardWidget> = {
    component: DashboardWidget,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },
        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn(),
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { DashboardWidget },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <DashboardWidget v-bind="args" :foo="foo">
    //             ${args.default}
    //         </DashboardWidget>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;"><story/></div>' })],
};

export default meta;
type Story = StoryObj<typeof DashboardWidget>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        label: 'Cash Flow',
    },
};
