<template>
    <Modal :is-open="isOpen" @close="handleModalClose" class="add-widget-modal">
        <template #header>
            <div class="add-widget-modal-header">
                <span class="add-widget-modal-title">Add Widget</span>
                <button class="add-widget-modal-close" @click.prevent="handleModalClose">
                    <SvgIcon class="add-widget-modal-close-icon" shape="close" width="24" height="24" />
                </button>
            </div>
        </template>

        <div class="add-widget-modal-body">
            <div class="add-widget-modal-selected" v-if="selectedTypeData">
                <div class="add-widget-modal-type-row add-widget-modal-type-row-filled">
                    <div class="add-widget-modal-type-icon" v-html="selectedTypeData.icon"></div>
                    <div class="add-widget-modal-type-text">
                        <h5 v-text="selectedTypeData.title"></h5>
                        <p v-text="selectedTypeData.description"></p>
                    </div>
                </div>

                <div class="add-widget-modal-form">
                    <div class="add-widget-modal-form-row">
                        <label class="add-widget-modal-form-label">Name</label>
                        <div class="add-widget-modal-form-field">
                            <TextInput :placeholder="selectedTypeData.title" size="sm" v-model="name" />
                        </div>
                    </div>
                    <div class="add-widget-modal-form-group">
                        <div class="add-widget-modal-form-row">
                            <label class="add-widget-modal-form-label">Type</label>
                            <div class="add-widget-modal-form-field">
                                <ChartTypeSelector v-model="chartType" />
                            </div>
                        </div>
                        <div class="add-widget-modal-form-row">
                            <label class="add-widget-modal-form-label">Time</label>
                            <div class="add-widget-modal-form-field">
                                <SelectInput
                                    :options="[{ value: '12mo', label: '12 months' }]"
                                    size="sm"
                                    v-model="time"
                                />
                            </div>
                        </div>
                        <div class="add-widget-modal-form-row">
                            <label class="add-widget-modal-form-label">Bucket Size</label>
                            <div class="add-widget-modal-form-field">
                                <SelectInput
                                    v-model="size"
                                    :options="[{ value: 'month', label: 'Months' }]"
                                    size="sm"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="add-widget-modal-form-group">
                        <div class="add-widget-modal-form-row">
                            <label class="add-widget-modal-form-label">Options</label>
                            <div class="add-widget-modal-form-field">
                                <div class="add-widget-modal-toggle-group">
                                    <ToggleSwitch v-model="options.showIncome" />
                                    <label class="add-widget-modal-toggle-group-label">Show income</label>
                                </div>
                            </div>
                        </div>
                        <div class="add-widget-modal-form-row">
                            <label class="add-widget-modal-form-label"></label>
                            <div class="add-widget-modal-form-field">
                                <div class="add-widget-modal-toggle-group">
                                    <ToggleSwitch v-model="options.showExpenses" />
                                    <label class="add-widget-modal-toggle-group-label">Show expenses</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="add-widget-modal-types-listing" v-else>
                <div
                    v-for="widgetType in allWidgetTypes"
                    :key="widgetType.name"
                    class="add-widget-modal-type-row"
                    @click.prevent="selectWidgetType(widgetType.name)"
                >
                    <div class="add-widget-modal-type-icon" v-html="widgetType.icon"></div>
                    <div class="add-widget-modal-type-text">
                        <h5 v-text="widgetType.title"></h5>
                        <p v-text="widgetType.description"></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="add-widget-modal-footer">
            <Button variant="muted" @click.prevent="handleModalClose">Cancel</Button>
            <Button v-if="selectedTypeData" @click.prevent="handleModalConfirm">Save &amp; Add</Button>
        </div>
    </Modal>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue';
import type { Ref } from 'vue';
import Modal from '../modals/Modal.vue';
import SvgIcon from '../images/SvgIcon.vue';
import TextInput from '../forms/TextInput.vue';
import SelectInput from '../forms/SelectInput.vue';
import ToggleSwitch from '../forms/ToggleSwitch.vue';
import ChartTypeSelector from '../forms/ChartTypeSelector.vue';
import Button from '../elements/Button.vue';

/** The modal to add a new widget to a dashboard */
defineOptions();

const name = ref();
const chartType = ref('bar');
const time = ref('12mo');
const size = ref('month');
const options = ref({
    showIncome: true,
    showExpenses: true,
});

export interface AddWidgetModalReturn {
    name: string;
    kind: string; // Expenses, Cash flow
    options: AddWidgetModalReturnOptions;
}

export interface CashFlowWidgetOptions {
    chartType: string;
    time: string;
    size: string;
    showIncome: boolean;
    showExpenses: boolean;
}
export type AddWidgetModalReturnOptions = CashFlowWidgetOptions;

export interface AddWidgetModalProps {
    isOpen: boolean;
}
const props = defineProps<AddWidgetModalProps>();

const emit = defineEmits<{
    close: [];
    confirm: [e: AddWidgetModalReturn];
}>();

const selectedType = ref<string | null>(null);
// const selectedType = ref<string | null>('cash-flow');
const selectedTypeData = computed(() => {
    if (selectedType.value) {
        return allWidgetTypes.find(widgetType => widgetType.name === selectedType.value);
    }
    return null;
});

const allWidgetTypes = [
    // {
    //     title: 'Expenses',
    //     name: 'expenses',
    //     description: 'View your total expenses and how they have changed over time.',
    //     icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    //         <path d="M66.0013 33L65.7684 33.121C64.0628 34.0072 62.2628 34.6985 60.4024 35.1818V35.1818L60.3125 35.2052C58.5135 35.6726 56.6623 35.9091 54.8036 35.9091V35.9091H54.5861C52.9049 35.9091 51.2637 36.4221 49.8817 37.3795V37.3795C49.4315 37.6914 49.0134 38.0473 48.6336 38.4419L46.4053 40.7576L45.0826 41.903C44.1301 42.7278 42.9123 43.1818 41.6523 43.1818V43.1818" stroke="#57719C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    //         <path d="M15 48.9999H17.8605H20.721H20.9614C22.6609 48.9999 24.3133 48.4417 25.6646 47.411V47.411C26.1808 47.0173 26.7443 46.69 27.342 46.4367L27.5373 46.354C28.7021 45.8604 29.9543 45.606 31.2194 45.606H32.1629V45.606C34.0506 45.606 35.919 45.2265 37.6571 44.49L37.8838 44.3939L40.7443 43.1818" stroke="#E9EDF1" stroke-width="2"/>
    //         <rect x="6" y="18" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="25" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="32" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="39" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="46" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="53" width="4" height="1" fill="#57719C"/>
    //         <rect x="6" y="60" width="4" height="1" fill="#57719C"/>
    //         <rect x="13" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="20.8555" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="28.7148" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="36.5703" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="44.4297" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="52.2852" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="60.1445" y="65" width="1" height="4" fill="#57719C"/>
    //         <rect x="68" y="65" width="1" height="4" fill="#57719C"/>
    //     </svg>`,
    // },
    {
        title: 'Cash flow',
        name: 'cash-flow',
        description: 'View income and expenses over time and visually understand the outliers.',
        icon: `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="14" y="26" width="9" height="16" fill="#E9EDF1"/>
            <rect x="14" y="44" width="9" height="9" fill="#57719C"/>
            <rect x="25" y="25" width="9" height="17" fill="#E9EDF1"/>
            <rect x="25" y="44" width="9" height="11" fill="#57719C"/>
            <rect x="36" y="23" width="9" height="19" fill="#E9EDF1"/>
            <rect x="36" y="44" width="9" height="7" fill="#57719C"/>
            <rect x="47" y="27" width="9" height="15" fill="#E9EDF1"/>
            <rect x="47" y="44" width="9" height="13" fill="#57719C"/>
            <rect x="58" y="26" width="9" height="16" fill="#E9EDF1"/>
            <rect x="58" y="44" width="9" height="11" fill="#57719C"/>
        </svg>`,
    },

    // // Savings goal progress
    // `<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
    //     <rect x="0.5" y="0.5" width="79" height="79" rx="3.5" stroke="#57719C"/>
    //     <g clip-path="url(#clip0_2249_25952)">
    //     <ellipse cx="40.0006" cy="54.3529" rx="31.7037" ry="31.4706" stroke="#57719C" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="1 3"/>
    //     <path d="M9.4374 46.5118C9.09546 46.4332 8.88181 46.0915 8.96846 45.7515C10.1705 41.0352 12.4587 36.6568 15.6562 32.9623C18.993 29.1071 23.2208 26.1114 27.978 24.2316C32.7353 22.3517 37.8804 21.6436 42.9735 22.1678C47.8553 22.6703 52.55 24.2907 56.6893 26.8978C56.9856 27.0844 57.065 27.4787 56.8706 27.7698C56.678 28.0581 56.2892 28.1364 55.9957 27.9517C52.0254 25.4541 47.5237 23.9017 42.8427 23.4199C37.9534 22.9167 33.014 23.5965 28.447 25.4011C23.8801 27.2058 19.8214 30.0816 16.6181 33.7826C13.5531 37.3239 11.3586 41.5199 10.2031 46.0396C10.1166 46.378 9.77777 46.5901 9.4374 46.5118Z" fill="#E9EDF1"/>
    //     </g>
    //     <rect x="29" y="47" width="22" height="4" fill="#E9EDF1"/>
    //     <rect x="29" y="53" width="22" height="4" fill="#57719C"/>
    //     <defs>
    //     <clipPath id="clip0_2249_25952">
    //     <rect width="64" height="25" fill="white" transform="translate(8 22)"/>
    //     </clipPath>
    //     </defs>
    // </svg>`
];

function selectWidgetType(name: string) {
    console.log(allWidgetTypes.find(widgetType => widgetType.name === name));
    selectedType.value = name;
}

function handleModalClose() {
    console.log('close me');
    emit('close');
}

function handleModalConfirm() {
    if (selectedType.value === null) return;
    emit('confirm', {
        name: name.value ? name.value : selectedTypeData.value?.title,
        kind: selectedType.value,
        options: {
            chartType: chartType.value,
            time: time.value,
            size: size.value,
            showIncome: options.value.showIncome ?? false,
            showExpenses: options.value.showExpenses ?? false,
        },
    });
}

function resetModalState() {
    selectedType.value = null;
    name.value = null;
    chartType.value = 'bar';
    time.value = '12mo';
    size.value = 'month';
    options.value.showIncome = true;
    options.value.showExpenses = true;
}

watch(
    () => props.isOpen,
    newValue => {
        if (newValue) {
            resetModalState();
        }
    },
);
</script>

<style lang="scss">
@import './AddWidgetModal';
</style>
