import numeral from 'numeral';
import {Ticks} from "chart.js";

export const colors = [
    '#055efa', // blue
    '#ff7a50', // orange
    '#02bebf', // cyan
    '#8a53ff', // purple
    '#cb007a'  // pink
]

export type AxisFormat = 'currency' | 'percent' | 'date' | null;

export function formatMoneyAxis(value: any) {
    return numeral(value).format('$0.00a');
}

export function formatPercentAxis(value: any) {
    return numeral(value).format('0.##%');
}

export function getAxisFormatter(type?: AxisFormat) {
    switch (type) {
        case 'currency':
            return formatMoneyAxis;
        case 'percent':
            return formatPercentAxis;
        default:
            return null;
    }
}
