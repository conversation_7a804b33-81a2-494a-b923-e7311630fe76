<template>
    <div class="tree-chart">
        <canvas :id="elementId" ref="canvas"></canvas>
    </div>
</template>

<script setup lang="ts">
import {computed, getCurrentInstance, nextTick, onMounted, ref} from 'vue';

import * as helpers from 'chart.js/helpers';
import {colors, getAxisFormatter} from './chartjs';
import {TreemapController, TreemapElement, type TreemapScriptableContext} from 'chartjs-chart-treemap';
import Chart, {type ChartConfiguration, type ChartData, type DefaultDataPoint, type TooltipItem} from 'chart.js/auto';


/** A tree chart component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'tree-chart-' + componentId);

export interface TreeChartProps {
    data: ChartData<'treemap', DefaultDataPoint<'treemap'>, string>;
    max: number;
}
const props = withDefaults(defineProps<TreeChartProps>(), {
});



const canvas = ref(null);

onMounted(async () => {
    Chart.register(TreemapController, TreemapElement);
    const ctx = canvas.value;
    if (ctx) {
        await nextTick();
        new Chart(ctx, chartProps.value)
    }
});

const chartProps = computed<ChartConfiguration<'treemap', DefaultDataPoint<'treemap'>>>(() => {
    return {
      type: 'treemap',
      data: props.data,
      options: {
        datasets: {
          treemap: {
            borderColor: '#19263C',
            borderWidth: 1,
            spacing: 0,
            backgroundColor: (ctx) => colorFromRaw(ctx),
            labels: {
              display: true,
              //formatter: (ctx) => props.data.labels?.[ctx.dataIndex] ?? undefined,
              //color:'#97ABCC',
              position: 'top',
              align: 'left',
              color: ['white', 'whiteSmoke'],
              hoverColor: ['whiteSmoke', 'white'],
              padding: 5,
              font: [{weight: 'bold', size:10}, {size: 9}],
              formatter(ctx) {
                if (ctx.type !== 'data') {
                  return;
                }
                const raw : any = ctx.raw;
                const label : string = raw._data.label;
                const valueFormatter = getAxisFormatter("currency");
                const val = valueFormatter ? valueFormatter(raw.v) : raw.v;
                return [label, `${val}`];
              }
            },
            captions: {
              display: true,
              position: 'top',
              align: 'left',
              color: 'white',
              hoverColor: 'whiteSmoke',
              padding: 3,
              font: {weight: 'bold', size:12},
              formatter(ctx) {
                const raw : any = ctx.raw;
                return ctx.raw.g || raw._data.label || '';
              }
            },
          }
        },
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: false,
          },
          legend: {
            display: false
          },
          tooltip: {
            displayColors: false,
            titleFont: {size: 12},
            callbacks: {
              title() {
                return "Values";
               },
              label: function (ctx: TooltipItem<'treemap'>) {
                const raw: any = ctx.raw;
                const valueFormatter = getAxisFormatter("currency");
                return `${raw.g}: ${valueFormatter ? valueFormatter(raw.v) : raw.v}`;
              }
            }
          }
        }
      }
    }
});

const categoryColorMap : Record<string, string> = {};

function colorFromRaw(ctx: TreemapScriptableContext) {
    if (ctx.type !== 'data') {
      return 'transparent';
    }
    const raw : any = ctx.raw;
    const path : string = raw._data.path;
    const paths = path.split('.');
    const category : string = paths[0];
    let color = categoryColorMap[category];
    if (!color) {
      color = colors[Object.keys(categoryColorMap).length % colors.length];
      categoryColorMap[category] = color;
    }
    const darken = (ctx.raw.l ?? 0) / 5;
    //let alpha = (1 + Math.log(value)) / 25;
  
    return helpers.color(color)
        .darken(darken)
        .rgbString();
}
</script>

<style lang="scss">
.tree-chart {
    flex: auto;
    display:flex;
    align-items: stretch;
    justify-content: stretch;
    position: relative;
    min-height: 90px;
    canvas {
        max-width: 100%;
    }
}
</style>
