<template>
    <div class="line-chart">
        <canvas :id="elementId"></canvas>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, nextTick } from 'vue';

import Chart, {
  type ChartConfiguration,
  type ChartData,
  type DefaultDataPoint
} from 'chart.js/auto';
import 'chartjs-adapter-date-fns';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import type { LegendOptions } from 'chart.js';
import {colors, getAxisFormatter} from './chartjs';
import type { AxisFormat } from './chartjs';

/** A line chart component that's a wrapper around a ChartJS chart */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'line-chart-' + componentId);

export interface LineChartProps {
    data: ChartData<'line'>;
    
    // Whether to show the legend, with optional configuration
    legend?: boolean | LegendOptions<'line'>;

    // Whether to show point markers on the line graph
    showPoints?: boolean;

    // Optional configuration for the x-axis
    xAxis?: boolean|string;
    
    xLabelFormat?: AxisFormat | null;

    // Optional configuration for the y-axis
    yAxis?: boolean|string;

    yLabelFormat?: AxisFormat | null;

    // Whether to curve the lines
    curved?: boolean;

    min?: number;

    max?: number;
}
const props = withDefaults(defineProps<LineChartProps>(), {
    legend: false,
    showPoints: false,
    xAxis: true,
    yAxis: true
})

onMounted(async () => {
    const ctx = document.getElementById(elementId.value);
    if (ctx instanceof HTMLCanvasElement) {
        await nextTick();
        new Chart(ctx, chartProps.value)
    }
});

const mappedDatasets = computed(() => {
    return props.data.datasets.map((dataset, index) => {
        const color = colors[index % colors.length];
        return {
            borderColor: color,
            backgroundColor: color,
            pointBorderColor: color,
            pointHoverBackgroundColor: color,
            pointHoverBorderColor: color,
            ...dataset,
        }
    })
})

const xAxisCallback = computed(() => {
    return (props.xLabelFormat) ? { callback: getAxisFormatter(props.xLabelFormat) } : {}
})

const yAxisCallback = computed(() => {
  return (props.yLabelFormat) ? { callback: getAxisFormatter(props.yLabelFormat) } : {}
})

const chartProps = computed<ChartConfiguration<'line', DefaultDataPoint<'line'>>>(() => {
  return {
    type: 'line',
    plugins: [ChartDataLabels],
    data: {
      labels: props.data.labels,
      datasets: mappedDatasets.value
    },
    options: {
      plugins: {
        legend: (typeof props.legend === 'boolean') ? {display: props.legend} : props.legend,
        datalabels: {
          color: 'white',
          align: 'end',
          anchor: 'center',
          display: 'auto',
          formatter: getAxisFormatter(props.yLabelFormat)
        },
        tooltip: {
          displayColors: false,
          titleFont: {size: 12},
          callbacks: {
            label: function(context) {
              const formatter = getAxisFormatter(props.yLabelFormat);
              const value = context.parsed.y;
              if (formatter && value !== null) {
                return formatter(value);
              }
              return null;
            }
          }
        }
      },
      elements: {
        point: {
          borderWidth: 2,
          backgroundColor: '#19263C',
          pointStyle: (props.showPoints) ? 'circle' : false
        }
      },
      datasets: {
        line: {
          borderWidth: 1,
          tension: (props.curved) ? 0.4 : 0,
        }
      },
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        x: {
          //type: 'time',
          type: props.xLabelFormat === 'date' ? 'time' : 'linear',
          grid: {
            display: false
          },
          border: {
            display: false
          },
          ticks: {
            display: !!props.xAxis,
            padding: 10,
            ...xAxisCallback.value,
          },
          time: {
            minUnit: 'day',
            tooltipFormat: 'MMM d, yyyy',
            displayFormats: {
              day: 'MMM d yy',
              week: "'Wk' w yy",
              month: 'MMM yyyy',
              quarter: 'QQQ yy',
              year: 'yyyy'
            }
          }
        },
        y: {
          type: props.yLabelFormat === 'date' ? 'time' : 'linear',
          suggestedMin: props.min,
          suggestedMax: props.max,
          grid: {
            display: false,
          },
          border: {
            display: false
          },
          ticks: {
            display: !!props.yAxis,
            ...yAxisCallback.value,
          },
          time: {
            minUnit: 'day',
            tooltipFormat: 'MMM d, yyyy',
            displayFormats: {
              day: 'MMM d yy',
              week: "'Wk' w yy",
              month: 'MMM yy',
              quarter: 'QQQ yy',
              year: 'yyyy'
            }
          }
        }
      }
    }
  };
});
/*    
    return {
        type: 'line',
        data: {
            labels: props.data.labels,
            // datasets: props.data.datasets
            datasets: mappedDatasets.value
        },
        options: {
          plugins: {
            legend: (props.legend) ? props.legend : {display: false},
          },
            elements: {
                point: {
                    pointBorderWidth: 2,
                    pointBackgroundColor: '#19263C',
                    pointStyle: (props.showPoints) ? 'circle' : false,
                }
            },
            datasets: {
                line: {
                    borderWidth: 1,
                    tension: (props.curved) ? 0.4 : 0,
                    // borderColor: colors,
                }
            },
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        display: (props.xAxis) ? true : false,
                        padding: 10,
                        ...xAxisCallback.value,
                    }
                },
                y: {
                    suggestedMin: props.min,
                    suggestedMax: props.max,
                    grid: {
                        display: false,
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        display: (props.yAxis) ? true : false,
                        ...yAxisCallback.value,
                    },
                }
            }
        }
    }
});*/
</script>

<style lang="scss">
.line-chart {
    flex: auto;
    display:flex;
    align-items: stretch;
    justify-content: stretch;
    position: relative;
    canvas {
        max-width: 100%;
    }
}
</style>
