<template>
    <div class="dashboard-widget" @click.prevent="handleClick">
        <component
            :is="dynamicComponent"
            v-if="dynamicComponent"
            v-bind="$props"
            @click.prevent="handleClick"
            @remove="handleRemove"
        />
    </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, getCurrentInstance, ref } from 'vue';
import SvgIcon from '../images/SvgIcon.vue';

/** TODO: Description of this component */
defineOptions();

import type { CashFlowWidgetOptions } from './widgets/CashFlowWidget.vue';

export interface DashboardLegendItem {
    label: string;
    color: string;
}
export type DashboardLegendData = DashboardLegendItem[];

export type DashboardWidgetKind = 'cash-flow' | 'expenses';
export type DashboardWidgetChartType = 'line' | 'bar';
export type DashboardWidgetOptions = CashFlowWidgetOptions;
export interface DashboardWidget {
    id: string;
    name: string;
    kind: DashboardWidgetKind;
    options: DashboardWidgetOptions;
}

export interface DashboardWidgetProps {
    widget: DashboardWidget;
    label?: string;
    mode?: 'view' | 'edit';
    draggable?: boolean;
}
const props = withDefaults(defineProps<DashboardWidgetProps>(), {
    mode: 'view',
    draggable: false,
});

const emit = defineEmits<{
    remove: [];
}>();

// Define the available components, and an object to map directly to
const components = {
    BarChart: defineAsyncComponent(() => import('./BarChart.vue')),
    CashFlowWidget: defineAsyncComponent(() => import('./widgets/CashFlowWidget.vue')),
    LineChart: defineAsyncComponent(() => import('./LineChart.vue')),
};
const componentMap: Record<DashboardWidgetKind, ReturnType<typeof defineAsyncComponent>> = {
    'cash-flow': components.CashFlowWidget,
    // 'bar': components.BarChart,
    // 'line': components.LineChart
};

// Which component should this instance render
const dynamicComponent = computed(() => componentMap[props.widget.kind] ?? null);

// Any additional props to pass to the chart component
const chartProps = computed(() => {
    // Currently these are specific to BarChart, should be abstracted out to support other types
    if (!props.widget?.options) return {};
    return {
        colorShift: props.widget.options.showIncome ? 0 : 1,
    };
});

function handleClick() {
    console.log('clicked');
    // emit('click');
}

function handleRemove() {
    emit('remove');
}
</script>

<style lang="scss">
@import './DashboardWidget';
</style>
