<template>
    <div class="cash-flow-widget">
        <div class="dashboard-widget-actions" v-if="mode === 'edit'">
            <button class="dashboard-widget-drag-handle" v-if="draggable">
                <SvgIcon shape="drag" />
            </button>
            <button class="dashboard-widget-action" @click.prevent="handleRemove">
                <SvgIcon shape="trash" />
            </button>
        </div>

        <div class="dashboard-widget-title-bar">
            <h5 class="dashboard-widget-label" v-text="label ?? widget.name"></h5>
            <div class="dashboard-widget-title-legend" v-if="legendData">
                <div v-for="legendItem in legendData" class="dashboard-widget-legend-item">
                    <span class="dashboard-widget-legend-color" :style="'background-color: ' + legendItem.color"></span>
                    <span class="dashboard-widget-legend-label" v-text="legendItem.label"></span>
                </div>
            </div>
        </div>

        <div class="dashboard-widget-chart">
            <BarChart v-if="chartType === 'bar' && dataHasFetched && liveData" :data="liveData" v-bind="chartProps" />
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref } from 'vue';
import type { Ref } from 'vue';
import type { DashboardLegendData, DashboardWidgetProps } from '../DashboardWidget.vue';

import BarChart from '~/components/dashboard/BarChart.vue';
import LineChart from '~/components/dashboard/LineChart.vue';
import SvgIcon from '~/components/images/SvgIcon.vue';

export type CashFlowWidgetChartType = 'line' | 'bar';
export type CashFlowWidget = DashboardWidget & {
    type: 'cash-flow';
    options: CashFlowWidgetOptions;
};
export interface CashFlowWidgetOptions {
    chartType: CashFlowWidgetChartType;
    time: string;
    size: string;
    showIncome: boolean;
    showExpenses: boolean;
}

/** TODO: Description of this component */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'cash-flow-widget-' + componentId);

interface CashFlowWidgetProps extends Omit<DashboardWidgetProps, 'widget'> {
    widget: CashFlowWidget;
}

const props = defineProps<CashFlowWidgetProps>();

const emit = defineEmits<{
    remove: [];
}>();

const chartType = computed(() => props.widget.options?.chartType);

function handleRemove() {
    emit('remove');
}

const legendData: DashboardLegendData = computed(() => {
    const data = [];
    if (props.widget.options.showIncome) {
        data.push({
            label: 'Income',
            color: '#4C85E4',
        });
    }
    if (props.widget.options.showExpenses) {
        data.push({
            label: 'Expenses',
            color: '#C79953',
        });
        if (props.widget.options.showIncome) {
            data.push({
                label: 'Deficit',
                color: '#881244',
            });
        }
    }
    return data;
});

//   ____        _          _____    _       _     _
//  |  _ \  __ _| |_ __ _  |  ___|__| |_ ___| |__ (_)_ __   __ _
//  | | | |/ _` | __/ _` | | |_ / _ \ __/ __| '_ \| | '_ \ / _` |
//  | |_| | (_| | || (_| | |  _|  __/ || (__| | | | | | | | (_| |
//  |____/ \__,_|\__\__,_| |_|  \___|\__\___|_| |_|_|_| |_|\__, |
//                                                         |___/

onMounted(() => fetchLiveData());
const liveData = reactive({});
const dataHasFetched = ref(false);
async function fetchLiveData() {
    if (props.mode === 'edit') {
        const dummyData = dummyBarChartData;
        if (props.widget?.options?.showIncome === false) {
            dummyData.datasets.splice(0, 1);

            // Remove deficit too
            dummyData.datasets.splice(1, 1);
        } else if (props.widget?.options?.showExpenses === false) {
            dummyData.datasets.splice(1, 1);

            // Remove deficit too
            dummyData.datasets.splice(1, 1);
        }
        Object.assign(liveData, dummyData);
        dataHasFetched.value = true;
    } else {
        const queryString = buildWidgetQueryString();
        const fetch = useRequestFetch();
        fetch('/api/reports/timeseries' + queryString).then(e => {
            // Transform the returned data to what the chart needs
            const response = transformTimeSeriesForChart(e.data, props.widget);
            Object.assign(liveData, response);
            dataHasFetched.value = true;
        });
    }
}

function buildWidgetQueryString(): string {
    if (!props.widget?.options) return '';

    let query = [
        // 'startDate=2024-11-01',
        // 'endDate=2025-04-01',
    ];
    if (props.widget.options.chartType === 'bar') {
        if (props.widget.options.showIncome || props.widget.options.showExpenses) {
            if (props.widget.options.showIncome !== props.widget.options.showExpenses) {
                const types = ['assets', 'liabilities', 'income', 'expenses'];
                if (props.widget.options.showIncome) {
                    query.push(`types=${types.filter(t => t !== 'expenses').join(',')}`);
                } else {
                    query.push(`types=${types.filter(t => t !== 'income').join(',')}`);
                }
            }
        }
        if (props.widget.options.time !== '12mo') {
            // TODO
        }
        if (props.widget.options.size !== 'month') {
            // TODO
        }
    }
    return query.length ? '?' + query.join('&') : '';
}

function transformTimeSeriesForChart(data: any, widget: any) {
    console.log('transformTimeSeriesForChart', data, widget);
    if (widget.options?.chartType === 'bar') {
        const optionTime = widget.options?.time ? widget.options.time : '12mo';
        // const monthCount = Number(optionTime.replace("mo", ""));

        if (optionTime === '12mo') {
            const obj: { labels: string[]; datasets: { label: string; data: { [month: string]: number } }[] } = {
                labels: [],
                datasets: [],
            };

            let incomeValues: number[] = [];
            if (widget.options.showIncome && data.income?.length) {
                incomeValues = data.income.map(d => d.value);
                obj.datasets.push({
                    label: 'Income',
                    data: incomeValues,
                });
                obj.labels = getPastXMonths(incomeValues.length);
            }
            if (widget.options.showExpenses && data.expenses?.length) {
                const expenseValues = data.expenses.map(d => d.value);

                if (incomeValues.length) {
                    // If any month's expenses exceed the income, set the expense to the income value
                    // Then add the difference to the "deficit" dataset
                    const deficitValues: number[] = [];
                    const mappedExpenses = expenseValues.map((expense: number, index: number) => {
                        if (expense > incomeValues[index]) {
                            deficitValues.push(expense * -1 + incomeValues[index]);
                            return incomeValues[index] * -1; // Expenses should be negative
                        } else {
                            deficitValues.push(0);
                            return expense * -1; // Expenses should be negative
                        }
                    });
                    obj.datasets.push({
                        label: 'Expenses',
                        data: mappedExpenses,
                    });
                    obj.datasets.push({
                        label: 'Deficit',
                        data: deficitValues,
                    });
                } else {
                    obj.datasets.push({
                        label: 'Expenses',
                        data: expenseValues.map(d => -1 * d),
                    });
                }
            }

            if (!obj.labels.length) {
                obj.labels = getPastXMonths(13);
            }

            return obj;
        }
    }
    if (widget.options.showExpenses) {
        widget.options.showExpenses = data.expenses.value.annually > 0;
    }
    if (widget.options.showIncome && widget.options.showExpenses) {
        widget.options.showIncome = data.income.value.annually > data.expenses.value.annually;
    }
    if (widget.options.showIncome) {
        // TODO
    }

    if (widget.options?.chartType === 'line') {
        // TODO
    }
}

function getPastXMonths(count?: number) {
    const months = [];
    const currentDate = new Date();
    const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    const countVal = count ?? 12;

    // Loop to get the past 12 months
    for (let i = countVal - 1; i >= 0; i--) {
        const monthIndex = (currentDate.getMonth() - i + 12) % 12; // Ensure the index wraps around
        months.push(monthNames[monthIndex]);
    }

    return months;
}

// Any additional props to pass to the chart component
const chartProps = computed(() => {
    // Currently these are specific to BarChart, should be abstracted out to support other types
    if (!props.widget?.options) return {};
    return {
        colorShift: props.widget.options.showIncome ? 0 : 1,
    };
});

const dummyBarChartData = {
    labels: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV'],
    datasets: [
        {
            label: 'Income',
            data: [1220, 450, 2300, 1230, 3030, 4000, 2300, 3000, 3500, 2000, 1200],
        },
        {
            label: 'Expenses',
            data: [-300, -500, -800, -1520, -350, -400, -250, -100, -500, -2000, -1000],
        },
        {
            label: 'Deficit',
            data: [0, -100, 0, -120, 0, 0, 0, 0, 0, 0, 0],
        },
    ],
};

const dummyLineChartData = {
    labels: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV'],
    datasets: [
        {
            label: 'Income',
            data: {
                JAN: 122,
                FEB: 18,
                MAR: 230,
                APR: 123,
                MAY: 303,
                JUN: 400,
                JUL: 230,
                AUG: 800,
                SEP: 450,
                OCT: 200,
                NOV: 120,
            },
        },
        {
            label: 'Expenses',
            data: {
                JAN: -30,
                FEB: -50,
                MAR: -80,
                APR: -123,
                MAY: -35,
                JUN: -40,
                JUL: -25,
                AUG: 0,
                SEP: -50,
                OCT: -200,
                NOV: -120,
            },
        },
        {
            label: 'Deficit',
            data: {
                JAN: 0,
                FEB: 0,
                MAR: 0,
                APR: -50,
                MAY: 0,
                JUN: 0,
                JUL: 0,
                AUG: -80,
                SEP: 0,
                OCT: -20,
                NOV: -12,
            },
        },
    ],
};
</script>

<style lang="scss">
.cash-flow-widget {
}
</style>
