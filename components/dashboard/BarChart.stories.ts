import Bar<PERSON>hart from './BarChart.vue';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof BarChart> = {
    component: BarChart,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },
        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn(),
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { Bar<PERSON>hart },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <BarChart v-bind="args" :foo="foo">
    //             ${args.default}
    //         </BarChart>
    //     `,
    // }),

    decorators: [() => ({ template: '<div style="margin: 1.5em 3em;width:800px; height:400px"><story/></div>' })],
};

export default meta;
type Story = StoryObj<typeof BarChart>;

export const Default: Story = {
    parameters: {
        // design: {
        //     type: 'figma',
        //     url: '',
        // },
    },
    args: {
        data: {
            labels: ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV'],
            datasets: [
                {
                    label: 'Income',
                    data: {
                        JAN: 122,
                        FEB: 18,
                        MAR: 230,
                        APR: 123,
                        MAY: 303,
                        JUN: 400,
                        JUL: 230,
                        AUG: 800,
                        SEP: 450,
                        OCT: 200,
                        NOV: 120,
                    },
                },
                {
                    label: 'Expenses',
                    data: {
                        JAN: -30,
                        FEB: -50,
                        MAR: -80,
                        APR: -123,
                        MAY: -35,
                        JUN: -40,
                        JUL: -25,
                        AUG: 0,
                        SEP: -50,
                        OCT: -200,
                        NOV: -120,
                    },
                },
                {
                    label: 'Deficit',
                    data: {
                        JAN: 0,
                        FEB: 0,
                        MAR: 0,
                        APR: -50,
                        MAY: 0,
                        JUN: 0,
                        JUL: 0,
                        AUG: -80,
                        SEP: 0,
                        OCT: -20,
                        NOV: -12,
                    },
                },
            ],
        },
    },
};
