@import "/assets/css/mixins";

.add-widget-modal {

    .modal-dialog-panel {
        width: 500px;
    }

    .modal-header {
        padding: 20px;
        height: auto;
    }

    .modal-content {
        padding: 20px;
    }

    .add-widget-modal-header {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
    }

    .add-widget-modal-title {
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 18px;

        color: var(--color-white);
    }

    .add-widget-modal-body {
        width: calc(100% + 20px);
        margin-left: -10px;
    }

    .add-widget-modal-types-listing {
        padding-bottom: 10px;
    }

    .add-widget-modal-type-row {
        display: flex;
        gap: 20px;
        padding: 10px;
        border-radius: 10px;
        transition: background .2s ease-in-out;
        cursor: pointer;

        &:hover, &.add-widget-modal-type-row-filled {
            background: rgba(var(--color-blue-grey-rgb), 0.2);
            .add-widget-modal-type-icon {
                background: rgba(var(--color-blue-grey-rgb), 0);
            }
        }
    }

    .add-widget-modal-type-icon {
        border-radius: 4px;
        width: 80px;
        flex: none;

        transition: background .2s ease-in-out;
        background: rgba(var(--color-blue-grey-rgb), 0.1);
        svg, img {
            width: 100%;
            height: auto;
        }
    }

    .add-widget-modal-type-text {
        width: auto;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        flex-direction: column;
        gap: 10px;

        h5 {
            font-weight: 700;
            font-size: 14px;
            line-height: 14px;
            color: var(--color-white);
        }

        p {
            font-weight: 400;
            font-size: 14px;
            line-height: 18px;
            color: var(--color-blue-grey);

        }
    }

    .add-widget-modal-selected {
        padding: 0 10px;
    }

    .add-widget-modal-form {
        display: flex;
        flex-direction: column;
        gap: 20px;
        padding-top: 20px;
    }

    .add-widget-modal-form-group {
        display: flex;
        flex-direction: column;
        gap: 20px;
        position: relative;

        padding: 21px 0;
        &:before, &:after {
            content: "";
            display: block;
            height: 1px;
            width: calc(100% + 40px);
            left: -20px;
            position: absolute;
            background: #fff;
            opacity: 0.2;
        }

        &:before {
            top: 0;
        }

        &:after {
            bottom: 0;
        }

        &:last-child {
            padding-bottom: 20px;
            &:after {
                display: none;
            }
        }
    }

    .add-widget-modal-form-group:first-child, .add-widget-modal-form-group + .add-widget-modal-form-group {
        padding-top: 20px;
        &:before {
            display: none;
        }
    }

    .add-widget-modal-form-group + .add-widget-modal-form-group {
        margin-top: -20px;
    }

    .add-widget-modal-form-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        .add-widget-modal-form-label {
            font-weight: 700;
            font-size: 14px;
            line-height: 18px;
            color: var(--color-white);
            width: 120px;
            flex: none;
        }
        .add-widget-modal-form-field {
            flex: 1 1 auto;
            > * {
                width: 100%;
            }
        }
    }

    .add-widget-modal-toggle-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        .toggle-switch {
            flex: none;
            width: 60px;
            height: 28px;
        }
        label {
            font-weight: 700;
            font-size: 14px;
            line-height: 18px;
            color: var(--color-white);
            flex: 1 1 auto;

            /* Label */

            width: 89px;
            height: 20px;
        }
    }

    .add-widget-modal-footer {
        padding: 20px 20px 0;
        border-top: 1px solid rgba(var(--color-white-rgb), 0.2);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 8px;
        width: calc(100% + 40px);
        margin-left: -20px;
    }
}
