@import "/assets/css/mixins";

.dashboard-widget {
    display: flex;
    padding: 20px 20px 0;
    align-items: stretch;
    justify-content: flex-start;
    flex-direction: column;
    width: 100%;
    gap: 20px;
    position: relative;

    border-radius: 4px;
    background: var(--color-grey-section);

    .dashboard-widget-meta {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        gap: 15px;
        flex: none;
    }

    .dashboard-widget-label {
        color: var(--color-grey);
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 1.125em;
        letter-spacing: 1px;
        text-transform: uppercase;
    }

    .dashboard-widget-title-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row;
        gap: 10px;
    }

    .dashboard-widget-actions {
        position: absolute;
        z-index: 100;
        top:0;
        left:0;
        width: 100%;
        height: 100%;
        background: rgba(var(--color-grey-section-rgb), .8);

        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        gap: 10px;
        color: var(--color-white);
        font-size: 12px;
        font-style: normal;

        opacity: 0;
        visibility: hidden;
        transition: opacity .2s ease-in-out, visibility .2s ease-in-out;

        .dashboard-widget-drag-handle {
            cursor: grab;
        }
    }

    .dashboard-widget-chart {
        padding-top: 10px;
    }

    .dashboard-widget-title-legend {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 20px;
    }

    .dashboard-widget-legend-item {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
    }

    .dashboard-widget-legend-color {
        display: block;
        width: 10px;
        height: 10px;
        border-radius: 2px;
        flex: none;
    }

    .dashboard-widget-legend-label {
        font-weight: 700;
        font-size: 12px;
        line-height: 1.2em;

        color: var(--color-white);
    }

    &:hover {
        .dashboard-widget-actions {
            opacity: 1;
            visibility: visible;
        }
    }
}
