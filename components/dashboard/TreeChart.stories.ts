import Tree<PERSON>hart from './TreeChart.vue';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof TreeChart> = {
    component: TreeChart,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { Tree<PERSON>hart },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <TreeChart v-bind="args" :foo="foo">
    //             ${args.default}
    //         </TreeChart>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;width:800px; height:160px; display: flex; align-items: center justify-content: stretch"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof TreeChart>;

export const Default: Story = {
    args: {
        data: {
            labels: ['One', 'Two', 'Three', 'Four', 'Five', 'Six'],
            datasets: [
                {
                    label: '# of Votes',
                    data: [],
                    tree: [120, 190, 30, 50, 20, 30],
                },
            ]
        }
    }
};
