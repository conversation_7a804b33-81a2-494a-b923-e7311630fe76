import LineChart from './LineChart.vue';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import { fn } from '@storybook/test';
import { ref } from 'vue';

const meta: Meta<typeof LineChart> = {
    component: LineChart,
    tags: ['autodocs'],

    //👇 All props that we want mapped in Storybook UI
    argTypes: {
        // exampleSelectType: {
        //     control: { type: 'select' },
        //     options: ['foo', 'bar'],
        //     description: '',
        // },

        // exampleTextType: {
        //     control: {type: 'text'},
        //     description: '',
        // },
    },

    //👇 Emitted events and default args
    args: {
        onClick: fn()
    },

    //👇 Our exports that end in "Data" are not stories.
    excludeStories: /.*Data$/,

    parameters: {
        a11y: {
            config: {
                // 👇 Add any accessibility rules that should be modified or ignored
                // rules: [{ id: 'label', enabled: false }],
            },
        },
    },

    //👇 Only necessary if you need to customize the render template (e.g. slots or v-model)
    // render: (args) => ({
    //     components: { LineChart },
    //     setup() {
    //         const foo = ref('bar');
    //         return { args, foo };
    //     },
    //     template: `
    //         <LineChart v-bind="args" :foo="foo">
    //             ${args.default}
    //         </LineChart>
    //     `,
    // }),

    // 👇 Include a 3rem padding around the component
    decorators: [() => ({ template: '<div style="margin: 3em;width:800px; height:160px; display: flex; align-items: center justify-content: stretch"><story/></div>' })]
};

export default meta;
type Story = StoryObj<typeof LineChart>;

export const Default: Story = {
    args: {
        data: {
            labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
            datasets: [
                {
                    label: '# of Votes',
                    data: [120, 190, 30, 50, 20, 30],
                    // borderWidth: 1
                },
            ]
        }
    }
};

export const WithPoints: Story = {
    args: {
        showPoints: true,
        yLabelFormat: 'currency',
        data: {
            labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
            datasets: [
                {
                    label: '# of Votes',
                    data: [1200000, 1900000, 300000, 500000, 200000, 300000],
                    // borderWidth: 1
                }
            ]
        }
    }
};


export const MultiLine: Story = {
    args: {
        curved: true,
        min: 0,
        max: 100,
        yLabelFormat: 'percent',
        data: {
            labels: ['40K', '50K', '60K', '70K', '80K', '90K', '100K'],
            datasets: [
                {
                    label: 'Monthly Income',
                    data: [45, 38, 58, 77, 81, 72, 61],
                    // borderWidth: 1
                },
                {
                    label: '# of Votes',
                    data: [50, 60, 62, 36, 48, 66, 40],
                    // borderWidth: 1
                },
            ]
        }
    }
};
