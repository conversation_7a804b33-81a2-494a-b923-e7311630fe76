<template>
    <div class="bar-chart">
        <canvas :id="elementId"></canvas>
        <div class="bar-chart-values">
            <div
                v-for="(label, index) in data.labels"
                :key="index"
                class="bar-chart-value"
                v-html="getDisplayExpenseHtml(index)"
            ></div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, onMounted, nextTick } from 'vue';

import Chart, { type ChartConfiguration, type ChartData, type DefaultDataPoint } from 'chart.js/auto';
import 'chartjs-adapter-date-fns';
import type { LegendOptions } from 'chart.js';
import { colors, getAxisFormatter } from './chartjs';
import type { AxisFormat } from './chartjs';
import { Ticks } from 'chart.js';

/** A bar chart component that's a wrapper around a ChartJS chart */
defineOptions();

// Generate a unique ID for this instance
const componentId = typeof useId === 'function' ? useId() : getCurrentInstance()?.uid;
const elementId = computed(() => 'bar-chart-' + componentId);

export interface BarChartProps {
    data: ChartData<'bar'>;

    // Whether to show the legend, with optional configuration
    legend?: boolean | LegendOptions<'bar'>;

    // Optional configuration for the x-axis
    xAxis?: boolean | string;

    xLabelFormat?: AxisFormat | null;

    // Optional configuration for the y-axis
    yAxis?: boolean | string;

    yLabelFormat?: AxisFormat | null;

    min?: number;

    max?: number;

    colorShift?: number;
}
const props = withDefaults(defineProps<BarChartProps>(), {
    legend: false,
    showPoints: false,
    xAxis: true,
    yAxis: true,
});

const ctx = ref();
onMounted(async () => {
    ctx.value = document.getElementById(elementId.value);
    if (ctx.value instanceof HTMLCanvasElement) {
        await nextTick();
        // new Chart(ctx, chartProps.value)
        Chart.defaults.font.family = "'Oxygen', sans-serif";
        Chart.defaults.font.weight = 900;
        new Chart(ctx.value, chartProps.value);
    }
});

const gradients = computed(() => {
    const gradients = [];

    // Blue
    const gradientOne = ctx.value.getContext('2d').createLinearGradient(0, 100, 0, 200); // Adjust the coordinates as needed
    gradientOne.addColorStop(0, '#4C85E3'); // Start color
    gradientOne.addColorStop(1, 'rgba(76, 133, 227, 0.5)'); // End color
    gradients.push(gradientOne);

    // Yellow
    const gradientTwo = ctx.value.getContext('2d').createLinearGradient(0, 100, 0, 200); // Adjust the coordinates as needed
    gradientTwo.addColorStop(0, 'rgba(211, 161, 85, 0.5)'); // Start color
    gradientTwo.addColorStop(1, '#D3A155'); // End color
    gradients.push(gradientTwo);

    // Red
    const gradientThree = ctx.value.getContext('2d').createLinearGradient(0, 50, 0, 120); // Adjust the coordinates as needed
    gradientThree.addColorStop(0, 'rgba(128, 30, 59, 0.2)'); // Start color
    gradientThree.addColorStop(1, '#881244'); // End color
    gradients.push(gradientThree);

    for (let i = 0; i < props.colorShift ?? 0; i++) {
        gradients.shift();
    }

    return gradients;
});

const mappedDatasets = computed(() =>
    props.data.datasets?.map((dataset, index) => {
        const color = gradients.value[index % gradients.value.length];
        return {
            backgroundColor: color,
            barPercentage: 0.9,
            categoryPercentage: 1,
            ...dataset,
        };
    }),
);

const xAxisCallback = computed(() => {
    return props.xLabelFormat ? { callback: getAxisFormatter(props.xLabelFormat) } : {};
});

const yAxisCallback = computed(() => {
    return props.yLabelFormat ? { callback: getAxisFormatter(props.yLabelFormat) } : {};
});

const chartProps = computed<ChartConfiguration<'bar', DefaultDataPoint<'bar'>>>(() => {
    return {
        type: 'bar',
        data: {
            labels: props.data.labels,
            datasets: mappedDatasets.value,
        },
        options: {
            plugins: {
                legend: typeof props.legend === 'boolean' ? { display: props.legend } : props.legend,
                // datalabels: {
                //     color: 'white',
                //     align: 'end',
                //     anchor: 'center',
                //     display: 'auto',
                //     formatter: getAxisFormatter(props.yLabelFormat)
                // },
                tooltip: {
                    // displayColors: false,
                    // titleFont: {size: 12},
                    // callbacks: {
                    //     label: function(context) {
                    //         console.log('TOOLTIP', context, props)
                    //         return 'yep'
                    //         const formatter = getAxisFormatter(props.yLabelFormat);
                    //         const value = context.parsed.y;
                    //         if (formatter && value !== null) {
                    //             return formatter(value);
                    //         }
                    //         return null;
                    //     }
                    // }
                },
            },
            layout: {
                padding: 0,
            },
            responsive: true,
            maintainAspectRatio: false,

            scales: {
                x: {
                    //type: 'time',
                    // type: props.xLabelFormat === 'date' ? 'time' : 'linear',
                    grid: {
                        display: false,
                    },
                    border: {
                        display: false,
                    },
                    ticks: {
                        color: '#97ABCC',
                        // display: !!props.xAxis,
                        // padding: 10,
                        // ...xAxisCallback.value,
                    },
                    // time: {
                    //     minUnit: 'day',
                    //     tooltipFormat: 'MMM d, yyyy',
                    //     displayFormats: {
                    //         day: 'MMM d yy',
                    //         week: "'Wk' w yy",
                    //         month: 'MMM yyyy',
                    //         quarter: 'QQQ yy',
                    //         year: 'yyyy'
                    //     }
                    // },
                    stacked: true,
                },
                y: {
                    type: props.yLabelFormat === 'date' ? 'time' : 'linear',
                    // suggestedMin: props.min,
                    // suggestedMax: props.max,
                    grid: {
                        display: false,
                    },
                    border: {
                        display: false,
                    },

                    // ticks: {
                    //     display: !!props.yAxis,
                    //     ...yAxisCallback.value,
                    // },
                    ticks: {
                        display: false,
                    },

                    // time: {
                    //     minUnit: 'day',
                    //     tooltipFormat: 'MMM d, yyyy',
                    //     displayFormats: {
                    //         day: 'MMM d yy',
                    //         week: "'Wk' w yy",
                    //         month: 'MMM yy',
                    //         quarter: 'QQQ yy',
                    //         year: 'yyyy'
                    //     }
                    // },
                    stacked: true,
                },
            },
        },
    };
});

// Add function to calculate total for each month

const getNetIncomeValue = (month: string | number | unknown) => {
    if (typeof month !== 'string' && typeof month !== 'number') return 0;

    const incomeDataset = props.data.datasets.find(dataset => dataset.label === 'Income');
    const expenseDataset = props.data.datasets.find(dataset => dataset.label === 'Expenses');

    const monthIncome: number = incomeDataset?.data[month as keyof typeof incomeDataset.data] ?? 0;
    const monthExpense: number = expenseDataset?.data[month as keyof typeof expenseDataset.data] ?? 0;

    // monthExpense values are already negative
    const value = monthIncome + monthExpense;

    return typeof value === 'number' ? Math.round(value) : 0;
};

const getDisplayExpenseHtml = (month: string | number | unknown) => {
    const value = getNetIncomeValue(month);

    return value < 0
        ? '<span class="bar-chart-label-red">($' + Math.round(value * -1).toLocaleString() + ')</span>'
        : '$' + Math.round(value).toLocaleString();
};
</script>

<style lang="scss">
.bar-chart {
    flex: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: stretch;
    position: relative;
    canvas {
        max-width: calc(100% + 8px);
        max-height: 150px;
        margin-left: -8px;
    }
}

.bar-chart-values {
    height: 45px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
}

.bar-chart-value {
    color: var(--primary-white, #e9edf1);
    font-size: 12px;
    font-family: 'Oxygen', sans-serif;
    font-weight: 700;
    line-height: 20px;
    word-wrap: break-word;
    text-align: center;
    flex: 1;
    .bar-chart-label-red {
        color: var(--color-red);
    }
}
</style>
