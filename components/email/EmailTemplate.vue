<!--suppress HtmlUnknownTag, UnknownAttribute -->
<template>
    <mjml>
        <mj-head>
            <mj-title>{{ title }}</mj-title>
            <mj-preview><slot name="header"/></mj-preview>

            <mj-font name="Oxygen" href="https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap"/>
            <mj-attributes>
                <mj-class name="top-section" background-color="#e9edf1" border-radius="8px 8px 0 0" padding-bottom="0" />
                <mj-class name="bottom-section" background-color="#d6dce4" border-radius="0 0 8px 8px" />
                <mj-class name="heading" font-size="24px" padding="20px 25px"/>
                <mj-class name="body" font-size="15px"/>
                <mj-class name="footer" font-size="13px" color="#57719c" align="center" padding="0px 25px" />
                <mj-button background-color="#055EFA" color="#E9EDF1" border-radius="4px" font-size="14px" font-weight="bold" />
                <mj-all font-family="Oxygen" line-height="22px" font-size="15px" color="#57719C"/>
            </mj-attributes>
            <!--suppress CssUnusedSymbol -->
            <mj-style inline="inline">
                p {
                    margin-bottom: 25px;
                }
                .bold {
                    font-weight: bold;
                }
                a.footer {
                    text-decoration:none;
                    color:#57719c;
                }
                ul.list {
                    list-style-type: none;
                    padding-left: 0;
                }
                .copy {
                    line-height: 15px;
                    vertical-align: 0px;
                    font-size:12px;
                }
            </mj-style>
        </mj-head>
        <mj-body background-color="#0D182A">
            <mj-wrapper background-color="#0D182A" full-width="full-width">
                <mj-section mj-class="top-section">
                    <mj-column>
                        <mj-text mj-class="heading"><slot name="header"/></mj-text>
                        <slot name="body" />
                    </mj-column>
                </mj-section>
                <mj-section mj-class="bottom-section">
                    <mj-column>
                        <mj-social icon-size="30px" mode="horizontal" css-class="social">
                            <mj-social-element name="facebook" href="#" background-color="#d6dce4" src="/images/logos/fb.png" />
                            <mj-social-element name="google" href="#" background-color="#d6dce4" src="/images/logos/google.png"></mj-social-element>
                            <mj-social-element name="linkedin" href="#" background-color="#d6dce4" src="/images/logos/in.png"></mj-social-element>
                            <mj-social-element name="twitter" href="#" background-color="#d6dce4" src="/images/logos/tw.png"></mj-social-element>
                        </mj-social>
                        <mj-text mj-class="footer">
                            <strong>Address:</strong> Your Street Name, 123, Area Name, Your City Name, Country Name.
                        </mj-text>
                        <mj-text mj-class="footer">
                            <strong>Call: </strong><a href="tel:123456789" class="footer">(123) 456 789</a> |
                            <strong>Email: </strong><a href="mailto:<EMAIL>" class="footer"><EMAIL></a>
                        </mj-text>
                    </mj-column>
                </mj-section>
                <mj-section padding="10px 25px">
                    <mj-column>
                        <mj-image width="80px" src="/images/logo.png" />
                        <mj-text mj-class="footer">
                            Copyright <span class="copy">&copy;</span>
                            2025 FinPro. All Rights Reserved.
                        </mj-text>
                    </mj-column>
                </mj-section>
            </mj-wrapper>
        </mj-body>
    </mjml>    
</template>
<script setup lang="ts">

export interface EmailTemplateProps {
    title: string;
}

defineProps<EmailTemplateProps>();

</script>