// noinspection JSUnusedGlobalSymbols

import type { TBigIconShapeKey } from '~/components/images/BigIconShapes';
import type { IconSelectorProps } from '~/components/elements/IconSelector.vue';
import type { SituationCardProps, SituationCardRow } from '~/components/situations/SituationCard.vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import type { SituationInputGroupProps } from '~/components/situations/SituationInputGroup.vue';
import {
    allAssetTypes,
    allExpenseTypes,
    allIncomeTypes,
    allInsuranceTypes,
    allLiabilityTypes,
    primaryResidenceAssetData,
    primaryResidenceExpenseData,
    type SituationType,
    transformSituationTypeForCard,
    transformSituationTypesForCard,
} from '~/components/situations/data/intake-data';
import { prepend, reduceSituationFieldsToTotal } from '~/utils';
import type { IntakeData } from '~/types/intake';
import { nanoid } from 'nanoid';
import { defineStore } from 'pinia';
import { useRequestFetch } from '#app';
import type { BaseLineItem, LineItem, LineItemKey, UnionLineItem } from '~/types/base';
import type { TSvgIconShapeKey } from '~/components/images/SvgIconShapes';

export type IntakeSituationKind = 'assets' | 'liabilities' | 'income' | 'expenses' | 'insurance';
export type IntakeSituationCategory = LineItem['category'];

function getSituationCategoryFromKind(kind: IntakeSituationKind): IntakeSituationCategory {
    switch (kind) {
        case 'assets':
            return 'Asset';
        case 'liabilities':
            return 'Liability';
        case 'income':
            return 'Income';
        case 'expenses':
            return 'Expense';
        case 'insurance':
            return 'Insurance';
    }
}

export interface IntakeStandardItem {
    id: number | string;
    isComplete?: boolean;
    situationName?: string;
    kind?: string;
    // The unique kind identifier that the backend expects
    itemsKind?: string;

    unit?: string;
    fields: SituationInputGroupProps[];
    associatedAssets?: Array<number | string>;
    associatedLiabilities?: Array<number | string>;
    associatedIncome?: Array<number | string>;
    associatedExpenses?: Array<number | string>;
    associatedInsurance?: Array<number | string>;
}

export interface IntakeStandardProperty {
    [id: number | string]: IntakeStandardItem;
}

export interface IntakeSituationItem {
    id?: string;
    cardMode?: SituationCardProps['mode'];
    footerRow?: SituationCardRow;
    iconShape?: TBigIconShapeKey;
    entryMode?: SituationEntryMode | null;
    situationGroup?: IntakeSituationKind;
    kind: string;
    name?: string;
    unit?: string;

    // Asset, Liability, etc
    category: IntakeSituationCategory;

    // The unique kind identifier that the backend expects
    itemsKind: string;

    totalSteps: number;
    completedSteps: number;
    simple: SituationInputGroupProps[];
    // standard: {id: number|string; fields: SituationInputGroupProps[]}[];
    standard: IntakeStandardProperty;
    template: SituationInputGroupProps[];
}

// export interface IntakeStore {
//     totals: {
//         assets: number;
//         liabilities: number;
//         netWorth: number;
//         netIncome: number;
//     },

//     progress: {
//         steps: {
//             iconShape?: string;
//             label: string;
//             title?: string;
//             name: string;
//             completion: number;
//         }[],
//         activeStep: string;
//         completion: number;
//     },

//     data: IntakeData,

//     situations: (IconSelectorProps & { id: number, value: string,  selected: boolean })[];

//     incomeTypes: (IconSelectorProps & { id?: number, value: string,  selected: boolean })[];

//     // assets: IntakeSituationItem[],
//     // incomeSources: IntakeSituationItem[],
//     assets: IntakeSituationItem[],
//     liabilities: IntakeSituationItem[],
//     expenses: IntakeSituationItem[],
//     income: IntakeSituationItem[],
//     insurance: IntakeSituationItem[],
//     owners: IntakeSituationItem[],
//     intakeCompleted?: boolean;

//     defaultMode: SituationEntryMode,

//     // TEMP: Flag whether the user has added asset situations
//     assetSituationsAdded: boolean,

//     // TEMP: A flag to track if they have opened a situation modal, and thus shouldn't see the onboarding state
//     showEntryModeIntro: boolean
//     // authUser?: UserData
// }

export type SituationRelationship = { kind: IntakeSituationKind; id: IntakeStandardItem['id'] };

// A callback to pass into a reduce function to sum up the values of a list of fields
function reduceSituationFields(acc: number, field: SituationInputGroupProps) {
    return reduceSituationFieldsToTotal(acc, field);
}
function updateSituationStatus(situationItem: IntakeSituationItem) {
    if (situationItem.entryMode === 'standard') {
        let completedSteps = 0;

        for (let itemKey in situationItem.standard) {
            const assetItem = situationItem.standard[itemKey];
            // Loop through each item in standard entry and check if it's complete
            const isNotComplete = assetItem.fields.some(field => field.required && !field.value && field.value !== 0);

            assetItem.isComplete = !isNotComplete;
            completedSteps = completedSteps + (isNotComplete ? 0 : 1);
        }
        situationItem.completedSteps = completedSteps;
        situationItem.totalSteps = Math.max(Object.keys(situationItem.standard).length, completedSteps);
        situationItem.cardMode = completedSteps === situationItem.totalSteps ? 'complete' : 'pending';
    } else {
        if (situationItem.simple.every(e => !e.required || ![null, undefined].includes(e.value))) {
            situationItem.completedSteps = 1;
            situationItem.cardMode = 'complete';
        } else {
            situationItem.completedSteps = 0;
            situationItem.cardMode = 'pending';
        }
    }
}

export const useIntakeStore = defineStore('intake', function () {
    /////////////////////////
    // █▀▀ ▀█▀ ▄▀▄ ▀█▀ █▀▀
    // ▄██  █  █▀█  █  ██▄
    //
    const state = {
        data: ref<IntakeData>({
            name: {
                firstName: '',
                middleName: '',
                lastName: '',
            },
            spouse: {},
            dependents: [],
            portfolio: {
                owners: [],
                assets: [],
                expenses: [],
                income: [],
                insurance: [],
                liabilities: [],
            },
            intakeCompleted: false,
            home: {},
            situations: [],
            incomeTypes: [],
        }),
        assets: ref<IntakeSituationItem[]>([]),
        liabilities: ref<IntakeSituationItem[]>([]),
        expenses: ref<IntakeSituationItem[]>([]),
        income: ref<IntakeSituationItem[]>([]),
        insurance: ref<IntakeSituationItem[]>([]),
        owners: ref<IntakeSituationItem[]>([]),

        situations: ref<(IconSelectorProps & { id: number; value: string; selected: boolean })[]>([
            {
                id: 2,
                iconShape: 'men-and-women',
                label: 'Married',
                value: 'married',
                selected: false,
            },
            {
                id: 3,
                iconShape: 'document-things',
                label: 'Kids',
                value: 'kids',
                selected: false,
            },
            {
                id: 4,
                iconShape: 'military',
                label: 'Military',
                value: 'military',
                selected: false,
            },
            {
                id: 5,
                iconShape: 'education',
                label: 'Student',
                value: 'student',
                selected: false,
            },
            {
                id: 6,
                iconShape: 'money-3',
                label: 'Investments',
                value: 'investments',
                selected: false,
            },
            {
                id: 7,
                iconShape: 'business',
                label: 'Business Owner',
                value: 'business-owner',
                selected: false,
            },
            {
                id: 8,
                iconShape: 'vacation-house',
                label: 'Retired',
                value: 'retired',
                selected: false,
            },
            {
                id: 9,
                iconShape: 'bank',
                label: 'Government',
                value: 'government',
                selected: false,
            },
        ]),
        incomeTypes: ref<(IconSelectorProps & { id?: number; value: string; selected: boolean })[]>([]),

        defaultMode: ref<SituationEntryMode>('simple'),
        assetSituationsAdded: ref(false),
        showEntryModeIntro: ref(true),
    };

    const totals = computed(() => {
        function computeTotals(items: MaybeRef<IntakeSituationItem[]>) {
            const fields = unref(items).flatMap(item => {
                if (item.entryMode === 'standard') {
                    return Object.values(item.standard).flatMap(si => si.fields);
                } else {
                    return item.simple;
                }
            });
            return {
                income: fields.filter(field => field.affects === 'income').reduce(reduceSituationFields, 0),
                expenses: fields
                    .filter(field => field.affects === 'expenses' && !field.entangled)
                    .reduce(reduceSituationFields, 0),
                liabilities: fields.filter(field => field.affects === 'liabilities').reduce(reduceSituationFields, 0),
                assets: fields.filter(field => field.affects === 'assets').reduce(reduceSituationFields, 0),
            };
        }

        const totalsList = [
            computeTotals(state.assets),
            computeTotals(state.liabilities),
            computeTotals(state.income),
            computeTotals(state.expenses),
            computeTotals(state.insurance),
        ];

        const initialTotals =
            !state.assets.value.some(asset => asset.name === 'primary-residence') &&
            state.data.value.home.type === 'own'
                ? {
                      assets: state.data.value.home.own?.estimatedValue ?? 0,
                      liabilities: state.data.value.home.own?.owed ?? 0,
                      income: 0,
                      expenses: 0,
                  }
                : {
                      assets: 0,
                      liabilities: 0,
                      income: 0,
                      expenses: 0,
                  };

        const allTotals = totalsList.reduce((acc, item) => {
            acc.assets += item.assets;
            acc.liabilities += item.liabilities;
            acc.income += item.income;
            acc.expenses += item.expenses;
            return acc;
        }, initialTotals);

        return {
            ...allTotals,
            netWorth: allTotals.assets - allTotals.liabilities,
            netIncome: allTotals.income - allTotals.expenses,
        };
    });

    const progress = (function () {
        const steps = ref([
            {
                label: 'Personal Info',
                name: 'personal',
                iconShape: 'info',
                completion: computed(() => {
                    const filled =
                        (state.data.value.name?.firstName ? 1 : 0) +
                        (state.data.value.name?.lastName ? 1 : 0) +
                        (state.data.value.birthdate ? 1 : 0) +
                        (state.data.value.maritalStatus ? 1 : 0);
                    return (filled / 4) * 100;
                }),
            },
            {
                label: 'Situation',
                name: 'situation',
                iconShape: 'grid-small',
                completion: computed(() => (state.situations.value.some(situation => situation.selected) ? 0 : 100)),
            },
            { label: 'Home', name: 'home', completion: computed(() => (state.data.value.home?.type ? 0 : 100)) },
            { label: 'Assets', name: 'assets', completion: computed(() => getCompletion(state.assets)) },
            { label: 'Liabilities', name: 'liabilities', completion: computed(() => getCompletion(state.liabilities)) },
            { label: 'Insurance', name: 'insurance', completion: computed(() => getCompletion(state.insurance)) },
            { label: 'Income', name: 'income', completion: computed(() => getCompletion(state.income)) },
            { label: 'Expenses', name: 'expenses', completion: computed(() => getCompletion(state.expenses)) },
            { label: 'Review', name: 'review', completion: 0 },
        ]);
        return ref({
            steps: steps,
            // stepsComp: stepsComp,
            activeStep: 'personal',
            completion: computed(() => {
                return Math.round(steps.value.reduce((acc, step) => acc + step.completion, 0) / steps.value.length);
            }),
        });
        function getCompletion(items: MaybeRef<IntakeSituationItem[]>) {
            const arr = unref(items);
            if (!arr.length) return 0;
            const completeCount = arr.filter(item => (item.totalSteps ?? 1) <= (item.completedSteps ?? 0)).length;
            return Math.round((completeCount / arr.length) * 100);
        }
    })();

    // Interum / hacky fix for intake nav progression
    const getCompletionComp = (items: MaybeRef<IntakeSituationItem[]>) => {
        const arr = unref(items);
        if (!arr.length) return 0;
        const completeCount = arr.filter(item => (item.totalSteps ?? 1) <= (item.completedSteps ?? 0)).length;
        return Math.round((completeCount / arr.length) * 100);
    };

    const stepsComp = computed(() => {
        return [
            {
                label: 'Personal Info',
                name: 'personal',
                iconShape: 'info',
                completion:
                    (((state.data.value.name?.firstName ? 1 : 0) +
                        (state.data.value.name?.lastName ? 1 : 0) +
                        (state.data.value.birthdate ? 1 : 0) +
                        (state.data.value.maritalStatus ? 1 : 0)) /
                        4) *
                    100,
            },
            {
                label: 'Situation',
                name: 'situation',
                iconShape: 'grid-small',
                completion: state.situations.value.some(situation => situation.selected) ? 100 : 0,
            },
            { label: 'Home', name: 'home', completion: state.data.value.home?.type ? 100 : 0 },
            { label: 'Assets', name: 'assets', completion: getCompletionComp(state.assets) },
            { label: 'Liabilities', name: 'liabilities', completion: getCompletionComp(state.liabilities) },
            { label: 'Insurance', name: 'insurance', completion: getCompletionComp(state.insurance) },
            { label: 'Income', name: 'income', completion: getCompletionComp(state.income) },
            { label: 'Expenses', name: 'expenses', completion: getCompletionComp(state.expenses) },
            { label: 'Review', name: 'review', completion: 0 },
        ];
    });

    const completionComp = computed(() =>
        Math.round(stepsComp.value.reduce((acc, step) => acc + step.completion, 0) / (stepsComp.value.length ?? 1)),
    );
    // /End interum / hacky fix for intake nav progression

    //////////////////////////////////
    // █▀▀ █▀▀ ▀█▀ ▀█▀ █▀▀ █▀█ █▀▀
    // █▄█ ██▄  █   █  ██▄ █▀▄ ▄██
    //
    const getters = (function () {
        function getStandardProperty(situationItems: IntakeSituationItem[] | Ref<IntakeSituationItem[]>) {
            const standardItems = unref(situationItems).filter(asset => asset.entryMode === 'standard');
            return standardItems.reduce((acc, standardItem) => {
                const standardItems = Object.values(standardItem.standard).reduce((acc, item) => {
                    acc[item.id] = {
                        kind: standardItem.kind,
                        itemsKind: standardItem.itemsKind,
                        unit: standardItem.unit,
                        ...item,
                    };
                    return acc;
                }, {} as IntakeStandardProperty);

                return {
                    ...acc,
                    ...standardItems,
                };
            }, {} as IntakeStandardProperty);
        }

        const standardAssets = computed(() => getStandardProperty(state.assets));
        const standardLiabilities = computed(() => getStandardProperty(state.liabilities));
        const standardExpenses = computed(() => getStandardProperty(state.expenses));
        const standardIncome = computed(() => getStandardProperty(state.income));
        const standardInsurance = computed(() => getStandardProperty(state.insurance));

        return {
            getSituationsByKind: computed(() => (kind: IntakeSituationKind) => {
                return state[kind].value;
            }),
            nextStep: computed(() => {
                const currentStepIndex = progress.value.steps.findIndex(
                    step => step.name === progress.value.activeStep,
                );
                if (currentStepIndex >= progress.value.steps.length - 1) return;
                return progress.value.steps[currentStepIndex + 1]?.name;
            }),
            prevStep: computed(() => {
                const currentStepIndex = progress.value.steps.findIndex(
                    step => step.name === progress.value.activeStep,
                );
                if (currentStepIndex <= 0) return;
                return (progress.value.activeStep = progress.value.steps[currentStepIndex - 1]?.name);
            }),
            standardAssets: standardAssets,
            standardLiabilities: standardLiabilities,
            standardExpenses: standardExpenses,
            standardIncome: standardIncome,
            standardInsurance: standardInsurance,
            getIntakeCompleted: computed(() => {
                return state.data.value?.intakeCompleted ?? false;
            }),
            standardItems: computed<IntakeStandardProperty>(() => {
                return {
                    ...standardAssets.value,
                    ...standardLiabilities.value,
                    ...standardExpenses.value,
                    ...standardIncome.value,
                    ...standardInsurance.value,
                };
            }),
        };
    })();

    ////////////////////////////////////////
    // ▄▀▄ █▀▀ ▀█▀ ▀█▀ █▀█ █▄ █ █▀▀
    // █▀█ █▄▄  █  ▄█▄ █▄█ █ ▀█ ▄██
    //
    const apiActions = (function () {
        return {
            async fetch() {
                try {
                    const fetch = useRequestFetch();
                    const data = await fetch<IntakeData>('/api/intake');

                    console.log('fetch', data);

                    if (data) {
                        data.intakeCompleted = !!data.intakeCompleted;
                        data.name = data.name ?? {
                            firstName: '',
                            middleName: '',
                            lastName: '',
                        };
                        data.home = data.home ?? {};
                        data.situations = data.situations ?? [];
                        data.incomeTypes = data.incomeTypes ?? [];
                        data.spouse = data.spouse ?? {};
                        data.dependents = data.dependents ?? [];

                        state.data.value = data;
                        //state.data.value = data;
                        resetDataFromApi(data);

                        // console.log(JSON.stringify({
                        //     assets: state.assets.value,
                        //     liabilities: state.liabilities.value,
                        //     income: state.income.value,
                        //     expenses: state.expenses.value,
                        //     insurance: state.insurance.value
                        // }, null, 2));
                    }
                    return state.data.value;
                } catch (e) {
                    console.log('Error in intakeStore.fetch()');
                    console.log(`${e}`);
                    return {};
                }
            },
            async update() {
                const toLog = {
                    assets: state.assets.value,
                    liabilities: state.liabilities.value,
                    expenses: state.expenses.value,
                    income: state.income.value,
                    insurance: state.insurance.value,
                };
                // console.log('update', JSON.stringify(toLog, null, 2));
                const data = getApiTransformedData();
                console.log('update data', data);
                try {
                    const fetch = useRequestFetch();
                    await fetch<IntakeData>('/api/intake', {
                        method: 'POST',
                        body: data,
                    });
                    // if (result) {
                    //     this.resetDataFromApi(result);
                    // }
                } catch (e) {
                    console.log('Error in intakeStore.update()');
                    console.log(`${e}`);
                }
            },
        };

        function resetDataFromApi(intakeData: IntakeData) {
            const { portfolio } = intakeData;
            if (!portfolio) return;

            const portfolioKinds: IntakeSituationKind[] = ['assets', 'liabilities', 'income', 'expenses', 'insurance'];
            portfolioKinds.forEach(portfolioKind => {
                const kindGroup = portfolio[portfolioKind] ?? null;
                if (kindGroup?.length) {
                    // Each kind group is an array of either standard or simple items
                    // We need to transform these into the IntakeSituationItem format

                    let allFieldsOfKind: SituationType[] = [];
                    switch (portfolioKind) {
                        case 'assets':
                            // The Primary residence (AssetHome) asset is a standalone object in intake-data.ts
                            allFieldsOfKind = [...allAssetTypes, primaryResidenceAssetData];
                            break;
                        case 'liabilities':
                            allFieldsOfKind = allLiabilityTypes;
                            break;
                        case 'income':
                            allFieldsOfKind = allIncomeTypes;
                            break;
                        case 'expenses':
                            // The Primary residence (ExpenseHousing) expense is a standalone object in intake-data.ts
                            allFieldsOfKind = [...allExpenseTypes, primaryResidenceExpenseData];
                            break;
                        case 'insurance':
                            allFieldsOfKind = allInsuranceTypes;
                            break;
                    }

                    kindGroup.forEach(item => {
                        const intakeKindShape = allFieldsOfKind.find(kind => kind.kind === item.kind);
                        if (!intakeKindShape) return;

                        let situation = state[portfolioKind].value.find(e => e.itemsKind === item.kind);

                        if (!situation) {
                            situation = {
                                id: item.id,
                                // TODO: Make this dynamic
                                cardMode: 'pending',
                                totalSteps: 1,
                                completedSteps: 0,
                                situationGroup: portfolioKind,
                                kind: intakeKindShape.label,
                                name: intakeKindShape.name,
                                unit: intakeKindShape.unit ?? undefined,

                                itemsKind: intakeKindShape.kind,
                                category: getSituationCategoryFromKind(portfolioKind),

                                iconShape: intakeKindShape.iconShape,
                                standard: {},
                                simple: intakeKindShape.fields.simple.map(e => ({ ...e })),
                                template: intakeKindShape.fields.standard.map(e => ({
                                    value: e.defaultValue ?? null,
                                    ...e,
                                })),
                                footerRow: { label: 'Onboard Status', percentage: 0 },
                                entryMode: item.mode === 'Standard' ? 'standard' : 'simple',
                                // ...overwrites
                            };

                            state[portfolioKind].value.push(situation);
                        }

                        if (item.mode === 'Standard') {
                            situation.standard[item.id] = {
                                id: item.id,
                                isComplete: false,
                                fields: intakeKindShape.fields.standard.map(e => situationMapper(e, item)),
                            };
                            if (item.entangledWith) {
                                situation.standard[item.id].entangledWith = item.entangledWith;
                            }
                        } else {
                            // Simple
                            situation.simple = intakeKindShape.fields.simple.map(e => situationMapper(e, item));
                        }

                        updateSituationStatus(situation);
                    });
                }
            });

            if (state.assets.value.length) {
                state.assetSituationsAdded.value = true;
            }
        }

        function situationMapper(e: SituationInputGroupProps, item: UnionLineItem) {
            const key = e.name as LineItemKey;
            if (e.type === 'currency-frequency' || e.type === 'number-frequency') {
                const defaultFrequency =
                    item.category === 'Income' || item.category === 'Expense' ? item.frequency : 'Monthly';
                return {
                    ...e,
                    value: {
                        number: item[key] ?? null,
                        frequency: item[e.frequencyName ?? 'paymentFrequency'] ?? defaultFrequency,
                    },
                };
            } else {
                return { ...e, value: item[key] ?? null };
            }
        }

        function getApiTransformedData() {
            return {
                ...state.data.value,
                portfolio: {
                    owners: [],
                    assets: transformKindGroupToItems('assets'),
                    liabilities: transformKindGroupToItems('liabilities'),
                    expenses: transformKindGroupToItems('expenses'),
                    income: transformKindGroupToItems('income'),
                    insurance: transformKindGroupToItems('insurance'),
                },
            };
        }
        function transformKindGroupToItems(kind: IntakeSituationKind) {
            const group = state[kind].value;
            if (!group) return [];

            return group.flatMap(item => {
                if (item.entryMode === 'standard') {
                    if (kind === 'expenses') console.log('expense item', item);
                    return Object.values(item.standard).map(standardItem =>
                        transformStandardItemToApi(standardItem, kind, item.itemsKind),
                    );
                } else {
                    // return item.simple
                    return transformSimpleItemToApi(item, kind);
                }
            });
        }
        function transformStandardItemToApi(
            item: IntakeStandardItem,
            itemKind: IntakeSituationKind,
            itemsKind: string,
        ) {
            const result: BaseLineItem = {
                id: `${item.id ?? nanoid()}`,
                mode: 'Standard',
                category: getSituationCategoryFromKind(itemKind),
                description: item.situationName ?? '',
                kind: itemsKind,
                ...item.fields.reduce(standardFieldToApiReducer, {}),
            };

            if (item.entangledWith) {
                result.entangledWith = item.entangledWith;
            }

            return result as LineItem;
        }
        function transformSimpleItemToApi(item: IntakeSituationItem, itemKind: IntakeSituationKind) {
            const result: BaseLineItem = {
                id: item.id ?? nanoid(),
                mode: 'Simple',
                category: getSituationCategoryFromKind(itemKind),
                description: item.name ?? '',
                kind: item.itemsKind,
                ...item.simple.reduce(standardFieldToApiReducer, {}),
            };

            return result as LineItem;
        }
        function standardFieldToApiReducer(acc: Partial<LineItem>, field: SituationInputGroupProps) {
            if (field.type === 'currency-frequency' || field.type === 'number-frequency') {
                const frequencyFieldName = field.frequencyName ?? 'paymentFrequency';
                return {
                    ...acc,
                    [field.name]: field.value.number,
                    [frequencyFieldName]: field.value.frequency,
                } as LineItem;
            } else {
                return {
                    ...acc,
                    [field.name]: field.value,
                } as LineItem;
            }
        }
    })();

    const cardActions = (function () {
        function populateExpenseSituationCard(
            situation: ReturnType<typeof transformSituationTypeForCard>,
            subOptions: {
                name: string;
                description?: string;
                iconShape?: TSvgIconShapeKey;
                label: string;
                value: boolean;
            }[],
            overwrites?: { entryMode: string },
        ) {
            const standardItems: IntakeStandardProperty = {};
            subOptions.forEach(option => {
                const newId = nanoid();

                const fields = situation.fields.standard.map(e => ({
                    value: e.defaultValue ?? null,
                    ...e,
                }));

                const descriptionField = fields.find(e => e.name === 'description');
                if (descriptionField) {
                    descriptionField.value = option.label;
                    descriptionField.readonly = true;
                }

                standardItems[newId] = {
                    id: newId,
                    isComplete: false,
                    fields: fields,
                };
            });

            const situationObj = {
                id: nanoid(),
                cardMode: 'pending',
                totalSteps: 0,
                completedSteps: 0,
                situationGroup: 'expenses',
                kind: situation.label,
                name: situation.name,
                unit: situation.unit ?? undefined,
                itemsKind: situation.kind,
                category: 'Expense',
                iconShape: situation.iconShape,
                simple: situation.fields.simple.map(e => ({ ...e })),
                standard: standardItems,
                template: situation.fields.standard.map(e => ({ value: e.defaultValue ?? null, ...e })),
                footerRow: { label: 'Onboard Status', percentage: 0 },
                entryMode: 'standard',
                ...overwrites,
            } as IntakeSituationItem;

            state.expenses.value.push(situationObj);
        }
        function populateSituationCard(
            situationKind: IntakeSituationKind,
            situation: ReturnType<typeof transformSituationTypeForCard>,
            overwrites: any = {},
        ) {
            const standardItemId = nanoid();

            const associated: {
                associatedAssets?: [];
                associatedExpenses?: [];
                associatedIncome?: [];
                associatedLiabilities?: [];
            } = {};
            if (situation.assetKinds?.length) associated.associatedAssets = [];
            if (situation.expenseKinds?.length) associated.associatedExpenses = [];
            if (situation.incomeKinds?.length) associated.associatedIncome = [];
            if (situation.liabilityKinds?.length) associated.associatedLiabilities = [];

            if (situation.entangledWith) {
                associated.entangledWith = situation.entangledWith;
            }

            state[situationKind].value?.push({
                id: nanoid(),
                cardMode: 'pending',
                totalSteps: 1,
                completedSteps: 0,
                situationGroup: situationKind,
                kind: situation.label,
                name: situation.name,
                unit: situation.unit ?? undefined,

                itemsKind: situation.kind,
                category: getSituationCategoryFromKind(situationKind),

                iconShape: situation.iconShape,
                simple: situation.fields.simple.map(e => ({ ...e })),
                standard: {
                    [standardItemId]: {
                        id: standardItemId,
                        isComplete: false,
                        fields: situation.fields.standard.map(e => ({ value: e.defaultValue ?? null, ...e })),
                        ...associated,
                    },
                },
                template: situation.fields.standard.map(e => ({ value: e.defaultValue ?? null, ...e })),
                footerRow: { label: 'Onboard Status', percentage: 0 },
                entryMode: state.defaultMode.value ?? 'simple',
                ...overwrites,
            });
        }
        async function populateSituationCards(
            situationKind: IntakeSituationKind,
            situations: ReturnType<typeof transformSituationTypesForCard>,
            entryMode?: string | null,
        ) {
            // Insurance and Expenses only have standard entry mode
            if (!entryMode && ['insurance', 'expenses'].includes(situationKind)) entryMode = 'standard';
            const overwrites = entryMode ? { entryMode } : undefined;

            console.log('populateSituationCards', situationKind, situations);

            situations.forEach(situation => {
                const selectedSubOptions = situation.subOptions?.options.filter(e => e.value);
                if (situation.subOptions?.options.length && !selectedSubOptions) return;

                if (situationKind === 'expenses') {
                    // Expense subtypes are added as line items to the situation, so handle that separately
                    if (selectedSubOptions?.length) {
                        populateExpenseSituationCard(situation, selectedSubOptions);
                    } else {
                        populateSituationCard(situationKind, situation, overwrites);
                    }
                } else {
                    // Assets, Liabilities... anything but expenses
                    if (selectedSubOptions?.length) {
                        selectedSubOptions.forEach(option => {
                            populateSituationCard(situationKind, situation, {
                                kind: situation.label + ' / ' + option.label,
                                name: situation.name + ':' + option.name,
                                ...overwrites,
                            });
                        });
                    } else {
                        populateSituationCard(situationKind, situation, overwrites);
                    }
                }
            });

            await apiActions.update();
        }
        return {
            populateSituationCards,
            populateSituationCard,
        };
    })();

    const fieldActions = (function () {
        function addSituationAndItem(
            situationGroup: IntakeSituationKind,
            situationKind: string,
            relationship?: SituationRelationship,
        ) {
            let existingSituation = state[situationGroup].value.find(e => e.kind === situationKind);
            let allTypesOfKind: ReturnType<typeof transformSituationTypesForCard>;
            switch (situationGroup) {
                case 'assets':
                    allTypesOfKind = transformSituationTypesForCard(allAssetTypes);
                    state.assetSituationsAdded.value = true;
                    break;
                case 'liabilities':
                    allTypesOfKind = transformSituationTypesForCard(allLiabilityTypes);
                    break;
                case 'income':
                    allTypesOfKind = transformSituationTypesForCard(allIncomeTypes);
                    break;
                case 'expenses':
                    allTypesOfKind = transformSituationTypesForCard(allExpenseTypes);
                    break;
                default:
                    allTypesOfKind = [];
            }

            const situation = allTypesOfKind.find(e => e.kind === situationKind);

            if (!situation) return;
            let newId: string | number;
            if (existingSituation) {
                newId = addSituationItem(existingSituation, relationship);
            } else {
                existingSituation = {
                    id: nanoid(),
                    cardMode: 'pending',
                    totalSteps: 1,
                    completedSteps: 0,
                    kind: situation.label,
                    name: situation.name,
                    unit: situation.unit ?? undefined,
                    situationGroup: situationGroup,
                    itemsKind: situation.kind,
                    category: getSituationCategoryFromKind(situationGroup),
                    iconShape: situation.iconShape,
                    simple: situation.fields.simple.map(e => ({ ...e })),
                    standard: {},
                    template: situation.fields.standard.map(e => ({ ...e })),
                    footerRow: { label: 'Onboard Status', percentage: 0 },
                    entryMode: 'standard',
                };
                state[situationGroup].value?.push(existingSituation);
                newId = addSituationItem(existingSituation, relationship);
            }
            return newId;
        }
        function addSituationItem(
            situationItem: IntakeSituationItem,
            relationship?: SituationRelationship,
            entangledWith?: string,
        ) {
            let relationshipObj = {};
            if (relationship) {
                if (relationship.kind === 'assets') {
                    relationshipObj = { associatedAssets: [relationship.id] };
                } else if (relationship.kind === 'liabilities') {
                    relationshipObj = { associatedLiabilities: [relationship.id] };
                } else if (relationship.kind === 'income') {
                    relationshipObj = { associatedIncome: [relationship.id] };
                } else if (relationship.kind === 'expenses') {
                    relationshipObj = { associatedExpenses: [relationship.id] };
                }
            }

            if (entangledWith) {
                relationshipObj.entangledWith = entangledWith;
            }

            const newId = nanoid();
            situationItem.standard[newId] = {
                id: newId,
                isComplete: false,
                fields: situationItem.template.map(e => ({ ...e })),
                ...relationshipObj,
            };

            return newId;
        }
        function removeSituationItem(situationItem: IntakeSituationItem, itemId: number | string) {
            delete situationItem.standard[itemId];
        }
        async function removeSituation(situationKind: IntakeSituationKind, situation: IntakeSituationItem) {
            const situationGroup = state[situationKind].value;
            const liveSituation = situationGroup.findIndex(e => e.name === situation.name);
            if (liveSituation !== -1) {
                situationGroup.splice(liveSituation, 1);
            }

            await apiActions.update();
        }
        async function removeSituationByName(situationKind: IntakeSituationKind, situationName: string | undefined) {
            if (!situationName) return;
            const situationIndex = state[situationKind].value?.findIndex(e => e.name === situationName);
            if (situationIndex !== -1) {
                state[situationKind].value.splice(situationIndex, 1);
            }
            await apiActions.update();
        }

        async function updateFieldByItemId(
            situationKind: IntakeSituationKind,
            itemId: string,
            fieldName: string,
            payload: any,
        ) {
            let standardItemsOfKind: IntakeStandardProperty = {};
            let parentSituation: any = {};
            switch (situationKind) {
                case 'assets':
                    standardItemsOfKind = getters.standardAssets.value;
                    parentSituation = state.assets.value.find(e => e.standard.hasOwnProperty(itemId));
                    break;
                case 'liabilities':
                    standardItemsOfKind = getters.standardLiabilities.value;
                    parentSituation = state.liabilities.value.find(e => e.standard.hasOwnProperty(itemId));
                    break;
                case 'expenses':
                    standardItemsOfKind = getters.standardExpenses.value;
                    parentSituation = state.expenses.value.find(e => e.standard.hasOwnProperty(itemId));
                    break;
                case 'income':
                    standardItemsOfKind = getters.standardIncome.value;
                    parentSituation = state.income.value.find(e => e.standard.hasOwnProperty(itemId));
                    break;
                default:
                    return;
            }
            const item = standardItemsOfKind[itemId];
            if (item) {
                const field = item.fields.find(e => e.name === fieldName);
                if (field) field.value = payload;

                // TODO: Update the item's parent situation status
                // Currently the item does not know which situation it belongs to
                updateSituationStatus(parentSituation);

                await apiActions.update();
            }
        }

        async function updateSimpleField(
            situationKind: IntakeSituationKind,
            situationName: string | null | undefined,
            fieldName: string,
            payload: any,
        ) {
            const situationItem = state[situationKind].value?.find(e => e.name === situationName);
            if (situationItem) {
                const field = situationItem.simple.find(e => e.name === fieldName);
                if (field) field.value = payload;
                updateSituationStatus(situationItem);
            }

            await apiActions.update();
        }
        async function updateStandardField(
            situationKind: IntakeSituationKind,
            situationName: string | null | undefined,
            itemId: string | number,
            fieldName: string,
            payload: any,
        ) {
            const situationItem = state[situationKind].value?.find(e => e.name === situationName);
            if (situationItem) {
                let item = situationItem.standard[itemId];
                if (item) {
                    const field = item.fields.find(e => e.name === fieldName);
                    if (field) {
                        field.value = payload;
                        if (field.entangled) {
                            syncEntangledFieldToObject(situationItem, field);
                        }
                    }
                } else {
                    item = {
                        id: itemId,
                        fields: situationItem.template.map(e => ({ ...e })),
                    };
                    situationItem.standard[itemId] = item;
                }

                updateSituationStatus(situationItem);
            }

            await apiActions.update();
        }
        function updateSituationEntryMode(
            situationKind: IntakeSituationKind,
            situationName: string | null | undefined,
            mode: SituationEntryMode,
            useAsDefault?: boolean,
        ) {
            const situationItem = state[situationKind].value?.find(e => e.name === situationName);
            if (situationItem) {
                if (mode === 'standard') {
                    situationItem.entryMode = mode;
                } else {
                    // Convert standard to simple
                    const completeItems = [];
                    for (let key in situationItem.standard) {
                        if (situationItem.standard[key]?.isComplete) {
                            completeItems.push(situationItem.standard[key]);
                        }
                    }

                    // If any standard items have been completed, map their sums to the simple fields
                    if (completeItems.length) {
                        const simpleValue = situationItem.simple.find(e => e.name === 'value');
                        if (simpleValue) {
                            simpleValue.value = completeItems
                                .map(item =>
                                    item.fields
                                        .filter(field => field.affects === 'assets')
                                        .reduce(reduceSituationFields, 0),
                                )
                                .reduce((acc, val) => acc + val, 0);
                        }

                        const simpleLiabilities = situationItem.simple.find(e => e.name === 'liabilities');
                        if (simpleLiabilities) {
                            simpleLiabilities.value = completeItems
                                .map(item =>
                                    item.fields
                                        .filter(field => field.affects === 'liabilities')
                                        .reduce(reduceSituationFields, 0),
                                )
                                .reduce((acc, val) => acc + val, 0);
                        }
                    }

                    // Update the entry mode
                    situationItem.entryMode = mode;
                }

                updateSituationStatus(situationItem);
            }

            if (useAsDefault) {
                changeDefaultEntryMode(mode);
            }
        }

        // When a field with an entangled field is updated, update the entangled field
        function syncEntangledFieldToObject(situationItem: IntakeSituationItem, field: SituationInputGroupProps) {
            // The type of this item's entangled field (e.g. ExpenseRent)
            const entangledObjectType = field.entangled;
            if (!entangledObjectType) return;
            console.log('syncEntangledFieldToObject', entangledObjectType);

            let entangledObjectKind: IntakeSituationKind | null; // e.g.expenses, assets, etc
            let allOfEntangledKind: any[]; // all of the entangled kind (e.g. all expenses)
            switch (entangledObjectType.slice(0, 3)) {
                case 'Exp':
                    entangledObjectKind = 'expenses';
                    allOfEntangledKind = getters.standardExpenses.value;
                    break;
                case 'Ass':
                    entangledObjectKind = 'assets';
                    allOfEntangledKind = getters.standardAssets.value;
                    break;
                case 'Lia':
                    entangledObjectKind = 'liabilities';
                    allOfEntangledKind = getters.standardLiabilities.value;
                    break;
                case 'Ins':
                    entangledObjectKind = 'insurance';
                    allOfEntangledKind = getters.standardInsurance.value;
                    break;
                case 'Inc':
                    entangledObjectKind = 'income';
                    allOfEntangledKind = getters.standardIncome.value;
                    break;
                default:
                    entangledObjectKind = null;
                    allOfEntangledKind = [];
            }

            if (!entangledObjectKind) return;

            console.log(allOfEntangledKind, console.log(situationItem.id + ':' + field.name));

            // Look through all of the entangled kind to see if there are any with an entangledWith field matching this item
            // The format is standardItemId:fieldName (e.g. 1234-56_abc:amount)
            const entangledItem = Object.values(allOfEntangledKind)?.find(
                item => item.entangledWith == situationItem.id + ':' + field.name,
            );

            if (entangledItem) {
                // Update the field(s) with the new value(s)
                const entangledField = entangledItem.fields.find((e: any) => e.name === 'amount');
                if (entangledField) {
                    console.log('field', entangledField);
                    entangledField.value = {
                        number: field.value?.number ?? 0,
                        frequency: field.value?.frequency ?? 'Monthly',
                    };
                }
                console.log('entangledField', entangledField);
            } else {
                // state.expenses.value
                // If situation already exists, just add a new item

                // Otherwise, create a new situation and an item
                const newType = allExpenseTypes.find(e => e.kind === entangledObjectType);
                if (!newType) return;

                const existingSituation = state[entangledObjectKind].value.find(e => e.itemsKind === newType.kind);

                console.log('newType', newType);

                if (existingSituation) {
                    // The situation already exists (e.g. ExpenseRent), just add an item to the group
                    console.log('existingSituation found, adding item', existingSituation);
                    const newId = addSituationItem(existingSituation, undefined, situationItem.id + ':' + field.name);
                    const newItem = getters.standardExpenses.value?.[newId];
                    console.log('newItem', newItem, newItem.fields);
                    if (newItem) {
                        const amountField = newItem.fields.find(e => e.name === 'amount');
                        if (amountField) {
                            amountField.value = {
                                number: field.value?.number ?? 0,
                                frequency: field.value?.frequency ?? 'Monthly',
                            };
                        }
                        const descriptionField = newItem.fields.find(e => e.name === 'description');
                        if (descriptionField) {
                            descriptionField.value = field.entangledDescription ?? '';
                        }
                    }
                } else {
                    const transformed = transformSituationTypesForCard([newType]).map(situation => {
                        const amountField = situation.fields.standard.find(e => e.name === 'amount');
                        if (amountField) {
                            amountField.value = {
                                number: field.value?.number ?? 0,
                                frequency: field.value?.frequency ?? 'Monthly',
                            };
                        }
                        const descriptionField = situation.fields.standard.find(e => e.name === 'description');
                        if (descriptionField) {
                            descriptionField.value = field.entangledDescription ?? '';
                        }

                        situation.entangledWith = situationItem.id + ':' + field.name;
                        return situation;
                    });
                    console.log('found', newType);

                    console.log('transformed', transformed);

                    cardActions.populateSituationCards(
                        entangledObjectKind, // 'expenses'
                        transformed, //
                        'standard',
                    );
                }
            }
        }

        function changeDefaultEntryMode(newMode: SituationEntryMode) {
            state.defaultMode.value = newMode;

            // Update any existing, non-dirty situations and make sure they're all on the new mode
            const loopItems = [
                ...state.income.value,
                ...state.assets.value,
                ...state.liabilities.value,
                ...state.expenses.value,
            ];
            loopItems
                .filter(situation => {
                    if (newMode === 'simple' && situation.entryMode === 'standard') {
                        return !Object.entries(situation.standard).some(([_key, item]) =>
                            item.fields.some((field: any) => field.value || field.value === 0),
                        );
                    } else if (newMode === 'standard' && situation.entryMode !== 'standard') {
                        return !situation.simple.some((field: any) => field.value || field.value === 0);
                    } else {
                        return false;
                    }
                })
                .forEach(situation => (situation.entryMode = newMode));
        }

        function getItemAssociatedItems(item: IntakeStandardItem, itemKind: IntakeSituationKind) {
            const owned = getItemOwnedItems(item, itemKind);
            const parents = getItemParentItems(item);

            return {
                assets: [...owned.assets, ...parents.assets],
                liabilities: [...owned.liabilities, ...parents.liabilities],
                expenses: [...owned.expenses, ...parents.expenses],
                income: [...owned.income, ...parents.income],
            };
        }

        return {
            addSituationAndItem,
            addSituationItem,
            removeSituationItem,
            removeSituation,
            removeSituationByName,
            updateFieldByItemId,
            updateSimpleField,
            updateStandardField,
            updateSituationEntryMode,
            getItemAssociatedItems,
            recalculateTotals: () => {},
        };

        // All the related items associated via the related items' associatedAssets, associatedLiabilities, etc.
        function getItemOwnedItems(item: IntakeStandardItem, itemKind: IntakeSituationKind) {
            const associatedAttribute = prepend('associated', itemKind);

            return {
                assets: Object.values(getters.standardAssets.value).filter(a =>
                    a[associatedAttribute]?.includes(item.id),
                ),
                liabilities: Object.values(getters.standardLiabilities.value).filter(a =>
                    a[associatedAttribute]?.includes(item.id),
                ),
                expenses: Object.values(getters.standardExpenses.value).filter(a =>
                    a[associatedAttribute]?.includes(item.id),
                ),
                income: Object.values(getters.standardIncome.value).filter(a =>
                    a[associatedAttribute]?.includes(item.id),
                ),
            };
        }

        // All the related items associated via this item's associatedAssets, associatedLiabilities, etc.
        function getItemParentItems(item: IntakeStandardItem) {
            return {
                assets: item.associatedAssets?.length
                    ? item.associatedAssets.map(id => getters.standardAssets.value[id])
                    : [],
                liabilities: item.associatedLiabilities?.length
                    ? item.associatedLiabilities.map(id => getters.standardLiabilities.value[id])
                    : [],
                expenses: item.associatedExpenses?.length
                    ? item.associatedExpenses.map(id => getters.standardExpenses.value[id])
                    : [],
                income: item.associatedIncome?.length
                    ? item.associatedIncome.map(id => getters.standardIncome.value[id])
                    : [],
            };
        }
    })();

    const navigationActions = (function () {
        async function goToStep(stepName: string) {
            await apiActions.update();
            if (progress.value.steps.some(step => step.name === stepName)) {
                progress.value.activeStep = stepName;
            }
        }
        return {
            async goToNextStep() {
                const nextStep = getters.nextStep.value;
                if (nextStep) {
                    await goToStep(nextStep);
                }
            },
            async goToPrevStep() {
                const prevStep = getters.prevStep.value;
                if (prevStep) {
                    await goToStep(prevStep);
                }
            },
        };
    })();

    const otherActions = {
        init(step: string) {
            // Set the active step to the step defined, or the first step if it doesn't exist
            progress.value.activeStep =
                (progress.value.steps.some(s => step === s.name) && step) || progress.value.steps[0].name;
        },
        async markIntakeAsCompleted() {
            state.data.value.intakeCompleted = true;
            await apiActions.update();
        },
        async devSeed() {
            const assetTypes = transformSituationTypesForCard(allAssetTypes);
            await cardActions.populateSituationCards('assets', [assetTypes[0]]);
            state.assetSituationsAdded.value = true;
            const asset = state.assets.value[0];
            fieldActions.updateSituationEntryMode('assets', asset.name, 'standard', true);
            const assetItem = Object.values(asset.standard)?.[0];

            const liabilityTypes = transformSituationTypesForCard(allLiabilityTypes);
            await cardActions.populateSituationCards('liabilities', [liabilityTypes[0]]);
            const liability = state.liabilities.value[0];
            fieldActions.updateSituationEntryMode('liabilities', liability.name, 'standard', true);
            const liabilityItem = Object.values(liability.standard)?.[0];
            liabilityItem.associatedAssets = [assetItem.id];
        },
    };

    return {
        ...state,
        progress: progress,
        stepsComp,
        completionComp,
        totals: totals,
        ...getters,
        ...otherActions,
        ...apiActions,
        ...cardActions,
        ...fieldActions,
        ...navigationActions,
    };
});
