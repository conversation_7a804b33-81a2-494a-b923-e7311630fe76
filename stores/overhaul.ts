// noinspection JSUnusedGlobalSymbols

import { computed, ref, reactive } from 'vue';
import { defineStore } from 'pinia';
import type { TBigIconShapeKey } from '~/components/images/BigIconShapes';
import type { IconSelectorProps } from '~/components/elements/IconSelector.vue';
import type { SituationCardProps, SituationCardRow } from '~/components/situations/SituationCard.vue';
import type { SituationEntryMode } from '~/components/situations/SituationItem.vue';
import type { SituationInputGroupProps } from '~/components/situations/SituationInputGroup.vue';
import { allTypes } from '~/components/situations/data/intake-data';
import {
    abbreviateFrequency,
    abbreviateNumber,
    cloneObj,
    frequencyToInt,
    NormalizeToFrequency,
    parseMonthYearString,
    prepend,
    reduceSituationFieldsToTotal,
    transformPopulatedItemToLineItem,
} from '~/utils';
import type { IntakeData } from '~/types/intake';
import { nanoid } from 'nanoid';
import { useRequestFetch } from '#app';
import type { BaseLineItem, LineItem, LineItemKey, UnionLineItem } from '~/types/base';
import type { TSvgIconShapeKey } from '~/components/images/SvgIconShapes';
import type { TabBarTabProps } from '~/components/navigation/TabBarTab.vue';
import type { IncomeEmployee, IncomeLineItemForm } from '~/types/income';
import type { Paystub, PaystubLine, PaystubFrequency, PaystubRequestBody } from '~/types/paystub';

// This was IntakeSituationKind
export type TOverhaulCategory = 'assets' | 'liabilities' | 'income' | 'expenses' | 'insurance';
export type TOverhaulItemCategory = LineItem['category']; // Asset|Liability|Income|Expense|Insurance

export interface TOverhaulSituation extends IconSelectorProps {
    id: number;
    value: string;
    selected: boolean;
}

export function getSituationCategoryFromKind(kind: TOverhaulCategory): TOverhaulItemCategory {
    switch (kind) {
        case 'assets':
            return 'Asset';
        case 'liabilities':
            return 'Liability';
        case 'income':
            return 'Income';
        case 'expenses':
            return 'Expense';
        case 'insurance':
            return 'Insurance';
    }
}

export function getCategoryVariants(variant: string): {
    lowerPlural: TOverhaulCategory;
    upperPlural: string;
    lowerSingular: string;
    upperSingular: TOverhaulItemCategory;
} {
    switch (variant) {
        case 'liabilities':
        case 'Liabilities':
        case 'Liability':
            return {
                lowerPlural: 'liabilities',
                upperPlural: 'Liabilities',
                upperSingular: 'Liability',
                lowerSingular: 'liability',
            };
        case 'income':
        case 'Income':
            return {
                lowerPlural: 'income',
                upperPlural: 'Income',
                upperSingular: 'Income',
                lowerSingular: 'income',
            };
        case 'expenses':
        case 'Expenses':
        case 'Expense':
            return {
                lowerPlural: 'expenses',
                upperPlural: 'Expenses',
                upperSingular: 'Expense',
                lowerSingular: 'expense',
            };
        case 'insurance':
        case 'Insurance':
            return {
                lowerPlural: 'insurance',
                upperPlural: 'Insurance',
                upperSingular: 'Insurance',
                lowerSingular: 'insurance',
            };
        default:
            return {
                lowerPlural: 'assets',
                upperPlural: 'Assets',
                upperSingular: 'Asset',
                lowerSingular: 'asset',
            };
    }
}

export type TEntryMode = 'Simple' | 'Standard';

export interface TPopulatedItem {
    id: string; // UUID
    kindLabel: string; // Vehicle
    kind: string; // AssetVehicle
    name: string; // liability-student-loan
    category: string; // Liability
    entryMode: TEntryMode | null; // Simple / Standard
    kindData: {
        expenseKinds?: string[];
        liabilityKinds?: string[];
        assetKinds?: string[];
        incomeKinds?: string[];
        fields: {
            simple: SituationInputGroupProps[];
            standard: SituationInputGroupProps[];
        };
    };
    description?: string | null; // 2024 Ferrari 911
    fields: SituationInputGroupProps[];
    unit?: string; // loan
    iconShape?: TBigIconShapeKey; // bungalow
    progress?: TItemProgress;
    stats?: TItemStat[];
    statPrimary?: string;
    forms?: { [key: string]: any }[];

    // subType?: {
    //     value: string;
    //     name: string;
    // }
    subType?: string;
    subTypeName?: string;
    // subTypeName?: 'accountType' | 'mortgageType' | 'retirementType' | 'expenseType' | 'insuranceType' | 'lifeInsuranceType' | 'disabilityInsuranceType';

    // All possible sub-type fields
    // accountType?: string;
    // mortgageType?: string;
    // retirementType?: string;
    // expenseType?: string;
    // insuranceType?: string;
    // lifeInsuranceType?: string;
    // disabilityInsuranceType?: string;

    paystubs?: string[];
    paystubItems?: Paystub[];

    // If this item should be entangled (i.e. synced) with another field (e.g. a monthly payment for a mortgage)
    // This only exists on some expenses
    entangledWith?: string;

    // The IDs of any other items that have _this_ item listed as an associatedAsset, associatedLiability, etc
    associatedVia?: string[];

    associatedAssets?: Set<string>;
    associatedLiabilities?: Set<string>;
    associatedExpenses?: Set<string>;
    associatedIncome?: Set<string>;
    associatedInsurance?: Set<string>;
}

export interface TItemProgress {
    percentage: number; // between 0 and 1
    fields: number; // Total number of fields
    required: number; // Total number of required fields
    requiredCompleted: number; // Number of required fields completed
    completed: number; // Number of all fields completed
}

export interface TItemStat {
    kind: 'asset' | 'liability' | 'expense' | 'income' | 'net-worth' | 'interestRate';
    value: number;
    frequency?: 'mo' | 'yr';
    slice?: number;
}

export interface TOverhaulLineItemKpi {
    label: string;
    value: number | string;
    formattedValue?: string;
    formatCurrency?: boolean;
    suffix?: string;
    direction?: 'pos' | 'neg';
}

export interface TOverhaulLineItem {
    id: string;
    kindLabel: string; // Real Estate, Vehicle
    kind: string; // AssetRealEstate, LiabilityVehicle
    entryMode?: TEntryMode;
    description?: string; // Nissan Rogue, 2420 Fieldlark Drive
    progress?: TItemProgress; // 0 - 1
    iconShape?: TBigIconShapeKey;
    status?: 'not-started' | 'in-progress' | 'complete';
    kpis?: TOverhaulLineItemKpi[];
    draggable?: boolean;
}

export interface TOverhaulItem {
    // Unique UUID for this item
    id: string;

    // Simple or Standard
    mode?: TEntryMode | null;

    // Asset, Liability, etc
    category: TOverhaulItemCategory;

    // The unique kind identifier that the backend expects (e.g. LiabilityStudentLoan)
    kind: string;

    // The fields for this item
    // These will be flattened into the top-level when mapping to the backend
    // fields: {[name: string]: OverhaulItemField}
    fields: { [name: string]: string | number | null | undefined };

    // Only applicable to income items; any tax forms that have been filled out for this income
    forms?: IncomeLineItemForm[];

    // Only applicable to income items; any tax forms that have been filled out for this income
    paystubs?: string[];

    subType?: string;
    // subType?: {
    //     value: string;
    //     name: string;
    // }

    entangledWith?: string;

    // Associated items
    associatedAssets?: Set<string>;
    associatedLiabilities?: Set<string>;
    associatedExpenses?: Set<string>;
    associatedIncome?: Set<string>;
    associatedInsurance?: Set<string>;
}

export type TOverhaulItemGroup = { [key: string]: TOverhaulItem };

export const useOverhaulStore = defineStore('overhaul', function () {
    const { refresh: userRefresh } = useOidcAuth();

    /////////////////////////
    // █▀▀ ▀█▀ ▄▀▄ ▀█▀ █▀▀
    // ▄██  █  █▀█  █  ██▄
    //
    const state = {
        data: reactive<IntakeData>({
            name: {
                firstName: '',
                middleName: '',
                lastName: '',
            },
            phone: '',
            spouse: {
                phone: '',
            },
            dependents: [],
            intakeCompleted: false,
            situations: [],
            home: {},
        }),
        assets: reactive<TOverhaulItemGroup>({}),
        liabilities: reactive<TOverhaulItemGroup>({}),
        expenses: reactive<TOverhaulItemGroup>({}),
        income: reactive<TOverhaulItemGroup>({}),
        insurance: reactive<TOverhaulItemGroup>({}),
        owners: reactive<TOverhaulItemGroup>({}),

        paystubs: reactive<{ [id: string]: Paystub }>({}),

        defaultMode: ref<TEntryMode>('Simple'),
        assetSituationsAdded: ref(false),
        showEntryModeIntro: ref(true),
    };

    ////////////////////////////////////////
    // ▄▀▄ █▀▀ ▀█▀ ▀█▀ █▀█ █▄ █ █▀▀
    // █▀█ █▄▄  █  ▄█▄ █▄█ █ ▀█ ▄██
    //
    const apiActions = (function () {
        return {
            async fetch() {
                try {
                    const fetch = useRequestFetch();
                    const data = await fetch<IntakeData>('/api/intake');

                    if (data) {
                        if (typeof data.intakeCompleted === 'boolean') {
                            state.data.intakeCompleted = data.intakeCompleted;
                        }
                        state.data.name = data.name ?? {
                            firstName: '',
                            middleName: '',
                            lastName: '',
                        };
                        state.data.phone = data.phone ?? '';
                        state.data.email = data.email ?? '';
                        state.data.home = data.home ?? {};
                        state.data.spouse = data.spouse ?? {};
                        state.data.dependents = data.dependents ?? [];

                        state.data.birthdate = data.birthdate ?? '';
                        state.data.maritalStatus = data.maritalStatus ?? '';

                        state.data.spouse = data.spouse ?? {
                            firstName: '',
                            middleName: '',
                            lastName: '',
                            birthdate: '',
                        };

                        state.data.situations = data.situations ?? [];

                        state.data.dependents = data.dependents ?? [];

                        resetDataFromApi(data);
                    }

                    // !!HACK!!
                    // For every income item that has paystubs, fetch the paystubs and add them to the store
                    // Paystubs ultimately should be included in the income item payload on the intake reequest
                    // setTimeout(() => {
                    const incomeWithPaystubs = Object.values(state.income)?.filter(
                        incomeItem => incomeItem.paystubs?.length,
                    );
                    for (const incomeItem of incomeWithPaystubs) {
                        const paystubIds = incomeItem.paystubs;
                        if (paystubIds) {
                            for (const paystubId of paystubIds) {
                                const fetchedPaystub = await fetch('/api/paystub?id=' + paystubId);

                                console.log('fetched paystub', fetchedPaystub?._id ?? 'nada');
                                if (fetchedPaystub) {
                                    paystubActions.updatePaystubInStore(fetchedPaystub);
                                }
                            }
                        }
                    }

                    return state.data;
                } catch (e) {
                    console.log('Error in overhaulStore.fetch()');
                    console.log(`${e}`);
                    return {};
                }
            },
            async update() {
                // const toLog = {
                //     assets: state.assets.value,
                //     liabilities: state.liabilities.value,
                //     expenses: state.expenses.value,
                //     income: state.income.value,
                //     insurance: state.insurance.value
                // }
                // console.log('update', JSON.stringify(toLog, null, 2));

                const data = getApiTransformedData();

                console.log('updating', data);

                try {
                    const fetch = useRequestFetch();
                    await fetch<IntakeData>('/api/intake', {
                        method: 'POST',
                        body: data,
                    });
                } catch (e) {
                    console.log('Error in overhaulStore.update()');
                    console.log(`${e}`);
                }
            },
        };

        function resetDataFromApi(intakeData: IntakeData) {
            const { portfolio } = intakeData;
            if (!portfolio) return;

            const categories: TOverhaulCategory[] = ['assets', 'liabilities', 'income', 'expenses', 'insurance'];

            categories.forEach(category => {
                const categoryGroup = portfolio[category] ?? null;

                if (categoryGroup) {
                    const stateObj = state[category]; // e.g. state.assets
                    categoryGroup.forEach(item => {
                        const {
                            id,
                            category,
                            kind,
                            mode,
                            owner,
                            forms,
                            paystubs,
                            entangledWith,
                            associatedAssets,
                            associatedLiabilities,
                            associatedExpenses,
                            associatedIncome,
                            associatedInsurance,
                            ...fields
                        } = item;

                        const kindData = allTypes.find(e => e.kind === item.kind);

                        const subTypeData: { [k: string]: string } = {};
                        if (kindData?.subTypeName && item[kindData.subTypeName]) {
                            subTypeData.subType = item[kindData.subTypeName];
                        }

                        // Find all of the frequency fields and split them into the correct properties
                        for (const key in fields) {
                            if (fields.hasOwnProperty(key) && fields[key] && typeof fields[key] === 'object') {
                                const field = fields[key];

                                const fieldData =
                                    item.mode === 'Standard'
                                        ? kindData?.fields.standard.find(e => e.name === key)
                                        : kindData?.fields.simple.find(e => e.name === key);

                                const frequencyFieldName = fieldData?.frequencyName ?? 'paymentFrequency';

                                fields[key] = field.number;
                                fields[frequencyFieldName] = field.frequency;
                            }
                        }

                        const obj = {
                            id,
                            mode,
                            category,
                            kind,
                            owner: owner ?? null,

                            fields,
                            ...subTypeData,
                        };

                        if (forms?.length) {
                            obj.forms = forms;
                        }

                        if (paystubs?.length) {
                            obj.paystubs = paystubs;
                        }

                        if (entangledWith) {
                            obj.entangledWith = entangledWith;
                        }

                        // Put all the associated items into an object to easily transform them from arrays to sets
                        const associatedProperties = {
                            associatedAssets,
                            associatedLiabilities,
                            associatedExpenses,
                            associatedIncome,
                            associatedInsurance,
                        };
                        for (const key in associatedProperties) {
                            if (associatedProperties.hasOwnProperty(key) && associatedProperties[key]?.length) {
                                associatedProperties[key] = new Set(associatedProperties[key]);
                            }
                        }

                        // TODO: Associated items
                        if (associatedAssets?.length) obj.associatedAssets = associatedProperties.associatedAssets;
                        if (associatedLiabilities?.length)
                            obj.associatedLiabilities = associatedProperties.associatedLiabilities;
                        if (associatedExpenses?.length)
                            obj.associatedExpenses = associatedProperties.associatedExpenses;
                        if (associatedIncome?.length) obj.associatedIncome = associatedProperties.associatedIncome;
                        if (associatedInsurance?.length)
                            obj.associatedInsurance = associatedProperties.associatedInsurance;

                        stateObj[item.id] = obj;
                    });
                }
            });

            // Set the assetSituationsAdded flag if there are any assets
            if (state.assets.length) {
                state.assetSituationsAdded.value = true;
            }
        }

        // Used by the update method to transform the state into the API structure
        function getApiTransformedData() {
            return {
                ...state.data,
                portfolio: {
                    owners: [],
                    assets: transformCategoryToApi('assets'),
                    liabilities: transformCategoryToApi('liabilities'),
                    expenses: transformCategoryToApi('expenses'),
                    income: transformCategoryToApi('income'),
                    insurance: transformCategoryToApi('insurance'),
                },
            };
        }

        // Transforms a single category group into API format
        function transformCategoryToApi(category: TOverhaulCategory) {
            const group = state[category];
            if (!group) return [];

            const mapped = Object.values(group).map(item => {
                const {
                    fields,
                    associatedAssets,
                    associatedLiabilities,
                    associatedExpenses,
                    associatedIncome,
                    associatedInsurance,
                    subType,
                    ...rest
                } = item;

                // Convert associated properties from Sets to arrays
                const associatedProperties = {
                    associatedAssets,
                    associatedLiabilities,
                    associatedExpenses,
                    associatedIncome,
                    associatedInsurance,
                };
                for (const key in associatedProperties) {
                    if (associatedProperties.hasOwnProperty(key) && associatedProperties[key]?.size) {
                        associatedProperties[key] = Array.from(associatedProperties[key]);
                    }
                }

                if (subType) {
                    const kind = rest.kind;
                    const kindData = allTypes.find(e => e.kind === kind);
                    if (kindData?.subTypeName) {
                        rest[kindData.subTypeName] = subType;
                    }
                }

                if (rest.forms?.length && item.kind === 'IncomeEmployee') {
                    rest.forms = cloneObj(rest.forms);
                }

                return { ...rest, ...associatedProperties, ...fields };
            });

            return mapped;
        }
    })();

    const otherActions = {
        // Generate a new item object and return it; does NOT add it to the state
        // item can be a populated item or simply a string of the "kind" of item to populate (e.g. AssetVehicle)
        generateItem(category: TOverhaulCategory, item: TPopulatedItem | string, entryMode?: TEntryMode) {
            const id = nanoid();

            const itemCategory = getCategoryVariants(category).upperSingular;

            const kind = typeof item === 'string' ? item : item.kind;

            const kindData = allTypes.find(e => e.kind === kind);

            if (!kindData) return;

            const newItem: TOverhaulItem = {
                id,
                mode: entryMode ?? null,
                category: itemCategory,
                kind: kind,
                fields: {},
            };

            let template: any;
            if (entryMode) {
                template =
                    entryMode === 'Simple' ? cloneObj(kindData.fields.simple) : cloneObj(kindData.fields.standard);
            }

            template?.forEach(templateField => {
                if (templateField.type === 'currency-frequency' || templateField.type === 'number-frequency') {
                    newItem.fields[templateField.name] = null;
                    newItem.fields[templateField.frequencyName ?? 'paymentFrequency'] = 'Monthly';
                } else {
                    newItem.fields[templateField.name] = null;
                }
            });

            state[category][id] = newItem;

            return newItem;
        },

        // Add a new item to a portfolio (e.g. a new Vehicle, Student Loan, etc)
        addItem(
            category: TOverhaulCategory,
            item: TPopulatedItem | string,
            entryMode?: TEntryMode,
            subKind?: string | null,
            entangledWith?: string | null,
        ) {
            const newItem = otherActions.generateItem(category, item, entryMode);

            if (newItem) {
                // If it has a subkind (e.g. "Social Security" under "Retirement")
                // Make sure the subkind is logged and default the "description" field to the subkind
                if (subKind) {
                    newItem.subType = subKind;

                    if (newItem.category === 'Expense' && newItem.fields.hasOwnProperty('description')) {
                        console.log('setting description to subKind', subKind);
                        newItem.fields.description = subKind;
                    }
                }

                if (entangledWith) {
                    newItem.entangledWith = entangledWith;
                }

                // Actually add it to the store
                state[category][newItem.id] = newItem;
            }

            return newItem;
        },

        // Add a new item to the store, and then associate it with an existing item
        addAndLinkNewItem(item: TPopulatedItem | TOverhaulItem, newItem: TOverhaulItem) {
            const newItemCategory = getCategoryVariants(newItem.category).lowerPlural;
            state[newItemCategory][newItem.id] = newItem;

            otherActions.associateItemToItem(item.id, newItemCategory, newItem.id);
        },

        // Update an individual field on an item (e.g. the 'description' field on a vehicle)
        async updateItemField(itemCategory: TOverhaulCategory, itemId: string, fieldName: string, payload: any) {
            let item = state[itemCategory]?.[itemId];

            if (!item) return;

            const kindData = allTypes.find(e => e.kind === item.kind);

            if (!kindData) {
                return;
            }

            console.log('updateItemField', { itemCategory, itemId, fieldName, payload });

            // Get the template for this item kind, so we can figure out if the field is a frequency field
            // If it is, the payload will be an object that needs to be split between multiple field attributes
            const fieldsTemplate = item.mode === 'Simple' ? kindData.fields.simple : kindData.fields.standard;
            const templateFieldData = fieldsTemplate.find(e => e.name === fieldName);

            let entangledData: { amount: number; paymentFrequency: string } | null = null;
            if (
                templateFieldData &&
                (templateFieldData.type === 'currency-frequency' || templateFieldData.type === 'number-frequency')
            ) {
                item.fields[fieldName] = payload['number'];
                item.fields[templateFieldData.frequencyName ?? 'paymentFrequency'] = payload['frequency'];

                entangledData = {
                    amount: payload['number'],
                    paymentFrequency: payload['frequency'],
                };
            } else {
                item.fields[fieldName] = payload;

                entangledData = payload;
            }

            if (templateFieldData.entangled ?? false) {
                this.syncEntangledFieldToItem(item, templateFieldData, entangledData);
            }

            if (item.entangledWith) {
                this.syncEntangledItemToField(item);
            }

            await apiActions.update();
        },

        // Change an item to Standard or Simple mode, and update the fields to match the new mode
        async updateItemMode(itemData: TPopulatedItem, mode: TEntryMode) {
            const category = getCategoryVariants(itemData.category)?.lowerPlural; // assets, liabilities, etc

            if (!state[category][itemData.id]) return;

            const item = state[category][itemData.id];
            item.mode = mode; // Actually update the mode

            // Replace the item fields with the default fields for the new mode
            let fields: { [key: string]: any } = {};
            if (mode === 'Simple') {
                const simpleTemplate = cloneObj(itemData.kindData.fields.simple);
                simpleTemplate.forEach(templateField => {
                    if (templateField.type === 'currency-frequency' || templateField.type === 'number-frequency') {
                        fields[templateField.name] = null;
                        fields[templateField.frequencyName ?? 'paymentFrequency'] = 'Monthly';
                    } else {
                        fields[templateField.name] = null;
                    }
                });
            } else {
                // Standard
                const standardTemplate = cloneObj(itemData.kindData.fields.standard);
                standardTemplate.forEach(templateField => {
                    if (templateField.type === 'currency-frequency' || templateField.type === 'number-frequency') {
                        fields[templateField.name] = null;
                        fields[templateField.frequencyName ?? 'paymentFrequency'] = 'Monthly';
                    } else {
                        fields[templateField.name] = null;
                    }
                });
            }

            item.fields = fields;

            await apiActions.update();
        },

        // When a field with an entangled field is updated, update the entangled field
        syncEntangledFieldToItem(
            item: TOverhaulItem,
            fieldData: SituationInputGroupProps,
            payload: { amount: number; paymentFrequency: string },
        ) {
            /*
             * !! Only Expense entangled field are supported at the moment !!
             * While there is some logic to accommodate each item category, once it gets to actually fetching and
             * populating the entangled item, it assumes it is an expense, and the data being synced is a number-frequency
             * object (with amount and paymentFrequency properties)
             */

            // The type of this item's entangled field (e.g. ExpenseRent)
            const entangledObjectType = fieldData.entangled;
            if (!entangledObjectType) return;

            let entangledObjectKind: IntakeSituationKind | null; // e.g.expenses, assets, etc
            let allOfEntangledKind: TPopulatedItem[]; // all of the entangled kind (e.g. all expenses)
            switch (entangledObjectType.slice(0, 3)) {
                case 'Exp':
                    entangledObjectKind = 'expenses';
                    allOfEntangledKind = getters.expenseItemsArr.value;
                    break;
                case 'Ass':
                    entangledObjectKind = 'assets';
                    allOfEntangledKind = getters.assetItemsArr.value;
                    break;
                case 'Lia':
                    entangledObjectKind = 'liabilities';
                    allOfEntangledKind = getters.liabilityItemsArr.value;
                    break;
                case 'Ins':
                    entangledObjectKind = 'insurance';
                    allOfEntangledKind = getters.insuranceItemsArr.value;
                    break;
                case 'Inc':
                    entangledObjectKind = 'income';
                    allOfEntangledKind = getters.incomeItemsArr.value;
                    break;
                default:
                    entangledObjectKind = null;
                    allOfEntangledKind = [];
            }

            if (!entangledObjectKind) return;

            // Look through all of the entangled kind to see if there are any with an entangledWith field matching this item
            // The format is standardItemId:fieldName (e.g. 1234-56_abc:amount)

            const existingEntangledItem = Object.values(allOfEntangledKind)?.find(
                loopItem => loopItem.entangledWith == item.id + ':' + fieldData.name,
            );

            if (existingEntangledItem) {
                // Get the raw, unmapped data from the state and update the fields there
                const rawExistingItem = state.expenses[existingEntangledItem.id];
                if (rawExistingItem) {
                    rawExistingItem.fields.amount = payload.amount ?? 0;
                    rawExistingItem.fields.paymentFrequency = payload.paymentFrequency ?? 'Monthly';
                }
            } else {
                // Create the new item
                const newType = allTypes.find(e => e.kind === entangledObjectType);
                if (!newType) return;

                const newEntangledItem = otherActions.addItem(
                    'expenses',
                    entangledObjectType,
                    'Standard',
                    null,
                    item.id + ':' + fieldData.name,
                );

                if (newEntangledItem) {
                    newEntangledItem.fields.description =
                        fieldData.entangledDescription ?? item.description + ' payments';
                    newEntangledItem.fields.amount = payload.amount ?? 0;
                    newEntangledItem.fields.paymentFrequency = payload.paymentFrequency ?? 'Monthly';
                }
            }
        },

        // When an item (currently only expenses) is updated, also any entangled fields
        syncEntangledItemToField(entangledExpense: TPopulatedItem) {
            // If it's not entangled with anything, our work here is done
            if (!entangledExpense.entangledWith) return;

            const [id, fieldName] = entangledExpense.entangledWith.split(':');
            if (!id || !fieldName) return;

            const parentItem = getters.allRawItems.value?.[id];

            const field = parentItem?.fields[fieldName];
            if (parentItem?.fields?.hasOwnProperty(fieldName)) {
                parentItem.fields[fieldName] = entangledExpense.fields.amount ?? 0;
            }
            if (parentItem?.fields?.hasOwnProperty('paymentFrequency')) {
                parentItem.fields['paymentFrequency'] = entangledExpense.fields.paymentFrequency ?? 'Monthly';
            }
        },

        async associateItemToItem(itemId: string, associatedCategory: TOverhaulCategory, associatedItemId: string) {
            const populatedItem = getters.allItems.value?.[itemId];

            if (!populatedItem) return;

            const itemCategoryVariants = getCategoryVariants(populatedItem?.category);

            const item = state[itemCategoryVariants.lowerPlural]?.[itemId];
            // const item = state[associatedCategory][associatedItemId];

            if (!item) return;

            // e.g. associatedAssets
            const categoryVariants = getCategoryVariants(associatedCategory);
            const attribute = 'associated' + categoryVariants.upperPlural;

            if (item.hasOwnProperty(attribute)) {
                (item[attribute as keyof TPopulatedItem] as Set<string>).add(associatedItemId);
            } else {
                (item[attribute as keyof TPopulatedItem] as Set<string>) = new Set([associatedItemId]);
            }

            await apiActions.update();
        },

        // Unlink an item from another item
        // The item with the associatedCATEGORY attribute, could be either itemId or associatedItemId, so many checks need to be done
        async dissociateItemFromItem(itemOneId: string, itemTwoId: string) {
            // We need to get the raw reference to the items, not the computed populated versions
            // But it's easiest to access the populated items via allItems, then use that to figure out exactly where the raw items are
            const populatedItemOne = getters.allItems.value?.[itemOneId];
            const populatedItemTwo = getters.allItems.value?.[itemTwoId];
            if (!populatedItemOne || !populatedItemTwo) return;

            const categoryVariantsOne = getCategoryVariants(populatedItemOne?.category);
            const itemOne = state[categoryVariantsOne.lowerPlural]?.[itemOneId]; // The raw item

            const categoryVariantsTwo = getCategoryVariants(populatedItemTwo?.category);
            const itemTwo = state[categoryVariantsTwo.lowerPlural]?.[itemTwoId]; // The raw item

            if (!itemOne || !itemTwo) return;

            // Figure out which attribute the relationship would be stored on both items
            // e.g. if itemTwo is an asset, look at itemOne.associtedAssets
            const attributeOne = 'associated' + getCategoryVariants(itemTwo.category).upperPlural;
            const attributeTwo = 'associated' + getCategoryVariants(itemOne.category).upperPlural;

            // Try to remove the relationship on both items to ensure it's fully unlinked
            if (itemOne[attributeOne] ?? null) {
                itemOne[attributeOne].delete(itemTwoId);
            }

            if (itemTwo[attributeTwo] ?? null) {
                itemTwo[attributeTwo].delete(itemOneId);
            }

            await apiActions.update();
        },

        // Take a raw (pinia state) item and transform it to a populated item
        populateRawItem(item: TOverhaulItem): TPopulatedItem | null {
            const kindData = allTypes.find(e => e.kind === item.kind);

            if (!kindData) return null;

            const populatedItem: Partial<TPopulatedItem> = {
                id: item.id,
                name: kindData.name, // liability-student-loan
                unit: kindData.unit ?? undefined, // loan

                kind: kindData.kind, // LiabilityStudentLoan
                kindLabel: kindData.label, // Student Loan
                category: kindData.category, // Liability

                iconShape: kindData.iconShape, // bungalow

                entryMode: item.mode ? item.mode : undefined,
                fields: [],

                kindData: {
                    expenseKinds: kindData.expenseKinds,
                    fields: kindData.fields,
                },
            };

            // An item's 'description' is the label for that item in the UI
            // Most kinds have a 'description' field, but some have an "address" instead
            const desc = item.fields?.description ?? item.fields?.address;
            populatedItem.description = desc ? String(desc) : null;

            // Populate all of the field based on the item kind's template for its current mode (Simple or Standard)
            if (item.mode === 'Simple') {
                const simpleTemplate = cloneObj(kindData.fields.simple);

                // Iterate over each field in the template
                simpleTemplate.forEach(templateField => {
                    // Find the matching field in the live item
                    const itemField = item.fields[templateField.name];
                    if (itemField || itemField === 0) {
                        if (templateField.type === 'currency-frequency' || templateField.type === 'number-frequency') {
                            const frequencyField = item.fields[templateField.frequencyName ?? 'paymentFrequency'];
                            templateField.value = {
                                number: itemField,
                                frequency: frequencyField ?? 'Monthly',
                            };
                        } else {
                            templateField.value = itemField;
                        }
                    }

                    // Replace any placeholders (e.g. {{ type }}) in the field label and description
                    (templateField.label = replaceFieldPlaceholders(templateField.label, populatedItem, true)),
                        (templateField.description = replaceFieldPlaceholders(
                            templateField.description ?? '',
                            populatedItem,
                            true,
                        ));
                });

                populatedItem.fields = simpleTemplate;
            } else {
                const templateFields = cloneObj(kindData.fields.standard);

                templateFields.forEach(field => {
                    if (item.fields[field.name] || item.fields[field.name] === 0) {
                        if (field.type === 'currency-frequency' || field.type === 'number-frequency') {
                            field.value = {
                                number: item.fields[field.name] ?? 0,
                                frequency: item.fields[field.frequencyName ?? 'paymentFrequency'],
                            };
                        } else {
                            field.value = item.fields[field.name];
                        }
                    }

                    // Replace any placeholders (e.g. {{ type }}) in the field label and description
                    (field.label = replaceFieldPlaceholders(field.label, populatedItem, true)),
                        (field.description = replaceFieldPlaceholders(field.description ?? '', populatedItem, true));

                    if (item.subType && item.category === 'expenses' && field.name === 'description') {
                        field.readonly = true;
                    }
                });

                if (item.fields?.description) {
                    populatedItem.description = String(item.fields.description);
                }

                // TODO: Entangled fields
                // if (item.entangledWith) {
                //     populatedItem.entangledWith = item.entangledWith;
                // }

                if (item.associatedAssets) {
                    populatedItem.associatedAssets = item.associatedAssets;
                }
                if (item.associatedLiabilities) {
                    populatedItem.associatedLiabilities = item.associatedLiabilities;
                }
                if (item.associatedExpenses) {
                    populatedItem.associatedExpenses = item.associatedExpenses;
                }
                if (item.associatedIncome) {
                    populatedItem.associatedIncome = item.associatedIncome;
                }

                if (item.paystubs) {
                    populatedItem.paystubs = item.paystubs;
                    populatedItem.paystubItems = item.paystubs
                        .map((paystubId: string) => state.paystubs[paystubId] ?? null)
                        .filter(Boolean);
                }

                const allAssociations = getters.associatedItemsById.value;
                const associatedVia = Array.from(allAssociations.keys()).filter(key => {
                    const values = allAssociations.get(key);
                    return values && Array.from(values).some(value => value.includes(item.id));
                });

                if (associatedVia.length) {
                    populatedItem.associatedVia = associatedVia;
                }

                populatedItem.fields = templateFields;
            }

            // Calculate the completion for this item
            const allFields = Object.values(populatedItem.fields);
            const completedFieldsCount: number = allFields.reduce((acc, field) => {
                const value =
                    field.type === 'currency-frequency' || field.type === 'number-frequency'
                        ? field.value?.number
                        : field.value;

                return value || value === 0 ? acc + 1 : acc;
            }, 0);

            const requiredFields = Object.values(populatedItem.fields)?.filter(field => field.required);

            // If there are required fields on this item, calculate a percentage of how many are filled
            // This does no validation other than 'does this field have a value'
            if (requiredFields?.length) {
                const requiredCompletedFieldsCount: number = requiredFields.reduce((acc, field) => {
                    const value =
                        field.type === 'currency-frequency' || field.type === 'number-frequency'
                            ? field.value?.number
                            : field.value;

                    // if (item.id === 'kvqhpyqYABZbmpuqrXlkp') {
                    //     console.log('field', value);
                    // }

                    return value || value === 0 ? acc + 1 : acc;
                }, 0);

                populatedItem.progress = {
                    percentage: Math.min(requiredCompletedFieldsCount / (requiredFields.length ?? 1), 1),
                    fields: Object.keys(populatedItem.fields).length,
                    required: requiredFields?.length ?? 0,
                    requiredCompleted: requiredCompletedFieldsCount,
                    completed: completedFieldsCount,
                };

                // if (item.id === 'kvqhpyqYABZbmpuqrXlkp') {
                //     console.log(populatedItem.progress);
                // }
            } else {
                populatedItem.progress = {
                    percentage: 1,
                    fields: Object.keys(populatedItem.fields).length,
                    required: requiredFields?.length ?? 0,
                    requiredCompleted: 0,
                    completed: completedFieldsCount,
                };
            }

            // If any of the fields are complete, generate the stats array with values for this items'
            // asset, liability, net worth, etc values
            if (populatedItem.progress?.percentage > 0) {
                const stats: TItemStat[] = [];

                const assetValue = populatedItem.fields
                    .filter(field => field.affects === 'assets')
                    .reduce(reduceSituationFieldsToTotal, 0);
                if (assetValue) {
                    stats.push({
                        kind: 'asset',
                        value: assetValue,
                    });
                }

                const liabilityValue = populatedItem.fields
                    .filter(field => field.affects === 'liabilities')
                    .reduce(reduceSituationFieldsToTotal, 0);
                if (liabilityValue) {
                    stats.push({
                        kind: 'liability',
                        value: liabilityValue,
                    });
                }

                const expenseFields = populatedItem.fields.filter(field => field.affects === 'expenses');

                if (expenseFields.length) {
                    const frequencyFields = expenseFields.filter(
                        field => field.type === 'currency-frequency' || field.type === 'number-frequency',
                    );

                    // Get the minimum frequency of all frequency fields
                    // Using Math.max instead of min because a lower frequency equates to a higher number
                    // e.g. 'Yearly' is 1, 'Monthly' is 12
                    const minFrequencyInt = Math.max(
                        frequencyFields.map(field => frequencyToInt[field.value.frequency] ?? 1),
                    );
                    const toFrequency = minFrequencyInt <= frequencyToInt['Monthly'] ? 'Monthly' : 'Yearly';

                    const totalNormalized = expenseFields.reduce((acc, field) => {
                        // In case any fields are not *-frequency types, default to 0 / month
                        const fieldValue = field.value?.number ?? field.value ?? 0;
                        const fieldFrequency = field.value?.frequency ?? 'Monthly';
                        return acc + NormalizeToFrequency(fieldValue, fieldFrequency, toFrequency);
                    }, 0);

                    stats.push({
                        kind: 'expense',
                        value: totalNormalized,
                        frequency: toFrequency === 'Monthly' ? 'mo' : 'yr',
                    });
                }

                const incomeFields = populatedItem.fields.filter(field => field.affects === 'income');
                if (incomeFields.length) {
                    const frequencyFields = incomeFields.filter(
                        field => field.type === 'currency-frequency' || field.type === 'number-frequency',
                    );

                    // Get the minimum frequency of all frequency fields
                    // Using Math.max instead of min because a lower frequency equates to a higher number
                    // e.g. 'Yearly' is 1, 'Monthly' is 12
                    const minFrequencyInt = Math.max(
                        frequencyFields.map(field => frequencyToInt[field.value.frequency] ?? 1),
                    );
                    const toFrequency = minFrequencyInt <= frequencyToInt['Monthly'] ? 'Monthly' : 'Yearly';

                    const totalNormalized = incomeFields.reduce((acc, field) => {
                        // In case any fields are not *-frequency types, default to 0 / month
                        const fieldValue = field.value?.number ?? field.value ?? 0;
                        const fieldFrequency = field.value?.frequency ?? 'Monthly';
                        return acc + NormalizeToFrequency(fieldValue, fieldFrequency, toFrequency);
                    }, 0);

                    stats.push({
                        kind: 'income',
                        value: totalNormalized,
                        frequency: toFrequency === 'Monthly' ? 'mo' : 'yr',
                    });
                }
                if (assetValue || liabilityValue) {
                    stats.push({
                        kind: 'net-worth',
                        value: (assetValue ?? 0) - (liabilityValue ?? 0),
                    });
                }

                const interestRateField = populatedItem.fields.find(field => field.name === 'interestRate');
                if (interestRateField) {
                    stats.push({
                        kind: 'interestRate',
                        value: interestRateField.value,
                    });
                }

                if (populatedItem.kind === 'IncomeEmployee' && populatedItem.paystubItems?.length) {
                    const latestPaystub = populatedItem.paystubItems.reduce((prev, curr) => {
                        return new Date(prev.payDate) > new Date(curr.payDate) ? prev : curr;
                    });

                    // Based on the paystub frequency, the multiplier that will normalize the values to an annual sum
                    const annualFrequencyMultiplier = frequencyToInt[latestPaystub.frequency] ?? 1;
                    const monthlyFrequencyMultiplier = annualFrequencyMultiplier ? annualFrequencyMultiplier / 12 : 1;

                    let incomeSum = 0;
                    if (latestPaystub.incomeItems?.length) {
                        const incomeLineItemsSum = latestPaystub.incomeItems.reduce(
                            (sum, item) => sum + (Number(item.current) || 0),
                            0,
                        );
                        incomeSum = incomeLineItemsSum * monthlyFrequencyMultiplier;
                    }

                    const incomeStat = stats.find(e => e.kind === 'income');
                    if (incomeStat) {
                        // Modify stats if they exist instead of adding
                        incomeStat.value += incomeSum;
                    } else {
                        // No income stat, so add it
                        stats.push({
                            kind: 'income',
                            value: incomeSum,
                            frequency: 'mo',
                        });
                    }

                    // TODO: Account for custom rows in income and expenses
                    const expenseItems = [
                        ...(latestPaystub.preTaxItems ?? []),
                        ...(latestPaystub.postTaxItems ?? []),
                        ...(latestPaystub.taxesItems ?? []),
                    ];

                    const expenseSum = expenseItems.length
                        ? expenseItems.reduce((sum, item) => sum + (Number(item.current) || 0), 0) *
                          monthlyFrequencyMultiplier
                        : 0;

                    const expenseStat = stats.find(e => e.kind === 'expense');
                    if (expenseStat) {
                        // Modify stats if they exist instead of adding
                        expenseStat.value += incomeSum;
                    } else {
                        // No income stat, so add it
                        stats.push({
                            kind: 'expense',
                            value: expenseSum,
                            frequency: 'mo',
                        });
                    }
                }

                populatedItem.stats = stats;

                // "Primary" Stat that is shown on certain listings; varies depending on the category of the item
                const categoryToPrimaryStatMap: Record<TOverhaulItemCategory, string | null> = {
                    Asset: 'asset',
                    Liability: 'liability',
                    Expense: 'expense',
                    Insurance: null,
                    Income: 'income',
                };

                const primaryStatKind = categoryToPrimaryStatMap[populatedItem.category ?? ''] ?? null;

                const stat = stats?.find(s => s.kind === primaryStatKind);

                if (stat) {
                    populatedItem.statPrimary = stat.hasOwnProperty('frequency')
                        ? abbreviateNumber(stat.value) + ' /' + stat.frequency
                        : abbreviateNumber(stat.value);
                }
            } // if progress

            if (item.forms) {
                populatedItem.forms = item.forms;
            }
            if (item.associatedAssets?.size) {
                populatedItem.associatedAssets = item.associatedAssets;
            }
            if (item.associatedLiabilities?.size) {
                populatedItem.associatedLiabilities = item.associatedLiabilities;
            }
            if (item.associatedExpenses?.size) {
                populatedItem.associatedExpenses = item.associatedExpenses;
            }
            if (item.associatedIncome?.size) {
                populatedItem.associatedIncome = item.associatedIncome;
            }
            if (item.associatedInsurance?.size) {
                populatedItem.associatedInsurance = item.associatedInsurance;
            }
            if (item.entangledWith) {
                populatedItem.entangledWith = item.entangledWith;
            }

            // Correctly populate out the subType if applicable
            if (item.subType && kindData.subTypeName) {
                populatedItem.subType = item.subType;
                populatedItem.subTypeName = kindData.subTypeName;
            }

            return populatedItem as TPopulatedItem;
        },

        // Delete an item from a portfolio
        async deleteItem(category: TOverhaulCategory, itemId: string) {
            delete state[category][itemId];

            await nextTick();
            await apiActions.update();
        },

        upsert1099(itemId: string, fields: { [key: string]: any }, existingFormId?: string) {
            const item = state.income[itemId];
            if (!item) return;

            const existingForm = item.forms?.find((form: any) => form.id === existingFormId) ?? null;
            const formId = existingFormId ?? nanoid();

            const formObj = {
                id: formId,
                kind: '1099',
                ...fields,
            } as IncomeLineItemForm;

            if (existingForm) {
                const index = item.forms?.findIndex((form: any) => form.id === existingFormId);
                if (typeof index === 'number' && index !== -1 && item.forms?.[index]) {
                    // item.forms[index] = formObj;
                    item.forms.splice(index, 1, formObj);
                }
            } else {
                if (item.forms?.length) {
                    item.forms.push(formObj);
                } else {
                    item.forms = [formObj];
                }
            }

            // TODO: Update existing related items when updating a form
            if (!existingForm && fields.federalIncomeTaxWithheld) {
                const generatedItem = otherActions.generateItem('expenses', 'ExpenseTaxes', 'Standard');

                if (generatedItem) {
                    generatedItem.fields['amount'] = fields.federalIncomeTaxWithheld;
                    generatedItem.fields['description'] = 'Federal Income Tax Withheld';
                    generatedItem.fields['paymentFrequency'] = 'Yearly';

                    otherActions.addAndLinkNewItem(item, generatedItem);
                }
            }

            if (!existingForm && fields.stateIncomeTaxWithheld) {
                const generatedItem = otherActions.generateItem('expenses', 'ExpenseTaxes', 'Standard');

                if (generatedItem) {
                    generatedItem.fields['amount'] = fields.stateIncomeTaxWithheld;
                    generatedItem.fields['description'] = 'State Income Tax Withheld';
                    generatedItem.fields['paymentFrequency'] = 'Yearly';

                    otherActions.addAndLinkNewItem(item, generatedItem);
                }
            }

            apiActions.update();
        },

        upsertW2(itemId: string, fields: { [key: string]: any }, existingFormId?: string) {
            const item = state.income[itemId];
            if (!item) return;

            const existingForm = item.forms?.find((form: any) => form.id === existingFormId) ?? null;
            const formId = existingFormId ?? nanoid();

            const formObj = {
                ...fields,
                id: formId,
                kind: 'w2',
            } as IncomeLineItemForm;

            console.log(formObj);

            if (existingForm) {
                const index = item.forms?.findIndex((form: any) => form.id === existingFormId);
                if (typeof index === 'number' && index !== -1 && item.forms?.[index]) {
                    item.forms.splice(index, 1, formObj);
                }
            } else {
                if (item.forms?.length) {
                    item.forms.push(formObj);
                } else {
                    item.forms = [formObj];
                }
            }

            // TODO: Update existing related items when updating a form
            if (!existingForm && fields.federalIncomeTaxWithheld) {
                const generatedItem = otherActions.generateItem('expenses', 'ExpenseTaxes', 'Standard');

                if (generatedItem) {
                    generatedItem.fields['amount'] = fields.federalIncomeTaxWithheld;
                    generatedItem.fields['description'] = 'Federal Income Tax Withheld';
                    generatedItem.fields['paymentFrequency'] = 'Yearly';

                    otherActions.addAndLinkNewItem(item, generatedItem);
                }
            }

            if (!existingForm && fields.socialSecurityTaxWithheld) {
                const generatedItem = otherActions.generateItem('expenses', 'ExpenseTaxes', 'Standard');

                if (generatedItem) {
                    generatedItem.fields['amount'] = fields.socialSecurityTaxWithheld;
                    generatedItem.fields['description'] = 'Social Security Tax Withheld';
                    generatedItem.fields['paymentFrequency'] = 'Yearly';

                    console.log(generatedItem);

                    otherActions.addAndLinkNewItem(item, generatedItem);
                }
            }

            if (!existingForm && fields.medicareTaxWithheld) {
                const generatedItem = otherActions.generateItem('expenses', 'ExpenseTaxes', 'Standard');

                if (generatedItem) {
                    generatedItem.fields['amount'] = fields.medicareTaxWithheld;
                    generatedItem.fields['description'] = 'Medicare Tax Withheld';
                    generatedItem.fields['paymentFrequency'] = 'Yearly';

                    otherActions.addAndLinkNewItem(item, generatedItem);
                }
            }

            apiActions.update();
        },

        async deleteFormFromItem(itemId: string, formId: string) {
            // TODO: Forms are only supported for income at the moment
            const item = state.income[itemId];
            if (!item) return;

            const index = item.forms?.findIndex((form: any) => form.id === formId);
            if (typeof index === 'number' && index !== -1 && item.forms?.[index]) {
                item.forms.splice(index, 1);
            }

            await nextTick();
            await apiActions.update();
        },

        async markIntakeAsCompleted() {
            const allItems = getters.allItems.value;
            for (const key in allItems) {
                if (allItems.hasOwnProperty(key)) {
                    const item = allItems[key];
                    if (item && !item.entryMode) {
                        const category = getCategoryVariants(item.category)?.lowerPlural;
                        if (category) {
                            await otherActions.deleteItem(category, item.id);
                        }
                    }
                }
            }

            state.data.intakeCompleted = true;
            await userRefresh(); // refresh the user so we get any updates on intake status and name info
            await apiActions.update();
        },

        /**
         * Marks a section as completed by setting the appropriate flag in state.data
         * @param sectionName - The section to mark as completed (e.g., 'assets', 'liabilities', etc.)
         */
        async markSectionAsCompleted(sectionName: string) {
            console.log('markSectionAsCompleted called with section:', sectionName);
            console.log('Current state before update:', state.data);

            switch (sectionName) {
                case 'assets':
                    state.data.assetsCompleted = true;
                    break;
                case 'liabilities':
                    state.data.liabilitiesCompleted = true;
                    break;
                case 'income':
                    state.data.incomeCompleted = true;
                    break;
                case 'expenses':
                    state.data.expensesCompleted = true;
                    break;
                case 'insurance':
                    state.data.insuranceCompleted = true;
                    break;
                default:
                    console.log('Unknown section:', sectionName);
                    break;
            }

            console.log('State after setting completion flag:', state.data);
            // Force an update to persist the state change
            await apiActions.update();
            console.log('State after API update:', state.data);
        },
    };

    const paystubActions = {
        updatePaystubInStore(paystub: Paystub, incomeEmployeeId?: string) {
            if (!paystub?._id) {
                console.error('Cannot update paystub without _id:', paystub);
                return;
            }

            state.paystubs[paystub._id] = { ...paystub };

            if (incomeEmployeeId) {
                if (Array.isArray(state.income[incomeEmployeeId]?.paystubs)) {
                    state.income[incomeEmployeeId].paystubs.push(paystub._id);
                } else {
                    state.income[incomeEmployeeId].paystubs = [paystub._id];
                }
            }
        },

        async saveNewPaystub(paystub: Paystub, incomeEmployeeId: string) {
            const body: PaystubRequestBody = {
                employmentType: 'W2',
                incomeEmployeeId: incomeEmployeeId,
                paystub: paystub,
            };

            let response;
            const fetch = useRequestFetch();
            await fetch<PaystubRequestBody>('/api/paystub', {
                method: 'POST',
                body: body,
            }).then((r: Paystub) => {
                paystubActions.updatePaystubInStore(r, incomeEmployeeId);
                response = r;
            });
            // .catch(e => {
            //     console.log('Error in overhaulStore.saveNewPaystub()');
            //     console.log(`${e}`);
            // });
            return response ?? null;
        },

        async updatePaystub(paystubId: string, paystub: Paystub, incomeEmployeeId: string) {
            // Remove a few immutable properties from the paystub object before submitting it
            const { _id, __v, updatedAt, updatedBy, createdAt, ...trimmedPaystub } = paystub;

            const body: PaystubRequestBody = {
                paystubId,
                incomeEmployeeId,
                paystub: trimmedPaystub,
            };

            // try {
            const fetch = useRequestFetch();
            await fetch<PaystubRequestBody>('/api/paystub', {
                method: 'PATCH',
                body: body,
            }).then(() => paystubActions.updatePaystubInStore(paystub));
            // Omitting exception handling here so it can be handled on via the calling method
            // } catch (e) {
            //     console.log('Error in overhaulStore.paystubActions()');
            //     console.log(`${e}`);
            // }
        },

        async deletePaystubFromItem(paystubId: string, incomeEmployeeId: string) {
            const incomeItem = state.income[incomeEmployeeId];
            if (!incomeItem?.paystubs) return;

            // Remove the paystub id from the paystubs array on the item
            const index = incomeItem?.paystubs.findIndex(e => e === paystubId);
            incomeItem.paystubs.splice(index, 1);

            await apiActions.update();

            // TODO: Make a call to the API to delete the paystub from the DB
            // const fetch = useRequestFetch();
            // await fetch<IntakeData>('/api/intake', {
            //     method: 'DELETE',
            //     body: {_id: paystubId},
            // });
        },
    };

    //    ____      _   _
    //   / ___| ___| |_| |_ ___ _ __ ___
    //  | |  _ / _ \ __| __/ _ \ '__/ __|
    //  | |_| |  __/ |_| ||  __/ |  \__ \
    //   \____|\___|\__|\__\___|_|  |___/
    const getters = {
        allRawItems: computed<{ [key: string]: TOverhaulItem }>(() => ({
            ...state.assets,
            ...state.liabilities,
            ...state.expenses,
            ...state.income,
            ...state.insurance,
        })),

        // All portfolio items in an object keyed by item id
        allItems: computed<{ [key: string]: TPopulatedItem }>(() => {
            const allItems = {
                ...state.assets,
                ...state.liabilities,
                ...state.expenses,
                ...state.income,
                ...state.insurance,
            };

            const allItemsObj: { [key: string]: TPopulatedItem } = {};

            for (let key in allItems) {
                if (allItems.hasOwnProperty(key)) {
                    // Check if the property belongs to the object itself
                    const item = allItems[key];

                    const populatedItem = otherActions.populateRawItem(item);

                    allItemsObj[item.id] = populatedItem as TPopulatedItem;
                }
            }

            return allItemsObj;
        }),

        allItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value ?? {});
        }),

        // All asset items in an array
        assetItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value ?? {}).filter(
                item => item?.category === 'Asset',
            ) as TPopulatedItem[];
        }),

        // All asset items formatted for OverhaulLineItem, in an array
        assetLineItems: computed<TOverhaulLineItem[]>(() => {
            return getters.assetItemsArr.value.map(transformPopulatedItemToLineItem);
        }),

        // All liability items in an array
        liabilityItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value).filter(
                item => item?.category === 'Liability',
            ) as TPopulatedItem[];
        }),

        // All liability items formatted for OverhaulLineItem, in an array
        liabilityLineItems: computed<TOverhaulLineItem[]>(() => {
            return getters.liabilityItemsArr.value.map(transformPopulatedItemToLineItem);
        }),

        // All income items in an array
        incomeItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value).filter(
                item => item?.category === 'Income',
            ) as TPopulatedItem[];
        }),

        // All income items formatted for OverhaulLineItem, in an array
        incomeLineItems: computed<TOverhaulLineItem[]>(() => {
            return getters.incomeItemsArr.value.map(transformPopulatedItemToLineItem);
        }),

        // All expense items in an array
        expenseItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value).filter(
                item => item?.category === 'Expense',
            ) as TPopulatedItem[];
        }),

        // All expense items formatted for OverhaulLineItem, in an array
        expenseLineItems: computed<TOverhaulLineItem[]>(() => {
            return getters.expenseItemsArr.value.map(transformPopulatedItemToLineItem);
        }),

        // All insurance items in an array
        insuranceItemsArr: computed<TPopulatedItem[]>(() => {
            return Object.values(getters.allItems.value).filter(
                item => item?.category === 'Insurance',
            ) as TPopulatedItem[];
        }),

        // All expense items formatted for OverhaulLineItem, in an array
        insuranceLineItems: computed<TOverhaulLineItem[]>(() => {
            return getters.insuranceItemsArr.value.map(transformPopulatedItemToLineItem);
        }),

        associatedItemsById: computed(() => {
            const allRawItems = {
                ...state.assets,
                ...state.liabilities,
                ...state.expenses,
                ...state.income,
                ...state.insurance,
            };

            return Object.values(allRawItems ?? {})?.reduce((acc: Map<string, Set<string>>, item: TOverhaulItem) => {
                const {
                    associatedAssets,
                    associatedLiabilities,
                    associatedExpenses,
                    associatedIncome,
                    associatedInsurance,
                } = item;

                const set = new Set([
                    ...(associatedAssets ?? []),
                    ...(associatedLiabilities ?? []),
                    ...(associatedExpenses ?? []),
                    ...(associatedIncome ?? []),
                    ...(associatedInsurance ?? []),
                ]);

                if (set.size) {
                    acc.set(item.id, set);
                }

                return acc;
            }, new Map());
        }),

        // An object with the completion percentage for each category
        completion: computed(() => {
            const reducer = (acc: number, item: TOverhaulLineItem) => {
                return acc + (item.progress?.percentage >= 1 ? 1 : 0);
            };

            const completeCounts = {
                assets: getters.assetLineItems.value.reduce(reducer, 0),
                liabilities: getters.liabilityLineItems.value.reduce(reducer, 0),
                income: getters.incomeLineItems.value.reduce(reducer, 0),
                expenses: getters.expenseLineItems.value.reduce(reducer, 0),
                insurance: getters.insuranceLineItems.value.reduce(reducer, 0),
            };

            // Calculate personal info completion based on required fields
            const personalInfoFields = [
                state.data.name?.firstName,
                state.data.name?.lastName,
                state.data.birthdate,
                state.data.maritalStatus,
            ];
            const personalInfoCompletion = Math.round(
                (personalInfoFields.filter(Boolean).length / personalInfoFields.length) * 100,
            );

            const homeItem = [...getters.assetItemsArr.value, ...getters.expenseItemsArr.value].find(item =>
                ['AssetHome', 'ExpensePrimaryResidence'].includes(item.kind),
            );
            const homeCompletion = homeItem?.progress?.percentage >= 1 ? 100 : 0;

            // Calculate section completions - only complete if items are completed or section was explicitly continued through
            const sectionCompletions = [
                personalInfoCompletion,
                homeCompletion,
                getters.assetLineItems.value.length > 0
                    ? Math.round((completeCounts.assets / getters.assetLineItems.value.length) * 100)
                    : state.data.assetsCompleted
                      ? 100
                      : 0,
                getters.liabilityLineItems.value.length > 0
                    ? Math.round((completeCounts.liabilities / getters.liabilityLineItems.value.length) * 100)
                    : state.data.liabilitiesCompleted
                      ? 100
                      : 0,
                getters.incomeLineItems.value.length > 0
                    ? Math.round((completeCounts.income / getters.incomeLineItems.value.length) * 100)
                    : state.data.incomeCompleted
                      ? 100
                      : 0,
                getters.expenseLineItems.value.length > 0
                    ? Math.round((completeCounts.expenses / getters.expenseLineItems.value.length) * 100)
                    : state.data.expensesCompleted
                      ? 100
                      : 0,
                getters.insuranceLineItems.value.length > 0
                    ? Math.round((completeCounts.insurance / getters.insuranceLineItems.value.length) * 100)
                    : state.data.insuranceCompleted
                      ? 100
                      : 0,
            ];

            const totalCompletion = Math.round(
                sectionCompletions.reduce((acc, value) => acc + value, 0) / sectionCompletions.length,
            );

            return {
                home: homeCompletion,
                assets:
                    getters.assetLineItems.value.length > 0
                        ? Math.round((completeCounts.assets / getters.assetLineItems.value.length) * 100)
                        : state.data.assetsCompleted
                          ? 100
                          : 0,
                liabilities:
                    getters.liabilityLineItems.value.length > 0
                        ? Math.round((completeCounts.liabilities / getters.liabilityLineItems.value.length) * 100)
                        : state.data.liabilitiesCompleted
                          ? 100
                          : 0,
                income:
                    getters.incomeLineItems.value.length > 0
                        ? Math.round((completeCounts.income / getters.incomeLineItems.value.length) * 100)
                        : state.data.incomeCompleted
                          ? 100
                          : 0,
                expenses:
                    getters.expenseLineItems.value.length > 0
                        ? Math.round((completeCounts.expenses / getters.expenseLineItems.value.length) * 100)
                        : state.data.expensesCompleted
                          ? 100
                          : 0,
                insurance:
                    getters.insuranceLineItems.value.length > 0
                        ? Math.round((completeCounts.insurance / getters.insuranceLineItems.value.length) * 100)
                        : state.data.insuranceCompleted
                          ? 100
                          : 0,
                total: totalCompletion,
                personalInfo: personalInfoCompletion,
            };
        }),

        // Props for the TabBar component along the top of the intake screens
        tabBarSteps: computed<TabBarTabProps[]>(() => {
            const completionObj = getters.completion.value;
            return [
                {
                    label: 'Personal Info',
                    name: 'personal',
                    iconShape: 'info',
                    completion: completionObj.personalInfo,
                },
                {
                    label: 'Situation',
                    name: 'situation',
                    iconShape: 'grid-small',
                    completion: state.data.situations.length ? 100 : 0,
                },
                { label: 'Home', name: 'home', completion: completionObj.home },
                { label: 'Assets', name: 'assets', completion: completionObj.assets },
                { label: 'Liabilities', name: 'liabilities', completion: completionObj.liabilities },
                { label: 'Insurance', name: 'insurance', completion: completionObj.insurance },
                { label: 'Income', name: 'income', completion: completionObj.income },
                { label: 'Expenses', name: 'expenses', completion: completionObj.expenses },
                { label: 'Review', name: 'review', completion: 0 },
            ];
        }),

        totalValues: computed(() => {
            const allFields = Object.values(getters.allItems.value)
                .filter(Boolean)
                .reduce((acc: SituationInputGroupProps[], item) => {
                    return acc.concat(item?.fields);
                }, []);

            type TTotalValueKeys = 'assets' | 'liabilities' | 'income' | 'expenses' | 'netWorth' | 'netIncome';

            type TTotalValues = {
                [K in TTotalValueKeys]: number;
            };

            const totals: TTotalValues = {
                assets: allFields.filter(field => field.affects === 'assets').reduce(reduceSituationFieldsToTotal, 0),
                liabilities: allFields
                    .filter(field => field.affects === 'liabilities')
                    .reduce(reduceSituationFieldsToTotal, 0),
                income: allFields.filter(field => field.affects === 'income').reduce(reduceSituationFieldsToTotal, 0),
                expenses: allFields
                    .filter(field => field.affects === 'expenses')
                    .reduce(reduceSituationFieldsToTotal, 0),
                netIncome: 0,
                netWorth: 0,
            };

            // Check each income item for their "income" stats (generated in populateRawItem)
            getters.incomeItemsArr.value?.forEach((item: TPopulatedItem) => {
                const incomeStat = item.stats?.find(s => s.kind === 'income');
                if (incomeStat) {
                    totals.income += incomeStat.value * (incomeStat.frequency === 'mo' ? 12 : 1);
                }
            });

            totals.netWorth = (totals.assets ?? 0) - (totals.liabilities ?? 0);
            totals.netIncome = (totals.income ?? 0) - (totals.expenses ?? 0);

            return totals;
        }),
    };

    const helpers = {
        getItemById(itemId: string) {
            return getters.allItems.value?.[itemId];
        },
    };

    return {
        ...state,
        ...otherActions,
        ...apiActions,
        ...paystubActions,
        ...getters,
        ...helpers,
    };
});

// Helper method to replace any placeholders (e.g. {{ type }}) in field labels and description
// Used when populating a TPopulatedItem from the structure data in intake-data.ts
function replaceFieldPlaceholders(str: string, item: { kindLabel: string; unit?: string }, toLower?: boolean) {
    return str
        ?.replace('{{ type }}', toLower ? toLowerCase(item.kindLabel) : item.kindLabel)
        .replace('{{ unit }}', item.unit ?? 'item')
        .replace('{{ units }}', pluralize(item.unit ?? 'item'));

    // TODO: Not sure how this is going to be structured going forward
    // .replace('{{ subType }}', itemSubType.value?.toLowerCase() ?? '')
}
